{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts"], "names": ["_decorator", "ccenum", "Component", "director", "LabelComponent", "Prefab", "ProgressBar", "WECHAT", "BundleName", "initBundle", "MyApp", "DevLoginUI", "UIMgr", "logDebug", "ccclass", "property", "warnCustom", "console", "warn", "res", "indexOf", "groupCustom", "group", "GameLogLevel", "ResUpdate", "start", "initializeLayers", "THIS", "preloadScene", "OnLoadProgress", "bind", "loadUI", "Common", "plane", "resMgr", "loadAsync", "planeMgr", "initPlanePreFab", "loadScene", "completedCount", "totalCount", "progress", "toFixed", "node", "per<PERSON><PERSON><PERSON>", "string", "<PERSON><PERSON><PERSON><PERSON>", "loadingBar", "onLoad", "onDestroy", "update", "deltaTime"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,c,OAAAA,c;AAAgBC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACjEC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,U,iBAAAA,U;;AAEZC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;AAE9B,UAAIO,MAAJ,EAAY;AACJS,QAAAA,UADI,GACSC,OAAO,CAACC,IADjB;;AAERD,QAAAA,OAAO,CAACC,IAAR,GAAe,UAAUC,GAAV,EAAe;AAC1B,cAAI,OAAOA,GAAP,IAAc,QAAd,IAA0BA,GAAG,CAACC,OAAJ,CAAY,gBAAZ,IAAgC,CAAC,CAA/D,EAAkE;AAC9D;AACH,WAFD,MAEO;AACHJ,YAAAA,UAAU,CAACG,GAAD,CAAV;AACH;AAGJ,SARD;;AASIE,QAAAA,WAXI,GAWUJ,OAAO,CAACK,KAXlB;;AAYRL,QAAAA,OAAO,CAACK,KAAR,GAAgB,UAAUH,GAAV,EAAe;AAC3B,cAAI,OAAOA,GAAP,IAAc,QAAd,IAA0BA,GAAG,CAACC,OAAJ,CAAY,YAAZ,IAA4B,CAAC,CAA3D,EAA8D;AAC1D;AACH,WAFD,MAEO;AACHC,YAAAA,WAAW,CAACF,GAAD,CAAX;AACH;AACJ,SAND;AAOH;;AAEII,MAAAA,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;QAAAA,Y;;AAQLtB,MAAAA,MAAM,CAACsB,YAAD,CAAN;;2BAGaC,S,WADZV,OAAO,CAAC,WAAD,C,UAKHC,QAAQ,CAACX,cAAD,C,UAERW,QAAQ,CAACX,cAAD,C,UAERW,QAAQ,CAACX,cAAD,C,UAERW,QAAQ,CAACT,WAAD,C,2BAXb,MACakB,SADb,SAC+BtB,SAD/B,CACyC;AAAA;AAAA;;AACrC;AACA;AAFqC;;AAAA;;AAAA;;AAAA;AAAA;;AAarCuB,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,8BAAMC,gBAAN,GADI,CAEJ;;AACA,cAAIC,IAAI,GAAG,IAAX;AACAxB,UAAAA,QAAQ,CAACyB,YAAT,CAAsB,MAAtB,EAA8B,KAAKC,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAA9B,iCAA8D,aAAY;AACtE;AACA,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,yCAAN;AACA,kBAAM;AAAA;AAAA,0CAAW;AAAA;AAAA,0CAAWC,MAAtB,CAAN;AACA,gBAAIC,KAAK,SAAS;AAAA;AAAA,gCAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,0CAAWH,MAAlC,EAA0C,cAA1C,EAA0D3B,MAA1D,CAAlB;AACA;AAAA;AAAA,gCAAM+B,QAAN,CAAeC,eAAf,CAA+BJ,KAA/B;AACA9B,YAAAA,QAAQ,CAACmC,SAAT,CAAmB,MAAnB;AACH,WAPD;AAQH;;AAEDT,QAAAA,cAAc,CAACU,cAAD,EAAyBC,UAAzB,EAA6C;AACvD,cAAIC,QAAQ,GAAIF,cAAc,GAAGC,UAAjC;AACA;AAAA;AAAA,oCAAS,WAAT,wBAA0CD,cAA1C,UAA6DC,UAA7D,UAA4E,CAACC,QAAQ,GAAG,GAAZ,EAAiBC,OAAjB,CAAyB,CAAzB,CAA5E;;AACA,cAAI,KAAKC,IAAL,IAAa,IAAjB,EAAuB;AACnB;AACH;;AACD,eAAKC,QAAL,CAAeC,MAAf,GAAwB,CAACJ,QAAQ,GAAG,GAAZ,EAAiBC,OAAjB,CAAyB,CAAzB,IAA8B,GAAtD;AACA,eAAKI,UAAL,CAAiBD,MAAjB,GAA0B,YAAYN,cAAZ,GAA6B,GAA7B,GAAmCC,UAAnC,GAAgD,GAA1E;AACA,eAAKO,UAAL,CAAiBN,QAAjB,GAA4BA,QAA5B;AACH;;AAEDO,QAAAA,MAAM,GAAG,CACR;;AACDC,QAAAA,SAAS,GAAG,CACX;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CACtB;AACA;AACA;AACA;AACA;AACH;;AAjDoC,O;;;;;iBAKO,I;;;;;;;iBAEF,I;;;;;;;iBAEI,I;;;;;;;iBAEL,I", "sourcesContent": ["import { _decorator, ccenum, Component, director, LabelComponent, Prefab, ProgressBar } from 'cc';\r\nimport { WECHAT } from \"cc/env\";\r\nimport { BundleName, initBundle } from '../../bundles/Bundle';\r\nimport \"../AAA/init_cs_proto.js\";\r\nimport { MyApp } from '../MyApp';\r\nimport { DevLoginUI } from '../ui/DevLoginUI';\r\nimport { UIMgr } from \"../ui/UIMgr\";\r\nimport { logDebug } from \"../Utils/Logger\";\r\nconst { ccclass, property } = _decorator;\r\n\r\nif (WECHAT) {\r\n    var warnCustom = console.warn;\r\n    console.warn = function (res) {\r\n        if (typeof res == \"string\" && res.indexOf(\"文件路径在真机上可能无法读取\") > -1) {\r\n            return;\r\n        } else {\r\n            warnCustom(res)\r\n        }\r\n\r\n\r\n    }\r\n    var groupCustom = console.group;\r\n    console.group = function (res) {\r\n        if (typeof res == \"string\" && res.indexOf(\"读取文件/文件夹警告\") > -1) {\r\n            return;\r\n        } else {\r\n            groupCustom(res)\r\n        }\r\n    }\r\n}\r\n\r\nenum GameLogLevel {\r\n    TRACE = 0,\r\n    DEBUG = 1,\r\n    LOG = 2,\r\n    INFO = 3,\r\n    WARN = 4,\r\n    ERROR = 5,\r\n}\r\nccenum(GameLogLevel)\r\n\r\n@ccclass(\"ResUpdate\")\r\nexport class ResUpdate extends Component {\r\n    /* class member could be defined like this */\r\n    // dummy = '';\r\n\r\n    @property(LabelComponent)\r\n    private countLabel: LabelComponent | null = null;\r\n    @property(LabelComponent)\r\n    private perLabel: LabelComponent | null = null;\r\n    @property(LabelComponent)\r\n    private versionLabel: LabelComponent | null = null;\r\n    @property(ProgressBar)\r\n    private loadingBar: ProgressBar | null = null;\r\n\r\n    start() {\r\n        UIMgr.initializeLayers();\r\n        // Your initialization goes here.\r\n        let THIS = this;\r\n        director.preloadScene(\"Main\", this.OnLoadProgress.bind(this), async () => {\r\n            // dev 先load login UI\r\n            await UIMgr.loadUI(DevLoginUI)\r\n            await initBundle(BundleName.Common)\r\n            let plane = await MyApp.resMgr.loadAsync(BundleName.Common, \"prefab/Plane\", Prefab)\r\n            MyApp.planeMgr.initPlanePreFab(plane)\r\n            director.loadScene(\"Main\")\r\n        })\r\n    }\r\n\r\n    OnLoadProgress(completedCount: number, totalCount: number) {\r\n        let progress = (completedCount / totalCount)\r\n        logDebug(\"ResUpdate\", `load main scene, ${completedCount}, ${totalCount}, ${(progress * 100).toFixed(2)}`)\r\n        if (this.node == null) {\r\n            return;\r\n        }\r\n        this.perLabel!.string = (progress * 100).toFixed(2) + \"%\"\r\n        this.countLabel!.string = '加载中...(' + completedCount + '/' + totalCount + ')'\r\n        this.loadingBar!.progress = progress\r\n    }\r\n\r\n    onLoad() {\r\n    }\r\n    onDestroy() {\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n        //if (this.isMainSceneLoaded) //&& window.MyApp && window.MyApp.getLogin().isLogined)\r\n        //{\r\n        //    director.loadScene(\"MainScene\")\r\n        //}\r\n        // Your update function goes here.\r\n    }\r\n}\r\n"]}