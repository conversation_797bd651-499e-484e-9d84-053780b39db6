System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, Vec3, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, PlaneRes;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfMainPlane(extras) {
    _reporterNs.report("MainPlane", "../../AutoGen/Luban/schema", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      Vec3 = _cc.Vec3;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "65758dhbxdF4IEuSjMszJFs", "PlaneRes", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'Rect', 'UITransform', 'Vec3', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PlaneRes", PlaneRes = (_dec = ccclass('PlaneRes'), _dec2 = property(Node), _dec(_class = (_class2 = class PlaneRes extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "bulletLayer", _descriptor, this);

          this.m_screenDatas = [];
          // 屏幕数据
          this.m_config = null;
          this.worldPos = new Vec3();
        } // start() {
        //     this.initPlaneById(70100);
        //     this.bulletLayer!.getWorldPosition(this.worldPos);
        // }
        // protected onDisable(): void {
        //     this.setFireEnable(false);
        //     this.bulletLayer?.removeAllChildren();
        // }
        // update(deltaTime: number) {
        //     this.checkRemoveBullet();
        // }
        // initPlaneById(planeId: number) {
        //     this.m_config = MyApp.lubanTables.TbMainPlane.get(planeId)!;
        //     this.changeScreenLv(1);
        //     this.setFireEnable(true);
        // }
        // /**
        //  * 启用或禁用火力
        //  * @param {boolean} enable 是否启用火力
        //  */
        // setFireEnable(enable: boolean) {
        //     for (let i = 0; i < this.m_fires.length; i++) {
        //         const fire = this.m_fires[i];
        //         fire.isEnabled = enable;
        //     }
        // }
        // /**
        // * 改变屏幕等级
        // * @param {number} level 屏幕等级
        // */
        // changeScreenLv(level: number) {
        //     if (!this.m_config) return;
        //     this.m_screenDatas = [];
        //     const attackData = this.m_config.shiftingatk1;
        //     for (let i = 0; i < attackData.length; i += 8) {
        //         const screenData = attackData.slice(i, i + 8);
        //         this.m_screenDatas.push(screenData);
        //     }
        //     this.m_screenDatas.forEach((data, index) => {
        //         if (this.m_fires[index] == null) {
        //             this.createAttackPoint(data);
        //         } else {
        //             this.changeScreen(index, data);
        //         }
        //     });
        //     for (let i = this.m_screenDatas.length; i < this.m_fires.length; i++) {
        //         this.changeScreen(i, []);
        //     }
        // }
        // /**
        //  * 移除所有火力点
        //  */
        // removeAllFire() {
        //     this.m_fires.forEach((fire) => {
        //         fire.setData([]);
        //     });
        // }
        // createAttackPoint(data: any[]) {
        //     const fireNode = new Node("fire");
        //     fireNode.parent = this.node;
        //     const fire = fireNode.addComponent(FireShells);
        //     fire.setData(data, null, this.bulletLayer);
        //     this.m_fires.push(fire);
        //     return fire;
        // }
        // /**
        //  * 改变屏幕上的火力点
        //  * @param {number} index 火力点索引
        //  * @param {Array|null} data 火力点数据
        //  */
        // changeScreen(index: number, data: any[]) {
        //     if (data == null) {
        //         if (index < this.m_fires.length) {
        //             const fire = this.m_fires[index];
        //             fire.setData([]);
        //             fire.node.active = false;
        //         }
        //     } else {
        //         if (index < this.m_fires.length) {
        //             const fire = this.m_fires[index];
        //             fire.node!.active = true;
        //             fire.setData(data, null, this.bulletLayer);
        //         } else {
        //             this.createAttackPoint(data);
        //         }
        //     }
        // }
        // checkRemoveBullet() {
        //     if (!this.bulletLayer) return;
        //     const bullets = this.bulletLayer.children;
        //     for (let i = bullets.length - 1; i >= 0; i--) {
        //         const bullet = bullets[i];
        //         const uiTransform = bullet.getComponent(UITransform);
        //         if (uiTransform) {
        //             const aabb = uiTransform.getBoundingBoxToWorld();
        //             if (this.isOutOfScreen(aabb)) {
        //                 bullet.destroy();
        //             }
        //         }
        //     }
        // }
        // isOutOfScreen(aabb: Rect): boolean {
        //     const visibleSize = view.getVisibleSize();
        //     const screenLeft = -200;
        //     const screenRight = visibleSize.width + 200;
        //     const screenTop = Math.min(this.worldPos.y + 100, visibleSize.height + 200);
        //     const screenBottom = -200;
        //     // 判断是否超出屏幕边界
        //     return (
        //         aabb.x + aabb.width < screenLeft || // 超出屏幕左边
        //         aabb.x > screenRight ||            // 超出屏幕右边
        //         aabb.y + aabb.height < screenBottom || // 超出屏幕下边
        //         aabb.y > screenTop                 // 超出屏幕上边
        //     );
        // }


      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bulletLayer", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=57f410c64f801a19a0c8f067ffaf2ac22fe94a8b.js.map