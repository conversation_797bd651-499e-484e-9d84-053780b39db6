System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, AttributeConst, AttributeComeConst;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b17f1qiSOpMbYcLq8G7sjhp", "AttributeConst", undefined);

      _export("AttributeConst", AttributeConst = {
        MaxHP: 1,
        Attack: 2,
        AttackBoss: 3,
        AttackNormal: 4,
        BulletHurtResistance: 5,
        CollisionHurtResistance: 6,
        Fortunate: 7,
        MissRate: 8,
        KillScoreRate: 9,
        FinalScoreRate: 10,
        EnergyRecovery: 11,
        EnergyRecoveryRate: 12,
        HPRecovery: 13,
        HPRecoveryRate: 14,
        PickRadius: 15,
        BombMax: 16,
        BombHurt: 17
      });

      _export("AttributeComeConst", AttributeComeConst = {
        EQUIP: "Equip",
        //装备
        SKILL: "Skill",
        //技能
        BUFF: "Buff" //buff

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=e57a55f03225333ecca397277f6551408dba9b7b.js.map