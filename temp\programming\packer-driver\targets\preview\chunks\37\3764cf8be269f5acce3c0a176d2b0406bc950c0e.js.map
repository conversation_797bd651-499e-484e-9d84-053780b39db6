{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneTypes.ts"], "names": ["TabStatus", "BagSortType", "EquipMentAction", "OpenEquipInfoUISource"], "mappings": ";;;;;;;;;;;;;;2BACYA,S,0BAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;AAAAA,QAAAA,S;eAAAA,S;;;6BAKAC,W,0BAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;AAAAA,QAAAA,W;eAAAA,W;;;iCAMAC,e,0BAAAA,e;AAAAA,QAAAA,e;AAAAA,QAAAA,e;eAAAA,e;;;uCAIAC,qB,0BAAAA,qB;AAAAA,QAAAA,qB;AAAAA,QAAAA,qB;eAAAA,qB", "sourcesContent": ["\nexport enum TabStatus {\n    None = 'None',\n    Bag = 'Bag',\n    Merge = 'Merge',\n}\nexport enum BagSortType {\n    None = 'None',\n    Quality = 'Quality',\n    Part = 'Part',\n    Merge = 'Merge',\n}\nexport enum EquipMentAction {\n    Equip = 'Equip',\n    UnEquip = 'UnEquip',\n}\nexport enum OpenEquipInfoUISource {\n    DisPlay = \"DisPlay\",\n    BagGrid = \"BagGrid\"\n}"]}