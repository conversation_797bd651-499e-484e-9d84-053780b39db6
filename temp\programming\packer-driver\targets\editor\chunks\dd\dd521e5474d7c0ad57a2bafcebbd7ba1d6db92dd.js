System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, find, path, Rect, EventGroup, BulletSystem, _crd, ccclass, join;

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "./Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroup(extras) {
    _reporterNs.report("EventGroup", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupData(extras) {
    _reporterNs.report("EventGroupData", "../data/bullet/EventGroupData", _context.meta, extras);
  }

  _export("BulletSystem", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      find = _cc.find;
      path = _cc.path;
      Rect = _cc.Rect;
    }, function (_unresolved_2) {
      EventGroup = _unresolved_2.EventGroup;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7cb83oXVZVMBJYZY7lJNPxV", "BulletSystem", undefined);

      __checkObsolete__(['_decorator', 'find', 'path', 'Vec2', 'Node', 'resources', 'JsonAsset', 'Rect']);

      ({
        ccclass
      } = _decorator);
      ({
        join
      } = path);
      /**
       * BulletSystem - manages all bullets in the game world
       * Handles bullet creation, movement, collision, and cleanup
       */

      _export("BulletSystem", BulletSystem = class BulletSystem {
        /**
         * Main update loop
         */
        static tick(dt) {
          const dtInMiliseconds = dt * 1000;
          this.tickEmitters(dtInMiliseconds);
          this.tickBullets(dtInMiliseconds);
          this.tickEventGroups(dtInMiliseconds);
        }

        static tickEmitters(dt) {
          for (const emitter of this.allEmitters) {
            emitter.tick(dt);
          }
        }

        static tickBullets(dt) {
          for (const bullet of this.allBullets) {
            bullet.tick(dt);
          }
        }

        static tickEventGroups(dt) {
          for (let i = this.allEventGroups.length - 1; i >= 0; i--) {
            const group = this.allEventGroups[i];
            group.tick(dt); // group will remove itself when stopped
            // if (group.status === eEventGroupStatus.Stopped) {
            //     this.allEventGroups.splice(i, 1);
            // }
          }
        }

        static onCreateEmitter(emitter) {
          for (let i = 0; i < this.allEmitters.length; i++) {
            if (this.allEmitters[i] === emitter) {
              return;
            }
          }

          this.allEmitters.push(emitter);

          if (!this.bulletParent || !this.bulletParent.isValid) {
            if (this.bulletParentPath.length > 0) {
              const foundNode = find(this.bulletParentPath);

              if (foundNode) {
                this.bulletParent = foundNode;
              } else {
                console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);
                this.bulletParent = emitter.node;
              }
            }
          }
        }

        static onDestroyEmitter(emitter) {
          const index = this.allEmitters.indexOf(emitter, 0);

          if (index > -1) {
            this.allEmitters.splice(index, 1);
          }
        }

        static onCreateBullet(emitter, bullet) {
          // 这个检查是否会比较冗余
          // for (let i = 0; i < this.allBullets.length; i++) {
          //     if (this.allBullets[i] === bullet) {
          //         return;
          //     }
          // }
          bullet.onCreate(emitter, emitter.bulletID);
          this.allBullets.push(bullet);
          bullet.node.setParent(this.bulletParent, true);
        }

        static onDestroyBullet(bullet) {
          bullet.willDestroy();
          const index = this.allBullets.indexOf(bullet, 0);

          if (index > -1) {
            this.allBullets.splice(index, 1);
          }
        }

        static destroyAllBullets(isEditor = false) {
          for (const bullet of this.allBullets) {
            bullet.willDestroy();
          }

          this.allBullets = [];

          if (isEditor && this.bulletParent) {
            this.bulletParent.removeAllChildren();
          }
        }

        static createEmitterEventGroup(ctx, data) {
          var _ctx$emitter;

          (_ctx$emitter = ctx.emitter) == null || _ctx$emitter.eventGroups.push(new (_crd && EventGroup === void 0 ? (_reportPossibleCrUseOfEventGroup({
            error: Error()
          }), EventGroup) : EventGroup)(ctx, data));
        }

        static createBulletEventGroup(ctx, data) {
          var _ctx$bullet;

          // the name is the json file name
          // let finalPath = join(this.bulletEventGroupPath, name);
          // resources.load(finalPath, JsonAsset, (err, data) => {
          //     if (err) {
          //         console.error("Failed to load bullet event group:", err);
          //         return null;
          //     }
          //     const eventData = EventGroupData.fromJSON(data.json);
          //     const eventGroup = new EventGroup(ctx, eventData);
          //     eventGroup.start();
          //     ctx.bullet?.eventGroups.push(eventGroup);
          // });
          // console.log('createBulletEventGroup: ', data.name);
          const eventGroup = new (_crd && EventGroup === void 0 ? (_reportPossibleCrUseOfEventGroup({
            error: Error()
          }), EventGroup) : EventGroup)(ctx, data);
          eventGroup.start();
          (_ctx$bullet = ctx.bullet) == null || _ctx$bullet.eventGroups.push(eventGroup);
        }
        /**
         * Called when a new event group is created or turn active
         */


        static onCreateEventGroup(eventGroup) {
          this.allEventGroups.push(eventGroup);
        }

        static onDestroyEventGroup(eventGroup) {
          const index = this.allEventGroups.indexOf(eventGroup, 0);

          if (index > -1) {
            this.allEventGroups.splice(index, 1);
          } // console.log('onDestroyEventGroup: ', eventGroup.data.name);

        }

      });

      BulletSystem.bulletParentPath = 'Canvas/bullet_root';
      BulletSystem.emitterEventGroupPath = 'Game/emitter/events/Emitter';
      BulletSystem.bulletEventGroupPath = 'Game/emitter/events/Bullet';

      /**
       * All active bullets
       */
      BulletSystem.allBullets = [];

      /**
       * All active emitters
       */
      BulletSystem.allEmitters = [];

      /**
       * All active action groups
       */
      BulletSystem.allEventGroups = [];
      // public static isEmitterEnabled: boolean = true;
      // public static isBulletEnabled: boolean = true;
      BulletSystem.bulletParent = void 0;

      /**
       * Bounds of the game world for bullet cleanup
       * 这个值需要在合适的地方适当调整
       */
      BulletSystem.worldBounds = new Rect(-375, -667, 750, 1334);

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=dd521e5474d7c0ad57a2bafcebbd7ba1d6db92dd.js.map