import { _decorator, Component, Label, Node, ProgressBar, Sprite } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('ProgressPanel')
export class ProgressPanel extends Component {
    @property(Node)
    rewardParentNode: Node | null = null;
    @property(Node)
    progressNumberParentNode: Node | null = null;
    @property(Node)
    progressStartNumber: Node | null = null;
    @property(ProgressBar)
    progress: ProgressBar | null = null;
    @property(Node)
    countDownParentNode: Node | null = null;

    private _rewardNodes: Node[] = [];
    private _progressNumberNodes: Node[] = [];
    private _statusColors: string[] = ["#A5A5A5", "#FFCE33"];
    private _numbers: string[] = ["20", "40", "60", "80", "100"];
    protected onLoad(): void {
        this.rewardParentNode?.children.forEach((node) => {
            this._rewardNodes.push(node);
        })
        this.progressNumberParentNode?.children.forEach((node) => {
            this._progressNumberNodes.push(node);
        })
    }

    init() {
        this._rewardNodes.forEach((node, index) => {
            this.renderReward(node, index);
        })
        this._progressNumberNodes.forEach((node, index) => {
            this.renderProgressNumber(node, index);
        })
    }

    private renderReward(node: Node, index: number) {
        const icon = node.getComponentsInChildren(Sprite)[0];
        const mask = node.getComponentsInChildren(Sprite)[1];
    }

    private renderProgressNumber(node: Node, index: number) {
        const number = node.getComponentInChildren(Label)!;
        const bg = node.getComponentInChildren(Sprite)!;
        number.string = this._numbers[index];
    }
}