System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, Node, view, LevelDataEventTriggerType, MyApp, Wave, GameIns, _dec, _class, _crd, ccclass, TerrainsNodeName, DynamicNodeName, WaveNodeName, EventNodeName, LevelLayerUI;

  function _reportPossibleCrUseOfLevelDataEvent(extras) {
    _reporterNs.report("LevelDataEvent", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataWave(extras) {
    _reporterNs.report("LevelDataWave", "../../../leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerType(extras) {
    _reporterNs.report("LevelDataEventTriggerType", "../../../leveldata/trigger/LevelDataEventTrigger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerWave(extras) {
    _reporterNs.report("LevelDataEventTriggerWave", "../../../leveldata/trigger/LevelDataEventTriggerWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataEventTriggerLog(extras) {
    _reporterNs.report("LevelDataEventTriggerLog", "../../../leveldata/trigger/LevelDataEventTriggerLog", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfWave(extras) {
    _reporterNs.report("Wave", "../../wave/Wave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      view = _cc.view;
    }, function (_unresolved_2) {
      LevelDataEventTriggerType = _unresolved_2.LevelDataEventTriggerType;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      Wave = _unresolved_4.Wave;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d139a2/Z9NEzIGUXE+qqxb8", "LevelLayerUI", undefined);

      __checkObsolete__(['_decorator', 'assetManager', 'Component', 'instantiate', 'Node', 'Prefab', 'Vec2', 'view']);

      ({
        ccclass
      } = _decorator);
      TerrainsNodeName = "terrains";
      DynamicNodeName = "dynamic";
      WaveNodeName = "waves";
      EventNodeName = "events";

      _export("LevelLayerUI", LevelLayerUI = (_dec = ccclass('LevelLayerUI'), _dec(_class = class LevelLayerUI extends Component {
        constructor() {
          super(...arguments);
          this.backgrounds = [];
          this._offSetY = 0;
          // 当前关卡的偏移量
          this._bTrackBackground = true;
          // 是否跟随背景移动（预加载关卡未在显示区域的时候跟随）
          this.terrainsNode = null;
          this.dynamicNode = null;
          this.waves = [];
          this.events = [];
          this.enableEvents = [];
        }

        get TrackBackground() {
          return this._bTrackBackground;
        }

        set TrackBackground(value) {
          this._bTrackBackground = value;
        }

        onLoad() {}

        initByLevelData(data, offSetY) {
          var _data$terrains;

          this._offSetY = offSetY;
          this.node.setPosition(0, offSetY, 0);
          this.terrainsNode = this._getOrAddNode(this.node, TerrainsNodeName);
          this.dynamicNode = this._getOrAddNode(this.node, DynamicNodeName);
          console.log('LevelLayerUI', " initByLevelData");
          this.backgrounds = [];
          (_data$terrains = data.terrains) == null || _data$terrains.forEach(terrain => {
            var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.defaultBundleName, terrain.uuid);
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
              if (err) {
                console.error('LevelLayerUI', " initByLevelData load terrain prefab err", err);
                return;
              }

              var terrainNode = instantiate(prefab);
              terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);
              terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);
              terrainNode.setRotationFromEuler(0, 0, terrain.rotation);
              this.terrainsNode.addChild(terrainNode);
            });
          });
          this.waves = [...data.waves];
          this.waves.sort((a, b) => a.position.y - b.position.y);
          this.events = [...data.events];
          this.events.sort((a, b) => a.position.y - b.position.y);
        }

        tick(deltaTime, speed) {
          var _this = this;

          if (this.TrackBackground === true) {
            var posY = this.node.getPosition().y;
            var topPosY = view.getVisibleSize().height / 2;

            if (posY < topPosY) {
              this._bTrackBackground = false;
            }
          }

          var prePosY = this.node.getPosition().y;
          this.node.setPosition(0, prePosY - deltaTime * speed, 0);

          var _loop = function _loop() {
            var wave = _this.waves[0];

            _this.waves.shift();

            var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.defaultBundleName, wave.waveUUID);
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
              if (err) {
                console.error('LevelLayerUI', " tick load wave prefab err", err);
                return;
              }

              var waveComp = instantiate(prefab).getComponent(_crd && Wave === void 0 ? (_reportPossibleCrUseOfWave({
                error: Error()
              }), Wave) : Wave);
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).waveManager.addWaveByLevel(waveComp, wave.position.x, wave.position.y - _this.node.position.y);
            });
          };

          while (this.waves.length > 0 && this.waves[0].position.y < this.node.getPosition().y) {
            _loop();
          }

          while (this.events.length > 0 && this.events[0].position.y < this.node.getPosition().y) {
            var event = this.events[0];
            this.events.shift();
            this.enableEvents.push(event);
          }

          var _loop2 = function _loop2() {
            var event = _this.enableEvents[i];
            var condResult = true;

            for (var cond of event.conditions) {}

            if (condResult) {
              _this.enableEvents.splice(i, 1);

              for (var trigger of event.triggers) {
                switch (trigger._type) {
                  case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                    error: Error()
                  }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Log:
                    console.log("LevelLayerUI", "trigger log", trigger.message);
                    break;

                  case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                    error: Error()
                  }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Audio:
                    break;

                  case (_crd && LevelDataEventTriggerType === void 0 ? (_reportPossibleCrUseOfLevelDataEventTriggerType({
                    error: Error()
                  }), LevelDataEventTriggerType) : LevelDataEventTriggerType).Wave:
                    var waveTriger = trigger;
                    var path = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.getAssetPath((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.defaultBundleName, waveTriger.waveUUID);
                    console.log("LevelLayerUI", "trigger wave", path);
                    (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                      error: Error()
                    }), MyApp) : MyApp).resMgr.load(path, (err, prefab) => {
                      if (err) {
                        console.error('LevelLayerUI', " tick load wave prefab err", err);
                        return;
                      }

                      console.log("LevelLayerUI", "trigger wave!!", prefab);
                      var waveComp = instantiate(prefab).getComponent(_crd && Wave === void 0 ? (_reportPossibleCrUseOfWave({
                        error: Error()
                      }), Wave) : Wave);
                      (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                        error: Error()
                      }), GameIns) : GameIns).waveManager.addWaveByLevel(waveComp, event.position.x, Math.max(0, event.position.y - _this.node.position.y));
                    });
                    break;
                }
              }
            }
          };

          for (var i = this.enableEvents.length - 1; i >= 0; i--) {
            _loop2();
          }
        }

        _getOrAddNode(node_parent, name) {
          var node = node_parent.getChildByName(name);

          if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
          }

          return node;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6f335cfc6bb419dfa35ef3d6f0b4b8155ff91549.js.map