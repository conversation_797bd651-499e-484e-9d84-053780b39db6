{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagItem.ts"], "names": ["_decorator", "Component", "Label", "math", "Node", "Sprite", "DataMgr", "EventMgr", "MyApp", "logDebug", "PlaneUIEvent", "ButtonPlus", "TabStatus", "ccclass", "property", "BagItem", "_item", "_tabStatus", "None", "_fakeColors", "onLoad", "getComponent", "addClick", "onClick", "onDestroy", "targetOff", "<PERSON><PERSON>", "mask", "active", "selectedIcon", "equip", "eqCombine", "isFull", "emit", "BagItemClick", "onBagTabStatusRender", "item", "Bag", "onMetaDataRender", "onCombineTabStatusRender", "isCanCombine", "info", "getByGuid", "guid", "EquipCfg", "lubanTables", "TbEquip", "get", "item_id", "onEquipDataRender", "itemCfg", "TbItem", "onItemDataRender", "getComponentInChildren", "string", "equipCfg", "itemNum", "node", "name", "quality", "color", "count", "toString"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AAE1CC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AAGAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;yBAGjBe,O,WADZF,OAAO,CAAC,SAAD,C,UAEHC,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACV,IAAD,C,UAERU,QAAQ,CAACZ,KAAD,C,2BANb,MACaa,OADb,SAC6Bd,SAD7B,CACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAQ3Be,KAR2B,GAQQ,IARR;AAAA,eAS3BC,UAT2B,GASH;AAAA;AAAA,sCAAUC,IATP;AAAA,eAU3BC,WAV2B,GAUH,CAC5B,SAD4B,EAE5B,SAF4B,EAG5B,SAH4B,EAI5B,SAJ4B,EAK5B,SAL4B,EAM5B,WAN4B,EAO5B,SAP4B,CAVG;AAAA;;AAoBzBC,QAAAA,MAAM,GAAS;AACrB,eAAKC,YAAL;AAAA;AAAA,wCAA+BC,QAA/B,CAAwC,KAAKC,OAA7C,EAAsD,IAAtD;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEOF,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,oCAAS,SAAT;AACA,cAAI,CAAC,KAAKP,KAAV,EAAiB;;AACjB,cAAI,KAAKC,UAAL,IAAmB;AAAA;AAAA,sCAAUS,KAAjC,EAAwC;AACpC,gBAAI,KAAKC,IAAL,CAAWC,MAAX,IAAqB,CAAC,KAAKC,YAA/B,EAA6C;AACzC;AACH;;AACD,gBAAI,CAAC,KAAKF,IAAL,CAAWC,MAAZ,IAAsB;AAAA;AAAA,oCAAQE,KAAR,CAAcC,SAAd,CAAwBC,MAAxB,EAA1B,EAA4D;AACxD;AACH;AACJ;;AACD;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,4CAAaC,YAA3B,EAAyC,KAAKlB,KAA9C;AACH;;AAEDmB,QAAAA,oBAAoB,CAACC,IAAD,EAA2B;AAC3C,eAAKnB,UAAL,GAAkB;AAAA;AAAA,sCAAUoB,GAA5B;AACA,eAAKrB,KAAL,GAAaoB,IAAb;AACA,eAAKP,YAAL,CAAmBD,MAAnB,GAA4B,KAA5B;AACA,eAAKD,IAAL,CAAWC,MAAX,GAAoB,KAApB;AACA,eAAKU,gBAAL;AACH;;AAEDC,QAAAA,wBAAwB,CAACH,IAAD,EAA2B;AAC/C,eAAKnB,UAAL,GAAkB;AAAA;AAAA,sCAAUS,KAA5B;AACA,eAAKV,KAAL,GAAaoB,IAAb;;AACA,cAAI;AAAA;AAAA,kCAAQN,KAAR,CAAcC,SAAd,CAAwBS,YAAxB,CAAqC,KAAKxB,KAA1C,CAAJ,EAAsD;AAClD,gBAAMyB,IAAI,GAAG;AAAA;AAAA,oCAAQX,KAAR,CAAcC,SAAd,CAAwBW,SAAxB,CAAkC,KAAK1B,KAAL,CAAW2B,IAA7C,CAAb;;AACA,gBAAIF,IAAJ,EAAU;AACN,mBAAKZ,YAAL,CAAmBD,MAAnB,GAA4B,IAA5B;AACA,mBAAKD,IAAL,CAAWC,MAAX,GAAoB,IAApB;AACH,aAHD,MAGO;AACH,mBAAKC,YAAL,CAAmBD,MAAnB,GAA4B,KAA5B;AACA,mBAAKD,IAAL,CAAWC,MAAX,GAAoB,KAApB;AACH;AACJ,WATD,MASO;AACH,iBAAKC,YAAL,CAAmBD,MAAnB,GAA4B,KAA5B;AACA,iBAAKD,IAAL,CAAWC,MAAX,GAAoB,IAApB;AACH;;AACD,eAAKU,gBAAL;AACH;;AAEOA,QAAAA,gBAAgB,GAAG;AAAA;;AACvB,cAAMM,QAAQ,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,GAA1B,aAA8B,KAAK/B,KAAL,CAAYgC,OAA1C,uBAAqD,CAArD,CAAjB;;AACA,cAAIJ,QAAJ,EAAc;AACV,iBAAKK,iBAAL,CAAuBL,QAAvB;AACH,WAFD,MAEO;AAAA;;AACH,gBAAMM,OAAO,GAAG;AAAA;AAAA,gCAAML,WAAN,CAAkBM,MAAlB,CAAyBJ,GAAzB,cAA6B,KAAK/B,KAAL,CAAYgC,OAAzC,wBAAoD,CAApD,CAAhB;;AACA,gBAAIE,OAAJ,EAAa;AACT,mBAAKE,gBAAL,CAAsBF,OAAtB;AACH,aAFD,MAEO;AACH,mBAAKG,sBAAL,CAA4BnD,KAA5B,EAAoCoD,MAApC,GAA6C,IAA7C;AACH;AACJ;AACJ;;AAEOL,QAAAA,iBAAiB,CAACM,QAAD,EAAqB;AAC1C,eAAKC,OAAL,CAAcC,IAAd,CAAmB7B,MAAnB,GAA4B,KAA5B;AACA,eAAKyB,sBAAL,CAA4BnD,KAA5B,EAAoCoD,MAApC,GAA6C,CAAAC,QAAQ,QAAR,YAAAA,QAAQ,CAAEG,IAAV,yBAAwBH,QAAxB,oBAAwBA,QAAQ,CAAEI,OAAlC,QAA7C;AACA,eAAKF,IAAL,CAAUJ,sBAAV,CAAiChD,MAAjC,EAA0CuD,KAA1C,GAAkDzD,IAAI,CAACyD,KAAL,CAAW,KAAKzC,WAAL,CAAiBoC,QAAQ,CAACI,OAA1B,CAAX,CAAlD;AACH;;AAEOP,QAAAA,gBAAgB,CAACF,OAAD,EAAmB;AAAA;;AACvC,eAAKM,OAAL,CAAcC,IAAd,CAAmB7B,MAAnB,GAA4B,IAA5B;AACA,eAAK4B,OAAL,CAAcF,MAAd,+BAAuB,KAAKtC,KAA5B,qBAAuB,YAAY6C,KAAZ,CAAmBC,QAAnB,EAAvB,wBAAwD,GAAxD;AACA,eAAKT,sBAAL,CAA4BnD,KAA5B,EAAoCoD,MAApC,GAA6C,CAAAJ,OAAO,QAAP,YAAAA,OAAO,CAAEQ,IAAT,yBAAuBR,OAAvB,oBAAuBA,OAAO,CAAES,OAAhC,QAA7C;AACA,eAAKF,IAAL,CAAUJ,sBAAV,CAAiChD,MAAjC,EAA0CuD,KAA1C,GAAkDzD,IAAI,CAACyD,KAAL,CAAW,KAAKzC,WAAL,CAAiB+B,OAAO,CAACS,OAAzB,CAAX,CAAlD;AACH;;AA9FkC,O;;;;;iBAEP,I;;;;;;;iBAER,I;;;;;;;iBAEI,I", "sourcesContent": ["import { _decorator, Component, Label, math, Node, Sprite } from 'cc';\n\nimport { DataMgr } from \"db://assets/bundles/common/script/data/DataManager\";\nimport { EventMgr } from \"db://assets/bundles/common/script/event/EventManager\";\nimport { ResEquip, ResItem } from 'db://assets/scripts/AutoGen/Luban/schema';\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { MyApp } from 'db://assets/scripts/MyApp';\nimport { logDebug } from 'db://assets/scripts/Utils/Logger';\nimport { PlaneUIEvent } from \"../../../../event/PlaneUIEvent\";\nimport { ButtonPlus } from \"../../../common/components/button/ButtonPlus\";\nimport { TabStatus } from \"../../PlaneTypes\";\nconst { ccclass, property } = _decorator;\n\n@ccclass('BagItem')\nexport class BagItem extends Component {\n    @property(Node)\n    selectedIcon: Node | null = null;\n    @property(Node)\n    mask: Node | null = null;\n    @property(Label)\n    itemNum: Label | null = null;\n\n    private _item: csproto.cs.ICSItem | null = null;\n    private _tabStatus: TabStatus = TabStatus.None;\n    private _fakeColors: string[] = [\n        \"#A0A0A0\",\n        \"#1EFF00\",\n        \"#0070FF\",\n        \"#A335EE\",\n        \"#FF8000\",\n        \"#80e6e6ff\",\n        \"#E6CC80\"\n    ]\n\n    protected onLoad(): void {\n        this.getComponent(ButtonPlus)!.addClick(this.onClick, this)\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this)\n    }\n\n    private onClick() {\n        logDebug(\"PlaneUI\", `onClick`)\n        if (!this._item) return\n        if (this._tabStatus == TabStatus.Merge) {\n            if (this.mask!.active && !this.selectedIcon) {\n                return\n            }\n            if (!this.mask!.active && DataMgr.equip.eqCombine.isFull()) {\n                return\n            }\n        }\n        EventMgr.emit(PlaneUIEvent.BagItemClick, this._item)\n    }\n\n    onBagTabStatusRender(item: csproto.cs.ICSItem) {\n        this._tabStatus = TabStatus.Bag;\n        this._item = item;\n        this.selectedIcon!.active = false;\n        this.mask!.active = false;\n        this.onMetaDataRender()\n    }\n\n    onCombineTabStatusRender(item: csproto.cs.ICSItem) {\n        this._tabStatus = TabStatus.Merge;\n        this._item = item;\n        if (DataMgr.equip.eqCombine.isCanCombine(this._item)) {\n            const info = DataMgr.equip.eqCombine.getByGuid(this._item.guid!)\n            if (info) {\n                this.selectedIcon!.active = true;\n                this.mask!.active = true;\n            } else {\n                this.selectedIcon!.active = false;\n                this.mask!.active = false;\n            }\n        } else {\n            this.selectedIcon!.active = false;\n            this.mask!.active = true;\n        }\n        this.onMetaDataRender()\n    }\n\n    private onMetaDataRender() {\n        const EquipCfg = MyApp.lubanTables.TbEquip.get(this._item!.item_id ?? 0)\n        if (EquipCfg) {\n            this.onEquipDataRender(EquipCfg)\n        } else {\n            const itemCfg = MyApp.lubanTables.TbItem.get(this._item!.item_id ?? 0)\n            if (itemCfg) {\n                this.onItemDataRender(itemCfg)\n            } else {\n                this.getComponentInChildren(Label)!.string = \"未知\"\n            }\n        }\n    }\n\n    private onEquipDataRender(equipCfg: ResEquip) {\n        this.itemNum!.node.active = false;\n        this.getComponentInChildren(Label)!.string = equipCfg?.name + `(品质:${equipCfg?.quality})`\n        this.node.getComponentInChildren(Sprite)!.color = math.color(this._fakeColors[equipCfg.quality])\n    }\n\n    private onItemDataRender(itemCfg: ResItem) {\n        this.itemNum!.node.active = true;\n        this.itemNum!.string = this._item?.count!.toString() ?? \"0\"\n        this.getComponentInChildren(Label)!.string = itemCfg?.name + `(品质:${itemCfg?.quality})`\n        this.node.getComponentInChildren(Sprite)!.color = math.color(this._fakeColors[itemCfg.quality])\n    }\n}"]}