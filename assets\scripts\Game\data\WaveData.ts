
import { _decorator, error, v2, Vec2, Prefab, Enum } from "cc";
import { ExpressionValue } from "./bullet/ExpressionValue";
import { IEventConditionData, eCompareOp, eConditionOp, IEventActionData, eTargetValueType } from "./bullet/EventGroupData";
import { eEasing } from "../bullet/Easing";
const { ccclass, property } = _decorator;

export enum eSpawnOrder {
    Sequential = 0,
    Random = 1,
}

export enum eWaveAngleType {
    Fixed, FacingPlayer, FacingMoveDir
}

export enum eWaveConditionType {
    Player_Level,       // 玩家等级

    Spawn_Index,        // 当前生成的索引
}

export enum eWaveActionType {
    Speed,
    SpeedAngle,
}

// 和发射器的事件组类似
@ccclass("WaveConditionData")
export class WaveConditionData implements IEventConditionData {
    @property({ type: Enum(eConditionOp), displayName: '条件关系' })
    public op: eConditionOp = eConditionOp.And;

    @property({visible:false})
    public type: eWaveConditionType = eWaveConditionType.Player_Level;

    @property({ type: Enum(eCompareOp), displayName: '比较方式' })
    public compareOp: eCompareOp = eCompareOp.Equal;
    
    // 条件值: 例如持续时间、距离
    @property({visible:false})
    public targetValue : ExpressionValue = new ExpressionValue('0');
    @property({displayName: '目标值'})
    public get targetValueStr(): string { return this.targetValue.raw; }
    public set targetValueStr(value: string) { this.targetValue.raw = value; }
}

@ccclass("WaveActionData")
export class WaveActionData implements IEventActionData {
    @property({visible:false})
    public type: eWaveActionType = eWaveActionType.Speed;
    
    // 持续时间: 0表示立即执行
    @property({visible:false})
    public duration: ExpressionValue = new ExpressionValue('0');
    @property({ displayName: '持续时间' })
    public get durationStr(): string { return this.duration.raw; }
    public set durationStr(value: string) { this.duration.raw = value; }
    
    @property({visible:false})
    public targetValue: ExpressionValue = new ExpressionValue('0');
    @property({displayName: '目标值'})
    public get targetValueStr(): string { return this.targetValue.raw; }
    public set targetValueStr(value: string) { this.targetValue.raw = value; }
    
    @property({ type: Enum(eTargetValueType), displayName: '目标值类型' })
    public targetValueType: eTargetValueType = eTargetValueType.Absolute;
    
    @property({ type: Enum(eEasing), displayName: '缓动函数' })
    public easing: eEasing = eEasing.Linear;
}

@ccclass("WaveEventGroupData")
export class WaveEventGroupData {
    @property({ displayName: '事件组名称' })
    public name: string = "";

    @property({ type: [WaveConditionData], displayName: '条件列表' })
    public conditions: WaveConditionData[] = [];

    @property({ type: [WaveActionData], displayName: '行为列表' })
    public actions: WaveActionData[] = [];
}

/**
 * 波次数据：未来代替现有的EnemyWave
 * 所有时间相关的，单位都是毫秒(ms)
 */
@ccclass("WaveData")
export class WaveData {
    // 波次都由LevelTrigger来触发，例如: 上一波结束后触发，或者到达某个距离后触发
    // 因此这里不再配置触发条件
    @property({visible:false})
    planeList: number[] = [];

    @property({group: "基础属性", displayName: "飞机列表(,号分隔)", multiline: false})
    public get planeListStr(): string {
        return (this.planeList || []).join(",");
    }
    public set planeListStr(value: string) {
        if (value === "") {
            this.planeList = [];
            return;
        }
        this.planeList = value.split(",").map((item) => parseInt(item));
    }

    @property({group: "基础属性", type: Enum(eSpawnOrder), displayName: "出生顺序"})
    spawnOrder: eSpawnOrder = eSpawnOrder.Sequential;

    @property({group: "基础属性", displayName: "出生间隔(ms)"})
    spawnInterval: number = 1000;

    @property({group: "基础属性", displayName: "出生位置"})
    spawnPos: Vec2 = v2(0, 0);

    @property({visible:false})
    public spawnAngle : ExpressionValue = new ExpressionValue('0');
    @property({group: "基础属性", displayName: "出生角度"})
    public get spawnAnglenStr(): string { return this.spawnAngle.raw; }
    public set spawnAnglenStr(value: string) { this.spawnAngle.raw = value; }

    @property({group: "基础属性", displayName: "出生速度"})
    spawnSpeed: number = 500;

    

    @property({group: '事件组', type: [WaveEventGroupData], displayName: '事件组'})
    eventGroupData: WaveEventGroupData[] = [];
}