System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, director, Label, Node, BundleName, MyApp, LoadingUI, BaseUI, UILayer, UIMgr, DataEvent, EventMgr, HomeUIEvent, ButtonPlus, PopupUI, BuidingUI, BuildingInfoUI, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, StoryUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMode(extras) {
    _reporterNs.report("GameMode", "db://assets/scripts/AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLoadingUI(extras) {
    _reporterNs.report("LoadingUI", "db://assets/scripts/ui/LoadingUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUIEvent(extras) {
    _reporterNs.report("HomeUIEvent", "../../event/HomeUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPopupUI(extras) {
    _reporterNs.report("PopupUI", "../home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBuidingUI(extras) {
    _reporterNs.report("BuidingUI", "./BuidingUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBuildingInfoUI(extras) {
    _reporterNs.report("BuildingInfoUI", "./BuildingInfoUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      director = _cc.director;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      LoadingUI = _unresolved_4.LoadingUI;
    }, function (_unresolved_5) {
      BaseUI = _unresolved_5.BaseUI;
      UILayer = _unresolved_5.UILayer;
      UIMgr = _unresolved_5.UIMgr;
    }, function (_unresolved_6) {
      DataEvent = _unresolved_6.DataEvent;
    }, function (_unresolved_7) {
      EventMgr = _unresolved_7.EventMgr;
    }, function (_unresolved_8) {
      HomeUIEvent = _unresolved_8.HomeUIEvent;
    }, function (_unresolved_9) {
      ButtonPlus = _unresolved_9.ButtonPlus;
    }, function (_unresolved_10) {
      PopupUI = _unresolved_10.PopupUI;
    }, function (_unresolved_11) {
      BuidingUI = _unresolved_11.BuidingUI;
    }, function (_unresolved_12) {
      BuildingInfoUI = _unresolved_12.BuildingInfoUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "52836Q5mVNOTYxe0CiSR7pB", "StoryUI", undefined);

      __checkObsolete__(['_decorator', 'director', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("StoryUI", StoryUI = (_dec = ccclass("StoryUI"), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(Label), _dec4 = property(Node), _dec5 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec(_class = (_class2 = class StoryUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "btnMap", _descriptor, this);

          _initializerDefineProperty(this, "lblBattle", _descriptor2, this);

          _initializerDefineProperty(this, "buidings", _descriptor3, this);

          this.index = 0;
          this.imageUrls = ["item_7_1", "item_7_2", "item_7_3", "item_7_4", "item_7_5", "item_7_6", "item_7_8"];
          this.gameMode = undefined;

          _initializerDefineProperty(this, "btnClose", _descriptor4, this);
        }

        static getUrl() {
          return "prefab/ui/StoryUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeStory;
        }

        onLoad() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).BattleItemClick, this.onBattleItemClick, this);
          this.gameMode = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbGameMode.get(2001); //let list: GameMode[] = MyApp.lubanTables.TbGameMode.getDataList().filter(element => element.modeType == res.ModeType.STORY);

          var list = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbGameMode.getDataList();
          var row = 0;
          var bagItems = this.buidings.children.forEach(element => {
            element.getComponent(_crd && BuidingUI === void 0 ? (_reportPossibleCrUseOfBuidingUI({
              error: Error()
            }), BuidingUI) : BuidingUI).setNewFrame(this.imageUrls[row]);
            element.getComponent(_crd && BuidingUI === void 0 ? (_reportPossibleCrUseOfBuidingUI({
              error: Error()
            }), BuidingUI) : BuidingUI).setTitle(list[row].ID, "\u7B2C" + (row + 1) + "\u5173");
            row++;
          });
          this.btnClose.addClick(this.closeUI, this);
        }

        closeUI() {
          return _asyncToGenerator(function* () {
            //await UIMgr.openUI(HomeUI)
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(StoryUI);
          })();
        }

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

        onBattleItemClick(index, item) {
          this.index = index;
          this.lblBattle.string = item + "(" + index + ")";
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && BuildingInfoUI === void 0 ? (_reportPossibleCrUseOfBuildingInfoUI({
            error: Error()
          }), BuildingInfoUI) : BuildingInfoUI);
        }

        onShow() {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this.btnMap.addClick(_this.onMapClick, _this);
          })();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

        onListRender(listItem, row) {
          listItem.name = "listItem" + row;
          listItem.getComponentInChildren(Label).string = "\u7B2C" + (row + 1) + "\u5173";
        }

        onMapClick() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            if (_this2.index == 0) {
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
                error: Error()
              }), PopupUI) : PopupUI, "未选择地图");
              return;
            }

            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && LoadingUI === void 0 ? (_reportPossibleCrUseOfLoadingUI({
              error: Error()
            }), LoadingUI) : LoadingUI);
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).Leave);
            director.preloadScene("Game", /*#__PURE__*/_asyncToGenerator(function* () {
              director.loadScene("Game");
            }));
          })();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnMap", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "lblBattle", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "buidings", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "btnClose", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fac430a3ea3298469d664700720f404884b654ea.js.map