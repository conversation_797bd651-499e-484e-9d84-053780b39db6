{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts"], "names": ["_decorator", "BundleName", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "SkyIslandUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getBundleName", "HomeSkyIsland", "onLoad", "onShow", "onHide", "onClose", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;6BAGjBM,W,WADZF,OAAO,CAAC,aAAD,C,gBAAR,MACaE,WADb;AAAA;AAAA,4BACwC;AAChB,eAANC,MAAM,GAAW;AAAE,iBAAO,uBAAP;AAAiC;;AAC5C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AACpC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,aAAlB;AAAiC;;AAE/DC,QAAAA,MAAM,GAAS,CAExB;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAhBmC,O", "sourcesContent": ["import { _decorator } from 'cc';\n\nimport { BundleName } from 'db://assets/bundles/Bundle';\nimport { BaseUI, UILayer } from 'db://assets/scripts/ui/UIMgr';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('SkyIslandUI')\nexport class SkyIslandUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/SkyIslandUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    public static getBundleName(): string { return BundleName.HomeSkyIsland }\n\n    protected onLoad(): void {\n\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}