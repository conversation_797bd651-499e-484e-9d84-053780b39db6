import { _decorator, Component, instantiate, Node, size, Sprite, Tween, tween, UITransform, v3, Vec2, Vec3 } from 'cc';
import TrackComponent from '../../base/TrackComponent';
import { GameEnum } from '../../../const/GameEnum';
import { Tools } from '../../../utils/Tools';
import { GameIns } from '../../../GameIns';
import { TrackGroup } from '../../../data/EnemyWave';
import { BossData } from '../../../data/BossData';
import PlaneBase from '../PlaneBase';
import EnemyPlaneRole from '../enemy/EnemyPlaneRole';
import FBoxCollider from '../../../collider-system/FBoxCollider';
import FCollider, { ColliderGroupType } from '../../../collider-system/FCollider';
import Bullet from '../../bullet/Bullet';
import { AttributeData } from 'db://assets/bundles/common/script/data/base/AttributeData';
const { ccclass, property } = _decorator;

@ccclass("BossPlane")
export default class BossPlane extends PlaneBase {

    @property(EnemyPlaneRole)
    role: EnemyPlaneRole | null = null;

    @property(Sprite)
    hpBg: Sprite | null = null;
    @property(Sprite)
    hpSpr: Sprite | null = null;
    @property(Sprite)
    hpWhite: Sprite | null = null;

    _datas: BossData[] = [];
    _data: BossData | null = null;
    _trackCom: TrackComponent | null = null;


    _idleName: string = "idle1";
    _formIndex: number = -1;//形态索引
    _formNum: number = 0;//形态数量

    _posX: number = 0;
    _posY: number = 0;

    _moveToX: number = 0;
    _moveToY: number = 0;
    _moveSpeed: number = 0;
    _bArriveDes: boolean = false;//是否达到目标点
    _transFormMove: boolean = false;
    //下一个航点
    _nextWayPointTime: number = 0;
    _nextWayPointX: number = 0;
    _nextWayPointY: number = 0;
    _nextWayPointInterval: number = 0;
    _nextWaySpeed: number = 0;
    _shootAble: boolean = true;
    _atkActions: any[] = [];

    _bOrderAttack: boolean = false;
    _orderIndex: number = 0;
    _orderAtkArr: number[] = [];

    _atkPointDatas: any[] = [];

    _action: number = -1;
    _bDamageable: boolean = false;
    _bAttackMove: boolean = false;
    _bFirstWayPoint: boolean = false;
    transformBattle: boolean = true;
    _bRemoveable: boolean = false;
    // _shadow: any = null;
    // wingmanPlanes: any[] = [];
    // _cloakeAnim: PfFrameAnim | null = null;

    _nextAttackInterval: number = 0;
    _nextAttackTime: number = 0;
    _attackID: number = 0;

    tip: string = "";
    _hpWhiteTween: any = null;
    bullets: any[] = [];

    /**
     * 初始化 Boss 数据
     * @param datas Boss 数据数组
     */
    initBoss(datas: BossData[]) {
        super.init();
        this._datas = datas;
        this._formNum = this._datas.length;
        this._bFirstWayPoint = true;
        this._initUI();
        this._initProperty();
        this._initTrack();
        this._initCollide();
        this.setFormIndex(0);
    }

    /**
 * 初始化 UI
 */
    _initUI() {

    }

    _initProperty(){
        //暂时写死，后续读取新配表
        this.curHp = 4500;
        this.maxHp = this.curHp;
        this.attack = 60;
    }

    _initTrack() {
        this._trackCom = Tools.addScript(this.node, TrackComponent);
        this._trackCom!.setTrackGroupStartCall(() => { });
        this._trackCom!.setTrackGroupOverCall(() => {
            if (this._action === GameEnum.BossAction.Appear) {
                this._trackCom!.setTrackAble(false);
                this.setAction(GameEnum.BossAction.Transform);
            }
        });
        this._trackCom!.setTrackOverCall(() => { });
        this._trackCom!.setTrackLeaveCall(() => { });
        this._trackCom!.setTrackStartCall((track: any) => {

        });
    }

    _initCollide(): void {
        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);
        this.collideComp!.init(this, size(100, 100)); // 初始化碰撞组件
        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;
        this.colliderEnabled = false;
    }

    setCollideAble(isEnabled: boolean) {
        this.collideComp!.isEnable = isEnabled;
    }

    setAction(action: number) {
        if (this._action !== action) {
            this._action = action;

            let BossAction = GameEnum.BossAction;
            switch (this._action) {
                case BossAction.Normal:
                    this._playSkel(this._idleName, true, () => { });
                    this.setDamangeable(true);
                    break;

                case BossAction.Appear:
                    this._playSkel(`enter${this._formIndex + 1}`, true, () => { });
                    this.setDamangeable(false);
                    this._startAppearTrack();
                    break;

                case BossAction.Transform:
                    this._playSkel(`ready${this._formIndex + 1}`, false, () => {
                        this.transformBattle && this.transformEnd();
                    });
                    this.setDamangeable(false);
                    break;

                case BossAction.AttackPrepare:
                    this.scheduleOnce(() => {
                        this.setAction(BossAction.AttackIng);
                    });
                    this.setDamangeable(true);
                    break;

                case BossAction.AttackIng:
                case BossAction.AttackOver:
                    this.setDamangeable(true);
                    break;

                case BossAction.Blast:
                    this.setDamangeable(false);
                    break;

                default:
                    this.setDamangeable(true);
            }
        }
    }

    /**
 * 设置是否可被攻击
 * @param damageable 是否可被攻击
 */
    setDamangeable(damageable: boolean) {
        this._bDamageable = damageable;
    }


    _playSkel(animName: string, loop: boolean, callback: Function) {
        this.role!.playAnim(animName, loop, callback)
    }

    /**
     * 设置提示信息
     * @param tip 提示信息
     */
    setTip(tip: string) {
        this.tip = tip;
    }
    /**
* 变形结束
*/
    transformEnd() {
        if (this.tip !== "") {
            GameIns.battleManager.bossChangeFinish(this.tip);
        } else {
            GameIns.battleManager.bossFightStart();
        }
    }


    /**
     * 设置形态索引
     * @param index 形态索引
     */
    setFormIndex(index: number) {
        if (this._formIndex !== index) {
            this._formIndex = index;
            this._bOrderAttack = true;
            this._orderIndex = 0;
            this._data = this._datas[this._formIndex];
            this._idleName = `idle${this._formIndex + 1}`;

            if (index === 0) {
                this.setAction(GameEnum.BossAction.Appear);
            }

            this._orderAtkArr = [];
            for (let i = 0; i < this._data.attackActions.length; i++) {
                this._orderAtkArr.push(i);
            }

            this._atkPointDatas = [];
            for (const point of this._data.attackPoints) {
                const data = [point.bAvailable, point];
                this._atkPointDatas.push(data);
            }

            this._atkActions = [...this._data.attackActions];
        }
    }

    /**
     * 进入下一形态
     */
    enterNextForm() {
        if (this._formIndex < this._datas.length - 1) {
            this._formIndex++;
            this.setFormIndex(this._formIndex);
        }
    }

    /**
     * 开始战斗
     */
    startBattle() {
        this._startNormalTrack();
        this.setAction(GameEnum.BossAction.Normal);
        this.colliderEnabled = true;
    }

    /**
     * 更新游戏逻辑
     * @param deltaTime 每帧时间
     */
    updateGameLogic(deltaTime: number) {
        if (!this.isDead) {
            let BossAction = GameEnum.BossAction;
            switch (this._action) {
                case BossAction.Normal:
                    this._processNextWayPoint(deltaTime);
                    this._updateMove(deltaTime);
                    this._processNextAttack(deltaTime);
                    break;

                case BossAction.Appear:
                    this._updateMove(deltaTime);
                    if (this._bArriveDes) {
                        this.setAction(BossAction.Transform);
                    }
                    break;

                case BossAction.Transform:
                    if (this._transFormMove) {
                        this._updateMove(deltaTime);
                    }
                    break;

                case BossAction.AttackPrepare:
                    this._processNextWayPoint(deltaTime);
                    if (this._bAttackMove) {
                        this._updateMove(deltaTime);
                    }
                    break;

                case BossAction.AttackIng:
                    this._processNextWayPoint(deltaTime);
                    if (this._bAttackMove) {
                        this._updateMove(deltaTime);
                    }
                    this._udpateShoot(deltaTime);
                    break;

                case BossAction.AttackOver:
                    this._processNextWayPoint(deltaTime);
                    if (this._bAttackMove) {
                        this._updateMove(deltaTime);
                    }
                    this.setAction(BossAction.Normal);
                    break;

                case BossAction.Blast:
                    break;
            }
        }
    }

    /**
     * 是否可被攻击
     */
    isDamageable(): boolean {
        return this._bDamageable;
    }

    /**
     * 是否可移除
     */
    get removeAble(): boolean {
        return this._bRemoveable;
    }

    set removeAble(value: boolean) {
        this._bRemoveable = value;
    }


    /**
     * 开始出现轨迹
     */
    _startAppearTrack() {
        const trackGroup = new TrackGroup();
        trackGroup.loopNum = 1;
        trackGroup.trackIDs = [this._data!.appearParam[2]];
        trackGroup.speeds = [this._data!.appearParam[3]];
        trackGroup.trackIntervals = [0];

        this._trackCom!.init(this, [trackGroup], [], this._data!.appearParam[0], this._data!.appearParam[1]);
        this._trackCom!.setTrackAble(true);
        this._trackCom!.startTrack();
    }

    /**
     * 开始正常轨迹
     */
    _startNormalTrack() {
        this._trackCom!.init(this, this._data!.trackGroups, [], this.node.x, this.node.y);
        this._trackCom!.setTrackAble(true);
        this._trackCom!.startTrack();
        this.setAction(GameEnum.BossAction.Normal);
    }



    /**
     * 移动到指定位置
     * @param x X 坐标
     * @param y Y 坐标
     * @param speed 移动速度
     * @param transformMove 是否为变形移动
     */
    moveToPos(x: number, y: number, speed: number, transformMove: boolean = false) {
        this._moveToX = x;
        this._moveToY = y;
        this._moveSpeed = speed;
        this._bArriveDes = false;
        this._transFormMove = transformMove;
    }


    setPos(x: number, y: number, update: boolean = true) {
        this.node.setPosition(x, y);
        this._posX = x;
        this._posY = y;
    }

    /**
     * 处理下一个路径点
     * @param deltaTime 每帧时间
     */
    _processNextWayPoint(deltaTime: number) {
        if (this._bArriveDes && this._data!.trackGroups.length === 0) {
            this._nextWayPointTime += deltaTime;
            if (this._nextWayPointTime > this._nextWayPointInterval) {
                this._nextWayPointInterval = Tools.getRandomInArray(this._data!.wayPointIntervals)!;
                this._nextWayPointTime = 0;

                if (this._bFirstWayPoint) {
                    this._bFirstWayPoint = false;
                } else {
                    const index = Tools.random_int(0, this._data!.wayPointXs.length - 1);
                    this._nextWayPointX = this._data!.wayPointXs[index];
                    this._nextWayPointY = this._data!.wayPointYs[index];
                    this._nextWaySpeed = Tools.getRandomInArray(this._data!.speeds)!;
                    this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);
                }
            }
        }
    }


    _updateMove(deltaTime: number) {
        if (this._action === GameEnum.BossAction.Appear || this._data!.trackGroups.length > 0) {
            // 如果 Boss 在出现阶段或有轨迹组，则更新轨迹逻辑
            this._trackCom!.updateGameLogic(deltaTime);
        } else if (!this._bArriveDes) {
            // 如果未到达目标位置，则更新移动逻辑
            // this._prePosX = this._posX;
            // this._prePosY = this._posY;

            const deltaX = this._moveToX - this._posX;
            const deltaY = this._moveToY - this._posY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

            let moveX = 0;
            let moveY = 0;

            // 如果距离小于等于移动速度，则直接到达目标点
            if (distance <= this._moveSpeed) {
                moveX = deltaX;
                moveY = deltaY;
            }
            // 否则按比例移动
            else {
                moveX = this._moveSpeed * deltaX / distance;
                moveY = this._moveSpeed * deltaY / distance;
            }

            // 更新位置
            this._posX += moveX;
            this._posY += moveY;
            this.setPos(this._posX, this._posY);

            // 检查是否到达目的地（当移动量很小时认为已到达）
            this._bArriveDes = (Math.abs(moveX) < 0.5 && Math.abs(moveY) < 0.5);
        }
    }

    /**
     * 处理下一次攻击
     * @param deltaTime 每帧时间
     */
    _processNextAttack(deltaTime: number) {
        // if (this._shootAble && this._action === GameEnum.BossAction.Normal) {
        //     this._nextAttackTime += deltaTime;
        //     if (this._nextAttackTime > this._nextAttackInterval) {
        //         this._nextAttackInterval = Tools.getRandomInArray(this._data!.attackIntervals)!;
        //         this._nextAttackTime = 0;

        //         let attackAction = null;
        //         if (this._bOrderAttack) {
        //             const randomIndex = Tools.getRandomInArray(this._orderAtkArr)!;
        //             Tools.arrRemove(this._orderAtkArr, randomIndex);
        //             attackAction = this._atkActions[randomIndex];
        //             this._orderIndex++;
        //             if (this._orderIndex > this._atkActions.length - 1) {
        //                 this._bOrderAttack = false;
        //             }
        //         } else {
        //             attackAction = Tools.getRandomInArray(this._atkActions);
        //         }

        //         if (attackAction) {
        //             this._bAttackMove = attackAction.bAtkMove;
        //             this._attackID = attackAction.atkActId;
        //             this._attackPoints.splice(0);

        //             for (const pointId of attackAction.atkPointId) {
        //                 const pointData = this._atkPointDatas[pointId];
        //                 if (pointData[0]) {
        //                     let attackPoint = this._atkPointsPool[pointId]
        //                     if (!attackPoint) {
        //                         const pointNode = new Node();
        //                         this.node.addChild(pointNode);
        //                         attackPoint = pointNode.addComponent(AttackPoint);
        //                         this._atkPointsPool.push(attackPoint);
        //                     }
        //                     attackPoint.initForBoss(pointData[1], this);
        //                     this._attackPoints.push(attackPoint);
        //                 }
        //             }

        //             if (this._attackPoints.length > 0) {
        //                 this.setAction(GameEnum.BossAction.AttackPrepare);
        //             }
        //         }
        //     }
        // }
    }

    /**
     * 更新射击逻辑
     * @param deltaTime 每帧时间
     */
    async _udpateShoot(deltaTime: number) {
        // if (this._shootAble) {
        //     let allAttacksOver = true;

        //     for (const attackPoint of this._attackPoints) {
        //         await attackPoint.updateGameLogic(deltaTime);
        //         if (!attackPoint.isAttackOver()) {
        //             allAttacksOver = false;
        //         }
        //     }

        //     if (allAttacksOver) {
        //         this.setAction(GameEnum.BossAction.AttackOver);
        //     }
        // }
    }


    onCollide(collider: FCollider): void {
        if (!this.isDead && this.isDamageable()) {
            if (collider.entity instanceof Bullet) {
                let damage = collider.entity.getAttack();
                try {
                    GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), damage);
                } catch (error) {
                    console.error(error);
                }
                this.hurt(-damage);
            }
        }
    }

    hurt(damage: number): boolean {
        if (this.isDead || !this.isDamageable()) {
            return false;
        }

        this.curHp += damage;
        if (this.curHp < 0) {
            this.curHp = 0;
        } else if (this.curHp > this.maxHp) {
            this.curHp = this.maxHp;
        }
        this._refreshHpBar();
        this.checkHp();
        if (!this.isDead) {
            // this.role.winkWhite();
        }
        return true;
    }

    checkHp() {
        if (this.curHp <= 0) {
            this._toDie();
            return true;
        }
        return false;
    }

    _toDie() {
        if (!super.toDie()) {
            return false;
        }
        this.colliderEnabled = false;

        if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();
            this._hpWhiteTween = null;
        }

        this.onDie();

        this.setAction(GameEnum.BossAction.Blast);
        this._playDieAnim();
    }

    onDie() {
        for (const plane of GameIns.enemyManager.planes) {
            plane.die(GameEnum.EnemyDestroyType.Die);
        }
        this.removeBullets();
    }

    _playDieAnim(): void {
        // this._skel.setAnimation(0, `shake${this._bossPlane.formIndex + 1}`, false);
        this._bRemoveable = true;
    }

    _refreshHpBar() {
        const hpRatio = this.curHp / this.maxHp;
        const isDecreasing = hpRatio < this.hpSpr!.fillRange;

        // 更新血条显示
        this.hpSpr!.fillRange = hpRatio;

        // 停止之前的血条动画
        if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();
            this._hpWhiteTween = null;
        }

        // 如果血量减少，播放白色血条的动画
        if (isDecreasing) {
            const duration = Math.abs(this.hpWhite!.fillRange - this.hpSpr!.fillRange);
            this._hpWhiteTween = tween(this.hpWhite!)
                .to(duration, { fillRange: this.hpSpr!.fillRange })
                .call(() => {
                    this._hpWhiteTween = null;
                })
                .start();
        } else {
            this.hpWhite!.fillRange = hpRatio;
        }
    }

    /**
     * 添加子弹
     * @param bullet 子弹对象
     */
    addBullet(bullet: any) {
        if (this.bullets) {
            this.bullets.push(bullet);
        }
    }

    /**
     * 移除子弹
     * @param bullet 子弹对象
     */
    removeBullet(bullet: any) {
        if (this.bullets) {
            const index = this.bullets.indexOf(bullet);
            if (index >= 0) {
                this.bullets.splice(index, 1);
            }
        }
    }

    /**
     * 移除子弹
     */
    removeBullets() {
        for (const bullet of this.bullets) {
            bullet.dieRemove();
        }
        this.bullets.splice(0);
    }
}