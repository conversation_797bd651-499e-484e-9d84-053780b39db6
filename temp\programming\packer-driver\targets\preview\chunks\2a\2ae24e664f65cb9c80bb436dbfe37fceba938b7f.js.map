{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/MailUI.ts"], "names": ["_decorator", "BundleName", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "MyApp", "List", "HomeUI", "MailCellUI", "ccclass", "property", "MailUI", "keys", "items", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "Home", "onLoad", "btnClose", "addClick", "closeUI", "Array", "from", "lubanTables", "TbItem", "getDataMap", "list", "node", "active", "numItems", "Math", "min", "length", "openUI", "onShow", "onHide", "onClose", "onDestroy", "start", "onList<PERSON>ender", "listItem", "row", "key", "cell", "getComponent", "setData"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AAEjBC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,I;;AACEC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;wBAGjBY,M,WADZF,OAAO,CAAC,QAAD,C,UAEHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,uB,2BAJb,MACaC,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAK/BC,IAL+B,GAKd,EALc;AAAA,eAM/BC,KAN+B,GAMD,EANC;AAAA;;AAOX,eAANC,MAAM,GAAW;AAAE,iBAAO,uBAAP;AAAiC;;AAC5C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAyB;;AACvDC,QAAAA,MAAM,GAAS;AACrB,eAAKC,QAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACA,eAAKV,IAAL,GAAYW,KAAK,CAACC,IAAN,CAAW;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,MAAlB,CAAyBC,UAAzB,GAAsCf,IAAtC,EAAX,CAAZ;AACA,eAAKgB,IAAL,CAAWC,IAAX,CAAgBC,MAAhB,GAAyB,IAAzB;AACA,eAAKF,IAAL,CAAWG,QAAX,GAAsBC,IAAI,CAACC,GAAL,CAAS,KAAKrB,IAAL,CAAUsB,MAAnB,EAA2B,EAA3B,CAAtB;AACH;;AACKZ,QAAAA,OAAO,GAAG;AAAA;AACZ;AAAA;AAAA,gCAAMA,OAAN,CAAcX,MAAd;AACA,kBAAM;AAAA;AAAA,gCAAMwB,MAAN;AAAA;AAAA,iCAAN;AAFY;AAGf;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AACSC,QAAAA,SAAS,GAAS,CAC3B;;AACDC,QAAAA,KAAK,GAAG,CAEP;;AACDC,QAAAA,YAAY,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AAAC;AACvC,cAAMC,GAAG,GAAGD,GAAG,GAAG,KAAK/B,IAAL,CAAUsB,MAAhB,GAAyB,KAAKtB,IAAL,CAAU+B,GAAV,CAAzB,GAA0C,KAAK/B,IAAL,CAAU,CAAV,CAAtD;AACA,cAAMiC,IAAI,GAAGH,QAAQ,CAACI,YAAT;AAAA;AAAA,uCAAb;;AACA,cAAID,IAAI,KAAK,IAAb,EAAmB;AACfA,YAAAA,IAAI,CAACE,OAAL,CAAaH,GAAb;AACH;AACJ;;AArC8B,O;;;;;iBAED,I;;;;;;;iBAEV,I", "sourcesContent": ["import { _decorator, Node } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/Bundle';\r\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\r\nimport { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/ui/UIMgr';\r\nimport csproto from '../../../../../../scripts/AutoGen/PB/cs_proto.js';\r\nimport { MyApp } from '../../../../../../scripts/MyApp';\r\nimport List from '../../common/components/list/List';\r\nimport { HomeUI } from '../HomeUI';\r\nimport { MailCellUI } from './MailCellUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('MailUI')\r\nexport class MailUI extends BaseUI {\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus | null = null;\r\n    @property(List)\r\n    list: List | null = null;\r\n    keys: number[] = [];\r\n    items: csproto.cs.ICSItem[] = [];\r\n    public static getUrl(): string { return \"prefab/ui/mail/MailUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.Home; }\r\n    protected onLoad(): void {\r\n        this.btnClose!.addClick(this.closeUI, this);\r\n        this.keys = Array.from(MyApp.lubanTables.TbItem.getDataMap().keys());\r\n        this.list!.node.active = true;\r\n        this.list!.numItems = Math.min(this.keys.length, 20);\r\n    }\r\n    async closeUI() {\r\n        UIMgr.closeUI(MailUI);\r\n        await UIMgr.openUI(HomeUI)\r\n    }\r\n    async onShow(): Promise<void> {\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n    }\r\n    start() {\r\n\r\n    }\r\n    onListRender(listItem: Node, row: number) {// 有数据要在 this.list.numItems 之前设置\r\n        const key = row < this.keys.length ? this.keys[row] : this.keys[0];\r\n        const cell = listItem.getComponent(MailCellUI);\r\n        if (cell !== null) {\r\n            cell.setData(key);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}