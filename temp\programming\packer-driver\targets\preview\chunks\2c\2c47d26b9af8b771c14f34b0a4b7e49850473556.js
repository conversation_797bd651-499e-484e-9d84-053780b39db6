System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, EditBox, Label, Node, DataMgr, ButtonPlus, DropDown, List, csproto, MyApp, BaseUI, UILayer, UIMgr, logDebug, BundleName, GmButtonUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _crd, ccclass, property, GmUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "db://assets/bundles/common/script/data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "db://assets/bundles/common/script/ui/common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDropDown(extras) {
    _reporterNs.report("DropDown", "db://assets/bundles/common/script/ui/common/components/dropdown/DropDown", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "db://assets/bundles/common/script/ui/common/components/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../../Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGmButtonUI(extras) {
    _reporterNs.report("GmButtonUI", "./GmButtonUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      EditBox = _cc.EditBox;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      DataMgr = _unresolved_2.DataMgr;
    }, function (_unresolved_3) {
      ButtonPlus = _unresolved_3.ButtonPlus;
    }, function (_unresolved_4) {
      DropDown = _unresolved_4.DropDown;
    }, function (_unresolved_5) {
      List = _unresolved_5.default;
    }, function (_unresolved_6) {
      csproto = _unresolved_6.default;
    }, function (_unresolved_7) {
      MyApp = _unresolved_7.MyApp;
    }, function (_unresolved_8) {
      BaseUI = _unresolved_8.BaseUI;
      UILayer = _unresolved_8.UILayer;
      UIMgr = _unresolved_8.UIMgr;
    }, function (_unresolved_9) {
      logDebug = _unresolved_9.logDebug;
    }, function (_unresolved_10) {
      BundleName = _unresolved_10.BundleName;
    }, function (_unresolved_11) {
      GmButtonUI = _unresolved_11.GmButtonUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "019daPIf6xP/LD6RFKEsz1P", "GmUI", undefined);

      __checkObsolete__(['_decorator', 'EditBox', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("GmUI", GmUI = (_dec = ccclass('GmUI'), _dec2 = property(_crd && DropDown === void 0 ? (_reportPossibleCrUseOfDropDown({
        error: Error()
      }), DropDown) : DropDown), _dec3 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec4 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec5 = property(Node), _dec6 = property(Label), _dec7 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec(_class = (_class2 = class GmUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "tabDropDown", _descriptor, this);

          _initializerDefineProperty(this, "cmdBtnList", _descriptor2, this);

          _initializerDefineProperty(this, "sendBtn", _descriptor3, this);

          _initializerDefineProperty(this, "inputParentNode", _descriptor4, this);

          _initializerDefineProperty(this, "logLabel", _descriptor5, this);

          _initializerDefineProperty(this, "clearBtn", _descriptor6, this);

          this._inputNodeList = [];
        }

        static getUrl() {
          return "prefab/ui/GmUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Top;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Gm;
        }

        static getUIOption() {
          return {
            isClickBgHideUI: true
          };
        }

        onLoad() {
          this.inputParentNode.children.forEach(v => {
            this._inputNodeList.push(v.getComponentInChildren(EditBox));
          });
          this.sendBtn.addClick(this.onSendBtnClick, this);
          this.clearBtn.addClick(this.onClearBtnClick, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GM, this.onGmMsg, this);
        }

        onClearBtnClick() {
          this.logLabel.string = "";
        }

        onGmMsg(res) {
          this.log("\u63A5\u6536 => " + res.body.gm.text);
        }

        onCmdBtnRender(item, idx) {
          var cmdInfo = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey)[idx];
          var label = item.getComponentInChildren(Label);
          label.string = cmdInfo.cfg.name;
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("GmUI", "onCmdBtnRender " + cmdInfo.cfg.name);
        }

        onCmdBtnClick(item, idx) {
          var cmdInfo = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).filter(v => v.cfg.name !== "")[idx];
          this._inputNodeList[0].string = cmdInfo.cfg.cmd;
          this._inputNodeList[1].placeholder = cmdInfo.cfg.desc;
        }

        onDropDownOptionRender(nd, optKey) {
          var tabID = Number(optKey);
          var cmdList = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(tabID);
          nd.getComponentInChildren(Label).string = cmdList[0].cfg.tabName;
        }

        onDropDownOptionClick(optKey) {
          var tabID = Number(optKey);
          var cmdList = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(tabID).filter(v => v.cfg.name !== "");
          this.cmdBtnList.numItems = cmdList.length;
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("GmUI", "onDropDownOptionClick " + cmdList.length);
        }

        log(msg) {
          this.logLabel.string += "[" + new Date().toLocaleString() + "] " + msg + "\n";
        }

        onSendBtnClick() {
          this._inputNodeList.forEach(v => {
            if (v.string == v.placeholder) {
              v.string = "";
            }
          });

          var cmd = this._inputNodeList[0].string;
          var args = this._inputNodeList[1].string;
          var target = this._inputNodeList[2].string;

          if (target !== "") {
            target = "[destuin " + target + "]";
          }

          var cmdInfo = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).filter(v => v.cfg.cmd === cmd)[0];

          if (cmdInfo != null && cmdInfo.onSendClick) {
            var res = cmdInfo.onSendClick(args);
            this.log("[" + cmd + "] \u53D1\u9001 => " + res);
          } else {
            var gmStr = cmd + " " + target + " " + args;
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
              error: Error()
            }), csproto) : csproto).cs.CS_CMD.CS_CMD_GM, {
              gm: {
                gm_str: gmStr
              }
            });
            this.log("[" + cmd + "] \u53D1\u9001 => " + gmStr);
          }
        } // 显示 UI 的方法，需要子类实现


        onShow() {
          var tabIDList = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.tabIDList;
          this.tabDropDown.init(tabIDList, this.onDropDownOptionRender.bind(this), this.onDropDownOptionClick.bind(this));
          this.cmdBtnList.numItems = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).gm.getCmdBtnListByTabID(this.tabDropDown.selectedKey).length;
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("GmUI", "onShow " + this.cmdBtnList.numItems);
          this.onClearBtnClick();
          return Promise.resolve();
        }

        // 隐藏 UI 的方法，需要子类实现
        onHide() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && GmButtonUI === void 0 ? (_reportPossibleCrUseOfGmButtonUI({
            error: Error()
          }), GmButtonUI) : GmButtonUI);
          return Promise.resolve();
        }

        // 关闭 UI 的方法，需要子类实现
        onClose() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && GmButtonUI === void 0 ? (_reportPossibleCrUseOfGmButtonUI({
            error: Error()
          }), GmButtonUI) : GmButtonUI);
          return Promise.resolve();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "tabDropDown", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "cmdBtnList", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "sendBtn", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "inputParentNode", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "logLabel", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "clearBtn", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2c47d26b9af8b771c14f34b0a4b7e49850473556.js.map