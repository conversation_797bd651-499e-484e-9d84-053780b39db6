{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/scenes/GameMain.ts"], "names": ["_decorator", "Component", "director", "EPhysics2DDrawFlags", "find", "Node", "PhysicsSystem2D", "BottomUI", "HomeUI", "TopUI", "UIMgr", "MBoomUI", "GameIns", "BulletSystem", "GameConst", "GameEnum", "BattleLayer", "GameMapRun", "ccclass", "property", "GameMain", "onLoad", "gameMainUI", "bulletParent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fColliderManager", "enable", "setGlobalColliderEnterCall", "colliderA", "colliderB", "entity", "onCollide", "ColliderDraw", "instance", "debugDrawFlags", "<PERSON><PERSON><PERSON>", "start", "battleManager", "startLoading", "openUI", "showGameResult", "isSuccess", "gameEnd", "active", "LabelWin", "LabelFail", "onBtnAgainClicked", "mainReset", "closeUI", "loadScene", "update", "deltaTime", "gameType", "GameType", "Common", "lateUpdate", "dt", "gameRuleManager", "isInBattle", "isGameWillOver"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,mB,OAAAA,mB;AAAqBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,e,OAAAA,e;;AAClEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Y,iBAAAA,Y;;AAEAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,kBAAAA,Q;;AACFC,MAAAA,W;;AACAC,MAAAA,U;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBnB,U;;0BAGjBoB,Q,WADZF,OAAO,CAAC,UAAD,C,UAIHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,qC,UAGRA,QAAQ,CAACd,IAAD,C,2BATb,MACae,QADb,SAC8BnB,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAY1BoB,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,kCAAQC,UAAR,GAAqB,IAArB;AACA;AAAA;AAAA,4CAAaC,YAAb,GAA4B,KAAKP,WAAL,CAAkBQ,eAA9C,CAFqB,CAE0C;;AAE/D;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,MAAzB,GAAkC,IAAlC;AACA;AAAA;AAAA,kCAAQD,gBAAR,CAAyBE,0BAAzB,CAAoD,CAACC,SAAD,EAAuBC,SAAvB,KAAgD;AAAA;;AAChG,iCAAAD,SAAS,CAACE,MAAV,+BAAkBC,SAAlB,8BAAkBA,SAAlB,CAA8BF,SAA9B;AACA,iCAAAA,SAAS,CAACC,MAAV,+BAAkBC,SAAlB,8BAAkBA,SAAlB,CAA8BH,SAA9B;AACH,WAHD;;AAKA,cAAI;AAAA;AAAA,sCAAUI,YAAd,EAA4B;AACxB1B,YAAAA,eAAe,CAAC2B,QAAhB,CAAyBP,MAAzB,GAAkC,IAAlC;AACApB,YAAAA,eAAe,CAAC2B,QAAhB,CAAyBC,cAAzB,GAA0C/B,mBAAmB,CAACgC,IAA9D;AACH;AACJ;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB;AACA;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA;AACH;;AAEDC,QAAAA,cAAc,CAACC,SAAD,EAAqB;AAC/B,eAAKC,OAAL,CAAcC,MAAd,GAAuB,IAAvB;AAEA,cAAIC,QAAQ,GAAGxC,IAAI,CAAC,UAAD,EAAa,KAAKsC,OAAlB,CAAnB;AACA,cAAIG,SAAS,GAAGzC,IAAI,CAAC,WAAD,EAAc,KAAKsC,OAAnB,CAApB;AACAE,UAAAA,QAAQ,CAAED,MAAV,GAAmBF,SAAnB;AACAI,UAAAA,SAAS,CAAEF,MAAX,GAAoB,CAACF,SAArB;AACH;;AAEKK,QAAAA,iBAAiB,GAAG;AAAA;;AAAA;AACtB,YAAA,KAAI,CAACJ,OAAL,CAAcC,MAAd,GAAuB,KAAvB;AACA;AAAA;AAAA,oCAAQN,aAAR,CAAsBU,SAAtB;AACA;AAAA;AAAA,gCAAMC,OAAN;AAAA;AAAA;AACA,kBAAM;AAAA;AAAA,gCAAMT,MAAN;AAAA;AAAA,iCAAN;AACA,kBAAM;AAAA;AAAA,gCAAMA,MAAN;AAAA;AAAA,qCAAN;AACA,kBAAM;AAAA;AAAA,gCAAMA,MAAN;AAAA;AAAA,+BAAN;AACArC,YAAAA,QAAQ,CAAC+C,SAAT,CAAmB,MAAnB;AAPsB;AAQzB;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,SAAD,EAA0B;AAC5B;AACA,cAAIA,SAAS,GAAG,GAAhB,EAAqB;AACjBA,YAAAA,SAAS,GAAG,iBAAZ,CADiB,CACc;AAClC;;AAED,kBAAQ;AAAA;AAAA,kCAAQd,aAAR,CAAsBe,QAA9B;AACI,iBAAK;AAAA;AAAA,sCAASC,QAAT,CAAkBC,MAAvB;AACI;AAAA;AAAA,sCAAQjB,aAAR,CAAsBa,MAAtB,CAA6BC,SAA7B;AACA;AAHR;AAKH;;AAEDI,QAAAA,UAAU,CAACC,EAAD,EAAmB;AACzB,cAAI;AAAA;AAAA,kCAAQC,eAAR,CAAwBC,UAAxB,MAAwC;AAAA;AAAA,kCAAQD,eAAR,CAAwBE,cAAxB,EAA5C,EAAsF;AAClF;AAAA;AAAA,oCAAQlC,gBAAR,CAAyByB,MAAzB,CAAgCM,EAAhC;AACH;AACJ;;AAzEmC,O;;;;;iBAIJ,I;;;;;;;iBAEE,I;;;;;;;iBAGX,I", "sourcesContent": ["import { _decorator, Component, director, EPhysics2DDrawFlags, find, Node, PhysicsSystem2D } from 'cc';\r\nimport { BottomUI } from 'db://assets/bundles/common/script/ui/home/<USER>';\r\nimport { HomeUI } from 'db://assets/bundles/common/script/ui/home/<USER>';\r\nimport { TopUI } from 'db://assets/bundles/common/script/ui/home/<USER>';\r\nimport { UIMgr } from '../../ui/UIMgr';\r\nimport { MBoomUI } from '../../ui/game/MBoomUI';\r\nimport { GameIns } from '../GameIns';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\nimport FCollider from '../collider-system/FCollider';\r\nimport { GameConst } from '../const/GameConst';\r\nimport { GameEnum } from '../const/GameEnum';\r\nimport BattleLayer from '../ui/layer/BattleLayer';\r\nimport GameMapRun from '../ui/map/GameMapRun';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('GameMain')\r\nexport class GameMain extends Component {\r\n\r\n\r\n    @property(GameMapRun)\r\n    GameMapRun: GameMapRun | null = null;\r\n    @property(BattleLayer)\r\n    BattleLayer: BattleLayer | null = null;\r\n\r\n    @property(Node)\r\n    gameEnd: Node | null = null;\r\n\r\n\r\n    protected onLoad(): void {\r\n        GameIns.gameMainUI = this;\r\n        BulletSystem.bulletParent = this.BattleLayer!.selfBulletLayer!;//设置子弹父节点\r\n\r\n        GameIns.fColliderManager.enable = true;\r\n        GameIns.fColliderManager.setGlobalColliderEnterCall((colliderA: FCollider, colliderB: FCollider) => {\r\n            colliderA.entity?.onCollide?.(colliderB);\r\n            colliderB.entity?.onCollide?.(colliderA);\r\n        });\r\n\r\n        if (GameConst.ColliderDraw) {\r\n            PhysicsSystem2D.instance.enable = true;\r\n            PhysicsSystem2D.instance.debugDrawFlags = EPhysics2DDrawFlags.Aabb;\r\n        }\r\n    }\r\n\r\n    start() {\r\n        GameIns.battleManager.startLoading();\r\n        UIMgr.openUI(MBoomUI)\r\n    }\r\n\r\n    showGameResult(isSuccess: boolean) {\r\n        this.gameEnd!.active = true;\r\n\r\n        let LabelWin = find(\"LabelWin\", this.gameEnd!);\r\n        let LabelFail = find(\"LabelFail\", this.gameEnd!);\r\n        LabelWin!.active = isSuccess;\r\n        LabelFail!.active = !isSuccess;\r\n    }\r\n\r\n    async onBtnAgainClicked() {\r\n        this.gameEnd!.active = false;\r\n        GameIns.battleManager.mainReset();\r\n        UIMgr.closeUI(MBoomUI)\r\n        await UIMgr.openUI(HomeUI)\r\n        await UIMgr.openUI(BottomUI)\r\n        await UIMgr.openUI(TopUI)\r\n        director.loadScene(\"Main\");\r\n    }\r\n\r\n    /**\r\n     * 每帧更新逻辑\r\n     * @param deltaTime 时间增量\r\n     */\r\n    update(deltaTime: number): void {\r\n        // 限制 deltaTime 的最大值\r\n        if (deltaTime > 0.2) {\r\n            deltaTime = 0.016666666666667; // 约等于 1/60 秒\r\n        }\r\n\r\n        switch (GameIns.battleManager.gameType) {\r\n            case GameEnum.GameType.Common:\r\n                GameIns.battleManager.update(deltaTime);\r\n                break;\r\n        }\r\n    }\r\n\r\n    lateUpdate(dt: number): void {\r\n        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {\r\n            GameIns.fColliderManager.update(dt);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}