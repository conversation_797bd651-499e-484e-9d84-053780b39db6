System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, Boss, buffer, Bullet, Chapter, ConsumeItem, ConsumeMoney, Enemy, EnemyUI, EquipProp, GameMap, GameMode, GlobalAttr, Level, LevelGroup, MainPlane, MainPlaneLv, PlaneEffect, PlaneMaterial, PlaneProperty, PropInc, ResEffect, ResEquip, ResEquipUpgrade, ResGM, ResItem, ResPlane, ResWeapon, ResWhiteList, skill, Stage, Task, Track, Unit, Wave, TbGM, TbPlane, TbEffect, TbWeapon, TbEquipUpgrade, TbEquip, TbItem, TbGlobalAttr, TbBoss, Tbbuffer, TbBullet, TbChapter, TbEnemy, TbEnemyUI, TbGameMap, TbGameMode, TbLevel, TbLevelGroup, TbMainPlane, TbMainPlaneLv, Tbskill, TbStage, TbTask, TbTrack, TbUnit, TbWave, Tables, _crd, res, TargetScanStrategy, TaskClass, TaskCondType, TaskObjectType, TaskPeriodType, builtin;

  _export({
    Boss: void 0,
    buffer: void 0,
    Bullet: void 0,
    Chapter: void 0,
    ConsumeItem: void 0,
    ConsumeMoney: void 0,
    Enemy: void 0,
    EnemyUI: void 0,
    EquipProp: void 0,
    GameMap: void 0,
    GameMode: void 0,
    GlobalAttr: void 0,
    Level: void 0,
    LevelGroup: void 0,
    MainPlane: void 0,
    MainPlaneLv: void 0,
    PlaneEffect: void 0,
    PlaneMaterial: void 0,
    PlaneProperty: void 0,
    PropInc: void 0,
    ResEffect: void 0,
    ResEquip: void 0,
    ResEquipUpgrade: void 0,
    ResGM: void 0,
    ResItem: void 0,
    ResPlane: void 0,
    ResWeapon: void 0,
    ResWhiteList: void 0,
    skill: void 0,
    Stage: void 0,
    Task: void 0,
    Track: void 0,
    Unit: void 0,
    Wave: void 0,
    TbGM: void 0,
    TbPlane: void 0,
    TbEffect: void 0,
    TbWeapon: void 0,
    TbEquipUpgrade: void 0,
    TbEquip: void 0,
    TbItem: void 0,
    TbGlobalAttr: void 0,
    TbBoss: void 0,
    Tbbuffer: void 0,
    TbBullet: void 0,
    TbChapter: void 0,
    TbEnemy: void 0,
    TbEnemyUI: void 0,
    TbGameMap: void 0,
    TbGameMode: void 0,
    TbLevel: void 0,
    TbLevelGroup: void 0,
    TbMainPlane: void 0,
    TbMainPlaneLv: void 0,
    Tbskill: void 0,
    TbStage: void 0,
    TbTask: void 0,
    TbTrack: void 0,
    TbUnit: void 0,
    TbWave: void 0,
    Tables: void 0,
    res: void 0,
    builtin: void 0
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "da084rha1tNepSmiH/bt+BS", "schema", undefined);

      (function (_res13) {
        let BuffType = /*#__PURE__*/function (BuffType) {
          BuffType[BuffType["Positive"] = 0] = "Positive";
          BuffType[BuffType["Neutral"] = 1] = "Neutral";
          BuffType[BuffType["Negative"] = 2] = "Negative";
          return BuffType;
        }({});

        _res13.BuffType = BuffType;
      })(res || _export("res", res = {}));

      (function (_res) {
        let EffectType = /*#__PURE__*/function (EffectType) {
          EffectType[EffectType["AttrMaxHPPer"] = 1] = "AttrMaxHPPer";
          EffectType[EffectType["AttrMaxHPAdd"] = 2] = "AttrMaxHPAdd";
          EffectType[EffectType["AttrHPRecoveryPer"] = 3] = "AttrHPRecoveryPer";
          EffectType[EffectType["AttrHPRecoveryAdd"] = 4] = "AttrHPRecoveryAdd";
          EffectType[EffectType["AttrHPRecoveryMaxHPPerAdd"] = 5] = "AttrHPRecoveryMaxHPPerAdd";
          EffectType[EffectType["RecoveryMaxHPPer"] = 6] = "RecoveryMaxHPPer";
          EffectType[EffectType["RecoveryLoseHPPer"] = 7] = "RecoveryLoseHPPer";
          EffectType[EffectType["RecoveryHP"] = 8] = "RecoveryHP";
          EffectType[EffectType["AttrAttackPer"] = 9] = "AttrAttackPer";
          EffectType[EffectType["AttrAttackAdd"] = 10] = "AttrAttackAdd";
          EffectType[EffectType["AttrAttackBossPer"] = 11] = "AttrAttackBossPer";
          EffectType[EffectType["AttrAttackNormalPer"] = 12] = "AttrAttackNormalPer";
          EffectType[EffectType["AttrFortunatePer"] = 13] = "AttrFortunatePer";
          EffectType[EffectType["AttrFortunateAdd"] = 14] = "AttrFortunateAdd";
          EffectType[EffectType["AttrMissAdd"] = 15] = "AttrMissAdd";
          EffectType[EffectType["AttrBulletHurtResistancePer"] = 16] = "AttrBulletHurtResistancePer";
          EffectType[EffectType["AttrBulletHurtResistanceAdd"] = 17] = "AttrBulletHurtResistanceAdd";
          EffectType[EffectType["AttrCollisionHurtResistancePer"] = 18] = "AttrCollisionHurtResistancePer";
          EffectType[EffectType["AttrCollisionHurtResistanceAdd"] = 19] = "AttrCollisionHurtResistanceAdd";
          EffectType[EffectType["AttrFinalScoreAdd"] = 20] = "AttrFinalScoreAdd";
          EffectType[EffectType["AttrKillScoreAdd"] = 21] = "AttrKillScoreAdd";
          EffectType[EffectType["AttrEnergyRecoveryPerAdd"] = 22] = "AttrEnergyRecoveryPerAdd";
          EffectType[EffectType["AttrEnergyRecoveryAdd"] = 23] = "AttrEnergyRecoveryAdd";
          EffectType[EffectType["AttrPickRadiusPer"] = 24] = "AttrPickRadiusPer";
          EffectType[EffectType["AttrPickRadius"] = 25] = "AttrPickRadius";
          EffectType[EffectType["ApplyBuff"] = 26] = "ApplyBuff";
          EffectType[EffectType["ImmuneBulletHurt"] = 27] = "ImmuneBulletHurt";
          EffectType[EffectType["ImmuneCollisionHurt"] = 28] = "ImmuneCollisionHurt";
          EffectType[EffectType["IgnoreBullet"] = 29] = "IgnoreBullet";
          EffectType[EffectType["IgnoreCollision"] = 30] = "IgnoreCollision";
          EffectType[EffectType["ImmuneBombHurt"] = 31] = "ImmuneBombHurt";
          EffectType[EffectType["ImmuneActiveSkillHurt"] = 32] = "ImmuneActiveSkillHurt";
          EffectType[EffectType["Invincible"] = 33] = "Invincible";
          EffectType[EffectType["AttrBombMax"] = 34] = "AttrBombMax";
          EffectType[EffectType["BulletHurt"] = 35] = "BulletHurt";
          EffectType[EffectType["HurtMaxHPPer"] = 36] = "HurtMaxHPPer";
          EffectType[EffectType["HurtCurHPPer"] = 37] = "HurtCurHPPer";
          EffectType[EffectType["AttrBombHurtAdd"] = 38] = "AttrBombHurtAdd";
          EffectType[EffectType["AttrBombHurtPer"] = 39] = "AttrBombHurtPer";
          EffectType[EffectType["Kill"] = 40] = "Kill";
          EffectType[EffectType["Hurt"] = 41] = "Hurt";
          return EffectType;
        }({});

        _res.EffectType = EffectType;
      })(res || _export("res", res = {}));

      (function (_res2) {
        let EquipClass = /*#__PURE__*/function (EquipClass) {
          EquipClass[EquipClass["NONE"] = 0] = "NONE";
          EquipClass[EquipClass["WEAPON"] = 1] = "WEAPON";
          EquipClass[EquipClass["SUB_WEAPON"] = 2] = "SUB_WEAPON";
          EquipClass[EquipClass["ARMOR"] = 3] = "ARMOR";
          EquipClass[EquipClass["TECHNIC"] = 4] = "TECHNIC";
          return EquipClass;
        }({});

        _res2.EquipClass = EquipClass;
      })(res || _export("res", res = {}));

      (function (_res3) {
        let GMTabID = /*#__PURE__*/function (GMTabID) {
          GMTabID[GMTabID["COMMON"] = 0] = "COMMON";
          GMTabID[GMTabID["BATTLE"] = 1] = "BATTLE";
          return GMTabID;
        }({});

        _res3.GMTabID = GMTabID;
      })(res || _export("res", res = {}));

      (function (_res4) {
        let ItemEffectType = /*#__PURE__*/function (ItemEffectType) {
          ItemEffectType[ItemEffectType["NONE"] = 0] = "NONE";
          ItemEffectType[ItemEffectType["DROP"] = 1] = "DROP";
          ItemEffectType[ItemEffectType["GEN_GOLD"] = 2] = "GEN_GOLD";
          ItemEffectType[ItemEffectType["GEN_DIAMOND"] = 3] = "GEN_DIAMOND";
          ItemEffectType[ItemEffectType["GEN_XP"] = 4] = "GEN_XP";
          ItemEffectType[ItemEffectType["GEN_ENERGY"] = 5] = "GEN_ENERGY";
          ItemEffectType[ItemEffectType["GEN_ITEM"] = 6] = "GEN_ITEM";
          return ItemEffectType;
        }({});

        _res4.ItemEffectType = ItemEffectType;
      })(res || _export("res", res = {}));

      (function (_res5) {
        let ItemUseType = /*#__PURE__*/function (ItemUseType) {
          ItemUseType[ItemUseType["NONE"] = 0] = "NONE";
          ItemUseType[ItemUseType["MANUAL"] = 1] = "MANUAL";
          ItemUseType[ItemUseType["AUTO"] = 2] = "AUTO";
          return ItemUseType;
        }({});

        _res5.ItemUseType = ItemUseType;
      })(res || _export("res", res = {}));

      (function (_res6) {
        let ModeType = /*#__PURE__*/function (ModeType) {
          ModeType[ModeType["ENDLESS"] = 0] = "ENDLESS";
          ModeType[ModeType["STORY"] = 1] = "STORY";
          ModeType[ModeType["EXPEDITION"] = 2] = "EXPEDITION";
          ModeType[ModeType["ENDLESSPK"] = 3] = "ENDLESSPK";
          ModeType[ModeType["FRIENDPK"] = 4] = "FRIENDPK";
          return ModeType;
        }({});

        _res6.ModeType = ModeType;
      })(res || _export("res", res = {}));

      (function (_res7) {
        let MoneyType = /*#__PURE__*/function (MoneyType) {
          MoneyType[MoneyType["NONE"] = 0] = "NONE";
          MoneyType[MoneyType["GOLD"] = 1] = "GOLD";
          MoneyType[MoneyType["DIAMOND"] = 2] = "DIAMOND";
          MoneyType[MoneyType["POWER"] = 3] = "POWER";
          MoneyType[MoneyType["ITEM"] = 4] = "ITEM";
          return MoneyType;
        }({});

        _res7.MoneyType = MoneyType;
      })(res || _export("res", res = {}));

      (function (_res8) {
        let PlayCycle = /*#__PURE__*/function (PlayCycle) {
          PlayCycle[PlayCycle["DAY"] = 0] = "DAY";
          PlayCycle[PlayCycle["WEEK"] = 1] = "WEEK";
          return PlayCycle;
        }({});

        _res8.PlayCycle = PlayCycle;
      })(res || _export("res", res = {}));

      (function (_res9) {
        let PropName = /*#__PURE__*/function (PropName) {
          PropName[PropName["NONE"] = 0] = "NONE";
          PropName[PropName["HURT"] = 1] = "HURT";
          PropName[PropName["HP"] = 2] = "HP";
          return PropName;
        }({});

        _res9.PropName = PropName;
      })(res || _export("res", res = {}));

      (function (_res10) {
        let QualityType = /*#__PURE__*/function (QualityType) {
          QualityType[QualityType["NONE"] = 0] = "NONE";
          QualityType[QualityType["COMMON"] = 1] = "COMMON";
          QualityType[QualityType["UNCOMMON"] = 2] = "UNCOMMON";
          QualityType[QualityType["RACE"] = 3] = "RACE";
          QualityType[QualityType["EPIC"] = 4] = "EPIC";
          QualityType[QualityType["LEGENDARY"] = 5] = "LEGENDARY";
          QualityType[QualityType["MYTHIC"] = 6] = "MYTHIC";
          return QualityType;
        }({});

        _res10.QualityType = QualityType;
      })(res || _export("res", res = {}));

      (function (_res11) {
        let SkillConditionType = /*#__PURE__*/function (SkillConditionType) {
          SkillConditionType[SkillConditionType["NONE"] = 0] = "NONE";
          return SkillConditionType;
        }({});

        _res11.SkillConditionType = SkillConditionType;
      })(res || _export("res", res = {}));

      (function (_res12) {
        let TargetType = /*#__PURE__*/function (TargetType) {
          TargetType[TargetType["Self"] = 0] = "Self";
          TargetType[TargetType["Main"] = 1] = "Main";
          TargetType[TargetType["MainFriendly"] = 2] = "MainFriendly";
          TargetType[TargetType["Enemy"] = 3] = "Enemy";
          TargetType[TargetType["BossEnemy"] = 4] = "BossEnemy";
          TargetType[TargetType["NormalEnemy"] = 5] = "NormalEnemy";
          return TargetType;
        }({});

        _res12.TargetType = TargetType;
      })(res || _export("res", res = {}));

      _export("TargetScanStrategy", TargetScanStrategy = /*#__PURE__*/function (TargetScanStrategy) {
        TargetScanStrategy[TargetScanStrategy["Refresh"] = 0] = "Refresh";
        TargetScanStrategy[TargetScanStrategy["Keep"] = 1] = "Keep";
        return TargetScanStrategy;
      }({}));

      _export("TaskClass", TaskClass = /*#__PURE__*/function (TaskClass) {
        TaskClass[TaskClass["DAILY_TASK"] = 1] = "DAILY_TASK";
        TaskClass[TaskClass["WEEKLY_TASK"] = 2] = "WEEKLY_TASK";
        TaskClass[TaskClass["CHALLENGE"] = 3] = "CHALLENGE";
        TaskClass[TaskClass["ACHIEVEMENT"] = 4] = "ACHIEVEMENT";
        TaskClass[TaskClass["ACTIVITY"] = 5] = "ACTIVITY";
        return TaskClass;
      }({}));

      _export("TaskCondType", TaskCondType = /*#__PURE__*/function (TaskCondType) {
        TaskCondType[TaskCondType["NONE"] = 0] = "NONE";
        TaskCondType[TaskCondType["LEVEL"] = 1] = "LEVEL";
        TaskCondType[TaskCondType["STAGE"] = 2] = "STAGE";
        TaskCondType[TaskCondType["STAR"] = 3] = "STAR";
        TaskCondType[TaskCondType["FORCE"] = 4] = "FORCE";
        return TaskCondType;
      }({}));

      _export("TaskObjectType", TaskObjectType = /*#__PURE__*/function (TaskObjectType) {
        TaskObjectType[TaskObjectType["PLAYER_MODE_TIMES"] = 1] = "PLAYER_MODE_TIMES";
        TaskObjectType[TaskObjectType["PLAYER_STAGE_TIMES"] = 2] = "PLAYER_STAGE_TIMES";
        TaskObjectType[TaskObjectType["KILL_MONSTER_CLASS_COUNT"] = 3] = "KILL_MONSTER_CLASS_COUNT";
        TaskObjectType[TaskObjectType["LOTTERY_TIMES"] = 4] = "LOTTERY_TIMES";
        TaskObjectType[TaskObjectType["AFK_TIMES"] = 5] = "AFK_TIMES";
        TaskObjectType[TaskObjectType["CHARGE_AMOUNT"] = 6] = "CHARGE_AMOUNT";
        TaskObjectType[TaskObjectType["BUY_ITEM_COUNT"] = 7] = "BUY_ITEM_COUNT";
        TaskObjectType[TaskObjectType["WATCH_AD_TIMES"] = 8] = "WATCH_AD_TIMES";
        TaskObjectType[TaskObjectType["LOGIN_DAYS"] = 9] = "LOGIN_DAYS";
        TaskObjectType[TaskObjectType["ROLE_LEVEL"] = 10] = "ROLE_LEVEL";
        TaskObjectType[TaskObjectType["CONSOME"] = 11] = "CONSOME";
        TaskObjectType[TaskObjectType["EQUIP_QUALITY"] = 12] = "EQUIP_QUALITY";
        TaskObjectType[TaskObjectType["EQUIP_LEVEL"] = 13] = "EQUIP_LEVEL";
        TaskObjectType[TaskObjectType["EQUIP_COMB_TIMES"] = 14] = "EQUIP_COMB_TIMES";
        TaskObjectType[TaskObjectType["EQUIP_UNDRAGE_TIMES"] = 15] = "EQUIP_UNDRAGE_TIMES";
        TaskObjectType[TaskObjectType["GUILD_DONATE_TIMES"] = 16] = "GUILD_DONATE_TIMES";
        TaskObjectType[TaskObjectType["FIGHTER_UNLOCK_COUNT"] = 17] = "FIGHTER_UNLOCK_COUNT";
        TaskObjectType[TaskObjectType["FIGHTER_STAR_TOTAL"] = 18] = "FIGHTER_STAR_TOTAL";
        TaskObjectType[TaskObjectType["ROLE_FORCE"] = 19] = "ROLE_FORCE";
        TaskObjectType[TaskObjectType["SPECIFY_BUG_CONSUME"] = 20] = "SPECIFY_BUG_CONSUME";
        TaskObjectType[TaskObjectType["USE_ITEM_TIMES"] = 21] = "USE_ITEM_TIMES";
        TaskObjectType[TaskObjectType["CHECK_IN"] = 22] = "CHECK_IN";
        return TaskObjectType;
      }({}));

      _export("TaskPeriodType", TaskPeriodType = /*#__PURE__*/function (TaskPeriodType) {
        TaskPeriodType[TaskPeriodType["SINGLE"] = 0] = "SINGLE";
        TaskPeriodType[TaskPeriodType["DAILY"] = 1] = "DAILY";
        TaskPeriodType[TaskPeriodType["WEEKLY"] = 2] = "WEEKLY";
        TaskPeriodType[TaskPeriodType["MONTHLY"] = 3] = "MONTHLY";
        return TaskPeriodType;
      }({}));

      _export("Boss", Boss = class Boss {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * 飞机id
           */
          this.bId = void 0;

          /**
           * 子类型
           */
          this.sId = void 0;

          /**
           * 出场参数
           */
          this.app = void 0;

          /**
           * inAudio
           */
          this.ta = void 0;

          /**
           * 死亡掉落延迟
           */
          this.ft = void 0;

          /**
           * leave
           */
          this.leave = void 0;

          /**
           * exp
           */
          this.exp = void 0;

          /**
           * rid
           */
          this.rid = void 0;

          /**
           * 爆炸震动参数
           */
          this.sk = void 0;

          /**
           * 爆炸参数
           */
          this.blp = void 0;

          /**
           * us
           */
          this.us = void 0;

          /**
           * ua
           */
          this.ua = void 0;

          /**
           * va
           */
          this.va = void 0;

          /**
           * sv
           */
          this.sv = void 0;

          /**
           * fl
           */
          this.fl = void 0;

          /**
           * loot
           */
          this.loot = void 0;

          /**
           * adsorb
           */
          this.adsorb = void 0;

          /**
           * lp0
           */
          this.lp0 = void 0;

          /**
           * lp1
           */
          this.lp1 = void 0;

          /**
           * dh
           */
          this.dh = void 0;

          /**
           * atk
           */
          this.atk = void 0;

          /**
           * col
           */
          this.col = void 0;

          /**
           * tway
           */
          this.tway = void 0;

          /**
           * way
           */
          this.way = void 0;

          /**
           * wi
           */
          this.wi = void 0;

          /**
           * sp
           */
          this.sp = void 0;

          /**
           * ai
           */
          this.ai = void 0;

          /**
           * ra
           */
          this.ra = void 0;

          /**
           * a0
           */
          this.a0 = void 0;

          /**
           * a1
           */
          this.a1 = void 0;

          /**
           * a2
           */
          this.a2 = void 0;

          /**
           * a3
           */
          this.a3 = void 0;

          /**
           * a4
           */
          this.a4 = void 0;

          /**
           * a5
           */
          this.a5 = void 0;

          /**
           * a6
           */
          this.a6 = void 0;

          /**
           * a7
           */
          this.a7 = void 0;

          /**
           * a8
           */
          this.a8 = void 0;

          /**
           * a9
           */
          this.a9 = void 0;

          /**
           * a10
           */
          this.a10 = void 0;

          /**
           * a11
           */
          this.a11 = void 0;

          /**
           * a12
           */
          this.a12 = void 0;

          /**
           * a13
           */
          this.a13 = void 0;

          /**
           * a14
           */
          this.a14 = void 0;

          /**
           * a15
           */
          this.a15 = void 0;

          /**
           * a16
           */
          this.a16 = void 0;

          /**
           * a17
           */
          this.a17 = void 0;

          /**
           * a18
           */
          this.a18 = void 0;

          /**
           * a19
           */
          this.a19 = void 0;

          /**
           * a20
           */
          this.a20 = void 0;

          /**
           * a21
           */
          this.a21 = void 0;

          /**
           * a22
           */
          this.a22 = void 0;

          /**
           * a100
           */
          this.a100 = void 0;

          /**
           * a101
           */
          this.a101 = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.bId === undefined) {
            throw new Error();
          }

          this.bId = _json_.bId;

          if (_json_.sId === undefined) {
            throw new Error();
          }

          this.sId = _json_.sId;

          if (_json_.app === undefined) {
            throw new Error();
          }

          this.app = _json_.app;

          if (_json_.ta === undefined) {
            throw new Error();
          }

          this.ta = _json_.ta;

          if (_json_.ft === undefined) {
            throw new Error();
          }

          this.ft = _json_.ft;

          if (_json_.leave === undefined) {
            throw new Error();
          }

          this.leave = _json_.leave;

          if (_json_.exp === undefined) {
            throw new Error();
          }

          this.exp = _json_.exp;

          if (_json_.rid === undefined) {
            throw new Error();
          }

          this.rid = _json_.rid;

          if (_json_.sk === undefined) {
            throw new Error();
          }

          this.sk = _json_.sk;

          if (_json_.blp === undefined) {
            throw new Error();
          }

          this.blp = _json_.blp;

          if (_json_.us === undefined) {
            throw new Error();
          }

          this.us = _json_.us;

          if (_json_.ua === undefined) {
            throw new Error();
          }

          this.ua = _json_.ua;

          if (_json_.va === undefined) {
            throw new Error();
          }

          this.va = _json_.va;

          if (_json_.sv === undefined) {
            throw new Error();
          }

          this.sv = _json_.sv;

          if (_json_.fl === undefined) {
            throw new Error();
          }

          this.fl = _json_.fl;

          if (_json_.loot === undefined) {
            throw new Error();
          }

          this.loot = _json_.loot;

          if (_json_.adsorb === undefined) {
            throw new Error();
          }

          this.adsorb = _json_.adsorb;

          if (_json_.lp0 === undefined) {
            throw new Error();
          }

          this.lp0 = _json_.lp0;

          if (_json_.lp1 === undefined) {
            throw new Error();
          }

          this.lp1 = _json_.lp1;

          if (_json_.dh === undefined) {
            throw new Error();
          }

          this.dh = _json_.dh;

          if (_json_.atk === undefined) {
            throw new Error();
          }

          this.atk = _json_.atk;

          if (_json_.col === undefined) {
            throw new Error();
          }

          this.col = _json_.col;

          if (_json_.tway === undefined) {
            throw new Error();
          }

          this.tway = _json_.tway;

          if (_json_.way === undefined) {
            throw new Error();
          }

          this.way = _json_.way;

          if (_json_.wi === undefined) {
            throw new Error();
          }

          this.wi = _json_.wi;

          if (_json_.sp === undefined) {
            throw new Error();
          }

          this.sp = _json_.sp;

          if (_json_.ai === undefined) {
            throw new Error();
          }

          this.ai = _json_.ai;

          if (_json_.ra === undefined) {
            throw new Error();
          }

          this.ra = _json_.ra;

          if (_json_.a0 === undefined) {
            throw new Error();
          }

          this.a0 = _json_.a0;

          if (_json_.a1 === undefined) {
            throw new Error();
          }

          this.a1 = _json_.a1;

          if (_json_.a2 === undefined) {
            throw new Error();
          }

          this.a2 = _json_.a2;

          if (_json_.a3 === undefined) {
            throw new Error();
          }

          this.a3 = _json_.a3;

          if (_json_.a4 === undefined) {
            throw new Error();
          }

          this.a4 = _json_.a4;

          if (_json_.a5 === undefined) {
            throw new Error();
          }

          this.a5 = _json_.a5;

          if (_json_.a6 === undefined) {
            throw new Error();
          }

          this.a6 = _json_.a6;

          if (_json_.a7 === undefined) {
            throw new Error();
          }

          this.a7 = _json_.a7;

          if (_json_.a8 === undefined) {
            throw new Error();
          }

          this.a8 = _json_.a8;

          if (_json_.a9 === undefined) {
            throw new Error();
          }

          this.a9 = _json_.a9;

          if (_json_.a10 === undefined) {
            throw new Error();
          }

          this.a10 = _json_.a10;

          if (_json_.a11 === undefined) {
            throw new Error();
          }

          this.a11 = _json_.a11;

          if (_json_.a12 === undefined) {
            throw new Error();
          }

          this.a12 = _json_.a12;

          if (_json_.a13 === undefined) {
            throw new Error();
          }

          this.a13 = _json_.a13;

          if (_json_.a14 === undefined) {
            throw new Error();
          }

          this.a14 = _json_.a14;

          if (_json_.a15 === undefined) {
            throw new Error();
          }

          this.a15 = _json_.a15;

          if (_json_.a16 === undefined) {
            throw new Error();
          }

          this.a16 = _json_.a16;

          if (_json_.a17 === undefined) {
            throw new Error();
          }

          this.a17 = _json_.a17;

          if (_json_.a18 === undefined) {
            throw new Error();
          }

          this.a18 = _json_.a18;

          if (_json_.a19 === undefined) {
            throw new Error();
          }

          this.a19 = _json_.a19;

          if (_json_.a20 === undefined) {
            throw new Error();
          }

          this.a20 = _json_.a20;

          if (_json_.a21 === undefined) {
            throw new Error();
          }

          this.a21 = _json_.a21;

          if (_json_.a22 === undefined) {
            throw new Error();
          }

          this.a22 = _json_.a22;

          if (_json_.a100 === undefined) {
            throw new Error();
          }

          this.a100 = _json_.a100;

          if (_json_.a101 === undefined) {
            throw new Error();
          }

          this.a101 = _json_.a101;
        }

        resolve(tables) {}

      });

      _export("buffer", buffer = class buffer {
        constructor(_json_) {
          /**
           * ID
           */
          this.id = void 0;

          /**
           * 类别
           */
          this.buffType = void 0;

          /**
           * 持续时间
           */
          this.duration = void 0;

          /**
           * 持续时间加成
           */
          this.durationBonus = void 0;

          /**
           * 最大叠加次数
           */
          this.maxStack = void 0;

          /**
           * 叠加刷新策略
           */
          this.refreshType = void 0;

          /**
           * 周期
           */
          this.cycle = void 0;

          /**
           * 周期计数
           */
          this.cycleTimes = void 0;
          this.effects = void 0;

          /**
           * 禁用条件
           */
          this.conditionID = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.buffType === undefined) {
            throw new Error();
          }

          this.buffType = _json_.buffType;

          if (_json_.duration === undefined) {
            throw new Error();
          }

          this.duration = _json_.duration;

          if (_json_.durationBonus === undefined) {
            throw new Error();
          }

          this.durationBonus = _json_.durationBonus;

          if (_json_.maxStack === undefined) {
            throw new Error();
          }

          this.maxStack = _json_.maxStack;

          if (_json_.refreshType === undefined) {
            throw new Error();
          }

          this.refreshType = _json_.refreshType;

          if (_json_.cycle === undefined) {
            throw new Error();
          }

          this.cycle = _json_.cycle;

          if (_json_.cycleTimes === undefined) {
            throw new Error();
          }

          this.cycleTimes = _json_.cycleTimes;

          if (_json_.effects === undefined) {
            throw new Error();
          }

          {
            this.effects = [];

            for (let _ele0 of _json_.effects) {
              let _e0;

              _e0 = new builtin.EffectParam(_ele0);
              this.effects.push(_e0);
            }
          }

          if (_json_.conditionID === undefined) {
            throw new Error();
          }

          this.conditionID = _json_.conditionID;
        }

        resolve(tables) {}

      });

      (function (_builtin9) {
        class ApplyBuff {
          constructor(_json_) {
            this.target = void 0;
            this.buffID = void 0;

            if (_json_.target === undefined) {
              throw new Error();
            }

            this.target = _json_.target;

            if (_json_.buffID === undefined) {
              throw new Error();
            }

            this.buffID = _json_.buffID;
          }

          resolve(tables) {}

        }

        _builtin9.ApplyBuff = ApplyBuff;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin) {
        class ConParam {
          constructor(_json_) {
            this.con = void 0;
            this.param = void 0;

            if (_json_.con === undefined) {
              throw new Error();
            }

            this.con = _json_.con;

            if (_json_.param === undefined) {
              throw new Error();
            }

            this.param = _json_.param;
          }

          resolve(tables) {}

        }

        _builtin.ConParam = ConParam;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin2) {
        class EffectParam {
          constructor(_json_) {
            this.type = void 0;
            this.target = void 0;
            this.param = void 0;

            if (_json_.type === undefined) {
              throw new Error();
            }

            this.type = _json_.type;

            if (_json_.target === undefined) {
              throw new Error();
            }

            this.target = _json_.target;

            if (_json_.param === undefined) {
              throw new Error();
            }

            {
              this.param = [];

              for (let _ele0 of _json_.param) {
                let _e0;

                _e0 = _ele0;
                this.param.push(_e0);
              }
            }
          }

          resolve(tables) {}

        }

        _builtin2.EffectParam = EffectParam;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin3) {
        class randStrategy {
          constructor(_json_) {
            /**
             * 随机策略ID
             */
            this.ID = void 0;

            /**
             * ID的权重
             */
            this.Weight = void 0;

            if (_json_.ID === undefined) {
              throw new Error();
            }

            this.ID = _json_.ID;

            if (_json_.Weight === undefined) {
              throw new Error();
            }

            this.Weight = _json_.Weight;
          }

          resolve(tables) {}

        }

        _builtin3.randStrategy = randStrategy;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin4) {
        class RatingParam {
          constructor(_json_) {
            this.rating = void 0;
            this.param = void 0;

            if (_json_.rating === undefined) {
              throw new Error();
            }

            this.rating = _json_.rating;

            if (_json_.param === undefined) {
              throw new Error();
            }

            this.param = _json_.param;
          }

          resolve(tables) {}

        }

        _builtin4.RatingParam = RatingParam;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin5) {
        class SkillCondition {
          constructor(_json_) {
            this.type = void 0;
            this.param = void 0;

            if (_json_.type === undefined) {
              throw new Error();
            }

            this.type = _json_.type;

            if (_json_.param === undefined) {
              throw new Error();
            }

            {
              this.param = [];

              for (let _ele0 of _json_.param) {
                let _e0;

                _e0 = _ele0;
                this.param.push(_e0);
              }
            }
          }

          resolve(tables) {}

        }

        _builtin5.SkillCondition = SkillCondition;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin6) {
        class vector2 {
          constructor(_json_) {
            this.x = void 0;
            this.y = void 0;

            if (_json_.x === undefined) {
              throw new Error();
            }

            this.x = _json_.x;

            if (_json_.y === undefined) {
              throw new Error();
            }

            this.y = _json_.y;
          }

          resolve(tables) {}

        }

        _builtin6.vector2 = vector2;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin7) {
        class vector3 {
          constructor(_json_) {
            this.x = void 0;
            this.y = void 0;
            this.z = void 0;

            if (_json_.x === undefined) {
              throw new Error();
            }

            this.x = _json_.x;

            if (_json_.y === undefined) {
              throw new Error();
            }

            this.y = _json_.y;

            if (_json_.z === undefined) {
              throw new Error();
            }

            this.z = _json_.z;
          }

          resolve(tables) {}

        }

        _builtin7.vector3 = vector3;
      })(builtin || _export("builtin", builtin = {}));

      (function (_builtin8) {
        class vector4 {
          constructor(_json_) {
            this.x = void 0;
            this.y = void 0;
            this.z = void 0;
            this.w = void 0;

            if (_json_.x === undefined) {
              throw new Error();
            }

            this.x = _json_.x;

            if (_json_.y === undefined) {
              throw new Error();
            }

            this.y = _json_.y;

            if (_json_.z === undefined) {
              throw new Error();
            }

            this.z = _json_.z;

            if (_json_.w === undefined) {
              throw new Error();
            }

            this.w = _json_.w;
          }

          resolve(tables) {}

        }

        _builtin8.vector4 = vector4;
      })(builtin || _export("builtin", builtin = {}));

      _export("Bullet", Bullet = class Bullet {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * name
           */
          this.name = void 0;

          /**
           * am
           */
          this.am = void 0;

          /**
           * image
           */
          this.image = void 0;

          /**
           * 子弹类型
           */
          this.bustyle = void 0;

          /**
           * 旋转角度
           */
          this.angleSpeed = void 0;

          /**
           * 等待时间
           */
          this.waittime = void 0;

          /**
           * 速度
           */
          this.initialve = void 0;

          /**
           * 速度随机变量
           */
          this.spdiff = void 0;

          /**
           * 缩放
           */
          this.scale = void 0;

          /**
           * 子弹存活时间
           */
          this.retrieve = void 0;

          /**
           * 是否穿透
           */
          this.disappear = void 0;

          /**
           * 碰撞宽高
           */
          this.shiftingbody = void 0;

          /**
           * 碰撞
           */
          this.body = void 0;

          /**
           * 伤害粒子效果
           */
          this.exstyle1 = void 0;

          /**
           * 伤害粒子缩放
           */
          this.exstyle2 = void 0;

          /**
           * time
           */
          this.time = void 0;

          /**
           * accnumber
           */
          this.accnumber = void 0;

          /**
           * acc
           */
          this.acc = void 0;

          /**
           * offset
           */
          this.offset = void 0;

          /**
           * para
           */
          this.para = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.am === undefined) {
            throw new Error();
          }

          this.am = _json_.am;

          if (_json_.image === undefined) {
            throw new Error();
          }

          this.image = _json_.image;

          if (_json_.bustyle === undefined) {
            throw new Error();
          }

          this.bustyle = _json_.bustyle;

          if (_json_.angleSpeed === undefined) {
            throw new Error();
          }

          this.angleSpeed = _json_.angleSpeed;

          if (_json_.waittime === undefined) {
            throw new Error();
          }

          {
            this.waittime = [];

            for (let _ele0 of _json_.waittime) {
              let _e0;

              _e0 = _ele0;
              this.waittime.push(_e0);
            }
          }

          if (_json_.initialve === undefined) {
            throw new Error();
          }

          this.initialve = _json_.initialve;

          if (_json_.spdiff === undefined) {
            throw new Error();
          }

          this.spdiff = _json_.spdiff;

          if (_json_.scale === undefined) {
            throw new Error();
          }

          this.scale = _json_.scale;

          if (_json_.retrieve === undefined) {
            throw new Error();
          }

          this.retrieve = _json_.retrieve;

          if (_json_.disappear === undefined) {
            throw new Error();
          }

          this.disappear = _json_.disappear;

          if (_json_.shiftingbody === undefined) {
            throw new Error();
          }

          {
            this.shiftingbody = [];

            for (let _ele0 of _json_.shiftingbody) {
              let _e0;

              _e0 = _ele0;
              this.shiftingbody.push(_e0);
            }
          }

          if (_json_.body === undefined) {
            throw new Error();
          }

          this.body = _json_.body;

          if (_json_.exstyle1 === undefined) {
            throw new Error();
          }

          this.exstyle1 = _json_.exstyle1;

          if (_json_.exstyle2 === undefined) {
            throw new Error();
          }

          this.exstyle2 = _json_.exstyle2;

          if (_json_.time === undefined) {
            throw new Error();
          }

          this.time = _json_.time;

          if (_json_.accnumber === undefined) {
            throw new Error();
          }

          this.accnumber = _json_.accnumber;

          if (_json_.acc === undefined) {
            throw new Error();
          }

          this.acc = _json_.acc;

          if (_json_.offset === undefined) {
            throw new Error();
          }

          {
            this.offset = [];

            for (let _ele0 of _json_.offset) {
              let _e0;

              _e0 = _ele0;
              this.offset.push(_e0);
            }
          }

          if (_json_.para === undefined) {
            throw new Error();
          }

          {
            this.para = [];

            for (let _ele0 of _json_.para) {
              let _e0;

              _e0 = _ele0;
              this.para.push(_e0);
            }
          }
        }

        resolve(tables) {}

      });

      _export("Chapter", Chapter = class Chapter {
        constructor(_json_) {
          /**
           * 章节ID
           */
          this.id = void 0;

          /**
           * 章节关卡数量
           */
          this.levelCount = void 0;

          /**
           * 章节关卡组数量
           */
          this.levelGroupCount = void 0;

          /**
           * 1=随机<br/>2=随机不重复<br/>3=顺序重复
           */
          this.strategy = void 0;

          /**
           * 章节伤害加成
           */
          this.damageBonus = void 0;

          /**
           * 章节生命加成
           */
          this.lifeBounus = void 0;
          this.strategyList = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.levelCount === undefined) {
            throw new Error();
          }

          this.levelCount = _json_.levelCount;

          if (_json_.levelGroupCount === undefined) {
            throw new Error();
          }

          this.levelGroupCount = _json_.levelGroupCount;

          if (_json_.strategy === undefined) {
            throw new Error();
          }

          this.strategy = _json_.strategy;

          if (_json_.damageBonus === undefined) {
            throw new Error();
          }

          this.damageBonus = _json_.damageBonus;

          if (_json_.lifeBounus === undefined) {
            throw new Error();
          }

          this.lifeBounus = _json_.lifeBounus;

          if (_json_.strategyList === undefined) {
            throw new Error();
          }

          {
            this.strategyList = [];

            for (let _ele0 of _json_.strategyList) {
              let _e0;

              _e0 = new builtin.randStrategy(_ele0);
              this.strategyList.push(_e0);
            }
          }
        }

        resolve(tables) {
          for (let _e of this.strategyList) {
            _e == null || _e.resolve(tables);
          }
        }

      });
      /**
       * 消耗的材料
       */


      _export("ConsumeItem", ConsumeItem = class ConsumeItem {
        constructor(_json_) {
          this.id = void 0;
          this.num = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.num === undefined) {
            throw new Error();
          }

          this.num = _json_.num;
        }

        resolve(tables) {}

      });

      _export("ConsumeMoney", ConsumeMoney = class ConsumeMoney {
        constructor(_json_) {
          /**
           * 货币类型
           */
          this.type = void 0;

          /**
           * 货币数量
           */
          this.num = void 0;

          if (_json_.type === undefined) {
            throw new Error();
          }

          this.type = _json_.type;

          if (_json_.num === undefined) {
            throw new Error();
          }

          this.num = _json_.num;
        }

        resolve(tables) {}

      });

      _export("Enemy", Enemy = class Enemy {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * 敌机的显示id
           */
          this.uiId = void 0;

          /**
           * 攻击
           */
          this.atk = void 0;

          /**
           * 血量
           */
          this.hp = void 0;

          /**
           * 碰撞等级
           */
          this.collideLevel = void 0;

          /**
           * 是否改变方向
           */
          this.turn = void 0;

          /**
           * 是否显示血条
           */
          this.hpShow = void 0;

          /**
           * 碰撞伤害值
           */
          this.collideAttack = void 0;

          /**
           * 碰撞后是否死亡
           */
          this.bCollideDead = void 0;

          /**
           * 移动时是否攻击
           */
          this.bMoveAttack = void 0;

          /**
           * 静止时是否攻击
           */
          this.bStayAttack = void 0;

          /**
           * 攻击间隔时间
           */
          this.attackInterval = void 0;

          /**
           * 攻击次数
           */
          this.attackNum = void 0;

          /**
           * 攻击点位置数据(x,y;间隔,子弹id,子弹数量,子弹间隔,子弹攻击力百分比(100为1倍);)
           */
          this.attackData = void 0;

          /**
           * 自定义参数
           */
          this.param = void 0;

          /**
           * 死亡时发射的子弹数据
           */
          this.dieShoot = void 0;

          /**
           * 死亡时是否发射子弹
           */
          this.dieBullet = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.uiId === undefined) {
            throw new Error();
          }

          this.uiId = _json_.uiId;

          if (_json_.atk === undefined) {
            throw new Error();
          }

          this.atk = _json_.atk;

          if (_json_.hp === undefined) {
            throw new Error();
          }

          this.hp = _json_.hp;

          if (_json_.collideLevel === undefined) {
            throw new Error();
          }

          this.collideLevel = _json_.collideLevel;

          if (_json_.turn === undefined) {
            throw new Error();
          }

          this.turn = _json_.turn;

          if (_json_.hpShow === undefined) {
            throw new Error();
          }

          this.hpShow = _json_.hpShow;

          if (_json_.collideAttack === undefined) {
            throw new Error();
          }

          this.collideAttack = _json_.collideAttack;

          if (_json_.bCollideDead === undefined) {
            throw new Error();
          }

          this.bCollideDead = _json_.bCollideDead;

          if (_json_.bMoveAttack === undefined) {
            throw new Error();
          }

          this.bMoveAttack = _json_.bMoveAttack;

          if (_json_.bStayAttack === undefined) {
            throw new Error();
          }

          this.bStayAttack = _json_.bStayAttack;

          if (_json_.attackInterval === undefined) {
            throw new Error();
          }

          this.attackInterval = _json_.attackInterval;

          if (_json_.attackNum === undefined) {
            throw new Error();
          }

          this.attackNum = _json_.attackNum;

          if (_json_.attackData === undefined) {
            throw new Error();
          }

          this.attackData = _json_.attackData;

          if (_json_.param === undefined) {
            throw new Error();
          }

          this.param = _json_.param;

          if (_json_.dieShoot === undefined) {
            throw new Error();
          }

          this.dieShoot = _json_.dieShoot;

          if (_json_.dieBullet === undefined) {
            throw new Error();
          }

          this.dieBullet = _json_.dieBullet;
        }

        resolve(tables) {}

      });

      _export("EnemyUI", EnemyUI = class EnemyUI {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * 图片
           */
          this.image = void 0;

          /**
           * 是否动画
           */
          this.isAm = void 0;

          /**
           * 碰撞器数据
           */
          this.collider = void 0;

          /**
           * 血量参数
           */
          this.hpParam = void 0;

          /**
           * 爆炸音效 ID
           */
          this.blastSound = void 0;

          /**
           * 爆炸次数和爆炸参数
           */
          this.blp = void 0;

          /**
           * 爆炸持续时间
           */
          this.blastDurations = void 0;

          /**
           * 爆炸震动参数
           */
          this.blastShake = void 0;

          /**
           * 伤害参数
           */
          this.damageParam = void 0;

          /**
           * 额外参数
           */
          this.extraParam0 = void 0;

          /**
           * 额外参数 1
           */
          this.extraParam1 = void 0;

          /**
           * 技能抗性字典
           */
          this.skillResistUIDict = void 0;

          /**
           * 掉落参数 0
           */
          this.lootParam0 = void 0;

          /**
           * 掉落参数 1
           */
          this.lootParam1 = void 0;

          /**
           * 显示参数
           */
          this.showParam = void 0;

          /**
           * 潜行动画
           */
          this.sneakAnim = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.image === undefined) {
            throw new Error();
          }

          this.image = _json_.image;

          if (_json_.isAm === undefined) {
            throw new Error();
          }

          this.isAm = _json_.isAm;

          if (_json_.collider === undefined) {
            throw new Error();
          }

          this.collider = _json_.collider;

          if (_json_.hpParam === undefined) {
            throw new Error();
          }

          this.hpParam = _json_.hpParam;

          if (_json_.blastSound === undefined) {
            throw new Error();
          }

          this.blastSound = _json_.blastSound;

          if (_json_.blp === undefined) {
            throw new Error();
          }

          this.blp = _json_.blp;

          if (_json_.blastDurations === undefined) {
            throw new Error();
          }

          this.blastDurations = _json_.blastDurations;

          if (_json_.blastShake === undefined) {
            throw new Error();
          }

          this.blastShake = _json_.blastShake;

          if (_json_.damageParam === undefined) {
            throw new Error();
          }

          this.damageParam = _json_.damageParam;

          if (_json_.extraParam0 === undefined) {
            throw new Error();
          }

          this.extraParam0 = _json_.extraParam0;

          if (_json_.extraParam1 === undefined) {
            throw new Error();
          }

          this.extraParam1 = _json_.extraParam1;

          if (_json_.skillResistUIDict === undefined) {
            throw new Error();
          }

          this.skillResistUIDict = _json_.skillResistUIDict;

          if (_json_.lootParam0 === undefined) {
            throw new Error();
          }

          this.lootParam0 = _json_.lootParam0;

          if (_json_.lootParam1 === undefined) {
            throw new Error();
          }

          this.lootParam1 = _json_.lootParam1;

          if (_json_.showParam === undefined) {
            throw new Error();
          }

          this.showParam = _json_.showParam;

          if (_json_.sneakAnim === undefined) {
            throw new Error();
          }

          this.sneakAnim = _json_.sneakAnim;
        }

        resolve(tables) {}

      });
      /**
       * 装备属性
       */


      _export("EquipProp", EquipProp = class EquipProp {
        constructor(_json_) {
          this.id = void 0;
          this.value = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.value === undefined) {
            throw new Error();
          }

          this.value = _json_.value;
        }

        resolve(tables) {}

      });

      _export("GameMap", GameMap = class GameMap {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * level
           */
          this.level = void 0;

          /**
           * floor_res
           */
          this.floorRes = void 0;

          /**
           * hide_img
           */
          this.hideImg = void 0;

          /**
           * sky_res
           */
          this.skyRes = void 0;

          /**
           * imageSque_res
           */
          this.imageSqueRes = void 0;

          /**
           * floor_speed
           */
          this.floorSpeed = void 0;

          /**
           * sky_speed
           */
          this.skySpeed = void 0;

          /**
           * imageSque_speed
           */
          this.imageSqueSpeed = void 0;

          /**
           * floor_layer
           */
          this.floorLayer = void 0;

          /**
           * sky_layer
           */
          this.skyLayer = void 0;

          /**
           * imageSque_layer
           */
          this.imageSqueLayer = void 0;

          /**
           * imageSqueNode_move
           */
          this.imageSqueNodeMove = void 0;

          /**
           * imageSque_pos
           */
          this.imageSquePos = void 0;

          /**
           * skyNode_move
           */
          this.skyNodeMove = void 0;

          /**
           * link_y_distance
           */
          this.linkYDistance = void 0;

          /**
           * sky_angle
           */
          this.skyAngle = void 0;

          /**
           * sky_layout
           */
          this.skyLayout = void 0;

          /**
           * in_map_item
           */
          this.inMapItem = void 0;

          /**
           * start_y
           */
          this.startY = void 0;

          /**
           * total_rules
           */
          this.totalRules = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.level === undefined) {
            throw new Error();
          }

          this.level = _json_.level;

          if (_json_.floor_res === undefined) {
            throw new Error();
          }

          {
            this.floorRes = [];

            for (let _ele0 of _json_.floor_res) {
              let _e0;

              _e0 = _ele0;
              this.floorRes.push(_e0);
            }
          }

          if (_json_.hide_img === undefined) {
            throw new Error();
          }

          {
            this.hideImg = [];

            for (let _ele0 of _json_.hide_img) {
              let _e0;

              _e0 = _ele0;
              this.hideImg.push(_e0);
            }
          }

          if (_json_.sky_res === undefined) {
            throw new Error();
          }

          {
            this.skyRes = [];

            for (let _ele0 of _json_.sky_res) {
              let _e0;

              _e0 = _ele0;
              this.skyRes.push(_e0);
            }
          }

          if (_json_.imageSque_res === undefined) {
            throw new Error();
          }

          {
            this.imageSqueRes = [];

            for (let _ele0 of _json_.imageSque_res) {
              let _e0;

              _e0 = _ele0;
              this.imageSqueRes.push(_e0);
            }
          }

          if (_json_.floor_speed === undefined) {
            throw new Error();
          }

          {
            this.floorSpeed = [];

            for (let _ele0 of _json_.floor_speed) {
              let _e0;

              _e0 = _ele0;
              this.floorSpeed.push(_e0);
            }
          }

          if (_json_.sky_speed === undefined) {
            throw new Error();
          }

          {
            this.skySpeed = [];

            for (let _ele0 of _json_.sky_speed) {
              let _e0;

              _e0 = _ele0;
              this.skySpeed.push(_e0);
            }
          }

          if (_json_.imageSque_speed === undefined) {
            throw new Error();
          }

          {
            this.imageSqueSpeed = [];

            for (let _ele0 of _json_.imageSque_speed) {
              let _e0;

              _e0 = _ele0;
              this.imageSqueSpeed.push(_e0);
            }
          }

          if (_json_.floor_layer === undefined) {
            throw new Error();
          }

          {
            this.floorLayer = [];

            for (let _ele0 of _json_.floor_layer) {
              let _e0;

              _e0 = _ele0;
              this.floorLayer.push(_e0);
            }
          }

          if (_json_.sky_layer === undefined) {
            throw new Error();
          }

          {
            this.skyLayer = [];

            for (let _ele0 of _json_.sky_layer) {
              let _e0;

              _e0 = _ele0;
              this.skyLayer.push(_e0);
            }
          }

          if (_json_.imageSque_layer === undefined) {
            throw new Error();
          }

          {
            this.imageSqueLayer = [];

            for (let _ele0 of _json_.imageSque_layer) {
              let _e0;

              _e0 = _ele0;
              this.imageSqueLayer.push(_e0);
            }
          }

          if (_json_.imageSqueNode_move === undefined) {
            throw new Error();
          }

          {
            this.imageSqueNodeMove = [];

            for (let _ele0 of _json_.imageSqueNode_move) {
              let _e0;

              _e0 = _ele0;
              this.imageSqueNodeMove.push(_e0);
            }
          }

          if (_json_.imageSque_pos === undefined) {
            throw new Error();
          }

          {
            this.imageSquePos = [];

            for (let _ele0 of _json_.imageSque_pos) {
              let _e0;

              _e0 = _ele0;
              this.imageSquePos.push(_e0);
            }
          }

          if (_json_.skyNode_move === undefined) {
            throw new Error();
          }

          {
            this.skyNodeMove = [];

            for (let _ele0 of _json_.skyNode_move) {
              let _e0;

              _e0 = _ele0;
              this.skyNodeMove.push(_e0);
            }
          }

          if (_json_.link_y_distance === undefined) {
            throw new Error();
          }

          {
            this.linkYDistance = [];

            for (let _ele0 of _json_.link_y_distance) {
              let _e0;

              _e0 = _ele0;
              this.linkYDistance.push(_e0);
            }
          }

          if (_json_.sky_angle === undefined) {
            throw new Error();
          }

          {
            this.skyAngle = [];

            for (let _ele0 of _json_.sky_angle) {
              let _e0;

              _e0 = _ele0;
              this.skyAngle.push(_e0);
            }
          }

          if (_json_.sky_layout === undefined) {
            throw new Error();
          }

          {
            this.skyLayout = [];

            for (let _ele0 of _json_.sky_layout) {
              let _e0;

              _e0 = _ele0;
              this.skyLayout.push(_e0);
            }
          }

          if (_json_.in_map_item === undefined) {
            throw new Error();
          }

          {
            this.inMapItem = [];

            for (let _ele0 of _json_.in_map_item) {
              let _e0;

              _e0 = _ele0;
              this.inMapItem.push(_e0);
            }
          }

          if (_json_.start_y === undefined) {
            throw new Error();
          }

          this.startY = _json_.start_y;

          if (_json_.total_rules === undefined) {
            throw new Error();
          }

          this.totalRules = _json_.total_rules;
        }

        resolve(tables) {}

      });

      _export("GameMode", GameMode = class GameMode {
        constructor(_json_) {
          /**
           * ID
           */
          this.ID = void 0;

          /**
           * 模式类型
           */
          this.modeType = void 0;

          /**
           * 章节ID
           */
          this.chapterID = void 0;

          /**
           * 排序
           */
          this.order = void 0;

          /**
           * 入口资源
           */
          this.resourceID = void 0;

          /**
           * 文本介绍
           */
          this.description = void 0;
          this.conList = void 0;

          /**
           * 进入周期
           */
          this.cycle = void 0;

          /**
           * 进入次数
           */
          this.times = void 0;

          /**
           * 消耗类型
           */
          this.monType = void 0;

          /**
           * 消耗参数1
           */
          this.costParam1 = void 0;

          /**
           * 消耗参数2
           */
          this.costParam2 = void 0;

          /**
           * 复活次数
           */
          this.rebirthTimes = void 0;

          /**
           * 复活消耗
           */
          this.rebirthCost = void 0;

          /**
           * 战力评估
           */
          this.power = void 0;

          /**
           * 肉鸽组
           */
          this.rogueID = void 0;

          /**
           * 局内等级上限
           */
          this.LevelLimit = void 0;

          /**
           * 初始肉鸽选择
           */
          this.rogueFirst = void 0;

          /**
           * 扫荡次数
           */
          this.sweepLimit = void 0;

          /**
           * 奖励ID1
           */
          this.rewardID1 = void 0;

          /**
           * 奖励ID2
           */
          this.rewardID2 = void 0;
          this.ratingList = void 0;

          if (_json_.ID === undefined) {
            throw new Error();
          }

          this.ID = _json_.ID;

          if (_json_.modeType === undefined) {
            throw new Error();
          }

          this.modeType = _json_.modeType;

          if (_json_.chapterID === undefined) {
            throw new Error();
          }

          this.chapterID = _json_.chapterID;

          if (_json_.order === undefined) {
            throw new Error();
          }

          this.order = _json_.order;

          if (_json_.resourceID === undefined) {
            throw new Error();
          }

          this.resourceID = _json_.resourceID;

          if (_json_.description === undefined) {
            throw new Error();
          }

          this.description = _json_.description;

          if (_json_.conList === undefined) {
            throw new Error();
          }

          {
            this.conList = [];

            for (let _ele0 of _json_.conList) {
              let _e0;

              _e0 = new builtin.ConParam(_ele0);
              this.conList.push(_e0);
            }
          }

          if (_json_.cycle === undefined) {
            throw new Error();
          }

          this.cycle = _json_.cycle;

          if (_json_.times === undefined) {
            throw new Error();
          }

          this.times = _json_.times;

          if (_json_.monType === undefined) {
            throw new Error();
          }

          this.monType = _json_.monType;

          if (_json_.costParam1 === undefined) {
            throw new Error();
          }

          this.costParam1 = _json_.costParam1;

          if (_json_.costParam2 === undefined) {
            throw new Error();
          }

          this.costParam2 = _json_.costParam2;

          if (_json_.rebirthTimes === undefined) {
            throw new Error();
          }

          this.rebirthTimes = _json_.rebirthTimes;

          if (_json_.rebirthCost === undefined) {
            throw new Error();
          }

          this.rebirthCost = _json_.rebirthCost;

          if (_json_.power === undefined) {
            throw new Error();
          }

          this.power = _json_.power;

          if (_json_.rogueID === undefined) {
            throw new Error();
          }

          this.rogueID = _json_.rogueID;

          if (_json_.LevelLimit === undefined) {
            throw new Error();
          }

          this.LevelLimit = _json_.LevelLimit;

          if (_json_.rogueFirst === undefined) {
            throw new Error();
          }

          this.rogueFirst = _json_.rogueFirst;

          if (_json_.sweepLimit === undefined) {
            throw new Error();
          }

          this.sweepLimit = _json_.sweepLimit;

          if (_json_.rewardID1 === undefined) {
            throw new Error();
          }

          this.rewardID1 = _json_.rewardID1;

          if (_json_.rewardID2 === undefined) {
            throw new Error();
          }

          this.rewardID2 = _json_.rewardID2;

          if (_json_.ratingList === undefined) {
            throw new Error();
          }

          {
            this.ratingList = [];

            for (let _ele0 of _json_.ratingList) {
              let _e0;

              _e0 = new builtin.RatingParam(_ele0);
              this.ratingList.push(_e0);
            }
          }
        }

        resolve(tables) {}

      });

      _export("GlobalAttr", GlobalAttr = class GlobalAttr {
        constructor(_json_) {
          /**
           * 每回合发放的金币
           */
          this.GoldProducion = void 0;

          /**
           * 体力上限值
           */
          this.MaxEnergy = void 0;

          /**
           * 体力恢复的间隔时间
           */
          this.EnergyRecoverInterval = void 0;

          /**
           * 体力恢复的值
           */
          this.EnergyRecoverValue = void 0;

          /**
           * 局内道具拾取距离
           */
          this.ItemPickUpRadius = void 0;

          /**
           * 受击保护
           */
          this.PostHitProtection = void 0;

          if (_json_.GoldProducion === undefined) {
            throw new Error();
          }

          this.GoldProducion = _json_.GoldProducion;

          if (_json_.MaxEnergy === undefined) {
            throw new Error();
          }

          this.MaxEnergy = _json_.MaxEnergy;

          if (_json_.EnergyRecoverInterval === undefined) {
            throw new Error();
          }

          this.EnergyRecoverInterval = _json_.EnergyRecoverInterval;

          if (_json_.EnergyRecoverValue === undefined) {
            throw new Error();
          }

          this.EnergyRecoverValue = _json_.EnergyRecoverValue;

          if (_json_.ItemPickUpRadius === undefined) {
            throw new Error();
          }

          this.ItemPickUpRadius = _json_.ItemPickUpRadius;

          if (_json_.PostHitProtection === undefined) {
            throw new Error();
          }

          this.PostHitProtection = _json_.PostHitProtection;
        }

        resolve(tables) {}

      });

      _export("Level", Level = class Level {
        constructor(_json_) {
          /**
           * 关卡id
           */
          this.id = void 0;

          /**
           * 关卡prefab
           */
          this.prefab = void 0;

          /**
           * 是、否（默认值）
           */
          this.forbidFire = void 0;

          /**
           * 是、否（默认值）
           */
          this.forbidNBomb = void 0;

          /**
           * 是、否（默认值）
           */
          this.forbidActSkill = void 0;

          /**
           * 0到1（1表示正常碰撞）
           */
          this.planeCollisionScaling = void 0;

          /**
           * 1=常规关卡<br/>2=机关关卡<br/>3=金币关卡<br/>4=火箭关卡<br/>99=Boss关
           */
          this.levelType = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.prefab === undefined) {
            throw new Error();
          }

          this.prefab = _json_.prefab;

          if (_json_.forbidFire === undefined) {
            throw new Error();
          }

          this.forbidFire = _json_.forbidFire;

          if (_json_.forbidNBomb === undefined) {
            throw new Error();
          }

          this.forbidNBomb = _json_.forbidNBomb;

          if (_json_.forbidActSkill === undefined) {
            throw new Error();
          }

          this.forbidActSkill = _json_.forbidActSkill;

          if (_json_.planeCollisionScaling === undefined) {
            throw new Error();
          }

          this.planeCollisionScaling = _json_.planeCollisionScaling;

          if (_json_.levelType === undefined) {
            throw new Error();
          }

          this.levelType = _json_.levelType;
        }

        resolve(tables) {}

      });

      _export("LevelGroup", LevelGroup = class LevelGroup {
        constructor(_json_) {
          /**
           * 关卡组ID
           */
          this.id = void 0;

          /**
           * 常规关卡数量
           */
          this.normLevelCount = void 0;

          /**
           * 1=随机<br/>2=随机不重复<br/>3=顺序重复
           */
          this.normLevelST = void 0;
          this.normSTList = void 0;

          /**
           * BOSS关卡数量
           */
          this.bossLevelCount = void 0;

          /**
           * 1=随机<br/>2=随机不重复<br/>3=顺序重复
           */
          this.bossLevelST = void 0;
          this.bossSTList = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.normLevelCount === undefined) {
            throw new Error();
          }

          this.normLevelCount = _json_.normLevelCount;

          if (_json_.normLevelST === undefined) {
            throw new Error();
          }

          this.normLevelST = _json_.normLevelST;

          if (_json_.normSTList === undefined) {
            throw new Error();
          }

          {
            this.normSTList = [];

            for (let _ele0 of _json_.normSTList) {
              let _e0;

              _e0 = new builtin.randStrategy(_ele0);
              this.normSTList.push(_e0);
            }
          }

          if (_json_.bossLevelCount === undefined) {
            throw new Error();
          }

          this.bossLevelCount = _json_.bossLevelCount;

          if (_json_.bossLevelST === undefined) {
            throw new Error();
          }

          this.bossLevelST = _json_.bossLevelST;

          if (_json_.bossSTList === undefined) {
            throw new Error();
          }

          {
            this.bossSTList = [];

            for (let _ele0 of _json_.bossSTList) {
              let _e0;

              _e0 = new builtin.randStrategy(_ele0);
              this.bossSTList.push(_e0);
            }
          }
        }

        resolve(tables) {
          for (let _e of this.normSTList) {
            _e == null || _e.resolve(tables);
          }

          for (let _e of this.bossSTList) {
            _e == null || _e.resolve(tables);
          }
        }

      });

      _export("MainPlane", MainPlane = class MainPlane {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * type
           */
          this.type = void 0;

          /**
           * 碰撞范围
           */
          this.body = void 0;

          /**
           * 变现
           */
          this.transSrc = void 0;

          /**
           * 变形参数
           */
          this.transExt = void 0;

          /**
           * 火焰位置
           */
          this.zjdmtxzb = void 0;

          /**
           * transatk1
           */
          this.transatk1 = void 0;

          /**
           * shiftingatk1
           */
          this.shiftingatk1 = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.type === undefined) {
            throw new Error();
          }

          this.type = _json_.type;

          if (_json_.body === undefined) {
            throw new Error();
          }

          {
            this.body = [];

            for (let _ele0 of _json_.body) {
              let _e0;

              _e0 = _ele0;
              this.body.push(_e0);
            }
          }

          if (_json_.transSrc === undefined) {
            throw new Error();
          }

          {
            this.transSrc = [];

            for (let _ele0 of _json_.transSrc) {
              let _e0;

              _e0 = _ele0;
              this.transSrc.push(_e0);
            }
          }

          if (_json_.transExt === undefined) {
            throw new Error();
          }

          {
            this.transExt = [];

            for (let _ele0 of _json_.transExt) {
              let _e0;

              _e0 = _ele0;
              this.transExt.push(_e0);
            }
          }

          if (_json_.zjdmtxzb === undefined) {
            throw new Error();
          }

          {
            this.zjdmtxzb = [];

            for (let _ele0 of _json_.zjdmtxzb) {
              let _e0;

              _e0 = _ele0;
              this.zjdmtxzb.push(_e0);
            }
          }

          if (_json_.transatk1 === undefined) {
            throw new Error();
          }

          {
            this.transatk1 = [];

            for (let _ele0 of _json_.transatk1) {
              let _e0;

              _e0 = _ele0;
              this.transatk1.push(_e0);
            }
          }

          if (_json_.shiftingatk1 === undefined) {
            throw new Error();
          }

          {
            this.shiftingatk1 = [];

            for (let _ele0 of _json_.shiftingatk1) {
              let _e0;

              _e0 = _ele0;
              this.shiftingatk1.push(_e0);
            }
          }
        }

        resolve(tables) {}

      });

      _export("MainPlaneLv", MainPlaneLv = class MainPlaneLv {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * 血量
           */
          this.hp = void 0;

          /**
           * 攻击
           */
          this.atk = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.hp === undefined) {
            throw new Error();
          }

          this.hp = _json_.hp;

          if (_json_.atk === undefined) {
            throw new Error();
          }

          this.atk = _json_.atk;
        }

        resolve(tables) {}

      });
      /**
       * 战机效果
       */


      _export("PlaneEffect", PlaneEffect = class PlaneEffect {
        constructor(_json_) {
          this.effectId = void 0;

          if (_json_.effect_id === undefined) {
            throw new Error();
          }

          this.effectId = _json_.effect_id;
        }

        resolve(tables) {}

      });
      /**
       * 升星材料
       */


      _export("PlaneMaterial", PlaneMaterial = class PlaneMaterial {
        constructor(_json_) {
          this.materialId = void 0;
          this.materialCount = void 0;

          if (_json_.material_id === undefined) {
            throw new Error();
          }

          this.materialId = _json_.material_id;

          if (_json_.material_count === undefined) {
            throw new Error();
          }

          this.materialCount = _json_.material_count;
        }

        resolve(tables) {}

      });
      /**
       * 战机属性
       */


      _export("PlaneProperty", PlaneProperty = class PlaneProperty {
        constructor(_json_) {
          this.propType = void 0;
          this.propValue = void 0;

          if (_json_.prop_type === undefined) {
            throw new Error();
          }

          this.propType = _json_.prop_type;

          if (_json_.prop_value === undefined) {
            throw new Error();
          }

          this.propValue = _json_.prop_value;
        }

        resolve(tables) {}

      });
      /**
       * 属性增幅
       */


      _export("PropInc", PropInc = class PropInc {
        constructor(_json_) {
          this.id = void 0;

          /**
           * 万分比
           */
          this.inc = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.inc === undefined) {
            throw new Error();
          }

          this.inc = _json_.inc;
        }

        resolve(tables) {}

      });
      /**
       * 战机效果配置
       */


      _export("ResEffect", ResEffect = class ResEffect {
        constructor(_json_) {
          this.id = void 0;
          this.name = void 0;
          this.description = void 0;
          this.effectType = void 0;
          this.effectValue = void 0;
          this.effectParams = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.description === undefined) {
            throw new Error();
          }

          this.description = _json_.description;

          if (_json_.effect_type === undefined) {
            throw new Error();
          }

          this.effectType = _json_.effect_type;

          if (_json_.effect_value === undefined) {
            throw new Error();
          }

          this.effectValue = _json_.effect_value;

          if (_json_.effect_params === undefined) {
            throw new Error();
          }

          this.effectParams = _json_.effect_params;
        }

        resolve(tables) {}

      });
      /**
       * 装备
       */


      _export("ResEquip", ResEquip = class ResEquip {
        constructor(_json_) {
          this.id = void 0;
          this.name = void 0;
          this.quality = void 0;
          this.qualitySub = void 0;
          this.equipClass = void 0;
          this.props = void 0;
          this.consumeItems = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.quality === undefined) {
            throw new Error();
          }

          this.quality = _json_.quality;

          if (_json_.quality_sub === undefined) {
            throw new Error();
          }

          this.qualitySub = _json_.quality_sub;

          if (_json_.equip_class === undefined) {
            throw new Error();
          }

          this.equipClass = _json_.equip_class;

          if (_json_.props === undefined) {
            throw new Error();
          }

          {
            this.props = [];

            for (let _ele0 of _json_.props) {
              let _e0;

              _e0 = new EquipProp(_ele0);
              this.props.push(_e0);
            }
          }

          if (_json_.consume_items === undefined) {
            throw new Error();
          }

          {
            this.consumeItems = [];

            for (let _ele0 of _json_.consume_items) {
              let _e0;

              _e0 = new ConsumeItem(_ele0);
              this.consumeItems.push(_e0);
            }
          }
        }

        resolve(tables) {
          for (let _e of this.props) {
            _e == null || _e.resolve(tables);
          }

          for (let _e of this.consumeItems) {
            _e == null || _e.resolve(tables);
          }
        }

      });
      /**
       * 装备位升级
       */


      _export("ResEquipUpgrade", ResEquipUpgrade = class ResEquipUpgrade {
        constructor(_json_) {
          /**
           * 装备槽位的类型
           */
          this.equipClass = void 0;

          /**
           * 等级下限
           */
          this.levelFrom = void 0;

          /**
           * 等级上限
           */
          this.levelTo = void 0;
          this.propInc = void 0;
          this.consumeMoney = void 0;
          this.consumeItems = void 0;

          if (_json_.equip_class === undefined) {
            throw new Error();
          }

          this.equipClass = _json_.equip_class;

          if (_json_.level_from === undefined) {
            throw new Error();
          }

          this.levelFrom = _json_.level_from;

          if (_json_.level_to === undefined) {
            throw new Error();
          }

          this.levelTo = _json_.level_to;

          if (_json_.prop_inc === undefined) {
            throw new Error();
          }

          {
            this.propInc = [];

            for (let _ele0 of _json_.prop_inc) {
              let _e0;

              _e0 = new PropInc(_ele0);
              this.propInc.push(_e0);
            }
          }

          if (_json_.consume_money === undefined) {
            throw new Error();
          }

          this.consumeMoney = new ConsumeMoney(_json_.consume_money);

          if (_json_.consume_items === undefined) {
            throw new Error();
          }

          {
            this.consumeItems = [];

            for (let _ele0 of _json_.consume_items) {
              let _e0;

              _e0 = new ConsumeItem(_ele0);
              this.consumeItems.push(_e0);
            }
          }
        }

        resolve(tables) {
          var _this$consumeMoney;

          for (let _e of this.propInc) {
            _e == null || _e.resolve(tables);
          }

          (_this$consumeMoney = this.consumeMoney) == null || _this$consumeMoney.resolve(tables);

          for (let _e of this.consumeItems) {
            _e == null || _e.resolve(tables);
          }
        }

      });

      _export("ResGM", ResGM = class ResGM {
        constructor(_json_) {
          this.tabID = void 0;
          this.tabName = void 0;
          this.name = void 0;
          this.cmd = void 0;
          this.desc = void 0;

          if (_json_.tabID === undefined) {
            throw new Error();
          }

          this.tabID = _json_.tabID;

          if (_json_.tabName === undefined) {
            throw new Error();
          }

          this.tabName = _json_.tabName;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.cmd === undefined) {
            throw new Error();
          }

          this.cmd = _json_.cmd;

          if (_json_.desc === undefined) {
            throw new Error();
          }

          this.desc = _json_.desc;
        }

        resolve(tables) {}

      });
      /**
       * 道具
       */


      _export("ResItem", ResItem = class ResItem {
        constructor(_json_) {
          this.id = void 0;
          this.name = void 0;
          this.quality = void 0;
          this.qualitySub = void 0;
          this.useType = void 0;
          this.effectId = void 0;
          this.effectParam1 = void 0;
          this.effectParam2 = void 0;
          this.maxStackNum = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.quality === undefined) {
            throw new Error();
          }

          this.quality = _json_.quality;

          if (_json_.quality_sub === undefined) {
            throw new Error();
          }

          this.qualitySub = _json_.quality_sub;

          if (_json_.use_type === undefined) {
            throw new Error();
          }

          this.useType = _json_.use_type;

          if (_json_.effect_id === undefined) {
            throw new Error();
          }

          this.effectId = _json_.effect_id;

          if (_json_.effect_param1 === undefined) {
            throw new Error();
          }

          this.effectParam1 = _json_.effect_param1;

          if (_json_.effect_param2 === undefined) {
            throw new Error();
          }

          this.effectParam2 = _json_.effect_param2;

          if (_json_.max_stack_num === undefined) {
            throw new Error();
          }

          this.maxStackNum = _json_.max_stack_num;
        }

        resolve(tables) {}

      });
      /**
       * 战机
       */


      _export("ResPlane", ResPlane = class ResPlane {
        constructor(_json_) {
          this.id = void 0;
          this.starLevel = void 0;
          this.name = void 0;
          this.description = void 0;
          this.quality = void 0;
          this.properties = void 0;
          this.effects = void 0;
          this.materials = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.star_level === undefined) {
            throw new Error();
          }

          this.starLevel = _json_.star_level;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.description === undefined) {
            throw new Error();
          }

          this.description = _json_.description;

          if (_json_.quality === undefined) {
            throw new Error();
          }

          this.quality = _json_.quality;

          if (_json_.properties === undefined) {
            throw new Error();
          }

          {
            this.properties = [];

            for (let _ele0 of _json_.properties) {
              let _e0;

              _e0 = new PlaneProperty(_ele0);
              this.properties.push(_e0);
            }
          }

          if (_json_.effects === undefined) {
            throw new Error();
          }

          {
            this.effects = [];

            for (let _ele0 of _json_.effects) {
              let _e0;

              _e0 = new PlaneEffect(_ele0);
              this.effects.push(_e0);
            }
          }

          if (_json_.materials === undefined) {
            throw new Error();
          }

          {
            this.materials = [];

            for (let _ele0 of _json_.materials) {
              let _e0;

              _e0 = new PlaneMaterial(_ele0);
              this.materials.push(_e0);
            }
          }
        }

        resolve(tables) {
          for (let _e of this.properties) {
            _e == null || _e.resolve(tables);
          }

          for (let _e of this.effects) {
            _e == null || _e.resolve(tables);
          }

          for (let _e of this.materials) {
            _e == null || _e.resolve(tables);
          }
        }

      });
      /**
       * 卡牌
       */


      _export("ResWeapon", ResWeapon = class ResWeapon {
        constructor(_json_) {
          this.id = void 0;
          this.name = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;
        }

        resolve(tables) {}

      });

      _export("ResWhiteList", ResWhiteList = class ResWhiteList {
        constructor(_json_) {
          /**
           * 第一行默认是主键
           */
          this.openid = void 0;

          /**
           * 密码的MD5
           */
          this.password = void 0;

          /**
           * account  status: normal/disable
           */
          this.status = void 0;

          /**
           * 可以访问的内容
           */
          this.privilege = void 0;

          if (_json_.openid === undefined) {
            throw new Error();
          }

          this.openid = _json_.openid;

          if (_json_.password === undefined) {
            throw new Error();
          }

          this.password = _json_.password;

          if (_json_.status === undefined) {
            throw new Error();
          }

          this.status = _json_.status;

          if (_json_.privilege === undefined) {
            throw new Error();
          }

          this.privilege = _json_.privilege;
        }

        resolve(tables) {}

      });

      _export("skill", skill = class skill {
        constructor(_json_) {
          /**
           * ID
           */
          this.id = void 0;

          /**
           * 技能名称
           */
          this.name = void 0;

          /**
           * 描述
           */
          this.desc = void 0;

          /**
           * 技能图标prefab
           */
          this.icon = void 0;

          /**
           * 冷却时间
           */
          this.cd = void 0;

          /**
           * 费用ID
           */
          this.CostID = void 0;

          /**
           * 费用消耗值
           */
          this.CostNum = void 0;

          /**
           * 条件
           */
          this.conditionID = void 0;
          this.ApplyBuffs = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.name === undefined) {
            throw new Error();
          }

          this.name = _json_.name;

          if (_json_.desc === undefined) {
            throw new Error();
          }

          this.desc = _json_.desc;

          if (_json_.icon === undefined) {
            throw new Error();
          }

          this.icon = _json_.icon;

          if (_json_.cd === undefined) {
            throw new Error();
          }

          this.cd = _json_.cd;

          if (_json_.CostID === undefined) {
            throw new Error();
          }

          this.CostID = _json_.CostID;

          if (_json_.CostNum === undefined) {
            throw new Error();
          }

          this.CostNum = _json_.CostNum;

          if (_json_.conditionID === undefined) {
            throw new Error();
          }

          this.conditionID = _json_.conditionID;

          if (_json_.ApplyBuffs === undefined) {
            throw new Error();
          }

          {
            this.ApplyBuffs = [];

            for (let _ele0 of _json_.ApplyBuffs) {
              let _e0;

              _e0 = new builtin.ApplyBuff(_ele0);
              this.ApplyBuffs.push(_e0);
            }
          }
        }

        resolve(tables) {}

      });

      _export("Stage", Stage = class Stage {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * 关卡
           */
          this.mainStage = void 0;

          /**
           * 阶段
           */
          this.subStage = void 0;

          /**
           * 类型0:普通敌机 100：boss
           */
          this.type = void 0;

          /**
           * 波次id
           */
          this.enemyGroupID = void 0;

          /**
           * 延迟时间
           */
          this.delay = void 0;

          /**
           * 属性倍率（血量，攻击，碰撞攻击）
           */
          this.enemyNorRate = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.mainStage === undefined) {
            throw new Error();
          }

          this.mainStage = _json_.mainStage;

          if (_json_.subStage === undefined) {
            throw new Error();
          }

          this.subStage = _json_.subStage;

          if (_json_.type === undefined) {
            throw new Error();
          }

          this.type = _json_.type;

          if (_json_.enemyGroupID === undefined) {
            throw new Error();
          }

          this.enemyGroupID = _json_.enemyGroupID;

          if (_json_.delay === undefined) {
            throw new Error();
          }

          this.delay = _json_.delay;

          if (_json_.enemyNorRate === undefined) {
            throw new Error();
          }

          this.enemyNorRate = _json_.enemyNorRate;
        }

        resolve(tables) {}

      });

      _export("Task", Task = class Task {
        constructor(_json_) {
          /**
           * 任务 ID
           */
          this.taskId = void 0;

          /**
           * 任务集 ID
           */
          this.groupId = void 0;

          /**
           * 任务类型
           */
          this.taskClass = void 0;

          /**
           * 前置任务 ID
           */
          this.prevId = void 0;

          /**
           * 重置周期
           */
          this.periodType = void 0;

          /**
           * 任务接取条件
           */
          this.openType = void 0;

          /**
           * 条件参数
           */
          this.openValue = void 0;

          /**
           * 目标类型
           */
          this.goalType = void 0;

          /**
           * 目标参数1 
           */
          this.goalParams = void 0;

          /**
           * 是否累积
           */
          this.accumulate = void 0;

          /**
           * 奖励ID
           */
          this.awardId = void 0;

          /**
           * 奖励轨道集ID
           */
          this.orbitId = void 0;

          /**
           * 完成奖励值
           */
          this.orbitValue = void 0;

          /**
           * 开放日期(yyyymmdd)
           */
          this.openDate = void 0;

          /**
           * 开放时间(HHMMSS)
           */
          this.openTime = void 0;

          /**
           * 结束日期(yyyymmdd)
           */
          this.closeDate = void 0;

          /**
           * 结束时间(HHMMSS)
           */
          this.closeTime = void 0;

          if (_json_.task_id === undefined) {
            throw new Error();
          }

          this.taskId = _json_.task_id;

          if (_json_.group_id === undefined) {
            throw new Error();
          }

          this.groupId = _json_.group_id;

          if (_json_.task_class === undefined) {
            throw new Error();
          }

          this.taskClass = _json_.task_class;

          if (_json_.prev_id === undefined) {
            throw new Error();
          }

          this.prevId = _json_.prev_id;

          if (_json_.period_type === undefined) {
            throw new Error();
          }

          this.periodType = _json_.period_type;

          if (_json_.open_type === undefined) {
            throw new Error();
          }

          this.openType = _json_.open_type;

          if (_json_.open_value === undefined) {
            throw new Error();
          }

          this.openValue = _json_.open_value;

          if (_json_.goal_type === undefined) {
            throw new Error();
          }

          this.goalType = _json_.goal_type;

          if (_json_.goal_params === undefined) {
            throw new Error();
          }

          {
            this.goalParams = [];

            for (let _ele0 of _json_.goal_params) {
              let _e0;

              _e0 = _ele0;
              this.goalParams.push(_e0);
            }
          }

          if (_json_.accumulate === undefined) {
            throw new Error();
          }

          this.accumulate = _json_.accumulate;

          if (_json_.award_id === undefined) {
            throw new Error();
          }

          this.awardId = _json_.award_id;

          if (_json_.orbit_id === undefined) {
            throw new Error();
          }

          this.orbitId = _json_.orbit_id;

          if (_json_.orbit_value === undefined) {
            throw new Error();
          }

          this.orbitValue = _json_.orbit_value;

          if (_json_.open_date === undefined) {
            throw new Error();
          }

          this.openDate = _json_.open_date;

          if (_json_.open_time === undefined) {
            throw new Error();
          }

          this.openTime = _json_.open_time;

          if (_json_.close_date === undefined) {
            throw new Error();
          }

          this.closeDate = _json_.close_date;

          if (_json_.close_time === undefined) {
            throw new Error();
          }

          this.closeTime = _json_.close_time;
        }

        resolve(tables) {}

      });

      _export("Track", Track = class Track {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * 类型
           */
          this.tpe = void 0;

          /**
           * 值(不同的轨迹类型，数据代表的信息不一样)
           */
          this.value = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.tpe === undefined) {
            throw new Error();
          }

          this.tpe = _json_.tpe;

          if (_json_.value === undefined) {
            throw new Error();
          }

          this.value = _json_.value;
        }

        resolve(tables) {}

      });

      _export("Unit", Unit = class Unit {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * uId
           */
          this.uId = void 0;

          /**
           * im
           */
          this.im = void 0;

          /**
           * imp
           */
          this.imp = void 0;

          /**
           * am
           */
          this.am = void 0;

          /**
           * dam
           */
          this.dam = void 0;

          /**
           * hp
           */
          this.hp = void 0;

          /**
           * pos
           */
          this.pos = void 0;

          /**
           * hpp
           */
          this.hpp = void 0;

          /**
           * col
           */
          this.col = void 0;

          /**
           * sco
           */
          this.sco = void 0;

          /**
           * hc
           */
          this.hc = void 0;

          /**
           * hs
           */
          this.hs = void 0;

          /**
           * bla
           */
          this.bla = void 0;

          /**
           * so
           */
          this.so = void 0;

          /**
           * sk
           */
          this.sk = void 0;

          /**
           * act
           */
          this.act = void 0;

          /**
           * mix
           */
          this.mix = void 0;

          /**
           * turn
           */
          this.turn = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.uId === undefined) {
            throw new Error();
          }

          this.uId = _json_.uId;

          if (_json_.im === undefined) {
            throw new Error();
          }

          this.im = _json_.im;

          if (_json_.imp === undefined) {
            throw new Error();
          }

          this.imp = _json_.imp;

          if (_json_.am === undefined) {
            throw new Error();
          }

          this.am = _json_.am;

          if (_json_.dam === undefined) {
            throw new Error();
          }

          this.dam = _json_.dam;

          if (_json_.hp === undefined) {
            throw new Error();
          }

          this.hp = _json_.hp;

          if (_json_.pos === undefined) {
            throw new Error();
          }

          this.pos = _json_.pos;

          if (_json_.hpp === undefined) {
            throw new Error();
          }

          this.hpp = _json_.hpp;

          if (_json_.col === undefined) {
            throw new Error();
          }

          this.col = _json_.col;

          if (_json_.sco === undefined) {
            throw new Error();
          }

          this.sco = _json_.sco;

          if (_json_.hc === undefined) {
            throw new Error();
          }

          this.hc = _json_.hc;

          if (_json_.hs === undefined) {
            throw new Error();
          }

          this.hs = _json_.hs;

          if (_json_.bla === undefined) {
            throw new Error();
          }

          this.bla = _json_.bla;

          if (_json_.so === undefined) {
            throw new Error();
          }

          this.so = _json_.so;

          if (_json_.sk === undefined) {
            throw new Error();
          }

          this.sk = _json_.sk;

          if (_json_.act === undefined) {
            throw new Error();
          }

          this.act = _json_.act;

          if (_json_.mix === undefined) {
            throw new Error();
          }

          this.mix = _json_.mix;

          if (_json_.turn === undefined) {
            throw new Error();
          }

          this.turn = _json_.turn;
        }

        resolve(tables) {}

      });

      _export("Wave", Wave = class Wave {
        constructor(_json_) {
          /**
           * id
           */
          this.id = void 0;

          /**
           * 波次 ID
           */
          this.enemyGroupID = void 0;

          /**
           * 延迟时间
           */
          this.delay = void 0;

          /**
           * 0 表示普通敌机
           */
          this.planeType = void 0;

          /**
           * 敌机id
           */
          this.planeId = void 0;

          /**
           * 生成间隔时间
           */
          this.interval = void 0;

          /**
           * 根据敌机数量设置偏移位置
           */
          this.offsetPos = void 0;

          /**
           * 生成的敌机数量
           */
          this.num = void 0;

          /**
           * 初始位置
           */
          this.pos = void 0;

          /**
           * 轨迹路径(次数,轨迹索引,;id,速度,间隔；)
           */
          this.track = void 0;

          /**
           * 轨迹参数
           */
          this.trackParams = void 0;

          /**
           * 旋转速度
           */
          this.rotatioSpeed = void 0;

          /**
           * 首次射击延迟
           */
          this.FirstShootDelay = void 0;

          if (_json_.id === undefined) {
            throw new Error();
          }

          this.id = _json_.id;

          if (_json_.enemyGroupID === undefined) {
            throw new Error();
          }

          this.enemyGroupID = _json_.enemyGroupID;

          if (_json_.delay === undefined) {
            throw new Error();
          }

          this.delay = _json_.delay;

          if (_json_.planeType === undefined) {
            throw new Error();
          }

          this.planeType = _json_.planeType;

          if (_json_.planeId === undefined) {
            throw new Error();
          }

          this.planeId = _json_.planeId;

          if (_json_.interval === undefined) {
            throw new Error();
          }

          this.interval = _json_.interval;

          if (_json_.offsetPos === undefined) {
            throw new Error();
          }

          this.offsetPos = _json_.offsetPos;

          if (_json_.num === undefined) {
            throw new Error();
          }

          this.num = _json_.num;

          if (_json_.pos === undefined) {
            throw new Error();
          }

          this.pos = _json_.pos;

          if (_json_.track === undefined) {
            throw new Error();
          }

          this.track = _json_.track;

          if (_json_.trackParams === undefined) {
            throw new Error();
          }

          this.trackParams = _json_.trackParams;

          if (_json_.rotatioSpeed === undefined) {
            throw new Error();
          }

          this.rotatioSpeed = _json_.rotatioSpeed;

          if (_json_.FirstShootDelay === undefined) {
            throw new Error();
          }

          this.FirstShootDelay = _json_.FirstShootDelay;
        }

        resolve(tables) {}

      });
      /**
       * GM命令表
       */


      _export("TbGM", TbGM = class TbGM {
        constructor(_json_) {
          this._dataList = void 0;
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new ResGM(_json2_);

            this._dataList.push(_v);
          }
        }

        getDataList() {
          return this._dataList;
        }

        get(index) {
          return this._dataList[index];
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });
      /**
       * 战机表
       */


      _export("TbPlane", TbPlane = class TbPlane {
        constructor(_json_) {
          this._dataList = void 0;
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new ResPlane(_json2_);

            this._dataList.push(_v);
          }
        }

        getDataList() {
          return this._dataList;
        }

        get(index) {
          return this._dataList[index];
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });
      /**
       * 效果表
       */


      _export("TbEffect", TbEffect = class TbEffect {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new ResEffect(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });
      /**
       * 武器
       */


      _export("TbWeapon", TbWeapon = class TbWeapon {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new ResWeapon(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });
      /**
       * 装备位升级
       */


      _export("TbEquipUpgrade", TbEquipUpgrade = class TbEquipUpgrade {
        constructor(_json_) {
          this._dataList = void 0;
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new ResEquipUpgrade(_json2_);

            this._dataList.push(_v);
          }
        }

        getDataList() {
          return this._dataList;
        }

        get(index) {
          return this._dataList[index];
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });
      /**
       * 装备表
       */


      _export("TbEquip", TbEquip = class TbEquip {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new ResEquip(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });
      /**
       * 道具表
       */


      _export("TbItem", TbItem = class TbItem {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new ResItem(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbGlobalAttr", TbGlobalAttr = class TbGlobalAttr {
        constructor(_json_) {
          this._data = void 0;
          if (_json_.length != 1) throw new Error('table mode=one, but size != 1');
          this._data = new GlobalAttr(_json_[0]);
        }

        getData() {
          return this._data;
        }
        /**
         * 每回合发放的金币
         */


        get GoldProducion() {
          return this._data.GoldProducion;
        }
        /**
         * 体力上限值
         */


        get MaxEnergy() {
          return this._data.MaxEnergy;
        }
        /**
         * 体力恢复的间隔时间
         */


        get EnergyRecoverInterval() {
          return this._data.EnergyRecoverInterval;
        }
        /**
         * 体力恢复的值
         */


        get EnergyRecoverValue() {
          return this._data.EnergyRecoverValue;
        }
        /**
         * 局内道具拾取距离
         */


        get ItemPickUpRadius() {
          return this._data.ItemPickUpRadius;
        }
        /**
         * 受击保护
         */


        get PostHitProtection() {
          return this._data.PostHitProtection;
        }

        resolve(tables) {
          this._data.resolve(tables);
        }

      });

      _export("TbBoss", TbBoss = class TbBoss {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new Boss(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("Tbbuffer", Tbbuffer = class Tbbuffer {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new buffer(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbBullet", TbBullet = class TbBullet {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new Bullet(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbChapter", TbChapter = class TbChapter {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new Chapter(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbEnemy", TbEnemy = class TbEnemy {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new Enemy(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbEnemyUI", TbEnemyUI = class TbEnemyUI {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new EnemyUI(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbGameMap", TbGameMap = class TbGameMap {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new GameMap(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbGameMode", TbGameMode = class TbGameMode {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new GameMode(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.ID, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbLevel", TbLevel = class TbLevel {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new Level(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbLevelGroup", TbLevelGroup = class TbLevelGroup {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new LevelGroup(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbMainPlane", TbMainPlane = class TbMainPlane {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new MainPlane(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbMainPlaneLv", TbMainPlaneLv = class TbMainPlaneLv {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new MainPlaneLv(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("Tbskill", Tbskill = class Tbskill {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new skill(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbStage", TbStage = class TbStage {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new Stage(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbTask", TbTask = class TbTask {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new Task(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.taskId, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbTrack", TbTrack = class TbTrack {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new Track(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbUnit", TbUnit = class TbUnit {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new Unit(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("TbWave", TbWave = class TbWave {
        constructor(_json_) {
          this._dataMap = void 0;
          this._dataList = void 0;
          this._dataMap = new Map();
          this._dataList = [];

          for (var _json2_ of _json_) {
            let _v;

            _v = new Wave(_json2_);

            this._dataList.push(_v);

            this._dataMap.set(_v.id, _v);
          }
        }

        getDataMap() {
          return this._dataMap;
        }

        getDataList() {
          return this._dataList;
        }

        get(key) {
          return this._dataMap.get(key);
        }

        resolve(tables) {
          for (let data of this._dataList) {
            data.resolve(tables);
          }
        }

      });

      _export("Tables", Tables = class Tables {
        /**
         * GM命令表
         */
        get TbGM() {
          return this._TbGM;
        }

        /**
         * 战机表
         */
        get TbPlane() {
          return this._TbPlane;
        }

        /**
         * 效果表
         */
        get TbEffect() {
          return this._TbEffect;
        }

        /**
         * 武器
         */
        get TbWeapon() {
          return this._TbWeapon;
        }

        /**
         * 装备位升级
         */
        get TbEquipUpgrade() {
          return this._TbEquipUpgrade;
        }

        /**
         * 装备表
         */
        get TbEquip() {
          return this._TbEquip;
        }

        /**
         * 道具表
         */
        get TbItem() {
          return this._TbItem;
        }

        get TbGlobalAttr() {
          return this._TbGlobalAttr;
        }

        get TbBoss() {
          return this._TbBoss;
        }

        get Tbbuffer() {
          return this._Tbbuffer;
        }

        get TbBullet() {
          return this._TbBullet;
        }

        get TbChapter() {
          return this._TbChapter;
        }

        get TbEnemy() {
          return this._TbEnemy;
        }

        get TbEnemyUI() {
          return this._TbEnemyUI;
        }

        get TbGameMap() {
          return this._TbGameMap;
        }

        get TbGameMode() {
          return this._TbGameMode;
        }

        get TbLevel() {
          return this._TbLevel;
        }

        get TbLevelGroup() {
          return this._TbLevelGroup;
        }

        get TbMainPlane() {
          return this._TbMainPlane;
        }

        get TbMainPlaneLv() {
          return this._TbMainPlaneLv;
        }

        get Tbskill() {
          return this._Tbskill;
        }

        get TbStage() {
          return this._TbStage;
        }

        get TbTask() {
          return this._TbTask;
        }

        get TbTrack() {
          return this._TbTrack;
        }

        get TbUnit() {
          return this._TbUnit;
        }

        get TbWave() {
          return this._TbWave;
        }

        constructor(loader) {
          this._TbGM = void 0;
          this._TbPlane = void 0;
          this._TbEffect = void 0;
          this._TbWeapon = void 0;
          this._TbEquipUpgrade = void 0;
          this._TbEquip = void 0;
          this._TbItem = void 0;
          this._TbGlobalAttr = void 0;
          this._TbBoss = void 0;
          this._Tbbuffer = void 0;
          this._TbBullet = void 0;
          this._TbChapter = void 0;
          this._TbEnemy = void 0;
          this._TbEnemyUI = void 0;
          this._TbGameMap = void 0;
          this._TbGameMode = void 0;
          this._TbLevel = void 0;
          this._TbLevelGroup = void 0;
          this._TbMainPlane = void 0;
          this._TbMainPlaneLv = void 0;
          this._Tbskill = void 0;
          this._TbStage = void 0;
          this._TbTask = void 0;
          this._TbTrack = void 0;
          this._TbUnit = void 0;
          this._TbWave = void 0;
          this._TbGM = new TbGM(loader('tbgm'));
          this._TbPlane = new TbPlane(loader('tbplane'));
          this._TbEffect = new TbEffect(loader('tbeffect'));
          this._TbWeapon = new TbWeapon(loader('tbweapon'));
          this._TbEquipUpgrade = new TbEquipUpgrade(loader('tbequipupgrade'));
          this._TbEquip = new TbEquip(loader('tbequip'));
          this._TbItem = new TbItem(loader('tbitem'));
          this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'));
          this._TbBoss = new TbBoss(loader('tbboss'));
          this._Tbbuffer = new Tbbuffer(loader('tbbuffer'));
          this._TbBullet = new TbBullet(loader('tbbullet'));
          this._TbChapter = new TbChapter(loader('tbchapter'));
          this._TbEnemy = new TbEnemy(loader('tbenemy'));
          this._TbEnemyUI = new TbEnemyUI(loader('tbenemyui'));
          this._TbGameMap = new TbGameMap(loader('tbgamemap'));
          this._TbGameMode = new TbGameMode(loader('tbgamemode'));
          this._TbLevel = new TbLevel(loader('tblevel'));
          this._TbLevelGroup = new TbLevelGroup(loader('tblevelgroup'));
          this._TbMainPlane = new TbMainPlane(loader('tbmainplane'));
          this._TbMainPlaneLv = new TbMainPlaneLv(loader('tbmainplanelv'));
          this._Tbskill = new Tbskill(loader('tbskill'));
          this._TbStage = new TbStage(loader('tbstage'));
          this._TbTask = new TbTask(loader('tbtask'));
          this._TbTrack = new TbTrack(loader('tbtrack'));
          this._TbUnit = new TbUnit(loader('tbunit'));
          this._TbWave = new TbWave(loader('tbwave'));

          this._TbGM.resolve(this);

          this._TbPlane.resolve(this);

          this._TbEffect.resolve(this);

          this._TbWeapon.resolve(this);

          this._TbEquipUpgrade.resolve(this);

          this._TbEquip.resolve(this);

          this._TbItem.resolve(this);

          this._TbGlobalAttr.resolve(this);

          this._TbBoss.resolve(this);

          this._Tbbuffer.resolve(this);

          this._TbBullet.resolve(this);

          this._TbChapter.resolve(this);

          this._TbEnemy.resolve(this);

          this._TbEnemyUI.resolve(this);

          this._TbGameMap.resolve(this);

          this._TbGameMode.resolve(this);

          this._TbLevel.resolve(this);

          this._TbLevelGroup.resolve(this);

          this._TbMainPlane.resolve(this);

          this._TbMainPlaneLv.resolve(this);

          this._Tbskill.resolve(this);

          this._TbStage.resolve(this);

          this._TbTask.resolve(this);

          this._TbTrack.resolve(this);

          this._TbUnit.resolve(this);

          this._TbWave.resolve(this);
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1afdb977ae03ab577533081b369d65c5f421dc5a.js.map