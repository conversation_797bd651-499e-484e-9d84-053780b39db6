{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts"], "names": ["MainPlaneManager", "Prefab", "instantiate", "SingletonBase", "GameIns", "MyApp", "BattleLayer", "GameResourceList", "MainPlane", "AttributeConst", "MainPlaneData", "_planeData", "mainPlane", "planeFightData", "hurtTotal", "moveAble", "value", "setMoveAble", "setPlaneData", "planeData", "preload", "battleManager", "addLoadCount", "createMainPlane", "checkLoadFinish", "reset", "prefab", "resMgr", "loadAsync", "planeNode", "getComponent", "me", "addMainPlane", "node", "active", "initPlane", "mainReset", "destroy", "die", "battleQuit", "refreshPlaneData", "hp", "getFinalAttributeByKey", "MaxHP", "maxhp", "idToType", "id", "Math", "floor"], "mappings": ";;;4MAcaA,gB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATJC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,W,OAAAA,W;;AAJdC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACFC,MAAAA,W;;AAEAC,MAAAA,gB;;AAEEC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,a,iBAAAA,a;;;;;;;;;kCAIIV,gB,GAAN,MAAMA,gBAAN;AAAA;AAAA,0CAA+D;AAAA;AAAA;AAAA,eAElEW,UAFkE,GAEpC,IAFoC;AAE/B;AAF+B,eAGlEC,SAHkE,GAGrC,IAHqC;AAGhC;AAHgC,eAKlEC,cALkE,GAKlC;AAAA;AAAA,+CALkC;AAKd;AALc,eAMlEC,SANkE,GAM9C,CAN8C;AAAA;;AAStD,YAARC,QAAQ,CAACC,KAAD,EAAiB;AACzB,cAAI,KAAKJ,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeK,WAAf,CAA2BD,KAA3B;AACH;AACJ;;AAEDE,QAAAA,YAAY,CAACC,SAAD,EAAsB;AAC9B,eAAKR,UAAL,GAAkBQ,SAAlB;AACH;;AAEKC,QAAAA,OAAO,GAAG;AAAA;;AAAA;AACZ;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA,kBAAM,KAAI,CAACC,eAAL,EAAN;AACA;AAAA;AAAA,oCAAQF,aAAR,CAAsBG,eAAtB;;AACA,YAAA,KAAI,CAACC,KAAL;AAJY;AAKf;AAED;AACJ;AACA;AACA;AACA;;;AACUF,QAAAA,eAAe,GAA+B;AAAA;;AAAA;AAAA;;AAChD,gBAAI,MAAI,CAACX,SAAT,EAAoB;AAChB,qBAAO,MAAI,CAACA,SAAZ;AACH;;AAED,gBAAMc,MAAM,SAAS;AAAA;AAAA,gCAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,sDAAiBpB,SAAxC,EAAmDP,MAAnD,CAArB;AACA,gBAAI4B,SAAS,GAAG3B,WAAW,CAACwB,MAAD,CAA3B;AACA,YAAA,MAAI,CAACd,SAAL,GAAiBiB,SAAS,CAACC,YAAV;AAAA;AAAA,uCAAjB;AACA;AAAA;AAAA,4CAAYC,EAAZ,iBAAgBC,YAAhB;AACA,YAAA,MAAI,CAACpB,SAAL,CAAgBqB,IAAhB,CAAqBC,MAArB,GAA8B,KAA9B;AACA,gCAAA,MAAI,CAACtB,SAAL,8BAAgBuB,SAAhB,CAA0B,MAAI,CAACxB,UAA/B;AACA,mBAAO,MAAI,CAACC,SAAZ;AAXgD;AAYnD;;AAEDwB,QAAAA,SAAS,GAAG;AACR,cAAI,KAAKxB,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAeqB,IAAf,CAAoBI,OAApB;AACA,iBAAKzB,SAAL,GAAiB,IAAjB;AACH;AACJ;;AAEDa,QAAAA,KAAK,GAAG;AACJ,eAAKZ,cAAL,CAAoByB,GAApB,GAA0B,KAA1B;;AAEA,cAAI,KAAK1B,SAAT,EAAoB;AAChB,iBAAKA,SAAL,CAAe2B,UAAf;AACH;;AAED,eAAKC,gBAAL;AACH;;AAEDA,QAAAA,gBAAgB,GAAS;AAAA;;AACrB,eAAK3B,cAAL,CAAoB4B,EAApB,uBAAyB,KAAK9B,UAA9B,qBAAyB,iBAAiB+B,sBAAjB,CAAwC;AAAA;AAAA,gDAAeC,KAAvD,CAAzB;AACA,eAAK9B,cAAL,CAAoB+B,KAApB,GAA4B,KAAK/B,cAAL,CAAoB4B,EAAhD;AACH;;AAEDI,QAAAA,QAAQ,CAACC,EAAD,EAAqB;AACzB,iBAAOC,IAAI,CAACC,KAAL,CAAWF,EAAE,GAAG,GAAhB,CAAP;AACH;;AArEiE,O", "sourcesContent": ["\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { MyApp } from \"../../MyApp\";\r\nimport BattleLayer from \"../ui/layer/BattleLayer\";\r\nimport { Prefab, Node, instantiate } from \"cc\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport { PlaneData } from \"db://assets/bundles/common/script/data/plane/PlaneData\";\r\nimport { MainPlane } from \"../ui/plane/mainPlane/MainPlane\";\r\nimport { AttributeConst } from \"db://assets/bundles/common/script/const/AttributeConst\";\r\nimport { MainPlaneData } from \"../data/MainPlaneFightData\";\r\n\r\n\r\n\r\nexport class MainPlaneManager extends SingletonBase<MainPlaneManager> {\r\n\r\n    _planeData:PlaneData | null = null;//飞机数据\r\n    mainPlane:MainPlane | null = null;//飞机战斗UI\r\n\r\n    planeFightData: MainPlaneData = new MainPlaneData();//飞机战斗数据\r\n    hurtTotal: number = 0;\r\n\r\n\r\n    set moveAble(value: boolean) {\r\n        if (this.mainPlane) {\r\n            this.mainPlane.setMoveAble(value);\r\n        }\r\n    }\r\n\r\n    setPlaneData(planeData:PlaneData) {\r\n        this._planeData = planeData;\r\n    }\r\n\r\n    async preload() {\r\n        GameIns.battleManager.addLoadCount(1);\r\n        await this.createMainPlane();\r\n        GameIns.battleManager.checkLoadFinish();\r\n        this.reset();\r\n    }\r\n\r\n    /**\r\n     * 创建主飞机\r\n     * @param isTrans 是否为特殊状态\r\n     * @returns 主飞机对象\r\n     */\r\n    async createMainPlane(): Promise<MainPlane | null>  {\r\n        if (this.mainPlane) {\r\n            return this.mainPlane;\r\n        }\r\n\r\n        const prefab = await MyApp.resMgr.loadAsync(GameResourceList.MainPlane, Prefab);\r\n        let planeNode = instantiate(prefab);\r\n        this.mainPlane = planeNode.getComponent(MainPlane)\r\n        BattleLayer.me?.addMainPlane();\r\n        this.mainPlane!.node.active = false;\r\n        this.mainPlane?.initPlane(this._planeData!);\r\n        return this.mainPlane;\r\n    }\r\n\r\n    mainReset() {\r\n        if (this.mainPlane) {\r\n            this.mainPlane.node.destroy();\r\n            this.mainPlane = null;\r\n        }\r\n    }\r\n\r\n    reset() {\r\n        this.planeFightData.die = false;\r\n\r\n        if (this.mainPlane) {\r\n            this.mainPlane.battleQuit();\r\n        }\r\n\r\n        this.refreshPlaneData();\r\n    }\r\n\r\n    refreshPlaneData(): void {\r\n        this.planeFightData.hp = this._planeData?.getFinalAttributeByKey(AttributeConst.MaxHP)!;\r\n        this.planeFightData.maxhp = this.planeFightData.hp;\r\n    }\r\n\r\n    idToType(id: number): number {\r\n        return Math.floor(id / 100);\r\n    }\r\n}\r\n"]}