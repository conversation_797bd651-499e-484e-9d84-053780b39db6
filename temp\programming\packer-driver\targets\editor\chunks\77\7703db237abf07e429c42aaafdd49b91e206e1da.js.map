{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts"], "names": ["_decorator", "Component", "sp", "<PERSON><PERSON>", "LevelEditorUtils", "ccclass", "property", "executeInEditMode", "PlaneView", "type", "SkeletonData", "displayName", "tooltip", "_animSpeed", "spine", "currentAnimIndex", "cycle", "animSpeed", "value", "timeScale", "onLoad", "initSpine", "start", "playNode", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON>y<PERSON><PERSON>", "getComponentsInChildren", "for<PERSON>ach", "button", "on", "EventType", "CLICK", "playAnimation", "name", "getOrAddComp", "getOrAddNode", "Skeleton", "skeletonData", "setCompleteListener", "trackEntry", "console", "log", "animation", "anim<PERSON><PERSON>", "warn", "setAnimation", "error", "stopAnimation", "clearTracks", "pauseResumeAnimation", "paused", "setAnimationCycle", "get<PERSON>urrent", "downAnimationSpeed", "Math", "max", "upAnimationSpeed", "min", "getAvailableAnimations", "animations", "getRuntimeData", "i", "length", "push"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAiBC,MAAAA,E,OAAAA,E;AAAkCC,MAAAA,M,OAAAA,M;;AAC/DC,MAAAA,gB,iBAAAA,gB;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CP,U;;2BAIpCQ,S,WAFZH,OAAO,CAAC,WAAD,C,UACPE,iBAAiB,CAAC,IAAD,C,UAIbD,QAAQ,CAAC;AACNG,QAAAA,IAAI,EAAEP,EAAE,CAACQ,YADH;AAENC,QAAAA,WAAW,EAAE,WAFP;AAGNC,QAAAA,OAAO,EAAE;AAHH,OAAD,C,0CALb,MAEaJ,SAFb,SAE+BP,SAF/B,CAEyC;AAAA;AAAA;;AAErC;AAFqC;;AAAA,eAU7BY,UAV6B,GAUR,GAVQ;AAkBrC;AAlBqC,eAmB7BC,KAnB6B,GAmBD,IAnBC;AAAA,eAoB7BC,gBApB6B,GAoBF,CApBE;AAAA,eAqB7BC,KArB6B,GAqBZ,IArBY;AAAA;;AAWhB,YAATC,SAAS,CAACC,KAAD,EAAgB;AACjC,eAAKL,UAAL,GAAkBK,KAAlB;;AACA,cAAI,KAAKJ,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWK,SAAX,GAAuBD,KAAvB;AACH;AACJ;;AAODE,QAAAA,MAAM,GAAG;AACL,eAAKC,SAAL;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ,cAAIC,QAAQ,GAAG,KAAKC,IAAL,CAAUC,cAAV,CAAyB,SAAzB,CAAf;AACAF,UAAAA,QAAQ,CAAEG,uBAAV,CAAkCvB,MAAlC,EAA0CwB,OAA1C,CAAkDC,MAAM,IAAI;AACxDA,YAAAA,MAAM,CAACJ,IAAP,CAAYK,EAAZ,CAAe1B,MAAM,CAAC2B,SAAP,CAAiBC,KAAhC,EAAuC,MAAI;AACvC,mBAAKC,aAAL,CAAmBJ,MAAM,CAACJ,IAAP,CAAYS,IAA/B;AACH,aAFD;AAGH,WAJD;AAKH;AAED;AACJ;AACA;;;AACYZ,QAAAA,SAAS,GAAG;AAChB;AACA,eAAKP,KAAL,GAAa;AAAA;AAAA,oDAAiBoB,YAAjB,CAA8B;AAAA;AAAA,oDAAiBC,YAAjB,CAA8B,KAAKX,IAAnC,EAAyC,OAAzC,CAA9B,EAAiFtB,EAAE,CAACkC,QAApF,CAAb,CAFgB,CAIhB;;AACA,cAAI,KAAKC,YAAT,EAAuB;AACnB,iBAAKvB,KAAL,CAAWuB,YAAX,GAA0B,KAAKA,YAA/B;AACH,WAPe,CAShB;;;AACA,eAAKvB,KAAL,CAAWK,SAAX,GAAuB,KAAKN,UAA5B,CAVgB,CAYhB;;AACA,eAAKC,KAAL,CAAWwB,mBAAX,CAAgCC,UAAD,IAAgB;AAAA;;AAC3CC,YAAAA,OAAO,CAACC,GAAR,CAAa,WAAD,yBAAWF,UAAU,CAACG,SAAtB,qBAAW,sBAAsBT,IAAK,EAAlD;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,aAAa,CAACW,QAAD,EAAmB;AAC5B,cAAI,CAAC,KAAK7B,KAAV,EAAiB;AACb0B,YAAAA,OAAO,CAACI,IAAR,CAAa,aAAb;AACA;AACH;;AAED,cAAI,CAACD,QAAL,EAAe;AACXH,YAAAA,OAAO,CAACI,IAAR,CAAa,QAAb;AACA;AACH;;AAED,cAAID,QAAQ,IAAI,OAAhB,EAAyB;AACrBA,YAAAA,QAAQ,GAAG,WAAX;AACH,WAFD,MAEO,IAAIA,QAAQ,IAAI,UAAhB,EAA4B;AAC/BA,YAAAA,QAAQ,GAAG,YAAX;AACH;;AAED,eAAK9B,UAAL,GAAkB,GAAlB;;AACA,cAAI;AACA,iBAAKC,KAAL,CAAW+B,YAAX,CAAwB,CAAxB,EAA2BF,QAA3B,EAAqC,KAAK3B,KAA1C;AACAwB,YAAAA,OAAO,CAACC,GAAR,CAAa,SAAQE,QAAS,SAAQ,KAAK3B,KAAM,EAAjD;AACH,WAHD,CAGE,OAAO8B,KAAP,EAAc;AACZN,YAAAA,OAAO,CAACM,KAAR,CAAe,WAAUH,QAAS,EAAlC,EAAqCG,KAArC;AACH;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAKjC,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWkC,WAAX;AACAR,YAAAA,OAAO,CAACC,GAAR,CAAY,MAAZ;AACH;AACJ;AAED;AACJ;AACA;;;AACIQ,QAAAA,oBAAoB,GAAG;AACnB,cAAI,KAAKnC,KAAT,EAAgB;AACZ,iBAAKA,KAAL,CAAWoC,MAAX,GAAoB,CAAC,KAAKpC,KAAL,CAAWoC,MAAhC;AACAV,YAAAA,OAAO,CAACC,GAAR,CAAY,KAAK3B,KAAL,CAAWoC,MAAX,GAAoB,MAApB,GAA6B,MAAzC;AACH;AACJ;;AAEDC,QAAAA,iBAAiB,GAAG;AAChB,eAAKnC,KAAL,GAAa,CAAC,KAAKA,KAAnB;;AACA,cAAI,KAAKF,KAAT,EAAgB;AAAA;;AACZ,iBAAKA,KAAL,CAAW+B,YAAX,CAAwB,CAAxB,EAA2B,+BAAK/B,KAAL,CAAWsC,UAAX,CAAsB,CAAtB,6DAA0BV,SAA1B,2CAAqCT,IAArC,KAA6C,EAAxE,EAA4E,KAAKjB,KAAjF;AACH;AACJ;;AAEDqC,QAAAA,kBAAkB,GAAG;AACjB,eAAKpC,SAAL,GAAiBqC,IAAI,CAACC,GAAL,CAAS,KAAK1C,UAAL,GAAkB,CAA3B,EAA8B,KAA9B,CAAjB;AACH;;AACD2C,QAAAA,gBAAgB,GAAG;AACf,eAAKvC,SAAL,GAAiBqC,IAAI,CAACG,GAAL,CAAS,KAAK5C,UAAL,GAAkB,CAA3B,EAA8B,CAA9B,CAAjB;AACH;AAED;AACJ;AACA;;;AACI6C,QAAAA,sBAAsB,GAAa;AAC/B,cAAI,CAAC,KAAK5C,KAAN,IAAe,CAAC,KAAKA,KAAL,CAAWuB,YAA/B,EAA6C;AACzC,mBAAO,EAAP;AACH;;AAED,gBAAMsB,UAAoB,GAAG,EAA7B;AACA,gBAAMtB,YAAY,GAAG,KAAKvB,KAAL,CAAWuB,YAAX,CAAwBuB,cAAxB,EAArB;;AACA,cAAIvB,YAAY,IAAIA,YAAY,CAACsB,UAAjC,EAA6C;AACzC,iBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGxB,YAAY,CAACsB,UAAb,CAAwBG,MAA5C,EAAoDD,CAAC,EAArD,EAAyD;AACrDF,cAAAA,UAAU,CAACI,IAAX,CAAgB1B,YAAY,CAACsB,UAAb,CAAwBE,CAAxB,EAA2B5B,IAA3C;AACH;AACJ;;AACD,iBAAO0B,UAAP;AACH;;AA1IoC,O;;;;;iBAQE,I", "sourcesContent": ["import { _decorator, Component, Node, sp, CCString, CCFloat, CCBoolean, Button } from 'cc';\r\nimport { LevelEditorUtils } from '../level/utils';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('PlaneView')\r\n@executeInEditMode(true)\r\nexport class PlaneView extends Component {\r\n\r\n    // ========== Spine相关属性 ==========\r\n    @property({\r\n        type: sp.SkeletonData,\r\n        displayName: \"Spine骨骼数据\",\r\n        tooltip: \"拖拽Spine骨骼数据到这里\"\r\n    })\r\n    skeletonData: sp.SkeletonData | null = null;\r\n\r\n    private _animSpeed: number = 1.0;\r\n    private set animSpeed(value: number) {\r\n        this._animSpeed = value;\r\n        if (this.spine) {\r\n            this.spine.timeScale = value;\r\n        }\r\n    }\r\n\r\n    // ========== 私有属性 ==========\r\n    private spine: sp.Skeleton | null = null;\r\n    private currentAnimIndex: number = 0;\r\n    private cycle: boolean = true;\r\n\r\n    onLoad() {\r\n        this.initSpine();\r\n    }\r\n\r\n    start() {\r\n        let playNode = this.node.getChildByPath(\"ui/play\")\r\n        playNode!.getComponentsInChildren(Button).forEach(button => {\r\n            button.node.on(Button.EventType.CLICK, ()=>{\r\n                this.playAnimation(button.node.name)\r\n            });\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 初始化Spine组件\r\n     */\r\n    private initSpine() {\r\n        // 获取或创建Spine组件\r\n        this.spine = LevelEditorUtils.getOrAddComp(LevelEditorUtils.getOrAddNode(this.node, \"plane\"), sp.Skeleton);\r\n\r\n        // 设置骨骼数据\r\n        if (this.skeletonData) {\r\n            this.spine.skeletonData = this.skeletonData;\r\n        }\r\n\r\n        // 设置播放速度\r\n        this.spine.timeScale = this._animSpeed;\r\n\r\n        // 设置动画完成监听\r\n        this.spine.setCompleteListener((trackEntry) => {\r\n            console.log(`动画播放完成: ${trackEntry.animation?.name}`);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 播放指定动画\r\n     * @param animName 动画名称\r\n     * @param loop 是否循环\r\n     */\r\n    playAnimation(animName: string) {\r\n        if (!this.spine) {\r\n            console.warn(\"Spine组件未初始化\");\r\n            return;\r\n        }\r\n\r\n        if (!animName) {\r\n            console.warn(\"动画名称为空\");\r\n            return;\r\n        }\r\n\r\n        if (animName == 'Enter') {\r\n            animName = 'animation'\r\n        } else if (animName == 'Moveleft') {\r\n            animName = 'animation2'\r\n        }\r\n\r\n        this._animSpeed = 1.0;\r\n        try {\r\n            this.spine.setAnimation(0, animName, this.cycle);\r\n            console.log(`播放动画: ${animName}, 循环: ${this.cycle}`);\r\n        } catch (error) {\r\n            console.error(`播放动画失败: ${animName}`, error);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 停止当前动画\r\n     */\r\n    stopAnimation() {\r\n        if (this.spine) {\r\n            this.spine.clearTracks();\r\n            console.log(\"停止动画\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 暂停/恢复动画\r\n     */\r\n    pauseResumeAnimation() {\r\n        if (this.spine) {\r\n            this.spine.paused = !this.spine.paused;\r\n            console.log(this.spine.paused ? \"暂停动画\" : \"恢复动画\");\r\n        }\r\n    }\r\n\r\n    setAnimationCycle() {\r\n        this.cycle = !this.cycle;\r\n        if (this.spine) {\r\n            this.spine.setAnimation(0, this.spine.getCurrent(0)?.animation?.name || \"\", this.cycle);\r\n        }\r\n    }\r\n\r\n    downAnimationSpeed() {\r\n        this.animSpeed = Math.max(this._animSpeed / 2, 0.125)\r\n    }\r\n    upAnimationSpeed() {\r\n        this.animSpeed = Math.min(this._animSpeed * 2, 8)\r\n    }\r\n    \r\n    /**\r\n     * 获取所有可用的动画名称\r\n     */\r\n    getAvailableAnimations(): string[] {\r\n        if (!this.spine || !this.spine.skeletonData) {\r\n            return [];\r\n        }\r\n\r\n        const animations: string[] = [];\r\n        const skeletonData = this.spine.skeletonData.getRuntimeData();\r\n        if (skeletonData && skeletonData.animations) {\r\n            for (let i = 0; i < skeletonData.animations.length; i++) {\r\n                animations.push(skeletonData.animations[i].name);\r\n            }\r\n        }\r\n        return animations;\r\n    }\r\n}\r\n\r\n\r\n"]}