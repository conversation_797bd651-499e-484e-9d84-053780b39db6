System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, BundleName, ButtonPlus, HomeUI, PopupUI, csproto, MyApp, BaseUI, UILayer, UIMgr, ToastUI, PKHistoryUI, PKRewardIcon, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _crd, ccclass, property, PKUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "db://assets/bundles/common/script/ui/common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUI(extras) {
    _reporterNs.report("HomeUI", "db://assets/bundles/common/script/ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPopupUI(extras) {
    _reporterNs.report("PopupUI", "db://assets/bundles/common/script/ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfToastUI(extras) {
    _reporterNs.report("ToastUI", "../ToastUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPKHistoryUI(extras) {
    _reporterNs.report("PKHistoryUI", "./PKHistoryUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPKRewardIcon(extras) {
    _reporterNs.report("PKRewardIcon", "./PKRewardIcon", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      ButtonPlus = _unresolved_3.ButtonPlus;
    }, function (_unresolved_4) {
      HomeUI = _unresolved_4.HomeUI;
    }, function (_unresolved_5) {
      PopupUI = _unresolved_5.PopupUI;
    }, function (_unresolved_6) {
      csproto = _unresolved_6.default;
    }, function (_unresolved_7) {
      MyApp = _unresolved_7.MyApp;
    }, function (_unresolved_8) {
      BaseUI = _unresolved_8.BaseUI;
      UILayer = _unresolved_8.UILayer;
      UIMgr = _unresolved_8.UIMgr;
    }, function (_unresolved_9) {
      ToastUI = _unresolved_9.ToastUI;
    }, function (_unresolved_10) {
      PKHistoryUI = _unresolved_10.PKHistoryUI;
    }, function (_unresolved_11) {
      PKRewardIcon = _unresolved_11.PKRewardIcon;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "c731bfvTVVK+LCt7ttZYqxp", "PKUI", undefined);

      __checkObsolete__(['_decorator', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PKUI", PKUI = (_dec = ccclass('PKUI'), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec4 = property(Node), _dec5 = property(Node), _dec6 = property(Node), _dec7 = property(Node), _dec(_class = (_class2 = class PKUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "btnClose", _descriptor, this);

          _initializerDefineProperty(this, "btnHistory", _descriptor2, this);

          _initializerDefineProperty(this, "friendPk", _descriptor3, this);

          _initializerDefineProperty(this, "goldCoinPk", _descriptor4, this);

          _initializerDefineProperty(this, "diamondPk", _descriptor5, this);

          _initializerDefineProperty(this, "highDiamondPk", _descriptor6, this);
        }

        static getUrl() {
          return "prefab/ui/pk/PKUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home;
        }

        onLoad() {
          this.btnClose.addClick(this.closeUI, this);
          this.btnHistory.addClick(this.onHistoryClick, this);
          this.friendPk.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onFriendPk, this);
          this.goldCoinPk.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onGoldCoinPk, this);
          this.diamondPk.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onDiamondPk, this);
          this.highDiamondPk.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onHighDiamondPk, this);
          this.friendPk.getComponentInChildren(_crd && PKRewardIcon === void 0 ? (_reportPossibleCrUseOfPKRewardIcon({
            error: Error()
          }), PKRewardIcon) : PKRewardIcon).setData(888);
          var data = {
            PKRewardIcon: 123,
            PKRewardIcon2: 456
          };
          this.highDiamondPk.getComponentsInChildren(_crd && PKRewardIcon === void 0 ? (_reportPossibleCrUseOfPKRewardIcon({
            error: Error()
          }), PKRewardIcon) : PKRewardIcon).forEach(icon => {
            var _data$key;

            var key = icon.node.name;
            var value = (_data$key = data[key]) != null ? _data$key : 0; // 默认值为 0

            icon.setData(value);
          });
        }

        onFriendPk() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && ToastUI === void 0 ? (_reportPossibleCrUseOfToastUI({
            error: Error()
          }), ToastUI) : ToastUI, "点击了1");
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GAME_PVP_MATCH, {
            game_pvp_match: {
              map_id: 1
            }
          });
        }

        onGoldCoinPk() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, "点击了2");
        }

        onDiamondPk() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, "点击了3");
        }

        onHighDiamondPk() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, "点击了4");
        }

        closeUI() {
          return _asyncToGenerator(function* () {
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(PKUI);
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
              error: Error()
            }), HomeUI) : HomeUI);
          })();
        }

        onHistoryClick() {
          return _asyncToGenerator(function* () {
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(PKUI);
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && PKHistoryUI === void 0 ? (_reportPossibleCrUseOfPKHistoryUI({
              error: Error()
            }), PKHistoryUI) : PKHistoryUI);
          })();
        }

        onShow() {
          return _asyncToGenerator(function* () {})();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

        onDestroy() {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnClose", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "btnHistory", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "friendPk", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "goldCoinPk", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "diamondPk", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "highDiamondPk", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=ec2517ee90b24098fae77a61d5c9210abb6e7e39.js.map