import { _decorator, Label } from 'cc';
import { BaseUI, UILayer } from '../../../../../scripts/ui/UIMgr';
import { BundleName } from '../../../../Bundle';
import { EventMgr } from '../../event/EventManager';
const { ccclass, property } = _decorator;

@ccclass('TopUI')
export class TopUI extends BaseUI {

    @property(Label)

    public static getUrl(): string { return "prefab/ui/TopUI"; }
    public static getLayer(): UILayer { return UILayer.Background }
    public static getBundleName(): string { return BundleName.Home }

    protected onLoad(): void {

    }

    async onShow(...args: any[]): Promise<void> {
    }
    async onHide(...args: any[]): Promise<void> {
    }
    async onClose(...args: any[]): Promise<void> {
    }
    protected update(dt: number): void {
    }
    protected onDestroy(): void {
        EventMgr.targetOff(this)
    }
}

