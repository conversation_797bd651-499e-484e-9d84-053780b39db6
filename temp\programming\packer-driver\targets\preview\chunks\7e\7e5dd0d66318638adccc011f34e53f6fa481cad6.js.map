{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts"], "names": ["_decorator", "instantiate", "input", "Input", "Component", "RichText", "assetManager", "EDITOR", "Emitter", "BulletSystem", "ccclass", "playOnFocus", "executeInEditMode", "property", "disallowMultiple", "menu", "EmitterEditor", "visible", "displayName", "type", "override", "_updateInEditor", "targetFrameRate", "fixedDelta", "value", "resetInEditor", "onFocusInEditor", "node", "walk", "emitter", "getComponent", "setActive", "onLostFocusInEditor", "reset", "onLoad", "on", "EventType", "KEY_PRESSING", "onKeyPressing", "onDestroy", "off", "event", "start", "frameCount", "frameTimeInMilliseconds", "destroyAllBullets", "allEventGroups", "console", "log", "update", "dt", "updateInfoText", "milli_dt", "tick", "richText", "string", "toFixed", "allEmitters", "length", "allBullets", "instantiatePrefab", "prefabUuid", "loadAny", "uuid", "err", "prefab", "parent", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;AAA+BC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,Q,OAAAA,Q;AAAoBC,MAAAA,Y,OAAAA,Y;;AACpGC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,WAAX;AAAwBC,QAAAA,iBAAxB;AAA2CC,QAAAA,QAA3C;AAAqDC,QAAAA,gBAArD;AAAuEC,QAAAA;AAAvE,O,GAAiFf,U;;+BAO1EgB,a,WALZN,OAAO,CAAC,eAAD,C,UACPK,IAAI,CAAC,aAAD,C,UACJJ,WAAW,CAAC,IAAD,C,UACXC,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAEZD,QAAQ,CAAC;AAACI,QAAAA,OAAO,EAAC;AAAT,OAAD,C,UAGRJ,QAAQ,CAAC;AAACK,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UASRL,QAAQ,CAAC;AAACM,QAAAA,IAAI,EAAEd,QAAP;AAAiBe,QAAAA,QAAQ,EAAE,IAA3B;AAAiCF,QAAAA,WAAW,EAAE;AAA9C,OAAD,C,kGAlBb,MAKaF,aALb,SAKmCZ,SALnC,CAK6C;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAsCjCiB,eAtCiC,GAsCN,KAtCM;AAAA;;AAEd;AAGD,YAAfC,eAAe,GAAW;AACjC,iBAAO,OAAO,KAAKC,UAAnB;AACH;;AAEyB,YAAfD,eAAe,CAACE,KAAD,EAAgB;AACtC,eAAKD,UAAL,GAAkB,OAAOC,KAAzB;AACH;;AA6BDC,QAAAA,aAAa,GAAG;AACZ,eAAKJ,eAAL,GAAuB,IAAvB,CADY,CAEZ;AACH;;AAEDK,QAAAA,eAAe,GAAG;AACd,eAAKL,eAAL,GAAuB,IAAvB,CADc,CAEd;AAEA;AACA;AAEA;;AACA,eAAKM,IAAL,CAAUC,IAAV,CAAgBD,IAAD,IAAU;AACrB,gBAAME,OAAO,GAAGF,IAAI,CAACG,YAAL;AAAA;AAAA,mCAAhB;;AACA,gBAAID,OAAJ,EAAa;AACTA,cAAAA,OAAO,CAACE,SAAR,CAAkB,IAAlB;AACH;AACJ,WALD;AAMH;;AAEDC,QAAAA,mBAAmB,GAAS;AACxB,eAAKX,eAAL,GAAuB,KAAvB;AACA,eAAKY,KAAL;AACH;;AAEDC,QAAAA,MAAM,GAAG;AACLhC,UAAAA,KAAK,CAACiC,EAAN,CAAShC,KAAK,CAACiC,SAAN,CAAgBC,YAAzB,EAAuC,KAAKC,aAA5C,EAA2D,IAA3D;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACRrC,UAAAA,KAAK,CAACsC,GAAN,CAAUrC,KAAK,CAACiC,SAAN,CAAgBC,YAA1B,EAAwC,KAAKC,aAA7C,EAA4D,IAA5D;AACH;;AAEDA,QAAAA,aAAa,CAACG,KAAD,EAAuB;AAChC,cAAI,CAAC,KAAKpB,eAAV,EAA2B,OADK,CAGhC;AACH;;AAEDqB,QAAAA,KAAK,GAAG;AACJ,eAAKT,KAAL;AACH;;AAEDA,QAAAA,KAAK,GAAG;AACJjB,UAAAA,aAAa,CAAC2B,UAAd,GAA2B,CAA3B;AACA3B,UAAAA,aAAa,CAAC4B,uBAAd,GAAwC,CAAxC;AACA;AAAA;AAAA,4CAAaC,iBAAb,CAA+B,IAA/B;AACA,eAAKlB,IAAL,CAAUC,IAAV,CAAgBD,IAAD,IAAU;AACrB,gBAAME,OAAO,GAAGF,IAAI,CAACG,YAAL;AAAA;AAAA,mCAAhB;;AACA,gBAAID,OAAJ,EAAa;AACTA,cAAAA,OAAO,CAACE,SAAR,CAAkB,KAAlB;AACH;AACJ,WALD;AAMA;AAAA;AAAA,4CAAae,cAAb,GAA8B,EAA9B;AACAC,UAAAA,OAAO,CAACC,GAAR,CAAY,SAAZ,EAAuB,KAAK3B,eAA5B;AACH;;AAED4B,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,cAAI3C,MAAM,IAAI,KAAKc,eAAnB,EAAoC;AAChC,iBAAK8B,cAAL;AACA,gBAAMC,QAAQ,GAAGF,EAAE,GAAG,IAAtB;AACAlC,YAAAA,aAAa,CAAC2B,UAAd,IAA4B,CAA5B;AACA3B,YAAAA,aAAa,CAAC4B,uBAAd,IAAyCQ,QAAzC;AACA;AAAA;AAAA,8CAAaC,IAAb,CAAkBH,EAAlB;AACH;AACJ;;AAEDC,QAAAA,cAAc,GAAG;AACb,cAAI,KAAKG,QAAT,EAAmB;AACf,iBAAKA,QAAL,CAAcC,MAAd,kCAAgCvC,aAAa,CAAC4B,uBAAd,CAAsCY,OAAtC,CAA8C,CAA9C,CAAhC,sDAA8F;AAAA;AAAA,8CAAaC,WAAb,CAAyBC,MAAvH,gDAA0I;AAAA;AAAA,8CAAaC,UAAb,CAAwBD,MAAlK,sDAAsL;AAAA;AAAA,8CAAaZ,cAAb,CAA4BY,MAAlN;AACH;AACJ,SAhHwC,CAkHzC;;;AACOE,QAAAA,iBAAiB,CAACC,UAAD,EAAqB;AACzC;AACA;AACAvD,UAAAA,YAAY,CAACwD,OAAb,CAAqB;AAACC,YAAAA,IAAI,EAAEF;AAAP,WAArB,EAAyC,CAACG,GAAD,EAAMC,MAAN,KAAiB;AACtD,gBAAID,GAAJ,EAAS;AACLjB,cAAAA,OAAO,CAACC,GAAR,CAAY,wBAAZ,EAAsCgB,GAAtC;AACA;AACH;;AACD,gBAAMrC,IAAI,GAAG1B,WAAW,CAACgE,MAAD,CAAxB;AACA,gBAAMC,MAAM,GAAG,KAAKvC,IAApB;AACAuC,YAAAA,MAAM,CAAEC,QAAR,CAAiBxC,IAAjB;AACH,WARD;AASH,SA/HwC,CAiIzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAjJyC,O,UAoClCgB,U,GAAqB,C,UACrBC,uB,GAAkC,C;;;;;iBAnCrB,K;;;;;;;iBAYC,I", "sourcesContent": ["import { _decorator, misc, instantiate, input, Input, EventKeyboard, KeyCode, Component, Rich<PERSON><PERSON><PERSON>, director, assetManager } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { Emitter } from '../../scripts/Game/bullet/Emitter';\r\nimport { BulletSystem } from '../../scripts/Game/bullet/BulletSystem';\r\nconst { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu  } = _decorator;\r\n\r\n@ccclass('EmitterEditor')\r\n@menu('子弹系统/发射器编辑器')\r\n@playOnFocus(true)\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class EmitterEditor extends Component {\r\n    @property({visible:false})\r\n    fixedDelta:number = 16.67; // Fixed time step (e.g., 60 FPS), 单位: 毫秒\r\n\r\n    @property({displayName: \"目标帧率\"})\r\n    public get targetFrameRate(): number {\r\n        return 1000 / this.fixedDelta;\r\n    }\r\n\r\n    public set targetFrameRate(value: number) {\r\n        this.fixedDelta = 1000 / value;\r\n    }\r\n\r\n    @property({type: RichText, override: true, displayName: \"信息显示\"})\r\n    richText: RichText = null!;\r\n\r\n    // @property({displayName: \"当前时间(ms)\"})\r\n    // public get frameTime(): number {\r\n    //     return EmitterEditor.frameTimeInMilliseconds;\r\n    // }\r\n\r\n    // @property({displayName: \"当前发射器数量\"})\r\n    // public get emitterCount(): number {\r\n    //     return BulletSystem.allEmitters.length;\r\n    // }\r\n\r\n    // @property({displayName: \"当前子弹数量\"})\r\n    // public get bulletCount(): number {\r\n    //     return BulletSystem.allBullets.length;\r\n    // }\r\n\r\n    // @property({displayName: \"当前事件组数量\"})\r\n    // public get eventGroupCount(): number {\r\n    //     return BulletSystem.allEventGroups.length;\r\n    // }\r\n\r\n    static frameCount: number = 0;\r\n    static frameTimeInMilliseconds: number = 0;\r\n    private _updateInEditor: boolean = false;\r\n\r\n    resetInEditor() {\r\n        this._updateInEditor = true;\r\n        // console.log('resetInEditor');\r\n    }\r\n\r\n    onFocusInEditor() {\r\n        this._updateInEditor = true;\r\n        // console.log('onFocusInEditor');\r\n        \r\n        // @ts-ignore\r\n        // Editor.Selection.select('node', BulletSystem.allEmitters.map(emitter => emitter.node.uuid));\r\n\r\n        // loop all children to find emitters\r\n        this.node.walk((node) => {\r\n            const emitter = node.getComponent(Emitter);\r\n            if (emitter) {\r\n                emitter.setActive(true);\r\n            }\r\n        });\r\n    }\r\n\r\n    onLostFocusInEditor(): void {\r\n        this._updateInEditor = false;\r\n        this.reset();\r\n    }\r\n\r\n    onLoad() {\r\n        input.on(Input.EventType.KEY_PRESSING, this.onKeyPressing, this);\r\n    }\r\n\r\n    onDestroy() {\r\n        input.off(Input.EventType.KEY_PRESSING, this.onKeyPressing, this);\r\n    }\r\n\r\n    onKeyPressing(event: EventKeyboard) {\r\n        if (!this._updateInEditor) return;\r\n\r\n        // use wasd to move player sprite(which is not present right now)\r\n    }\r\n\r\n    start() {\r\n        this.reset();\r\n    }\r\n\r\n    reset() {\r\n        EmitterEditor.frameCount = 0;\r\n        EmitterEditor.frameTimeInMilliseconds = 0;\r\n        BulletSystem.destroyAllBullets(true);                \r\n        this.node.walk((node) => {\r\n            const emitter = node.getComponent(Emitter);\r\n            if (emitter) {\r\n                emitter.setActive(false);\r\n            }\r\n        });\r\n        BulletSystem.allEventGroups = [];\r\n        console.log('reset: ', this._updateInEditor);\r\n    }\r\n\r\n    update(dt: number) {\r\n        if (EDITOR && this._updateInEditor) {\r\n            this.updateInfoText();\r\n            const milli_dt = dt * 1000;\r\n            EmitterEditor.frameCount += 1;\r\n            EmitterEditor.frameTimeInMilliseconds += milli_dt;\r\n            BulletSystem.tick(dt);\r\n        }\r\n    }\r\n\r\n    updateInfoText() {\r\n        if (this.richText) {\r\n            this.richText.string = `当前时间: ${EmitterEditor.frameTimeInMilliseconds.toFixed(2)}\\n当前发射器数量: ${BulletSystem.allEmitters.length}\\n当前子弹数量: ${BulletSystem.allBullets.length}\\n当前事件组数量: ${BulletSystem.allEventGroups.length}`;\r\n        }\r\n    }\r\n\r\n    // 编辑器方法\r\n    public instantiatePrefab(prefabUuid: string) {\r\n        // replace db://assets/resources/Game/prefabs/emitter/ with assets/resources/Game/prefabs/emitter/\r\n        //prefabUrl = prefabUrl.replace('db://', '');\r\n        assetManager.loadAny({uuid: prefabUuid}, (err, prefab) => {\r\n            if (err) {\r\n                console.log('Failed to load prefab:', err);\r\n                return;\r\n            }\r\n            const node = instantiate(prefab!);\r\n            const parent = this.node;\r\n            parent!.addChild(node);\r\n        });\r\n    }\r\n\r\n    // public saveToPrefab(nodeUuid: string, prefabUrl: string): Promise<string> {\r\n    //     console.log('saveToPrefab in Component:', nodeUuid, prefabUrl);        \r\n    //     return new Promise<string>((resolve, reject) => {\r\n    //         const scene = director.getScene();\r\n    //         const target = scene!.getChildByUuid(nodeUuid);\r\n    //         if (!target) {\r\n    //             console.error(\"node not found:\", nodeUuid);\r\n    //             reject();\r\n    //             return;\r\n    //         }\r\n    //         const json = cce.Utils.serialize(target);\r\n    //         // 将节点保存为 Prefab\r\n    //         // _utils.applyTargetOverrides(target as Node);\r\n    //         // Editor.Message.request('asset-db', 'save-asset', prefabUrl, json);\r\n    //         resolve(json);\r\n    //     });\r\n    // }\r\n}\r\n"]}