{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts"], "names": ["_decorator", "Node", "tween", "UIOpacity", "instantiate", "Tween", "view", "v3", "size", "Prefab", "GameConst", "GameIns", "Bullet", "EffectLayer", "FBoxCollider", "ColliderGroupType", "MyApp", "Plane", "AttributeConst", "GameResourceList", "Emitter", "PlaneBase", "ccclass", "property", "MainPlane", "hpMidActin", "m_screenDatas", "m_moveEnable", "emitter<PERSON>omp", "_hurtActTime", "_hurtActDuration", "_planeData", "_plane", "onLoad", "collide<PERSON>omp", "getComponent", "addComponent", "init", "groupType", "PLAYER", "colliderEnabled", "start", "setFireEnable", "update", "dt", "initPlane", "planeData", "plane", "planeMgr", "getPlane", "planeParent", "<PERSON><PERSON><PERSON><PERSON>", "setEmitter", "curHp", "getFinalAttributeByKey", "MaxHP", "maxHp", "path", "EmitterPrefabPath", "resMgr", "loadAsync", "then", "prefab", "node", "NodeEmitter", "setPosition", "initBattle", "active", "updateHpUI", "planeIn", "self", "setScale", "posY", "getVisibleSize", "height", "setMoveAble", "stopAllByTarget", "frame", "ActionFrameTime", "battleManager", "animSpeed", "scheduleOnce", "targetY", "targetX", "position", "x", "to", "call", "begine", "scale", "hpNode", "opacity", "delay", "battleQuit", "onCollide", "collision", "damage", "entity", "getAttack", "hurt", "onControl", "posX", "mainPlaneManager", "planeFightData", "die", "Math", "min", "max", "ViewHeight", "relife", "gameDataManager", "reviveCount", "revive", "Attack", "to<PERSON><PERSON>", "_playDieAnim", "playHurtAnim", "me", "showRedScreen", "battleFail", "enable", "isContinue", "onPlaneIn", "attribute"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAsCC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAA8BC,MAAAA,K,OAAAA,K;AAAcC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,E,OAAAA,E;AAAqBC,MAAAA,I,OAAAA,I;AAAmBC,MAAAA,M,OAAAA,M;;AAC9JC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,M;;AACAC,MAAAA,W;;AACAC,MAAAA,Y;;AACaC,MAAAA,iB,iBAAAA,iB;;AACXC,MAAAA,K,iBAAAA,K;;AAEAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,c,kBAAAA,c;;AAEFC,MAAAA,gB;;AACEC,MAAAA,O,kBAAAA,O;;AACFC,MAAAA,S;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBvB,U;;2BAIjBwB,S,WADZF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACtB,IAAD,C,UAERsB,QAAQ,CAACtB,IAAD,C,UAERsB,QAAQ,CAACtB,IAAD,C,2BAPb,MACauB,SADb;AAAA;AAAA,kCACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eASrCC,UATqC,GASV,IATU;AASJ;AATI,eAUrCC,aAVqC,GAUT,EAVS;AAUL;AAVK,eAWrCC,YAXqC,GAWtB,IAXsB;AAWhB;AAXgB,eAYrCC,WAZqC,GAYP,IAZO;AAYD;AAZC,eAcrCC,YAdqC,GActB,CAdsB;AAcnB;AAdmB,eAerCC,gBAfqC,GAelB,GAfkB;AAeb;AAfa,eAiBrCC,UAjBqC,GAiBP,IAjBO;AAiBF;AAjBE,eAkBrCC,MAlBqC,GAkBf,IAlBe;AAAA;;AAkBV;AAG3BC,QAAAA,MAAM,GAAG;AACL,eAAKC,WAAL,GAAmB,KAAKC,YAAL;AAAA;AAAA,+CAAmC,KAAKC,YAAL;AAAA;AAAA,2CAAtD;AACA,eAAKF,WAAL,CAAkBG,IAAlB,CAAuB,IAAvB,EAA6B7B,IAAI,CAAC,EAAD,EAAK,EAAL,CAAjC,EAFK,CAEuC;;AAC5C,eAAK0B,WAAL,CAAkBI,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,MAAhD;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACH;;AAEDC,QAAAA,KAAK,GAAG;AACJ;AACA,eAAKC,aAAL,CAAmB,KAAnB;AACH;;AAEDC,QAAAA,MAAM,CAACC,EAAD,EAAa;AACf,eAAKf,YAAL,IAAqBe,EAArB;AACH;;AAEDC,QAAAA,SAAS,CAACC,SAAD,EAAsB;AAAA;;AAC3B,eAAKf,UAAL,GAAkBe,SAAlB,CAD2B,CAG3B;;AACA,cAAIC,KAAK,GAAG;AAAA;AAAA,8BAAMC,QAAN,CAAeC,QAAf,CAAwBH,SAAxB,CAAZ;AACA,eAAKd,MAAL,GAAce,KAAK,CAACZ,YAAN;AAAA;AAAA,6BAAd;AACA,oCAAKe,WAAL,+BAAkBC,QAAlB,CAA2BJ,KAA3B,EAN2B,CAQ3B;;AACA,eAAKK,UAAL;AAEA,eAAKC,KAAL,uBAAa,KAAKtB,UAAlB,qBAAa,iBAAiBuB,sBAAjB,CAAwC;AAAA;AAAA,gDAAeC,KAAvD,CAAb;AAA2E;AAC3E,eAAKC,KAAL,GAAa,KAAKH,KAAlB;AACH;;AAEDD,QAAAA,UAAU,GAAE;AACR;AACA,cAAIK,IAAI,GAAG;AAAA;AAAA,oDAAiBC,iBAAjB,GAAqC,iBAAhD;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,SAAb,CAAuBH,IAAvB,EAA6BhD,MAA7B,EAAqCoD,IAArC,CAA2CC,MAAD,IAAY;AAAA;;AAClD,gBAAIC,IAAI,GAAG3D,WAAW,CAAC0D,MAAD,CAAtB;AACA,sCAAKE,WAAL,+BAAkBb,QAAlB,CAA2BY,IAA3B;AACAA,YAAAA,IAAI,CAACE,WAAL,CAAiB,CAAjB,EAAmB,CAAnB;AAEA,iBAAKrC,WAAL,GAAmBmC,IAAI,CAAC5B,YAAL;AAAA;AAAA,mCAAnB;AACH,WAND;AAOH;;AAED+B,QAAAA,UAAU,GAAG;AACT,eAAKH,IAAL,CAAUI,MAAV,GAAmB,IAAnB;AACA,eAAK3B,eAAL,GAAuB,KAAvB;AACA,eAAK4B,UAAL;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,OAAO,GAAS;AACZ,cAAMC,IAAI,GAAG,IAAb,CADY,CAGZ;;AACA,eAAKpB,WAAL,CAAkBqB,QAAlB,CAA2B,CAA3B,EAA8B,CAA9B;AACA,cAAIC,IAAI,GAAG,CAAClE,IAAI,CAACmE,cAAL,GAAsBC,MAAvB,GAAgC,EAA3C;AACA,eAAKX,IAAL,CAAUE,WAAV,CAAsB,CAAtB,EAAyBO,IAAzB;AACA,eAAK9B,aAAL,CAAmB,KAAnB;AACA,eAAKiC,WAAL,CAAiB,KAAjB;AACAtE,UAAAA,KAAK,CAACuE,eAAN,CAAsB,KAAKb,IAA3B;AAEA,cAAIc,KAAK,GAAG;AAAA;AAAA,sCAAUC,eAAV,GAA4B;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,SAA9D,CAXY,CAYZ;;AACA,eAAKC,YAAL,CAAkB,MAAM;AACpB,gBAAMC,OAAO,GAAG,CAAC5E,IAAI,CAACmE,cAAL,GAAsBC,MAAvB,GAAgC,GAAhD;AACA,gBAAMS,OAAO,GAAG,KAAKpB,IAAL,CAAUqB,QAAV,CAAmBC,CAAnC;AACAnF,YAAAA,KAAK,CAAC,KAAK6D,IAAN,CAAL,CACKuB,EADL,CACQ,KAAKT,KADb,EACoB;AAAEO,cAAAA,QAAQ,EAAE7E,EAAE,CAAC4E,OAAD,EAAUD,OAAO,GAAG,EAApB;AAAd,aADpB,EAEKI,EAFL,CAEQ,KAAKT,KAFb,EAEoB;AAAEO,cAAAA,QAAQ,EAAE7E,EAAE,CAAC4E,OAAD,EAAUD,OAAO,GAAG,EAApB;AAAd,aAFpB,EAGKI,EAHL,CAGQ,KAAKT,KAHb,EAGoB;AAAEO,cAAAA,QAAQ,EAAE7E,EAAE,CAAC4E,OAAD,EAAUD,OAAO,GAAG,EAApB;AAAd,aAHpB,EAIKI,EAJL,CAIQ,KAAKT,KAJb,EAIoB;AAAEO,cAAAA,QAAQ,EAAE7E,EAAE,CAAC4E,OAAD,EAAUD,OAAV;AAAd,aAJpB,EAKKK,IALL,CAKU,MAAM;AACRjB,cAAAA,IAAI,CAACkB,MAAL;AACH,aAPL,EAQK/C,KARL;AAUAvC,YAAAA,KAAK,CAAC,KAAKgD,WAAN,CAAL,CACKoC,EADL,CACQ,KAAKT,KADb,EACoB;AAAEY,cAAAA,KAAK,EAAElF,EAAE,CAAC,GAAD,EAAM,GAAN;AAAX,aADpB,EAEK+E,EAFL,CAEQ,KAAKT,KAFb,EAEoB;AAAEY,cAAAA,KAAK,EAAElF,EAAE,CAAC,GAAD,EAAM,GAAN;AAAX,aAFpB,EAGK+E,EAHL,CAGQ,KAAKT,KAHb,EAGoB;AAAEY,cAAAA,KAAK,EAAElF,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,aAHpB,EAIK+E,EAJL,CAIQ,KAAKT,KAJb,EAIoB;AAAEY,cAAAA,KAAK,EAAElF,EAAE,CAAC,CAAD,EAAI,CAAJ;AAAX,aAJpB,EAKKkC,KALL;;AAOA,gBAAI,KAAKiD,MAAT,EAAiB;AACbxF,cAAAA,KAAK,CAAC,KAAKwF,MAAL,CAAavD,YAAb,CAA0BhC,SAA1B,CAAD,CAAL,CACKmF,EADL,CACQ,CADR,EACW;AAAEK,gBAAAA,OAAO,EAAE;AAAX,eADX,EAEKC,KAFL,CAEW,KAAKf,KAFhB,EAGKS,EAHL,CAGQ,KAAKT,KAHb,EAGoB;AAAEc,gBAAAA,OAAO,EAAE;AAAX,eAHpB,EAIKlD,KAJL;AAKH;AACJ,WA3BD,EA2BG,IAAIoC,KA3BP;AA4BH;AAED;AACJ;AACA;;;AACIgB,QAAAA,UAAU,GAAG,CAEZ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,SAAS,CAACC,SAAD,EAAuB;AAE5B,cAAIC,MAAM,GAAG,CAAb;;AACA,cAAID,SAAS,CAACE,MAAV;AAAA;AAAA,+BAAJ,EAAwC;AACpCD,YAAAA,MAAM,GAAGD,SAAS,CAACE,MAAV,CAAiBC,SAAjB,EAAT;AACH;;AAED,cAAIF,MAAM,GAAG,CAAb,EAAgB;AACZ,iBAAKG,IAAL,CAAUH,MAAV;AACH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACII,QAAAA,SAAS,CAACC,IAAD,EAAe7B,IAAf,EAA6B;AAClC,cAAI,CAAC;AAAA;AAAA,kCAAQ8B,gBAAR,CAAyBC,cAAzB,CAAwCC,GAAzC,IAAgD,KAAK7E,YAAzD,EAAuE;AACnE;AACA0E,YAAAA,IAAI,GAAGI,IAAI,CAACC,GAAL,CAAS,GAAT,EAAcL,IAAd,CAAP;AACAA,YAAAA,IAAI,GAAGI,IAAI,CAACE,GAAL,CAAS,CAAC,GAAV,EAAeN,IAAf,CAAP;AACA7B,YAAAA,IAAI,GAAGiC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYlC,IAAZ,CAAP;AACAA,YAAAA,IAAI,GAAGiC,IAAI,CAACE,GAAL,CAAS,CAAC;AAAA;AAAA,wCAAUC,UAApB,EAAgCpC,IAAhC,CAAP;AACA,iBAAKT,IAAL,CAAUE,WAAV,CAAsBoC,IAAtB,EAA4B7B,IAA5B;AACH;AACJ;;AAGDqC,QAAAA,MAAM,GAAG;AACL;AAAA;AAAA,kCAAQC,eAAR,CAAwBC,WAAxB,IAAuC,CAAvC,CADK,CACqC;AAC1C;;AACA;AAAA;AAAA,kCAAQT,gBAAR,CAAyBC,cAAzB,CAAyCC,GAAzC,GAA+C,KAA/C,CAHK,CAGiD;;AACtD;AAAA;AAAA,kCAAQF,gBAAR,CAAyBC,cAAzB,CAAyCS,MAAzC,GAAkD,IAAlD,CAJK,CAImD;;AACxD,eAAK/B,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,oCAAQqB,gBAAR,CAAyBC,cAAzB,CAAyCS,MAAzC,GAAkD,KAAlD;AACH,WAFD,EAEG,GAFH;AAIA,eAAK3D,KAAL,GAAa,KAAKG,KAAlB,CATK,CASoB;;AACzB,eAAKY,UAAL;AAAkB,WAVb,CAUe;AACvB;AAED;AACJ;AACA;AACA;;;AACI8B,QAAAA,SAAS,GAAG;AAAA;;AACR,sCAAO,KAAKnE,UAAZ,qBAAO,kBAAiBuB,sBAAjB,CAAwC;AAAA;AAAA,gDAAe2D,MAAvD,CAAP;AACH;;AAEDC,QAAAA,KAAK,GAAY;AACb,cAAI,CAAC,MAAMA,KAAN,EAAL,EAAoB;AAChB,mBAAO,KAAP;AACH,WAHY,CAKb;;;AACA;AAAA;AAAA,kCAAQZ,gBAAR,CAAyBC,cAAzB,CAAyCC,GAAzC,GAA+C,IAA/C,CANa,CAQb;;AACA,eAAKW,YAAL;;AACA,iBAAO,IAAP;AACH,SA5LoC,CA8LrC;;;AACAC,QAAAA,YAAY,GAAG;AACX,cAAI,KAAKvF,YAAL,GAAoB,KAAKC,gBAA7B,EAA+C;AAC3C,iBAAKD,YAAL,GAAoB,CAApB,CAD2C,CAE3C;;AACA;AAAA;AAAA,4CAAYwF,EAAZ,CAAeC,aAAf;AACH;AACJ;AAED;AACJ;AACA;;;AACIH,QAAAA,YAAY,GAAG;AACX;AACA;AACA;AACA;AAAA;AAAA,kCAAQpC,aAAR,CAAsBwC,UAAtB;AACH;AAED;AACJ;AACA;AACA;;;AACI5C,QAAAA,WAAW,CAAC6C,MAAD,EAAkB;AACzB,eAAK7F,YAAL,GAAoB6F,MAApB;AACH;;AAED9E,QAAAA,aAAa,CAAC8E,MAAD,EAAkB,CAC3B;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIhC,QAAAA,MAAM,CAACiC,UAAD,EAAqB;AAAA,cAApBA,UAAoB;AAApBA,YAAAA,UAAoB,GAAP,KAAO;AAAA;;AACvB,cAAIA,UAAJ,EAAgB;AACZ,iBAAK/E,aAAL,CAAmB,IAAnB;AACA,iBAAKiC,WAAL,CAAiB,IAAjB;AACA,iBAAKnC,eAAL,GAAuB,IAAvB;AACH,WAJD,MAIO;AACH;AAAA;AAAA,oCAAQuC,aAAR,CAAsB2C,SAAtB;AACH;AACJ;;AAEY,YAATC,SAAS,GAAkB;AAC3B,iBAAO,KAAK5F,UAAZ;AACH;;AAhPoC,O;;;;;iBAGV,I;;;;;;;iBAEA,I;;;;;;;iBAEL,I", "sourcesContent": ["import { _decorator, Node, Sprite, Animation, Label, Vec2, tween, UIOpacity, instantiate, sp, UITransform, Tween, Color, view, v3, v2, SpriteFrame, size, SpriteAtlas, Prefab } from \"cc\";\r\nimport { GameConst } from \"../../../const/GameConst\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport Bullet from \"../../bullet/Bullet\";\r\nimport EffectLayer from \"../../layer/EffectLayer\";\r\nimport FBoxCollider from \"../../../collider-system/FBoxCollider\";\r\nimport FCollider, { ColliderGroupType } from \"../../../collider-system/FCollider\";\r\nimport { MyApp } from \"db://assets/scripts/MyApp\";\r\nimport { PlaneData } from \"db://assets/bundles/common/script/data/plane/PlaneData\";\r\nimport { Plane } from \"db://assets/bundles/common/script/ui/Plane\";\r\nimport { AttributeConst } from \"db://assets/bundles/common/script/const/AttributeConst\";\r\nimport { AttributeData } from \"db://assets/bundles/common/script/data/base/AttributeData\";\r\nimport GameResourceList from \"../../../const/GameResourceList\";\r\nimport { Emitter } from \"../../../bullet/Emitter\";\r\nimport PlaneBase from \"../PlaneBase\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n\r\n@ccclass(\"MainPlane\")\r\nexport class MainPlane extends PlaneBase {\r\n\r\n    @property(Node)\r\n    planeParent: Node | null = null;\r\n    @property(Node)\r\n    NodeEmitter: Node | null = null;\r\n    @property(Node)\r\n    hpNode: Node | null = null;\r\n\r\n    hpMidActin: Tween | null = null; // 血条动画\r\n    m_screenDatas: number[][] = []; // 屏幕数据\r\n    m_moveEnable = true; // 是否允许移动\r\n    emitterComp: Emitter | null = null; // 发射器\r\n\r\n    _hurtActTime = 0; // 受伤动画时间\r\n    _hurtActDuration = 0.5; // 受伤动画持续时间\r\n\r\n    _planeData:PlaneData | null = null;//飞机数据\r\n    _plane:Plane | null = null;//飞机显示节点\r\n\r\n\r\n    onLoad() {\r\n        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this.collideComp!.init(this, size(40, 40)); // 初始化碰撞组件\r\n        this.collideComp!.groupType = ColliderGroupType.PLAYER;\r\n        this.colliderEnabled = false;\r\n    }\r\n\r\n    start() {\r\n        // 禁用射击\r\n        this.setFireEnable(false);\r\n    }\r\n\r\n    update(dt: number) {\r\n        this._hurtActTime += dt;\r\n    }\r\n\r\n    initPlane(planeData:PlaneData) {\r\n        this._planeData = planeData;\r\n\r\n        //加载飞机显示\r\n        let plane = MyApp.planeMgr.getPlane(planeData);\r\n        this._plane = plane.getComponent(Plane);\r\n        this.planeParent?.addChild(plane);\r\n\r\n        //设置飞机发射组件\r\n        this.setEmitter();\r\n\r\n        this.curHp = this._planeData?.getFinalAttributeByKey(AttributeConst.MaxHP);;\r\n        this.maxHp = this.curHp;\r\n    }\r\n\r\n    setEmitter(){\r\n        //后期根据飞机的数据，加载不同的发送组件预制体\r\n        let path = GameResourceList.EmitterPrefabPath + \"Emitter_main_01\";\r\n        MyApp.resMgr.loadAsync(path, Prefab).then((prefab) => {\r\n            let node = instantiate(prefab);\r\n            this.NodeEmitter?.addChild(node);\r\n            node.setPosition(0,0);\r\n\r\n            this.emitterComp = node.getComponent(Emitter);\r\n        });\r\n    }\r\n\r\n    initBattle() {\r\n        this.node.active = true;\r\n        this.colliderEnabled = false;\r\n        this.updateHpUI();\r\n    }\r\n\r\n    /**\r\n * 主飞机入场动画\r\n */\r\n    planeIn(): void {\r\n        const self = this;\r\n\r\n        // 设置初始位置和状态\r\n        this.planeParent!.setScale(1, 1);\r\n        let posY = -view.getVisibleSize().height - 80;\r\n        this.node.setPosition(0, posY)\r\n        this.setFireEnable(false);\r\n        this.setMoveAble(false)\r\n        Tween.stopAllByTarget(this.node)\r\n\r\n        let frame = GameConst.ActionFrameTime / GameIns.battleManager.animSpeed;\r\n        // 飞机入场动画\r\n        this.scheduleOnce(() => {\r\n            const targetY = -view.getVisibleSize().height * 0.7;\r\n            const targetX = this.node.position.x;\r\n            tween(this.node)\r\n                .to(20 * frame, { position: v3(targetX, targetY - 17) })\r\n                .to(11 * frame, { position: v3(targetX, targetY + 57) })\r\n                .to(10 * frame, { position: v3(targetX, targetY + 76) })\r\n                .to(27 * frame, { position: v3(targetX, targetY) })\r\n                .call(() => {\r\n                    self.begine();\r\n                })\r\n                .start();\r\n\r\n            tween(this.planeParent!)\r\n                .to(20 * frame, { scale: v3(1.9, 1.9) })\r\n                .to(11 * frame, { scale: v3(1.4, 1.4) })\r\n                .to(10 * frame, { scale: v3(1, 1) })\r\n                .to(27 * frame, { scale: v3(1, 1) })\r\n                .start();\r\n\r\n            if (this.hpNode) {\r\n                tween(this.hpNode!.getComponent(UIOpacity)!)\r\n                    .to(0, { opacity: 0 })\r\n                    .delay(31 * frame)\r\n                    .to(10 * frame, { opacity: 255 })\r\n                    .start();\r\n            }\r\n        }, 7 * frame);\r\n    }\r\n\r\n    /**\r\n     * 退出战斗\r\n     */\r\n    battleQuit() {\r\n        \r\n    }\r\n\r\n    /**\r\n     * 碰撞处理\r\n     * @param {Object} collision 碰撞对象\r\n     */\r\n    onCollide(collision: FCollider) {\r\n\r\n        let damage = 0;\r\n        if (collision.entity instanceof Bullet) {\r\n            damage = collision.entity.getAttack();\r\n        }\r\n\r\n        if (damage > 0) {\r\n            this.hurt(damage)\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 控制飞机移动\r\n     * @param {number} moveX 水平方向的移动量\r\n     * @param {number} moveY 垂直方向的移动量\r\n     */\r\n    onControl(posX: number, posY: number) {\r\n        if (!GameIns.mainPlaneManager.planeFightData.die && this.m_moveEnable) {\r\n            // 限制飞机移动范围\r\n            posX = Math.min(360, posX);\r\n            posX = Math.max(-360, posX);\r\n            posY = Math.min(0, posY);\r\n            posY = Math.max(-GameConst.ViewHeight, posY);\r\n            this.node.setPosition(posX, posY);\r\n        }\r\n    }\r\n\r\n\r\n    relife() {\r\n        GameIns.gameDataManager.reviveCount += 1; // 增加复活次数\r\n        // this.playRelifeAim(); // 播放复活动画\r\n        GameIns.mainPlaneManager.planeFightData!.die = false; // 设置飞机为非死亡状态\r\n        GameIns.mainPlaneManager.planeFightData!.revive = true; // 设置复活状态\r\n        this.scheduleOnce(() => {\r\n            GameIns.mainPlaneManager.planeFightData!.revive = false;\r\n        }, 0.5);\r\n\r\n        this.curHp = this.maxHp; // 恢复满血\r\n        this.updateHpUI();; // 触发血量更新事件\r\n    }\r\n\r\n    /**\r\n     * 获取攻击力\r\n     * @returns {number} 当前攻击力\r\n     */\r\n    getAttack() {\r\n        return this._planeData?.getFinalAttributeByKey(AttributeConst.Attack);\r\n    }\r\n\r\n    toDie(): boolean {\r\n        if (!super.toDie()) {\r\n            return false;\r\n        }\r\n\r\n        // 设置玩家状态为死亡\r\n        GameIns.mainPlaneManager.planeFightData!.die = true;\r\n\r\n        // 播放死亡动画\r\n        this._playDieAnim();\r\n        return true;\r\n    }\r\n\r\n    //实现父类的方法\r\n    playHurtAnim() {\r\n        if (this._hurtActTime > this._hurtActDuration) {\r\n            this._hurtActTime = 0;\r\n            // 显示红屏效果\r\n            EffectLayer.me.showRedScreen();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放死亡动画\r\n     */\r\n    _playDieAnim() {\r\n        // this.blast!.node!.getComponent(UIOpacity)!.opacity = 255; // 显示爆炸效果\r\n        // this.blast!.setCompleteListener(this._dieAnimEnd.bind(this)); // 设置动画完成回调\r\n        // this.blast!.setAnimation(0, \"play\", false); // 播放爆炸动画\r\n        GameIns.battleManager.battleFail();\r\n    }\r\n\r\n    /**\r\n     * 设置飞机是否可移动\r\n     * @param {boolean} enable 是否可移动\r\n     */\r\n    setMoveAble(enable: boolean) {\r\n        this.m_moveEnable = enable;\r\n    }\r\n\r\n    setFireEnable(enable: boolean) {\r\n        // for (let i = 0; i < this.m_fires.length; i++) {\r\n        //     const fire = this.m_fires[i];\r\n        //     fire.isEnabled = enable;\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     * @param {boolean} isContinue 是否继续战斗\r\n     */\r\n    begine(isContinue = false) {\r\n        if (isContinue) {\r\n            this.setFireEnable(true);\r\n            this.setMoveAble(true);\r\n            this.colliderEnabled = true;\r\n        } else {\r\n            GameIns.battleManager.onPlaneIn();\r\n        }\r\n    }\r\n\r\n    get attribute(): AttributeData {\r\n        return this._planeData!;\r\n    }\r\n}"]}