System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, Node, RichText, UIMgr, DataEvent, EventMgr, ButtonPlus, PopupUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _dec10, _dec11, _dec12, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _crd, ccclass, property, FriendCellUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../../../../../scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPopupUI(extras) {
    _reporterNs.report("PopupUI", "../PopupUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      Node = _cc.Node;
      RichText = _cc.RichText;
    }, function (_unresolved_2) {
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      DataEvent = _unresolved_3.DataEvent;
    }, function (_unresolved_4) {
      EventMgr = _unresolved_4.EventMgr;
    }, function (_unresolved_5) {
      ButtonPlus = _unresolved_5.ButtonPlus;
    }, function (_unresolved_6) {
      PopupUI = _unresolved_6.PopupUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5ffea2hGLNJxYi6IGhc1V3u", "FriendCellUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node', 'RichText']);

      ({
        ccclass,
        property
      } = _decorator); //参考 BagItem

      _export("FriendCellUI", FriendCellUI = (_dec = ccclass('FriendCellUI'), _dec2 = property(Label), _dec3 = property(RichText), _dec4 = property(Label), _dec5 = property(Node), _dec6 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec7 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec8 = property(Node), _dec9 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec10 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec11 = property(Node), _dec12 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec(_class = (_class2 = class FriendCellUI extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "txtName", _descriptor, this);

          _initializerDefineProperty(this, "txtPower", _descriptor2, this);

          _initializerDefineProperty(this, "txtOnline", _descriptor3, this);

          _initializerDefineProperty(this, "node1", _descriptor4, this);

          _initializerDefineProperty(this, "btnGet", _descriptor5, this);

          _initializerDefineProperty(this, "btnSend", _descriptor6, this);

          _initializerDefineProperty(this, "node2", _descriptor7, this);

          _initializerDefineProperty(this, "btnIgnore", _descriptor8, this);

          _initializerDefineProperty(this, "btnAgree", _descriptor9, this);

          _initializerDefineProperty(this, "node3", _descriptor10, this);

          _initializerDefineProperty(this, "btnApply", _descriptor11, this);
        }

        onLoad() {
          this.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onClick, this);
          this.setPowerText(4567500);
          this.btnGet.addClick(this.onGet, this);
          this.btnSend.addClick(this.onSend, this);
          this.btnIgnore.addClick(this.onIgnore, this);
          this.btnAgree.addClick(this.onAgree, this);
          this.btnApply.addClick(this.onApply, this);
        }

        setPowerText(powerValue) {
          var currentText = this.txtPower.string;
          var prefixColorRegex = /<color=([^>]+)>战力：<\/color>/;
          var prefixMatch = currentText.match(prefixColorRegex);
          var prefixColorTag = "<color=#00ff00>"; // 默认颜色

          var valueColorRegex = /<color=([^>]+)>\d+<\/color>/;
          var valueMatch = currentText.match(valueColorRegex);
          var valueColorTag = "<color=#0fffff>"; // 默认颜色

          if (prefixMatch && prefixMatch[1]) {
            prefixColorTag = "<color=" + prefixMatch[1] + ">";
          }

          if (valueMatch && valueMatch[1]) {
            valueColorTag = "<color=" + valueMatch[1] + ">";
          }

          var formattedValue;

          if (powerValue >= 1000) {
            formattedValue = (powerValue / 1000).toFixed(1) + "K";
          } else {
            formattedValue = powerValue.toString();
          }

          this.txtPower.string = prefixColorTag + "\u6218\u529B\uFF1A</color>" + valueColorTag + formattedValue + "</color>";
        }

        setLastOnlineTime(lastOnlineTimestamp) {
          if (lastOnlineTimestamp === 0) {
            this.txtOnline.string = "在线";
            return;
          }

          var currentTimestamp = Math.floor(Date.now() / 1000); // 当前时间戳（秒）

          var diffSeconds = currentTimestamp - lastOnlineTimestamp;

          if (diffSeconds < 0) {
            // 时间戳异常（未来时间）
            this.txtOnline.string = "";
            return;
          }

          var diffMinutes = Math.floor(diffSeconds / 60);
          var diffHours = Math.floor(diffSeconds / 3600);

          if (diffMinutes < 1) {
            this.txtOnline.string = "刚刚";
          } else if (diffHours < 1) {
            this.txtOnline.string = diffMinutes + "\u5206\u949F\u524D";
          } else if (diffHours < 24) {
            this.txtOnline.string = diffHours + "\u5C0F\u65F6\u524D";
          } else {
            var diffDays = Math.floor(diffSeconds / 86400);
            if (diffDays > 7) diffDays = 7;
            this.txtOnline.string = diffDays + "\u5929\u524D";
          }
        }

        onDestroy() {}

        onClick() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).BattleItemClick, this.getComponentInChildren(Label).string);
        }

        onGet() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, "收了");
        }

        onSend() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, "送了");
        }

        onIgnore() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, "忽略了");
        }

        onAgree() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, "同意了");
        }

        onApply() {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PopupUI === void 0 ? (_reportPossibleCrUseOfPopupUI({
            error: Error()
          }), PopupUI) : PopupUI, "申请了");
        } //cell的容器去手动调用


        onListRender(listItem, row) {
          listItem.name = "" + row;
        }

        setType(type) {
          if (type === 1) {
            this.node1.active = true;
            this.node2.active = false;
            this.node3.active = false;
          } else if (type === 2) {
            this.node1.active = false;
            this.node2.active = true;
            this.node3.active = false;
          } else if (type === 3) {
            this.node1.active = false;
            this.node2.active = false;
            this.node3.active = true;
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "txtName", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "txtPower", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "txtOnline", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "node1", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "btnGet", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "btnSend", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "node2", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "btnIgnore", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class2.prototype, "btnAgree", [_dec10], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class2.prototype, "node3", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class2.prototype, "btnApply", [_dec12], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3f2fcb0237d4605c0e21622c2042ed4a8b7b2b13.js.map