{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts"], "names": ["BulletManager", "instantiate", "<PERSON><PERSON><PERSON><PERSON>", "log", "Prefab", "SpriteAtlas", "SingletonBase", "GameIns", "Bullet", "GameResourceList", "MyApp", "_preloadFinish", "_mainStage", "mainBulletAtlas", "enemyBulletAtlas", "enemyComAtlas", "m_unUseBullets", "Map", "selfBullets", "enemyBullets", "m_nodeCatch", "_bulletCount", "_testIds", "preLoad", "stage", "battleManager", "addLoadCount", "spriteAtlases", "atlas_enemyBullet", "atlas_mainBullet", "resMgr", "load", "error", "atlas", "checkLoadFinish", "prefabs", "atlas_enemyBullet1", "clear", "releaseAssetByForce", "battleInit", "get", "bullets", "i", "bullet", "createNewBullet", "node", "push", "set", "removeAll", "getConfig", "bulletID", "includes", "console", "lubanTables", "TbBullet", "getBullet", "isEnemy", "removeBullet", "removeFromEntity", "remove", "forceDestroy", "removeEnemyBullets", "dieRemove", "for<PERSON>ach", "bulletList", "count", "length", "destroyAll", "parent", "destroy", "bulletType", "pop", "prefab", "loadAsync", "getComponent", "getSaveType", "config", "bustyle", "id"], "mappings": ";;;wLAWaA,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAXJC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,O,OAAAA,O;AAASC,MAAAA,G,OAAAA,G;AAAKC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACnCC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,O,iBAAAA,O;;AAEFC,MAAAA,M;;AACAC,MAAAA,gB;;AACEC,MAAAA,K,iBAAAA,K;;;;;;;;;+BAKIV,a,GAAN,MAAMA,aAAN;AAAA;AAAA,0CAAwD;AAAA;AAAA;AAAA,eAE3DW,cAF2D,GAE1C,KAF0C;AAAA,eAG3DC,UAH2D,GAG9C,CAH8C;AAAA,eAI3DC,eAJ2D,GAIrB,IAJqB;AAAA,eAK3DC,gBAL2D,GAKpB,IALoB;AAAA,eAM3DC,aAN2D,GAMvB,IANuB;AAAA,eAO3DC,cAP2D,GAO1C,IAAIC,GAAJ,EAP0C;AAAA,eAQ3DC,WAR2D,GAQ7C,IAAID,GAAJ,EAR6C;AAAA,eAS3DE,YAT2D,GAS5C,IAAIF,GAAJ,EAT4C;AAAA,eAU3DG,WAV2D,GAUpC,EAVoC;AAAA,eAW3DC,YAX2D,GAW5C,GAX4C;AAAA,eAY3DC,QAZ2D,GAYtC,EAZsC;AAAA;;AAc3DC,QAAAA,OAAO,CAACC,KAAK,GAAG,CAAT,EAAY;AACf;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AAAsC;AACtC,gBAAMC,aAAa,GAAG,CAAC;AAAA;AAAA,oDAAiBC,iBAAlB,EAAqC;AAAA;AAAA,oDAAiBC,gBAAtD,CAAtB;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,IAAb,CAAkBJ,aAAlB,EAAiCtB,WAAjC,EAA8C,CAAC2B,KAAD,EAAcC,KAAd,KAAsC;AAChF,iBAAKlB,aAAL,GAAqBkB,KAAK,CAAC,CAAD,CAA1B;AACA,iBAAKpB,eAAL,GAAuBoB,KAAK,CAAC,CAAD,CAA5B;AACA;AAAA;AAAA,oCAAQR,aAAR,CAAsBS,eAAtB;AAAwC;AAC3C,WAJD;AAMA;AAAA;AAAA,kCAAQT,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA,gBAAMS,OAAO,GAAG,CACZ;AAAA;AAAA,oDAAiB3B,MADL,CAAhB;AAIA;AAAA;AAAA,8BAAMsB,MAAN,CAAaC,IAAb,CAAkBI,OAAlB,EAA2B/B,MAA3B,EAAmC,MAAM;AACrC;AAAA;AAAA,oCAAQqB,aAAR,CAAsBS,eAAtB;AAAwC;AAC3C,WAFD;;AAIA,cAAIV,KAAK,GAAG,CAAZ,EAAe;AACX;AAAA;AAAA,oCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,CAAnC;AACA;AAAA;AAAA,gCAAMI,MAAN,CAAaC,IAAb,CAAkB;AAAA;AAAA,sDAAiBK,kBAAnC,EAAuD/B,WAAvD,EAAmE,CAAC2B,KAAD,EAAeC,KAAf,KAAsC;AACrG,mBAAKnB,gBAAL,GAAwBmB,KAAxB;AACA;AAAA;AAAA,sCAAQR,aAAR,CAAsBS,eAAtB;AAAwC;AAC3C,aAHD;AAIH;AACJ;;AAEDG,QAAAA,KAAK,GAAG;AACJ,cAAI,KAAKvB,gBAAT,EAA2B;AACvB;AAAA;AAAA,gCAAMgB,MAAN,CAAaQ,mBAAb,CAAiC,KAAKxB,gBAAtC;AACA,iBAAKA,gBAAL,GAAwB,IAAxB;AACH;AACJ;;AAEe,cAAVyB,UAAU,GAAG;AACf,cAAI,CAAC,KAAKvB,cAAL,CAAoBwB,GAApB,CAAwB,IAAxB,CAAL,EAAoC;AAChC,kBAAMC,OAAO,GAAG,EAAhB;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,GAApB,EAAyBA,CAAC,EAA1B,EAA8B;AAC1B,oBAAMC,MAAM,GAAI,MAAM,KAAKC,eAAL,CAAqB,CAArB,EAAwB,IAAxB,CAAtB;;AACA,kBAAID,MAAM,CAACE,IAAP,IAAe3C,OAAO,CAACyC,MAAD,CAA1B,EAAoC;AAChCF,gBAAAA,OAAO,CAACK,IAAR,CAAaH,MAAb;AACH;AACJ;;AACD,iBAAK3B,cAAL,CAAoB+B,GAApB,CAAwB,IAAxB,EAA8BN,OAA9B;AACH;;AACD,eAAKO,SAAL;AACH;;AAEDC,QAAAA,SAAS,CAACC,QAAD,EAAkB;AACvB,cAAI,CAAC,KAAK5B,QAAL,CAAc6B,QAAd,CAAuBD,QAAvB,CAAL,EAAuC;AACnCE,YAAAA,OAAO,CAACjD,GAAR,CAAY,WAAZ,EAAyB+C,QAAzB;;AACA,iBAAK5B,QAAL,CAAcwB,IAAd,CAAmBI,QAAnB;AACH;;AACD,iBAAO;AAAA;AAAA,8BAAMG,WAAN,CAAkBC,QAAlB,CAA2Bd,GAA3B,CAA+BU,QAA/B,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACmB,cAATK,SAAS,CAACL,QAAD,EAAmBM,OAAnB,EAAqC,CAChD;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACd,MAAD,EAAiBe,gBAAyB,GAAG,IAA7C,EAAmD,CAC3D;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,MAAM,CAAChB,MAAD,EAAgBiB,YAAY,GAAG,KAA/B,EAAsC,CACxC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AACD;AACJ;AACA;AACA;AACA;;;AAEIC,QAAAA,kBAAkB,CAACC,SAAS,GAAG,KAAb,EAAoB;AAClC,cAAIA,SAAJ,EAAe;AACX,iBAAK3C,YAAL,CAAkB4C,OAAlB,CAA2BC,UAAD,IAAgB;AACtC,kBAAIC,KAAK,GAAG,CAAZ;;AACA,qBAAOD,UAAU,CAACE,MAAX,GAAoB,CAApB,IAAyBD,KAAK,GAAG,IAAxC,EAA8C;AAC1CD,gBAAAA,UAAU,CAAC,CAAD,CAAV,CAAcF,SAAd;AACAG,gBAAAA,KAAK;AACR;AACJ,aAND;AAOH,WARD,MAQO;AACH,iBAAK9C,YAAL,CAAkB4C,OAAlB,CAA2BC,UAAD,IAAgB;AACtCA,cAAAA,UAAU,CAACD,OAAX,CAAoBpB,MAAD,IAAmB;AAClC,qBAAKgB,MAAL,CAAYhB,MAAZ,EAAoB,IAApB;AACH,eAFD;AAGH,aAJD;AAKH;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIK,QAAAA,SAAS,CAACmB,UAAU,GAAG,KAAd,EAAqBP,YAAY,GAAG,KAApC,EAA2C;AAChD,eAAKC,kBAAL;AAEA,eAAK3C,WAAL,CAAiB6C,OAAjB,CAA0BC,UAAD,IAAgB;AACrCA,YAAAA,UAAU,CAACD,OAAX,CAAoBpB,MAAD,IAAoB;AACnC,mBAAKgB,MAAL,CAAYhB,MAAZ,EAAoB,IAApB;AACH,aAFD;AAGH,WAJD;AAMA,eAAK3B,cAAL,CAAoB+C,OAApB,CAA6BC,UAAD,IAAgB;AACxCA,YAAAA,UAAU,CAACD,OAAX,CAAoBpB,MAAD,IAAoB;AACnC,kBAAI;AACA,oBAAI,CAACwB,UAAD,IAAe,KAAK/C,WAAL,CAAiB8C,MAAjB,GAA0B,KAAK7C,YAAlD,EAAgE;AAC5D,sBAAInB,OAAO,CAACyC,MAAD,CAAX,EAAqB;AACjBA,oBAAAA,MAAM,CAACE,IAAP,CAAYuB,MAAZ,GAAqB,IAArB;AACA,yBAAKhD,WAAL,CAAiB0B,IAAjB,CAAsBH,MAAtB;AACH;AACJ,iBALD,MAKO;AACHA,kBAAAA,MAAM,CAACE,IAAP,CAAYwB,OAAZ;AACH;AACJ,eATD,CASE,OAAOrC,KAAP,EAAc;AACZ7B,gBAAAA,GAAG,CAAC,wBAAD,CAAH;AACH;AACJ,aAbD;AAcH,WAfD;;AAiBA,cAAIgE,UAAJ,EAAgB;AACZ,iBAAK/C,WAAL,CAAiB2C,OAAjB,CAA0BlB,IAAD,IAAU;AAC/B,kBAAIA,IAAI,CAACA,IAAT,EAAeA,IAAI,CAACA,IAAL,CAAUwB,OAAV;AAClB,aAFD;AAGA,iBAAKjD,WAAL,GAAmB,EAAnB;AACH;;AAED,eAAKJ,cAAL,CAAoBqB,KAApB;AACA,eAAKlB,YAAL,CAAkBkB,KAAlB;AACA,eAAKnB,WAAL,CAAiBmB,KAAjB,GAnCgD,CAoChD;AACH;AACD;AACJ;AACA;AACA;AACA;AACA;;;AACyB,cAAfO,eAAe,CAAC0B,UAAD,EAAoBd,OAApB,EAAqD;AACtE,cAAIb,MAAJ;;AAEA,kBAAQ2B,UAAR;AACI;AAAS;AACL,kBAAI,KAAKlD,WAAL,CAAiB8C,MAAjB,GAA0B,CAA9B,EAAiC;AAC7BvB,gBAAAA,MAAM,GAAG,KAAKvB,WAAL,CAAiBmD,GAAjB,EAAT;AACH,eAFD,MAEO;AACH,oBAAIC,MAAM,GAAG,MAAM;AAAA;AAAA,oCAAM1C,MAAN,CAAa2C,SAAb,CAAuB;AAAA;AAAA,0DAAiBjE,MAAxC,EAAgDJ,MAAhD,CAAnB;AACAuC,gBAAAA,MAAM,GAAG1C,WAAW,CAACuE,MAAD,CAAX,CAAoBE,YAApB;AAAA;AAAA,qCAAT;AACH;;AAPT;;AAUA,iBAAO/B,MAAP;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIgC,QAAAA,WAAW,CAACC,MAAD,EAAuBpB,OAAvB,EAAyC;AAChD,cAAIA,OAAJ,EAAa;AACT,gBAAIoB,MAAM,CAACC,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,gBAAID,MAAM,CAACC,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,gBAAID,MAAM,CAACC,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,mBAAO,IAAP;AACH,WALD,MAKO;AACH,gBAAID,MAAM,CAACC,OAAP,KAAmB,EAAnB,IAAyBD,MAAM,CAACC,OAAP,KAAmB,EAAhD,EAAoD,OAAO,KAAP;AACpD,gBAAID,MAAM,CAACC,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,gBAAID,MAAM,CAACC,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,gBAAID,MAAM,CAACC,OAAP,KAAmB,EAAvB,EAA2B,OAAO,KAAP;AAC3B,mBAAQ,IAAGD,MAAM,CAACE,EAAG,EAArB;AACH;AACJ;;AA/S0D,O", "sourcesContent": ["import { instantiate, isValid, log, Prefab, SpriteAtlas } from \"cc\";\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport { GameFunc } from \"../GameFunc\";\r\nimport Bullet from \"../ui/bullet/Bullet\";\r\nimport GameResourceList from \"../const/GameResourceList\";\r\nimport { MyApp } from \"../../MyApp\";\r\nimport { Bullet as BulletConfig } from \"db://assets/scripts/AutoGen/Luban/schema\"\r\nimport EnemyPlane from \"../ui/plane/enemy/EnemyPlane\";\r\nimport BossPlane from \"../ui/plane/boss/BossPlane\";\r\n\r\nexport class BulletManager extends SingletonBase<BulletManager>{\r\n\r\n    _preloadFinish = false;\r\n    _mainStage = 0;\r\n    mainBulletAtlas: SpriteAtlas | null = null;\r\n    enemyBulletAtlas: SpriteAtlas | null = null;\r\n    enemyComAtlas: SpriteAtlas | null = null;\r\n    m_unUseBullets = new Map();\r\n    selfBullets = new Map();\r\n    enemyBullets = new Map();\r\n    m_nodeCatch:Bullet[] = [];\r\n    _bulletCount = 120;\r\n    _testIds: number[] = [];\r\n\r\n    preLoad(stage = 0) {\r\n        GameIns.battleManager.addLoadCount(1);;\r\n        const spriteAtlases = [GameResourceList.atlas_enemyBullet, GameResourceList.atlas_mainBullet];\r\n        MyApp.resMgr.load(spriteAtlases, SpriteAtlas, (error: Error,atlas:SpriteAtlas[]) => {\r\n            this.enemyComAtlas = atlas[0];\r\n            this.mainBulletAtlas = atlas[1];\r\n            GameIns.battleManager.checkLoadFinish();;\r\n        });\r\n\r\n        GameIns.battleManager.addLoadCount(1);\r\n        const prefabs = [\r\n            GameResourceList.Bullet\r\n        ];\r\n\r\n        MyApp.resMgr.load(prefabs, Prefab, () => {\r\n            GameIns.battleManager.checkLoadFinish();;\r\n        });\r\n\r\n        if (stage > 0) {\r\n            GameIns.battleManager.addLoadCount(1);\r\n            MyApp.resMgr.load(GameResourceList.atlas_enemyBullet1, SpriteAtlas,(error: Error, atlas: SpriteAtlas) => {\r\n                this.enemyBulletAtlas = atlas;\r\n                GameIns.battleManager.checkLoadFinish();;\r\n            });\r\n        }\r\n    }\r\n\r\n    clear() {\r\n        if (this.enemyBulletAtlas) {\r\n            MyApp.resMgr.releaseAssetByForce(this.enemyBulletAtlas);\r\n            this.enemyBulletAtlas = null;\r\n        }\r\n    }\r\n\r\n    async battleInit() {\r\n        if (!this.m_unUseBullets.get(\"e1\")) {\r\n            const bullets = [];\r\n            for (let i = 0; i < 150; i++) {\r\n                const bullet =  await this.createNewBullet(1, true);\r\n                if (bullet.node && isValid(bullet)) {\r\n                    bullets.push(bullet);\r\n                }\r\n            }\r\n            this.m_unUseBullets.set(\"e1\", bullets);\r\n        }\r\n        this.removeAll();\r\n    }\r\n\r\n    getConfig(bulletID:number) {\r\n        if (!this._testIds.includes(bulletID)) {\r\n            console.log(\"getBullet\", bulletID);\r\n            this._testIds.push(bulletID);\r\n        }\r\n        return MyApp.lubanTables.TbBullet.get(bulletID);\r\n    }\r\n\r\n    /**\r\n     * 获取子弹实例\r\n     * @param {number} bulletID 子弹ID\r\n     * @param {boolean} isEnemy 是否为敌方子弹\r\n     * @returns {Bullet} 子弹实例\r\n     */\r\n    async getBullet(bulletID: number, isEnemy: boolean) {\r\n        // try {\r\n        //     const config = this.getConfig(bulletID);\r\n        //     if (!config) return null;\r\n\r\n        //     const saveType = this.getSaveType(config, isEnemy);\r\n        //     let unusedBullets = this.m_unUseBullets.get(saveType);\r\n        //     let bullet : Bullet;\r\n\r\n        //     if (unusedBullets && unusedBullets.length > 0) {\r\n        //         bullet = unusedBullets.pop();\r\n        //         if (isValid(bullet) && isValid(bullet.node)) {\r\n        //             bullet.create(bulletID);\r\n        //             bullet.enemy = isEnemy;\r\n        //         } else {\r\n        //             log(\"bullet not valid\", bulletID, saveType);\r\n        //             bullet = await this.createNewBullet(config.bustyle, isEnemy);\r\n        //             bullet.bulletID = bulletID;\r\n        //             bullet.enemy = isEnemy;\r\n        //             bullet.create(bulletID);\r\n        //         }\r\n        //     } else {\r\n        //         bullet = await this.createNewBullet(config.bustyle, isEnemy);\r\n        //         bullet.bulletID = bulletID;\r\n        //         bullet.enemy = isEnemy;\r\n        //         bullet.create(bulletID);\r\n        //     }\r\n\r\n        //     bullet.new_uuid = GameFunc.uuid;\r\n\r\n        //     if (bullet.enemy) {\r\n        //         if (this.enemyBullets.has(bullet.bulletID)) {\r\n        //             this.enemyBullets.get(bullet.bulletID).push(bullet);\r\n        //         } else {\r\n        //             this.enemyBullets.set(bullet.bulletID, [bullet]);\r\n        //         }\r\n        //     } else {\r\n        //         if (this.selfBullets.has(bullet.bulletID)) {\r\n        //             this.selfBullets.get(bullet.bulletID).push(bullet);\r\n        //         } else {\r\n        //             this.selfBullets.set(bullet.bulletID, [bullet]);\r\n        //         }\r\n        //     }\r\n\r\n        //     bullet.refresh();\r\n        //     bullet.node.setPosition(0, 0);\r\n\r\n        //     return bullet;\r\n        // } catch (error) {\r\n        //     const config = this.getConfig(bulletID) as BulletConfig;\r\n        //     const saveType = this.getSaveType(config, isEnemy);\r\n        //     log(\"getBullet error\", 0, bulletID, isEnemy, config ? config.bustyle : 0, saveType);\r\n        //     return null;\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 移除子弹\r\n     * @param {Bullet} bullet 子弹实例\r\n     * @param {boolean} removeFromEntity 是否从实体中移除\r\n     */\r\n    removeBullet(bullet: Bullet, removeFromEntity: boolean = true) {\r\n        // let bulletList;\r\n        // let index;\r\n\r\n        // if (bullet.enemy) {\r\n        //     bulletList = this.enemyBullets.get(bullet.bulletID);\r\n        //     if (bulletList) {\r\n        //         index = bulletList.indexOf(bullet);\r\n        //         if (index >= 0) {\r\n        //             bulletList.splice(index, 1);\r\n        //             this.remove(bullet);\r\n        //         }\r\n        //     }\r\n        //     if (removeFromEntity && bullet.m_mainEntity) {\r\n        //         if (bullet.m_mainEntity instanceof EnemyPlane || bullet.m_mainEntity instanceof BossPlane) {\r\n        //             bullet.m_mainEntity.removeBullet(bullet);\r\n        //         }\r\n        //     }\r\n        // } else {\r\n        //     bulletList = this.selfBullets.get(bullet.bulletID);\r\n        //     if (bulletList) {\r\n        //         index = bulletList.indexOf(bullet);\r\n        //         if (index >= 0) {\r\n        //             bulletList.splice(index, 1);\r\n        //             this.remove(bullet);\r\n        //         } else {\r\n        //             log(\"b11 11111\");\r\n        //         }\r\n        //     }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 移除子弹并回收\r\n     * @param {Bullet} bullet 子弹实例\r\n     * @param {boolean} forceDestroy 是否强制销毁\r\n     */\r\n    remove(bullet:Bullet, forceDestroy = false) {\r\n        // bullet.removeAllComp();\r\n        // if (bullet.node) {\r\n        //     if (bullet.getType() !== 41) {\r\n        //         bullet.node.parent = null;\r\n        //         bullet.node.setScale(bullet.node.getScale().x,1);\r\n        //     }\r\n        //     const config = this.getConfig(bullet.bulletID) as BulletConfig;\r\n        //     const saveType = this.getSaveType(config, bullet.enemy);\r\n\r\n        //     if (this.m_unUseBullets.has(saveType)) {\r\n        //         if (isValid(bullet)) {\r\n        //             this.m_unUseBullets.get(saveType).push(bullet);\r\n        //         }\r\n        //     } else {\r\n        //         this.m_unUseBullets.set(saveType, [bullet]);\r\n        //     }\r\n        // } else if (isValid(bullet)) {\r\n        //     bullet.destroy();\r\n        // }\r\n    }\r\n    /**\r\n\r\n    /**\r\n     * 移除所有敌方子弹\r\n     * @param {boolean} dieRemove 是否调用子弹的死亡移除逻辑\r\n     */\r\n    removeEnemyBullets(dieRemove = false) {\r\n        if (dieRemove) {\r\n            this.enemyBullets.forEach((bulletList) => {\r\n                let count = 0;\r\n                while (bulletList.length > 0 && count < 9999) {\r\n                    bulletList[0].dieRemove();\r\n                    count++;\r\n                }\r\n            });\r\n        } else {\r\n            this.enemyBullets.forEach((bulletList) => {\r\n                bulletList.forEach((bullet:Bullet) => {\r\n                    this.remove(bullet, true);\r\n                });\r\n            });\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除所有子弹\r\n     * @param {boolean} destroyAll 是否销毁所有子弹\r\n     * @param {boolean} forceDestroy 是否强制销毁\r\n     */\r\n    removeAll(destroyAll = false, forceDestroy = false) {\r\n        this.removeEnemyBullets();\r\n\r\n        this.selfBullets.forEach((bulletList) => {\r\n            bulletList.forEach((bullet: Bullet) => {\r\n                this.remove(bullet, true);\r\n            });\r\n        });\r\n\r\n        this.m_unUseBullets.forEach((bulletList) => {\r\n            bulletList.forEach((bullet: Bullet) => {\r\n                try {\r\n                    if (!destroyAll && this.m_nodeCatch.length < this._bulletCount) {\r\n                        if (isValid(bullet)) {\r\n                            bullet.node.parent = null;\r\n                            this.m_nodeCatch.push(bullet);\r\n                        }\r\n                    } else {\r\n                        bullet.node.destroy();\r\n                    }\r\n                } catch (error) {\r\n                    log(\"bullet removeAll error\");\r\n                }\r\n            });\r\n        });\r\n\r\n        if (destroyAll) {\r\n            this.m_nodeCatch.forEach((node) => {\r\n                if (node.node) node.node.destroy();\r\n            });\r\n            this.m_nodeCatch = [];\r\n        }\r\n\r\n        this.m_unUseBullets.clear();\r\n        this.enemyBullets.clear();\r\n        this.selfBullets.clear();\r\n        // this.putAllStreak();\r\n    }\r\n    /**\r\n     * 创建新的子弹实例\r\n     * @param {number} bulletType 子弹类型\r\n     * @param {boolean} isEnemy 是否为敌方子弹\r\n     * @returns {Bullet} 子弹实例\r\n     */\r\n    async createNewBullet(bulletType:number, isEnemy:boolean):Promise<Bullet> {\r\n        let bullet: Bullet;\r\n\r\n        switch (bulletType) {\r\n            default: // 默认子弹\r\n                if (this.m_nodeCatch.length > 0) {\r\n                    bullet = this.m_nodeCatch.pop()!;\r\n                } else {\r\n                    let prefab = await MyApp.resMgr.loadAsync(GameResourceList.Bullet, Prefab);\r\n                    bullet = instantiate(prefab).getComponent(Bullet)!;\r\n                }\r\n        }\r\n\r\n        return bullet;\r\n    }\r\n\r\n    /**\r\n     * 获取子弹的保存类型\r\n     * @param {BulletConfig} config 子弹配置\r\n     * @param {boolean} isEnemy 是否为敌方子弹\r\n     * @returns {string} 保存类型\r\n     */\r\n    getSaveType(config: BulletConfig, isEnemy: boolean) {\r\n        if (isEnemy) {\r\n            if (config.bustyle === 23) return \"e23\";\r\n            if (config.bustyle === 26) return \"e26\";\r\n            if (config.bustyle === 39) return \"e39\";\r\n            return \"e1\";\r\n        } else {\r\n            if (config.bustyle === 27 || config.bustyle === 28) return \"f27\";\r\n            if (config.bustyle === 23) return \"f23\";\r\n            if (config.bustyle === 24) return \"f24\";\r\n            if (config.bustyle === 41) return \"f41\";\r\n            return `f${config.id}`;\r\n        }\r\n    }\r\n}"]}