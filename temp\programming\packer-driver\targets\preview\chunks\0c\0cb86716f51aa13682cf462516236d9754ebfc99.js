System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, instantiate, isValid, log, Prefab, SpriteAtlas, SingletonBase, GameIns, Bullet, GameResourceList, MyApp, BulletManager, _crd;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../ui/bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletConfig(extras) {
    _reporterNs.report("BulletConfig", "db://assets/scripts/AutoGen/Luban/schema", _context.meta, extras);
  }

  _export("BulletManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      instantiate = _cc.instantiate;
      isValid = _cc.isValid;
      log = _cc.log;
      Prefab = _cc.Prefab;
      SpriteAtlas = _cc.SpriteAtlas;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      Bullet = _unresolved_4.default;
    }, function (_unresolved_5) {
      GameResourceList = _unresolved_5.default;
    }, function (_unresolved_6) {
      MyApp = _unresolved_6.MyApp;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bab2e+sJTdO1q8jlmNS4sEf", "BulletManager", undefined);

      __checkObsolete__(['instantiate', 'isValid', 'log', 'Prefab', 'SpriteAtlas']);

      _export("BulletManager", BulletManager = class BulletManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        constructor() {
          super(...arguments);
          this._preloadFinish = false;
          this._mainStage = 0;
          this.mainBulletAtlas = null;
          this.enemyBulletAtlas = null;
          this.enemyComAtlas = null;
          this.m_unUseBullets = new Map();
          this.selfBullets = new Map();
          this.enemyBullets = new Map();
          this.m_nodeCatch = [];
          this._bulletCount = 120;
          this._testIds = [];
        }

        preLoad(stage) {
          if (stage === void 0) {
            stage = 0;
          }

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          ;
          var spriteAtlases = [(_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).atlas_enemyBullet, (_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).atlas_mainBullet];
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load(spriteAtlases, SpriteAtlas, (error, atlas) => {
            this.enemyComAtlas = atlas[0];
            this.mainBulletAtlas = atlas[1];
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
            ;
          });
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.addLoadCount(1);
          var prefabs = [(_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).Bullet];
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.load(prefabs, Prefab, () => {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.checkLoadFinish();
            ;
          });

          if (stage > 0) {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.addLoadCount(1);
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.load((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
              error: Error()
            }), GameResourceList) : GameResourceList).atlas_enemyBullet1, SpriteAtlas, (error, atlas) => {
              this.enemyBulletAtlas = atlas;
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).battleManager.checkLoadFinish();
              ;
            });
          }
        }

        clear() {
          if (this.enemyBulletAtlas) {
            (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.releaseAssetByForce(this.enemyBulletAtlas);
            this.enemyBulletAtlas = null;
          }
        }

        battleInit() {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (!_this.m_unUseBullets.get("e1")) {
              var bullets = [];

              for (var i = 0; i < 150; i++) {
                var bullet = yield _this.createNewBullet(1, true);

                if (bullet.node && isValid(bullet)) {
                  bullets.push(bullet);
                }
              }

              _this.m_unUseBullets.set("e1", bullets);
            }

            _this.removeAll();
          })();
        }

        getConfig(bulletID) {
          if (!this._testIds.includes(bulletID)) {
            console.log("getBullet", bulletID);

            this._testIds.push(bulletID);
          }

          return (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbBullet.get(bulletID);
        }
        /**
         * 获取子弹实例
         * @param {number} bulletID 子弹ID
         * @param {boolean} isEnemy 是否为敌方子弹
         * @returns {Bullet} 子弹实例
         */


        getBullet(bulletID, isEnemy) {// try {
          //     const config = this.getConfig(bulletID);
          //     if (!config) return null;
          //     const saveType = this.getSaveType(config, isEnemy);
          //     let unusedBullets = this.m_unUseBullets.get(saveType);
          //     let bullet : Bullet;
          //     if (unusedBullets && unusedBullets.length > 0) {
          //         bullet = unusedBullets.pop();
          //         if (isValid(bullet) && isValid(bullet.node)) {
          //             bullet.create(bulletID);
          //             bullet.enemy = isEnemy;
          //         } else {
          //             log("bullet not valid", bulletID, saveType);
          //             bullet = await this.createNewBullet(config.bustyle, isEnemy);
          //             bullet.bulletID = bulletID;
          //             bullet.enemy = isEnemy;
          //             bullet.create(bulletID);
          //         }
          //     } else {
          //         bullet = await this.createNewBullet(config.bustyle, isEnemy);
          //         bullet.bulletID = bulletID;
          //         bullet.enemy = isEnemy;
          //         bullet.create(bulletID);
          //     }
          //     bullet.new_uuid = GameFunc.uuid;
          //     if (bullet.enemy) {
          //         if (this.enemyBullets.has(bullet.bulletID)) {
          //             this.enemyBullets.get(bullet.bulletID).push(bullet);
          //         } else {
          //             this.enemyBullets.set(bullet.bulletID, [bullet]);
          //         }
          //     } else {
          //         if (this.selfBullets.has(bullet.bulletID)) {
          //             this.selfBullets.get(bullet.bulletID).push(bullet);
          //         } else {
          //             this.selfBullets.set(bullet.bulletID, [bullet]);
          //         }
          //     }
          //     bullet.refresh();
          //     bullet.node.setPosition(0, 0);
          //     return bullet;
          // } catch (error) {
          //     const config = this.getConfig(bulletID) as BulletConfig;
          //     const saveType = this.getSaveType(config, isEnemy);
          //     log("getBullet error", 0, bulletID, isEnemy, config ? config.bustyle : 0, saveType);
          //     return null;
          // }

          return _asyncToGenerator(function* () {})();
        }
        /**
         * 移除子弹
         * @param {Bullet} bullet 子弹实例
         * @param {boolean} removeFromEntity 是否从实体中移除
         */


        removeBullet(bullet, removeFromEntity) {// let bulletList;
          // let index;
          // if (bullet.enemy) {
          //     bulletList = this.enemyBullets.get(bullet.bulletID);
          //     if (bulletList) {
          //         index = bulletList.indexOf(bullet);
          //         if (index >= 0) {
          //             bulletList.splice(index, 1);
          //             this.remove(bullet);
          //         }
          //     }
          //     if (removeFromEntity && bullet.m_mainEntity) {
          //         if (bullet.m_mainEntity instanceof EnemyPlane || bullet.m_mainEntity instanceof BossPlane) {
          //             bullet.m_mainEntity.removeBullet(bullet);
          //         }
          //     }
          // } else {
          //     bulletList = this.selfBullets.get(bullet.bulletID);
          //     if (bulletList) {
          //         index = bulletList.indexOf(bullet);
          //         if (index >= 0) {
          //             bulletList.splice(index, 1);
          //             this.remove(bullet);
          //         } else {
          //             log("b11 11111");
          //         }
          //     }
          // }

          if (removeFromEntity === void 0) {
            removeFromEntity = true;
          }
        }
        /**
         * 移除子弹并回收
         * @param {Bullet} bullet 子弹实例
         * @param {boolean} forceDestroy 是否强制销毁
         */


        remove(bullet, forceDestroy) {// bullet.removeAllComp();
          // if (bullet.node) {
          //     if (bullet.getType() !== 41) {
          //         bullet.node.parent = null;
          //         bullet.node.setScale(bullet.node.getScale().x,1);
          //     }
          //     const config = this.getConfig(bullet.bulletID) as BulletConfig;
          //     const saveType = this.getSaveType(config, bullet.enemy);
          //     if (this.m_unUseBullets.has(saveType)) {
          //         if (isValid(bullet)) {
          //             this.m_unUseBullets.get(saveType).push(bullet);
          //         }
          //     } else {
          //         this.m_unUseBullets.set(saveType, [bullet]);
          //     }
          // } else if (isValid(bullet)) {
          //     bullet.destroy();
          // }

          if (forceDestroy === void 0) {
            forceDestroy = false;
          }
        }
        /**
          /**
         * 移除所有敌方子弹
         * @param {boolean} dieRemove 是否调用子弹的死亡移除逻辑
         */


        removeEnemyBullets(dieRemove) {
          if (dieRemove === void 0) {
            dieRemove = false;
          }

          if (dieRemove) {
            this.enemyBullets.forEach(bulletList => {
              var count = 0;

              while (bulletList.length > 0 && count < 9999) {
                bulletList[0].dieRemove();
                count++;
              }
            });
          } else {
            this.enemyBullets.forEach(bulletList => {
              bulletList.forEach(bullet => {
                this.remove(bullet, true);
              });
            });
          }
        }
        /**
         * 移除所有子弹
         * @param {boolean} destroyAll 是否销毁所有子弹
         * @param {boolean} forceDestroy 是否强制销毁
         */


        removeAll(destroyAll, forceDestroy) {
          if (destroyAll === void 0) {
            destroyAll = false;
          }

          if (forceDestroy === void 0) {
            forceDestroy = false;
          }

          this.removeEnemyBullets();
          this.selfBullets.forEach(bulletList => {
            bulletList.forEach(bullet => {
              this.remove(bullet, true);
            });
          });
          this.m_unUseBullets.forEach(bulletList => {
            bulletList.forEach(bullet => {
              try {
                if (!destroyAll && this.m_nodeCatch.length < this._bulletCount) {
                  if (isValid(bullet)) {
                    bullet.node.parent = null;
                    this.m_nodeCatch.push(bullet);
                  }
                } else {
                  bullet.node.destroy();
                }
              } catch (error) {
                log("bullet removeAll error");
              }
            });
          });

          if (destroyAll) {
            this.m_nodeCatch.forEach(node => {
              if (node.node) node.node.destroy();
            });
            this.m_nodeCatch = [];
          }

          this.m_unUseBullets.clear();
          this.enemyBullets.clear();
          this.selfBullets.clear(); // this.putAllStreak();
        }
        /**
         * 创建新的子弹实例
         * @param {number} bulletType 子弹类型
         * @param {boolean} isEnemy 是否为敌方子弹
         * @returns {Bullet} 子弹实例
         */


        createNewBullet(bulletType, isEnemy) {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            var bullet;

            switch (bulletType) {
              default:
                // 默认子弹
                if (_this2.m_nodeCatch.length > 0) {
                  bullet = _this2.m_nodeCatch.pop();
                } else {
                  var prefab = yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                    error: Error()
                  }), MyApp) : MyApp).resMgr.loadAsync((_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
                    error: Error()
                  }), GameResourceList) : GameResourceList).Bullet, Prefab);
                  bullet = instantiate(prefab).getComponent(_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
                    error: Error()
                  }), Bullet) : Bullet);
                }

            }

            return bullet;
          })();
        }
        /**
         * 获取子弹的保存类型
         * @param {BulletConfig} config 子弹配置
         * @param {boolean} isEnemy 是否为敌方子弹
         * @returns {string} 保存类型
         */


        getSaveType(config, isEnemy) {
          if (isEnemy) {
            if (config.bustyle === 23) return "e23";
            if (config.bustyle === 26) return "e26";
            if (config.bustyle === 39) return "e39";
            return "e1";
          } else {
            if (config.bustyle === 27 || config.bustyle === 28) return "f27";
            if (config.bustyle === 23) return "f23";
            if (config.bustyle === 24) return "f24";
            if (config.bustyle === 41) return "f41";
            return "f" + config.id;
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0cb86716f51aa13682cf462516236d9754ebfc99.js.map