System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, GameIns, Entity, FCollider, ColliderGroupType, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, Bullet;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "../base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../collider-system/FCollider", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      GameIns = _unresolved_2.GameIns;
    }, function (_unresolved_3) {
      Entity = _unresolved_3.default;
    }, function (_unresolved_4) {
      FCollider = _unresolved_4.default;
      ColliderGroupType = _unresolved_4.ColliderGroupType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "771e3z4lZJCFI7xLAXh7DiF", "Bullet", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", Bullet = (_dec = ccclass('Bullet'), _dec2 = property(_crd && FCollider === void 0 ? (_reportPossibleCrUseOfFCollider({
        error: Error()
      }), FCollider) : FCollider), _dec(_class = (_class2 = class Bullet extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "collideComp", _descriptor, this);

          this.enemy = false;
          this.attack = 0;
        }

        onLoad() {
          this.collideComp.isEnable = true;
        }

        initData(isEnemy, attack = 0, colliderGroupType = (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
          error: Error()
        }), ColliderGroupType) : ColliderGroupType).BULLET_SELF) {
          this.enemy = isEnemy;
          this.attack = attack;
          this.collideComp.groupType = colliderGroupType;
        }

        getAttack() {
          return this.attack;
        }
        /**
         * 播放子弹命中音效
         */


        playHurtAudio() {// if (this.m_config.hit.length > 0) {
          //     Bullet.playAudio('hit2');
          // }
        }

        onCollide(collider) {
          this.remove(true);
        }
        /**
         * 子弹超出屏幕处理
         */


        onOutScreen() {
          this.remove();
        }
        /**
         * 移除子弹
         * @param {boolean} force 是否强制移除
         */


        remove(force = false) {
          this.willRemove();
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).bulletManager.removeBullet(this);
        }
        /**
         * 子弹死亡移除
         */


        dieRemove() {
          this.willRemove();
        }
        /**
         * 子弹移除前的清理操作
         */


        willRemove() {
          if (this.collideComp) {
            this.collideComp.isEnable = false;
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "collideComp", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0327660c6417e6b430ccc7d906c2d0662723ff6f.js.map