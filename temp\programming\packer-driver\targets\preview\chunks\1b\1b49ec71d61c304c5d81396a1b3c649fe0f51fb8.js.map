{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "Component", "easing", "Label", "Node", "tween", "ButtonPlus", "ccclass", "property", "WheelSpinnerUI", "type", "isSpinning", "onLoad", "button", "addClick", "onButtonClick", "spin2", "targetIndex", "wheel", "angle", "anglePerSegment", "segments", "targetAngle", "angleLabel", "string", "toFixed", "to", "cubicOut", "call", "console", "log", "start", "randomIndex", "Math", "floor", "random"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;;AAC5CC,MAAAA,U,iBAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;gCAGjBS,c,WADZF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAAC;AAAEE,QAAAA,IAAI,EAAEN;AAAR,OAAD,C,UAMRI,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACL,KAAD,C,2BAXb,MACaM,cADb,SACoCR,SADpC,CAC8C;AAAA;AAAA;;AAAA;;AAEP;AAFO;;AAKZ;AALY;;AAAA;;AAAA,eAalCU,UAbkC,GAaZ,KAbY;AAAA;;AAaL;AAC3BC,QAAAA,MAAM,GAAS;AACrB,eAAKC,MAAL,CAAaC,QAAb,CAAsB,KAAKC,aAA3B,EAA0C,IAA1C;AACH,SAhByC,CAkB1C;;;AACOC,QAAAA,KAAK,CAACC,WAAD,EAA4B;AACpC,cAAI,KAAKN,UAAT,EAAqB;AACrB,eAAKA,UAAL,GAAkB,IAAlB,CAFoC,CAGpC;;AACA,eAAKO,KAAL,CAAYC,KAAZ,GAAoB,CAApB,CAJoC,CAKpC;;AACA,cAAMC,eAAe,GAAG,MAAM,KAAKC,QAAnC;AACA,cAAMC,WAAW,GAAG,CAAC,GAAD,GAAO,EAAP,GAAYF,eAAe,GAAGH,WAAlD,CAPoC,CAO2B;;AAE/D,eAAKM,UAAL,CAAiBC,MAAjB,GAA6BF,WAAW,CAACG,OAAZ,CAAoB,CAApB,CAA7B,UAToC,CAWpC;;AACApB,UAAAA,KAAK,CAAC,KAAKa,KAAN,CAAL,CACKQ,EADL,CACQ,GADR,EACa;AAAEP,YAAAA,KAAK,EAAEG;AAAT,WADb,EACqC;AAAEpB,YAAAA,MAAM,EAAEA,MAAM,CAACyB;AAAjB,WADrC,EAEKC,IAFL,CAEU,MAAM;AACR,iBAAKjB,UAAL,GAAkB,KAAlB;AACAkB,YAAAA,OAAO,CAACC,GAAR,gCAAoBb,WAAW,GAAG,CAAlC;AACH,WALL,EAMKc,KANL;AAOH,SAtCyC,CAuC1C;;;AACQhB,QAAAA,aAAa,GAAS;AAC1B,cAAMiB,WAAW,GAAGC,IAAI,CAACC,KAAL,CAAWD,IAAI,CAACE,MAAL,KAAgB,KAAKd,QAAhC,CAApB,CAD0B,CACqC;;AAC/D,eAAKL,KAAL,CAAWgB,WAAX;AACH;;AA3CyC,O;;;;;iBAEb,I;;mFAE5BxB,Q;;;;;iBAC0B,C;;;;;;;iBAGC,I;;;;;;;iBAGD,I", "sourcesContent": ["import { _decorator, Component, easing, Label, Node, tween } from 'cc';\r\nimport { ButtonPlus } from '../common/components/button/ButtonPlus';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('WheelSpinnerUI')\r\nexport class WheelSpinnerUI extends Component {\r\n    @property({ type: Node })\r\n    private wheel: Node | null = null; // 转盘节点\r\n\r\n    @property\r\n    private segments: number = 7; // 转盘分块数量\r\n\r\n    @property(ButtonPlus)\r\n    button: ButtonPlus | null = null;\r\n\r\n    @property(Label)\r\n    angleLabel: Label | null = null;\r\n\r\n    private isSpinning: boolean = false; // 是否正在转动\r\n    protected onLoad(): void {\r\n        this.button!.addClick(this.onButtonClick, this);\r\n    }\r\n\r\n    // 开始转动（顺时针）\r\n    public spin2(targetIndex: number): void {\r\n        if (this.isSpinning) return;\r\n        this.isSpinning = true;\r\n        // 重置角度为 0\r\n        this.wheel!.angle = 0;\r\n        // 计算目标角度（顺时针为负方向）\r\n        const anglePerSegment = 360 / this.segments;\r\n        const targetAngle = -360 * 15 - anglePerSegment * targetIndex; // 多转几圈（顺时针）\r\n\r\n        this.angleLabel!.string = `${targetAngle.toFixed(2)}°`;\r\n\r\n        // 使用 Tween 实现缓动动画\r\n        tween(this.wheel!)\r\n            .to(3.5, { angle: targetAngle }, { easing: easing.cubicOut })\r\n            .call(() => {\r\n                this.isSpinning = false;\r\n                console.log(`停止在第 ${targetIndex + 1} 格`);\r\n            })\r\n            .start();\r\n    }\r\n    // 测试用：点击按钮触发转动\r\n    private onButtonClick(): void {\r\n        const randomIndex = Math.floor(Math.random() * this.segments); // 随机选择一个格子\r\n        this.spin2(randomIndex);\r\n    }\r\n}\r\n\r\n\r\n"]}