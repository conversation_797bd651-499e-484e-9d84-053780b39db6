System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Node, tween, UIOpacity, instantiate, Tween, view, v3, size, Prefab, GameConst, GameIns, Bullet, EffectLayer, FBoxCollider, ColliderGroupType, MyApp, Plane, AttributeConst, GameResourceList, Emitter, PlaneBase, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, MainPlane;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfGameConst(extras) {
    _reporterNs.report("GameConst", "../../../const/GameConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEffectLayer(extras) {
    _reporterNs.report("EffectLayer", "../../layer/EffectLayer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "../../../collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneData(extras) {
    _reporterNs.report("PlaneData", "db://assets/bundles/common/script/data/plane/PlaneData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "db://assets/bundles/common/script/ui/Plane", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeConst(extras) {
    _reporterNs.report("AttributeConst", "db://assets/bundles/common/script/const/AttributeConst", _context.meta, extras);
  }

  function _reportPossibleCrUseOfAttributeData(extras) {
    _reporterNs.report("AttributeData", "db://assets/bundles/common/script/data/base/AttributeData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameResourceList(extras) {
    _reporterNs.report("GameResourceList", "../../../const/GameResourceList", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "../../../bullet/Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "../PlaneBase", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Node = _cc.Node;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
      instantiate = _cc.instantiate;
      Tween = _cc.Tween;
      view = _cc.view;
      v3 = _cc.v3;
      size = _cc.size;
      Prefab = _cc.Prefab;
    }, function (_unresolved_2) {
      GameConst = _unresolved_2.GameConst;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      Bullet = _unresolved_4.default;
    }, function (_unresolved_5) {
      EffectLayer = _unresolved_5.default;
    }, function (_unresolved_6) {
      FBoxCollider = _unresolved_6.default;
    }, function (_unresolved_7) {
      ColliderGroupType = _unresolved_7.ColliderGroupType;
    }, function (_unresolved_8) {
      MyApp = _unresolved_8.MyApp;
    }, function (_unresolved_9) {
      Plane = _unresolved_9.Plane;
    }, function (_unresolved_10) {
      AttributeConst = _unresolved_10.AttributeConst;
    }, function (_unresolved_11) {
      GameResourceList = _unresolved_11.default;
    }, function (_unresolved_12) {
      Emitter = _unresolved_12.Emitter;
    }, function (_unresolved_13) {
      PlaneBase = _unresolved_13.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "81e41b6hKRNqYJbS6uK9LDf", "MainPlane", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Sprite', 'Animation', 'Label', 'Vec2', 'tween', 'UIOpacity', 'instantiate', 'sp', 'UITransform', 'Tween', 'Color', 'view', 'v3', 'v2', 'SpriteFrame', 'size', 'SpriteAtlas', 'Prefab']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MainPlane", MainPlane = (_dec = ccclass("MainPlane"), _dec2 = property(Node), _dec3 = property(Node), _dec4 = property(Node), _dec(_class = (_class2 = class MainPlane extends (_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
        error: Error()
      }), PlaneBase) : PlaneBase) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "planeParent", _descriptor, this);

          _initializerDefineProperty(this, "NodeEmitter", _descriptor2, this);

          _initializerDefineProperty(this, "hpNode", _descriptor3, this);

          this.hpMidActin = null;
          // 血条动画
          this.m_screenDatas = [];
          // 屏幕数据
          this.m_moveEnable = true;
          // 是否允许移动
          this.emitterComp = null;
          // 发射器
          this._hurtActTime = 0;
          // 受伤动画时间
          this._hurtActDuration = 0.5;
          // 受伤动画持续时间
          this._planeData = null;
          //飞机数据
          this._plane = null;
        }

        //飞机显示节点
        onLoad() {
          this.collideComp = this.getComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider) || this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider);
          this.collideComp.init(this, size(40, 40)); // 初始化碰撞组件

          this.collideComp.groupType = (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).PLAYER;
          this.colliderEnabled = false;
        }

        start() {
          // 禁用射击
          this.setFireEnable(false);
        }

        update(dt) {
          this._hurtActTime += dt;
        }

        initPlane(planeData) {
          var _this$planeParent, _this$_planeData;

          this._planeData = planeData; //加载飞机显示

          var plane = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).planeMgr.getPlane(planeData);
          this._plane = plane.getComponent(_crd && Plane === void 0 ? (_reportPossibleCrUseOfPlane({
            error: Error()
          }), Plane) : Plane);
          (_this$planeParent = this.planeParent) == null || _this$planeParent.addChild(plane); //设置飞机发射组件

          this.setEmitter();
          this.curHp = (_this$_planeData = this._planeData) == null ? void 0 : _this$_planeData.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).MaxHP);
          ;
          this.maxHp = this.curHp;
        }

        setEmitter() {
          //后期根据飞机的数据，加载不同的发送组件预制体
          var path = (_crd && GameResourceList === void 0 ? (_reportPossibleCrUseOfGameResourceList({
            error: Error()
          }), GameResourceList) : GameResourceList).EmitterPrefabPath + "Emitter_main_01";
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadAsync(path, Prefab).then(prefab => {
            var _this$NodeEmitter;

            var node = instantiate(prefab);
            (_this$NodeEmitter = this.NodeEmitter) == null || _this$NodeEmitter.addChild(node);
            node.setPosition(0, 0);
            this.emitterComp = node.getComponent(_crd && Emitter === void 0 ? (_reportPossibleCrUseOfEmitter({
              error: Error()
            }), Emitter) : Emitter);
          });
        }

        initBattle() {
          this.node.active = true;
          this.colliderEnabled = false;
          this.updateHpUI();
        }
        /**
        * 主飞机入场动画
        */


        planeIn() {
          var self = this; // 设置初始位置和状态

          this.planeParent.setScale(1, 1);
          var posY = -view.getVisibleSize().height - 80;
          this.node.setPosition(0, posY);
          this.setFireEnable(false);
          this.setMoveAble(false);
          Tween.stopAllByTarget(this.node);
          var frame = (_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
            error: Error()
          }), GameConst) : GameConst).ActionFrameTime / (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.animSpeed; // 飞机入场动画

          this.scheduleOnce(() => {
            var targetY = -view.getVisibleSize().height * 0.7;
            var targetX = this.node.position.x;
            tween(this.node).to(20 * frame, {
              position: v3(targetX, targetY - 17)
            }).to(11 * frame, {
              position: v3(targetX, targetY + 57)
            }).to(10 * frame, {
              position: v3(targetX, targetY + 76)
            }).to(27 * frame, {
              position: v3(targetX, targetY)
            }).call(() => {
              self.begine();
            }).start();
            tween(this.planeParent).to(20 * frame, {
              scale: v3(1.9, 1.9)
            }).to(11 * frame, {
              scale: v3(1.4, 1.4)
            }).to(10 * frame, {
              scale: v3(1, 1)
            }).to(27 * frame, {
              scale: v3(1, 1)
            }).start();

            if (this.hpNode) {
              tween(this.hpNode.getComponent(UIOpacity)).to(0, {
                opacity: 0
              }).delay(31 * frame).to(10 * frame, {
                opacity: 255
              }).start();
            }
          }, 7 * frame);
        }
        /**
         * 退出战斗
         */


        battleQuit() {}
        /**
         * 碰撞处理
         * @param {Object} collision 碰撞对象
         */


        onCollide(collision) {
          var damage = 0;

          if (collision.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
            error: Error()
          }), Bullet) : Bullet)) {
            damage = collision.entity.getAttack();
          }

          if (damage > 0) {
            this.hurt(damage);
          }
        }
        /**
         * 控制飞机移动
         * @param {number} moveX 水平方向的移动量
         * @param {number} moveY 垂直方向的移动量
         */


        onControl(posX, posY) {
          if (!(_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.planeFightData.die && this.m_moveEnable) {
            // 限制飞机移动范围
            posX = Math.min(360, posX);
            posX = Math.max(-360, posX);
            posY = Math.min(0, posY);
            posY = Math.max(-(_crd && GameConst === void 0 ? (_reportPossibleCrUseOfGameConst({
              error: Error()
            }), GameConst) : GameConst).ViewHeight, posY);
            this.node.setPosition(posX, posY);
          }
        }

        relife() {
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).gameDataManager.reviveCount += 1; // 增加复活次数
          // this.playRelifeAim(); // 播放复活动画

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.planeFightData.die = false; // 设置飞机为非死亡状态

          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.planeFightData.revive = true; // 设置复活状态

          this.scheduleOnce(() => {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.planeFightData.revive = false;
          }, 0.5);
          this.curHp = this.maxHp; // 恢复满血

          this.updateHpUI();
          ; // 触发血量更新事件
        }
        /**
         * 获取攻击力
         * @returns {number} 当前攻击力
         */


        getAttack() {
          var _this$_planeData2;

          return (_this$_planeData2 = this._planeData) == null ? void 0 : _this$_planeData2.getFinalAttributeByKey((_crd && AttributeConst === void 0 ? (_reportPossibleCrUseOfAttributeConst({
            error: Error()
          }), AttributeConst) : AttributeConst).Attack);
        }

        toDie() {
          if (!super.toDie()) {
            return false;
          } // 设置玩家状态为死亡


          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).mainPlaneManager.planeFightData.die = true; // 播放死亡动画

          this._playDieAnim();

          return true;
        } //实现父类的方法


        playHurtAnim() {
          if (this._hurtActTime > this._hurtActDuration) {
            this._hurtActTime = 0; // 显示红屏效果

            (_crd && EffectLayer === void 0 ? (_reportPossibleCrUseOfEffectLayer({
              error: Error()
            }), EffectLayer) : EffectLayer).me.showRedScreen();
          }
        }
        /**
         * 播放死亡动画
         */


        _playDieAnim() {
          // this.blast!.node!.getComponent(UIOpacity)!.opacity = 255; // 显示爆炸效果
          // this.blast!.setCompleteListener(this._dieAnimEnd.bind(this)); // 设置动画完成回调
          // this.blast!.setAnimation(0, "play", false); // 播放爆炸动画
          (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).battleManager.battleFail();
        }
        /**
         * 设置飞机是否可移动
         * @param {boolean} enable 是否可移动
         */


        setMoveAble(enable) {
          this.m_moveEnable = enable;
        }

        setFireEnable(enable) {// for (let i = 0; i < this.m_fires.length; i++) {
          //     const fire = this.m_fires[i];
          //     fire.isEnabled = enable;
          // }
        }
        /**
         * 开始战斗
         * @param {boolean} isContinue 是否继续战斗
         */


        begine(isContinue) {
          if (isContinue === void 0) {
            isContinue = false;
          }

          if (isContinue) {
            this.setFireEnable(true);
            this.setMoveAble(true);
            this.colliderEnabled = true;
          } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.onPlaneIn();
          }
        }

        get attribute() {
          return this._planeData;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "planeParent", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "NodeEmitter", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "hpNode", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3f1fb1b0ed938bdce99b77de634e782c06fd0829.js.map