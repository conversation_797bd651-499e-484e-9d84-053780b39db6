System.register(["__unresolved_0", "cc", "cc/env", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Sprite, Color, EDITOR, ObjectPool, Movable, eSpriteDefaultFacing, BulletSystem, EventGroupContext, PropertyContainer, FCollider, ColliderGroupType, FBoxCollider, Entity, eEntityTag, BulletProperty, _dec, _dec2, _dec3, _dec4, _dec5, _class2, _class3, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, executeInEditMode, Bullet;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBulletData(extras) {
    _reporterNs.report("BulletData", "../data/bullet/BulletData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfObjectPool(extras) {
    _reporterNs.report("ObjectPool", "./ObjectPool", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMovable(extras) {
    _reporterNs.report("Movable", "../move/Movable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeSpriteDefaultFacing(extras) {
    _reporterNs.report("eSpriteDefaultFacing", "../move/Movable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "./BulletSystem", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroup(extras) {
    _reporterNs.report("EventGroup", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventGroupContext(extras) {
    _reporterNs.report("EventGroupContext", "./EventGroup", _context.meta, extras);
  }

  function _reportPossibleCrUseOfProperty(extras) {
    _reporterNs.report("Property", "./PropertyContainer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPropertyContainer(extras) {
    _reporterNs.report("PropertyContainer", "./PropertyContainer", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEmitter(extras) {
    _reporterNs.report("Emitter", "./Emitter", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletConfig(extras) {
    _reporterNs.report("BulletConfig", "db://assets/scripts/AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "db://assets/scripts/Game/collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "db://assets/scripts/Game/collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "db://assets/scripts/Game/collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEntity(extras) {
    _reporterNs.report("Entity", "db://assets/scripts/Game/ui/base/Entity", _context.meta, extras);
  }

  function _reportPossibleCrUseOfeEntityTag(extras) {
    _reporterNs.report("eEntityTag", "db://assets/scripts/Game/ui/base/Entity", _context.meta, extras);
  }

  _export("BulletProperty", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Sprite = _cc.Sprite;
      Color = _cc.Color;
    }, function (_ccEnv) {
      EDITOR = _ccEnv.EDITOR;
    }, function (_unresolved_2) {
      ObjectPool = _unresolved_2.ObjectPool;
    }, function (_unresolved_3) {
      Movable = _unresolved_3.Movable;
      eSpriteDefaultFacing = _unresolved_3.eSpriteDefaultFacing;
    }, function (_unresolved_4) {
      BulletSystem = _unresolved_4.BulletSystem;
    }, function (_unresolved_5) {
      EventGroupContext = _unresolved_5.EventGroupContext;
    }, function (_unresolved_6) {
      PropertyContainer = _unresolved_6.PropertyContainer;
    }, function (_unresolved_7) {
      FCollider = _unresolved_7.default;
      ColliderGroupType = _unresolved_7.ColliderGroupType;
    }, function (_unresolved_8) {
      FBoxCollider = _unresolved_8.default;
    }, function (_unresolved_9) {
      Entity = _unresolved_9.default;
    }, function (_unresolved_10) {
      eEntityTag = _unresolved_10.eEntityTag;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "21904rFEfNCRbgYx1LE+M6P", "Bullet", undefined);

      __checkObsolete__(['_decorator', 'misc', 'Component', 'Node', 'Sprite', 'Color', 'CCString']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("BulletProperty", BulletProperty = class BulletProperty extends (_crd && PropertyContainer === void 0 ? (_reportPossibleCrUseOfPropertyContainer({
        error: Error()
      }), PropertyContainer) : PropertyContainer) {
        // 是否追踪目标
        constructor() {
          super();
          this.duration = void 0;
          // 子弹持续时间(超出后销毁回收)
          this.delayDestroy = void 0;
          // 延迟销毁时间
          this.attack = void 0;
          // 子弹伤害
          this.speed = void 0;
          // 子弹速度
          this.speedAngle = void 0;
          // 子弹速度角度
          this.acceleration = void 0;
          // 子弹加速度
          this.accelerationAngle = void 0;
          // 子弹加速度角度
          this.scale = void 0;
          // 子弹缩放
          this.color = void 0;
          // 子弹颜色
          this.defaultFacing = void 0;
          // 子弹初始朝向
          this.isDestroyOutScreen = void 0;
          // 是否超出屏幕销毁
          this.isDestructive = void 0;
          // 是否可被破坏
          this.isDestructiveOnHit = void 0;
          // 命中时是否被销毁
          this.isFacingMoveDir = void 0;
          // 是否面向移动方向
          this.isTrackingTarget = void 0;
          this.duration = this.addProperty(0, 6000);
          this.delayDestroy = this.addProperty(1, 0);
          this.attack = this.addProperty(2, 1);
          this.speed = this.addProperty(3, 600);
          this.speedAngle = this.addProperty(4, 0);
          this.acceleration = this.addProperty(5, 0);
          this.accelerationAngle = this.addProperty(6, 0);
          this.scale = this.addProperty(7, 1);
          this.color = this.addProperty(8, Color.WHITE);
          this.defaultFacing = this.addProperty(9, (_crd && eSpriteDefaultFacing === void 0 ? (_reportPossibleCrUseOfeSpriteDefaultFacing({
            error: Error()
          }), eSpriteDefaultFacing) : eSpriteDefaultFacing).Up);
          this.isDestroyOutScreen = this.addProperty(10, true);
          this.isDestructive = this.addProperty(11, false);
          this.isDestructiveOnHit = this.addProperty(12, false);
          this.isFacingMoveDir = this.addProperty(13, false);
          this.isTrackingTarget = this.addProperty(14, false);
        }

        resetFromData(data) {
          this.duration.value = data.duration.eval();
          this.delayDestroy.value = data.delayDestroy.eval();
          this.speed.value = data.speed.eval(); // this.speedAngle.value = data.speedAngle.eval(); 

          this.acceleration.value = data.acceleration.eval();
          this.accelerationAngle.value = data.accelerationAngle.eval();
          this.scale.value = data.scale.eval(); // this.color.value = data.color.eval(); 

          this.isDestroyOutScreen.value = data.isDestroyOutScreen;
          this.isDestructive.value = data.isDestructive;
          this.isDestructiveOnHit.value = data.isDestructiveOnHit;
          this.isFacingMoveDir.value = data.isFacingMoveDir;
          this.isTrackingTarget.value = data.isTrackingTarget;
        }

        copyFrom(other) {
          this.forEachProperty((k, prop) => {
            prop.value = other.getPropertyValue(k);
          });
        }

        clear() {
          // Clear all listeners
          this.forEachProperty((k, prop) => prop.clear());
        }

      }); // 子弹 Bullet
      // 如何集成到项目里? 


      _export("Bullet", Bullet = (_dec = ccclass('Bullet'), _dec2 = executeInEditMode(true), _dec3 = property({
        type: _crd && Movable === void 0 ? (_reportPossibleCrUseOfMovable({
          error: Error()
        }), Movable) : Movable,
        displayName: "移动组件"
      }), _dec4 = property({
        type: Sprite,
        displayName: "子弹精灵"
      }), _dec5 = property({
        type: _crd && FCollider === void 0 ? (_reportPossibleCrUseOfFCollider({
          error: Error()
        }), FCollider) : FCollider,
        displayName: '碰撞组件'
      }), _dec(_class2 = _dec2(_class2 = (_class3 = class Bullet extends (_crd && Entity === void 0 ? (_reportPossibleCrUseOfEntity({
        error: Error()
      }), Entity) : Entity) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "mover", _descriptor, this);

          _initializerDefineProperty(this, "bulletSprite", _descriptor2, this);

          _initializerDefineProperty(this, "collider", _descriptor3, this);

          this.isAlive = false;
          this.elapsedTime = 0;
          // 子弹存活时间
          this.emitter = void 0;
          this.bulletData = void 0;
          // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里
          this.prop = new BulletProperty();
          this.eventGroups = [];
          this.m_config = null;
        }

        onLoad() {
          if (!this.mover) {
            var _this$getComponent;

            this.mover = (_this$getComponent = this.getComponent(_crd && Movable === void 0 ? (_reportPossibleCrUseOfMovable({
              error: Error()
            }), Movable) : Movable)) == null ? void 0 : _this$getComponent.addComponent(_crd && Movable === void 0 ? (_reportPossibleCrUseOfMovable({
              error: Error()
            }), Movable) : Movable);
          }

          this.mover.onBecomeInvisibleCallback = () => {
            if (this.prop.isDestroyOutScreen.value) {
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).onDestroyBullet(this);
            }
          };

          if (!this.collider) {
            var boxCollider = this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
              error: Error()
            }), FBoxCollider) : FBoxCollider);
            this.collider = boxCollider;
          }

          this.prop.defaultFacing.value = this.mover.defaultFacing; // listen to property changes

          this.prop.isFacingMoveDir.on(value => {
            this.mover.isFacingMoveDir = value;
          });
          this.prop.isTrackingTarget.on(value => {
            this.mover.isTrackingTarget = value;
          });
          this.prop.speed.on(value => {
            this.mover.speed = value;
          });
          this.prop.speedAngle.on(value => {
            this.mover.speedAngle = value;
          });
          this.prop.acceleration.on(value => {
            this.mover.acceleration = value;
          });
          this.prop.accelerationAngle.on(value => {
            this.mover.accelerationAngle = value;
          });
          this.prop.scale.on(value => {
            this.node.setScale(value, value, value);
          });
          this.prop.color.on(value => {
            if (this.bulletSprite) {
              this.bulletSprite.color = value;
            }
          });
        }

        onCreate(emitter, bulletID) {
          this.isAlive = true;
          this.elapsedTime = 0;
          this.emitter = emitter;
          this.bulletData = emitter.bulletData; // TODO: 创建entity的时候，设置正确的tag.

          var ent = emitter.getEntity();

          if (ent) {
            var isShootFromEnemy = ent.hasTag((_crd && eEntityTag === void 0 ? (_reportPossibleCrUseOfeEntityTag({
              error: Error()
            }), eEntityTag) : eEntityTag).Enemy) ? true : false;
            this.collider.initBaseData(ent);
            this.collider.groupType = isShootFromEnemy ? (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
              error: Error()
            }), ColliderGroupType) : ColliderGroupType).BULLET_ENEMY : (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
              error: Error()
            }), ColliderGroupType) : ColliderGroupType).BULLET_SELF;
            this.collider.isEnable = true;
            this.addTag(isShootFromEnemy ? (_crd && eEntityTag === void 0 ? (_reportPossibleCrUseOfeEntityTag({
              error: Error()
            }), eEntityTag) : eEntityTag).EnemyBullet : (_crd && eEntityTag === void 0 ? (_reportPossibleCrUseOfeEntityTag({
              error: Error()
            }), eEntityTag) : eEntityTag).PlayerBullet);
          }

          this.resetProperties();
        }

        destroySelf() {
          if (!this.node || !this.node.isValid) return;
          this.prop.clear();

          if (EDITOR) {
            this.node.destroy();
          } else {
            (_crd && ObjectPool === void 0 ? (_reportPossibleCrUseOfObjectPool({
              error: Error()
            }), ObjectPool) : ObjectPool).returnNode(this.node);
          }
        }

        resetProperties() {
          if (!this.emitter) return;
          this.prop.copyFrom(this.emitter.bulletProp);
          this.prop.notifyAll(true);
        }

        resetEventGroups() {
          // create event groups here
          if (this.bulletData && this.bulletData.eventGroupData.length > 0) {
            var ctx = new (_crd && EventGroupContext === void 0 ? (_reportPossibleCrUseOfEventGroupContext({
              error: Error()
            }), EventGroupContext) : EventGroupContext)();
            ctx.bullet = this;
            ctx.emitter = this.emitter;

            for (var eventName of this.bulletData.eventGroupData) {
              (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
                error: Error()
              }), BulletSystem) : BulletSystem).createBulletEventGroup(ctx, eventName);
            }
          }
        }

        tick(dt) {
          if (!this.isAlive) return;
          this.elapsedTime += dt;

          if (this.elapsedTime > this.prop.duration.value) {
            (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
              error: Error()
            }), BulletSystem) : BulletSystem).onDestroyBullet(this);
            return;
          } // 毫秒 -> 秒


          this.mover.tick(dt / 1000);
          this.prop.notifyAll();
        }

        willDestroy() {
          this.isAlive = false;

          if (this.eventGroups && this.eventGroups.length > 0) {
            this.eventGroups.forEach(group => group.stop()); // stop all event groups before destroying the bullet itself.

            this.eventGroups = []; // clear the event groups array
          }

          this.collider.isEnable = false;
          this.removeAllComp();
          this.clearTags();

          if (this.prop.delayDestroy && this.prop.delayDestroy.value > 0) {
            this.scheduleOnce(() => {
              this.destroySelf();
            }, this.prop.delayDestroy.value);
          } else {
            this.destroySelf();
          }
        }

        onCollide(collider) {
          this.remove();
        }

        remove() {
          (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).onDestroyBullet(this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class3.prototype, "mover", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      }), _descriptor2 = _applyDecoratedDescriptor(_class3.prototype, "bulletSprite", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class3.prototype, "collider", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class3)) || _class2) || _class2));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=deddf590dfec05ffc54869364dc6a032327e0911.js.map