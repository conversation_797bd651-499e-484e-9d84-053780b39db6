System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, Node, ProgressBar, Sprite, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, ProgressPanel;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      Node = _cc.Node;
      ProgressBar = _cc.ProgressBar;
      Sprite = _cc.Sprite;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "58a74Gx2WNCho7+HxpvhgWA", "ProgressPanel", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node', 'ProgressBar', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("ProgressPanel", ProgressPanel = (_dec = ccclass('ProgressPanel'), _dec2 = property(Node), _dec3 = property(Node), _dec4 = property(Node), _dec5 = property(ProgressBar), _dec6 = property(Node), _dec(_class = (_class2 = class ProgressPanel extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "rewardParentNode", _descriptor, this);

          _initializerDefineProperty(this, "progressNumberParentNode", _descriptor2, this);

          _initializerDefineProperty(this, "progressStartNumber", _descriptor3, this);

          _initializerDefineProperty(this, "progress", _descriptor4, this);

          _initializerDefineProperty(this, "countDownParentNode", _descriptor5, this);

          this._rewardNodes = [];
          this._progressNumberNodes = [];
          this._statusColors = ["#A5A5A5", "#FFCE33"];
          this._numbers = ["20", "40", "60", "80", "100"];
        }

        onLoad() {
          var _this$rewardParentNod, _this$progressNumberP;

          (_this$rewardParentNod = this.rewardParentNode) == null || _this$rewardParentNod.children.forEach(node => {
            this._rewardNodes.push(node);
          });
          (_this$progressNumberP = this.progressNumberParentNode) == null || _this$progressNumberP.children.forEach(node => {
            this._progressNumberNodes.push(node);
          });
        }

        init() {
          this._rewardNodes.forEach((node, index) => {
            this.renderReward(node, index);
          });

          this._progressNumberNodes.forEach((node, index) => {
            this.renderProgressNumber(node, index);
          });
        }

        renderReward(node, index) {
          var icon = node.getComponentsInChildren(Sprite)[0];
          var mask = node.getComponentsInChildren(Sprite)[1];
        }

        renderProgressNumber(node, index) {
          var number = node.getComponentInChildren(Label);
          var bg = node.getComponentInChildren(Sprite);
          number.string = this._numbers[index];
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "rewardParentNode", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "progressNumberParentNode", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "progressStartNumber", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "progress", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "countDownParentNode", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=78ff22544d5ec9dcba0f95705d15cec70e48565f.js.map