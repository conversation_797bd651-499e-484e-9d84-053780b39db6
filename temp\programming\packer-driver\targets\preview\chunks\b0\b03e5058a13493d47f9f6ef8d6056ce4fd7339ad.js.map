{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts"], "names": ["_decorator", "BundleName", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "ShopUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getBundleName", "HomeShop", "onLoad", "onShow", "onHide", "onClose", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OACX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;wBAGjBM,M,WADZF,OAAO,CAAC,QAAD,C,gBAAR,MACaE,MADb;AAAA;AAAA,4BACmC;AACX,eAANC,MAAM,GAAW;AAAE,iBAAO,kBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AACpC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,QAAlB;AAA4B;;AAE1DC,QAAAA,MAAM,GAAS,CAExB;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAhB8B,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { BundleName } from 'db://assets/bundles/Bundle';\nimport { BaseUI, UILayer } from 'db://assets/scripts/ui/UIMgr';\nconst { ccclass, property } = _decorator;\n\n@ccclass('ShopUI')\nexport class ShopUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/ShopUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    public static getBundleName(): string { return BundleName.HomeShop }\n\n    protected onLoad(): void {\n\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}