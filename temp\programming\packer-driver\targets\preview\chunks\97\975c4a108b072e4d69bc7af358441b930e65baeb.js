System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12", "__unresolved_13", "__unresolved_14", "__unresolved_15"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BundleName, IBundleEntry, MyApp, UIMgr, logDebug, EventMgr, HomeUIEvent, BottomTab, BottomUI, HomeUI, TopUI, PlaneUI, ShopUI, SkyIslandUI, TalentUI, StoryUI, _dec, _class, _crd, ccclass, CommonEntry;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfIBundleEntry(extras) {
    _reporterNs.report("IBundleEntry", "db://assets/bundles/Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "./event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUIEvent(extras) {
    _reporterNs.report("HomeUIEvent", "./event/HomeUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomTab(extras) {
    _reporterNs.report("BottomTab", "./ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomUI(extras) {
    _reporterNs.report("BottomUI", "./ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUI(extras) {
    _reporterNs.report("HomeUI", "./ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTopUI(extras) {
    _reporterNs.report("TopUI", "./ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUI(extras) {
    _reporterNs.report("PlaneUI", "./ui/plane/PlaneUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfShopUI(extras) {
    _reporterNs.report("ShopUI", "./ui/shop/ShopUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkyIslandUI(extras) {
    _reporterNs.report("SkyIslandUI", "./ui/skyisland/SkyIslandUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTalentUI(extras) {
    _reporterNs.report("TalentUI", "./ui/talent/TalentUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfStoryUI(extras) {
    _reporterNs.report("StoryUI", "./ui/story/StoryUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
      IBundleEntry = _unresolved_2.IBundleEntry;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      UIMgr = _unresolved_4.UIMgr;
    }, function (_unresolved_5) {
      logDebug = _unresolved_5.logDebug;
    }, function (_unresolved_6) {
      EventMgr = _unresolved_6.EventMgr;
    }, function (_unresolved_7) {
      HomeUIEvent = _unresolved_7.HomeUIEvent;
    }, function (_unresolved_8) {
      BottomTab = _unresolved_8.BottomTab;
    }, function (_unresolved_9) {
      BottomUI = _unresolved_9.BottomUI;
    }, function (_unresolved_10) {
      HomeUI = _unresolved_10.HomeUI;
    }, function (_unresolved_11) {
      TopUI = _unresolved_11.TopUI;
    }, function (_unresolved_12) {
      PlaneUI = _unresolved_12.PlaneUI;
    }, function (_unresolved_13) {
      ShopUI = _unresolved_13.ShopUI;
    }, function (_unresolved_14) {
      SkyIslandUI = _unresolved_14.SkyIslandUI;
    }, function (_unresolved_15) {
      TalentUI = _unresolved_15.TalentUI;
    }, function (_unresolved_16) {
      StoryUI = _unresolved_16.StoryUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "da61ekdwgZH35AizU/MwbIF", "CommonEntry", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass
      } = _decorator);

      _export("CommonEntry", CommonEntry = (_dec = ccclass('CommonEntry'), _dec(_class = class CommonEntry extends (_crd && IBundleEntry === void 0 ? (_reportPossibleCrUseOfIBundleEntry({
        error: Error()
      }), IBundleEntry) : IBundleEntry) {
        initEntry() {
          var _this = this;

          return _asyncToGenerator(function* () {
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("CommonEntry", "initEntry");
            yield (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
              error: Error()
            }), BundleName) : BundleName).Home);
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
              error: Error()
            }), HomeUI) : HomeUI);
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
              error: Error()
            }), BottomUI) : BottomUI);
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
              error: Error()
            }), TopUI) : TopUI); //暂时这样 后面再优化

            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).on((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).Leave, () => {
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).closeUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
                error: Error()
              }), HomeUI) : HomeUI);
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).closeUI(_crd && BottomUI === void 0 ? (_reportPossibleCrUseOfBottomUI({
                error: Error()
              }), BottomUI) : BottomUI); //这里会把下面的主界面都关闭  

              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).closeUI(_crd && TopUI === void 0 ? (_reportPossibleCrUseOfTopUI({
                error: Error()
              }), TopUI) : TopUI);
            });
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).Home, (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).get(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
              error: Error()
            }), HomeUI) : HomeUI));

            _this.preloadHomeSubBundles();
          })();
        }

        preloadHomeSubBundles() {
          //异步加载其他bundle
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomePlane).then( /*#__PURE__*/_asyncToGenerator(function* () {
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && PlaneUI === void 0 ? (_reportPossibleCrUseOfPlaneUI({
              error: Error()
            }), PlaneUI) : PlaneUI);
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).Plane, (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).get(_crd && PlaneUI === void 0 ? (_reportPossibleCrUseOfPlaneUI({
              error: Error()
            }), PlaneUI) : PlaneUI));
          }));
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeTalent).then( /*#__PURE__*/_asyncToGenerator(function* () {
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && TalentUI === void 0 ? (_reportPossibleCrUseOfTalentUI({
              error: Error()
            }), TalentUI) : TalentUI);
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).Talent, (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).get(_crd && TalentUI === void 0 ? (_reportPossibleCrUseOfTalentUI({
              error: Error()
            }), TalentUI) : TalentUI));
          }));
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeShop).then( /*#__PURE__*/_asyncToGenerator(function* () {
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && ShopUI === void 0 ? (_reportPossibleCrUseOfShopUI({
              error: Error()
            }), ShopUI) : ShopUI);
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).Shop, (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).get(_crd && ShopUI === void 0 ? (_reportPossibleCrUseOfShopUI({
              error: Error()
            }), ShopUI) : ShopUI));
          }));
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeSkyIsland).then( /*#__PURE__*/_asyncToGenerator(function* () {
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && SkyIslandUI === void 0 ? (_reportPossibleCrUseOfSkyIslandUI({
              error: Error()
            }), SkyIslandUI) : SkyIslandUI);
            (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
              error: Error()
            }), EventMgr) : EventMgr).emit((_crd && HomeUIEvent === void 0 ? (_reportPossibleCrUseOfHomeUIEvent({
              error: Error()
            }), HomeUIEvent) : HomeUIEvent).BottomTabRegister, (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).SkyIsLand, (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).get(_crd && SkyIslandUI === void 0 ? (_reportPossibleCrUseOfSkyIslandUI({
              error: Error()
            }), SkyIslandUI) : SkyIslandUI));
          }));
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).resMgr.loadBundle((_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomeStory).then( /*#__PURE__*/_asyncToGenerator(function* () {
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).loadUI(_crd && StoryUI === void 0 ? (_reportPossibleCrUseOfStoryUI({
              error: Error()
            }), StoryUI) : StoryUI);
          }));
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=975c4a108b072e4d69bc7af358441b930e65baeb.js.map