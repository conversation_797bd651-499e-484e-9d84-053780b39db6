System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCInteger, Label, Sprite, BundleName, BaseUI, UILayer, DataEvent, EventMgr, ButtonPlus, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _crd, ccclass, property, RogueSelectIcon;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../../../../../../scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../../../../../../scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../../event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCInteger = _cc.CCInteger;
      Label = _cc.Label;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      BaseUI = _unresolved_3.BaseUI;
      UILayer = _unresolved_3.UILayer;
    }, function (_unresolved_4) {
      DataEvent = _unresolved_4.DataEvent;
    }, function (_unresolved_5) {
      EventMgr = _unresolved_5.EventMgr;
    }, function (_unresolved_6) {
      ButtonPlus = _unresolved_6.ButtonPlus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "64be6jJf4VLq58vffIl0y0l", "RogueSelectIcon", undefined);

      __checkObsolete__(['_decorator', 'CCInteger', 'Label', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("RogueSelectIcon", RogueSelectIcon = (_dec = ccclass("RogueSelectIcon"), _dec2 = property(Sprite), _dec3 = property(Label), _dec4 = property(Label), _dec5 = property(Label), _dec6 = property(Sprite), _dec7 = property(Sprite), _dec8 = property(CCInteger), _dec(_class = (_class2 = class RogueSelectIcon extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "rogueIcon", _descriptor, this);

          _initializerDefineProperty(this, "rogueName", _descriptor2, this);

          _initializerDefineProperty(this, "rogueDesc", _descriptor3, this);

          _initializerDefineProperty(this, "rogueQua", _descriptor4, this);

          _initializerDefineProperty(this, "selectIcon", _descriptor5, this);

          _initializerDefineProperty(this, "delIcon", _descriptor6, this);

          _initializerDefineProperty(this, "index", _descriptor7, this);

          this.status = 1;
        }

        static getUrl() {
          return "prefab/ui/fight/RogueSelectIcon";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home;
        }

        onLoad() {
          this.selectIcon.node.active = false;
          this.delIcon.node.active = false;
          this.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onSelect, this);
          this.delIcon.getComponentInChildren(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onExcludeClick, this);
        }

        onExcludeClick() {
          this.node.active = false;
        }

        onSelect() {
          if (this.status == 1) {
            if (this.selectIcon.node.active) {
              (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
                error: Error()
              }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
                error: Error()
              }), DataEvent) : DataEvent).RogueSelectClick, 0);
            } else {
              (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
                error: Error()
              }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
                error: Error()
              }), DataEvent) : DataEvent).RogueSelectClick, this.index);
            }
          } else if (this.status == 2) {
            if (this.delIcon.node.active) {
              (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
                error: Error()
              }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
                error: Error()
              }), DataEvent) : DataEvent).RogueSelectClick, 0);
            } else {
              (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
                error: Error()
              }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
                error: Error()
              }), DataEvent) : DataEvent).RogueSelectClick, this.index);
            }
          }
        }

        updateStatus(status) {
          this.status = status;
        }

        updateActive(selectIndex) {
          if (this.status == 1) {
            this.delIcon.node.active = false;

            if (selectIndex == this.index) {
              this.selectIcon.node.active = true;
            } else {
              this.selectIcon.node.active = false;
            }
          } else if (this.status == 2) {
            this.selectIcon.node.active = false;

            if (selectIndex == this.index) {
              this.delIcon.node.active = true;
            } else {
              this.delIcon.node.active = false;
            }
          }
        }

        onDestroy() {}

        onShow() {
          return _asyncToGenerator(function* () {})();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "rogueIcon", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "rogueName", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "rogueDesc", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "rogueQua", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "selectIcon", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "delIcon", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "index", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fd81f257005648a162191647ddc942ace77684d9.js.map