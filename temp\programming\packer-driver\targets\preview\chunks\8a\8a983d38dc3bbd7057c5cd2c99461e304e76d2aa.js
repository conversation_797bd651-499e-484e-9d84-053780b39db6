System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, v3, DragButton, GameIns, logDebug, BaseUI, UILayer, _dec, _class, _crd, ccclass, property, MBoomUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfDragButton(extras) {
    _reporterNs.report("DragButton", "../../../bundles/common/script/ui/common/components/button/DragButton", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../Game/GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "../../Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "../UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "../UIMgr", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      v3 = _cc.v3;
    }, function (_unresolved_2) {
      DragButton = _unresolved_2.DragButton;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      logDebug = _unresolved_4.logDebug;
    }, function (_unresolved_5) {
      BaseUI = _unresolved_5.BaseUI;
      UILayer = _unresolved_5.UILayer;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5242f36HohPJ4HIOwEWC5Mq", "MBoomUI", undefined);

      __checkObsolete__(['_decorator', 'v3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MBoomUI", MBoomUI = (_dec = ccclass('MBoomUI'), _dec(_class = class MBoomUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        static getUrl() {
          return "ui/game/MBoomUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        start() {
          this.node.position = v3(-230, -400, 0);
          this.getComponent(_crd && DragButton === void 0 ? (_reportPossibleCrUseOfDragButton({
            error: Error()
          }), DragButton) : DragButton).addClick(this.onClick, this);
        }

        onClick() {
          return _asyncToGenerator(function* () {
            var _mainPlaneManager$mai;

            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("MBoomUI", "onClick", "aaaaaa");
            (_mainPlaneManager$mai = (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).mainPlaneManager.mainPlane) == null || _mainPlaneManager$mai.CastSkill(1);
          })();
        }

        onShow() {
          return _asyncToGenerator(function* () {})();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=8a983d38dc3bbd7057c5cd2c99461e304e76d2aa.js.map