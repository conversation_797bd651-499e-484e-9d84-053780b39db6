import { SingletonBase } from "../../core/base/SingletonBase";
import {GameEnum} from "../const/GameEnum";
import GameMapRun from "../ui/map/GameMapRun";
import EnemyPlane from "../ui/plane/enemy/EnemyPlane";


export class SceneManager extends SingletonBase<SceneManager> {

    /**
     * 获取场景中的位置
     * @param {Entity} entity 场景中的实体
     * @returns {Object} 包含 x 和 y 的位置对象
     */
    getScenePos(entity:any) {

        if (entity instanceof EnemyPlane) {
            let x = entity.node.position.x;
            let y = entity.node.position.y;
            let parentName = "enemyPlane";
            if (entity.type === GameEnum.EnemyType.Missile) {
                parentName = "enemyBullet";
            }

            let parent = entity.node.parent;
            let scaleX = 1;
            let scaleY = 1;
            let lastParent = null;

            while (parent && parent.name !== parentName) {
                scaleX *= parent.scale.x;
                scaleY *= parent.scale.y;
                x += parent.x;
                y += parent.y;
                lastParent = parent;
                parent = parent.parent;
            }

            if (lastParent) {
                x -= lastParent.x;
                y -= lastParent.y;
                x *= scaleX;
                y *= scaleY;
                x += lastParent.x;
                y += lastParent.y;
            } else {
                x *= scaleX;
                y *= scaleY;
            }

            return { x, y };
        }

        // if (entity instanceof BossUnitBase) {
        //     const scenePos = entity.getScenePos();
        //     return { x: scenePos.x, y: scenePos.y };
        // }

        // if (entity instanceof WinePlane) {
        //     return entity.scenePos;
        // }

        return { x: entity.node.x, y: entity.node.y };
    }

    /**
     * 获取场景层的速度
     * @param {Entity} entity 场景中的实体
     * @returns {number} 场景层的速度
     */
    getLayerSpeed(entity:any) {
        // if (entity instanceof EnemyBuild) {
        //     const sceneLayer = entity.sceneLayer;
        //     return this.getMapSpeed(sceneLayer);
        // }

        // if (entity instanceof EnemyTurret && entity.sceneLayer >= 0) {
        //     return this.getMapSpeed(entity.sceneLayer);
        // }

        return 0;
    }

    /**
     * 获取地图层的速度
     * @param {number} layer 地图层索引
     * @returns {number} 地图层的速度
     */
    getMapSpeed() {
        return GameMapRun.instance!.MapSpeed;
    }

    /**
     * 将地图坐标转换为战斗场景坐标
     * @param {number} x 地图坐标 x
     * @param {number} y 地图坐标 y
     * @param {number} layer 地图层索引
     * @returns {Object} 包含 x 和 y 的战斗场景坐标
     */
    mapToBattleScene(x:number, y:number, layer:any) {
        return {
            x,
            y: y - GameMapRun.instance!.ViewTop,
        };
    }

    /**
     * 获取场景中的实体
     * @param {Entity} entity 场景中的实体
     * @returns {Entity} 实体对象
     */
    getSceneEntity(entity:any) {
        let result = entity;

        return result;
    }
}