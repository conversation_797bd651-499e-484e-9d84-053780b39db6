System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, MyApp, GameIns, BaseComp, logInfo, logWarn, res, SkillComp, _crd;

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "../../base/BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfres(extras) {
    _reporterNs.report("res", "db://assets/scripts/AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlane(extras) {
    _reporterNs.report("Plane", "../PlaneBase", _context.meta, extras);
  }

  _export("default", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      MyApp = _unresolved_2.MyApp;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      BaseComp = _unresolved_4.default;
    }, function (_unresolved_5) {
      logInfo = _unresolved_5.logInfo;
      logWarn = _unresolved_5.logWarn;
    }, function (_unresolved_6) {
      res = _unresolved_6.res;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a0b80LTvOZBP779siDvr8m7", "SkillComp", undefined);

      _export("default", SkillComp = class SkillComp extends (_crd && BaseComp === void 0 ? (_reportPossibleCrUseOfBaseComp({
        error: Error()
      }), BaseComp) : BaseComp) {
        Cast(caster, skillID) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("Skill", "cast skill " + skillID);
          var skillData = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.Tbskill.get(skillID);

          if (!skillData) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("Skill", "cast skill " + skillID + " but config not found");
            return;
          }

          skillData.ApplyBuffs.forEach(applyBuff => {
            SkillComp.forEachByTargetType(caster, applyBuff.target, entity => {
              entity.buffComp.ApplyBuff(applyBuff.buffID);
            });
          });
        }

        static forEachByTargetType(caster, targetType, callback) {
          switch (targetType) {
            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).TargetType.Self:
              callback(caster);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).TargetType.Main:
              callback((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).mainPlaneManager.mainPlane);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).TargetType.MainFriendly:
              callback((_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).mainPlaneManager.mainPlane);
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).TargetType.Enemy:
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.enemies.forEach(plane => {
                callback(plane);
              });
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).bossManager.bosses.forEach(boss => {
                // boss.getUnits().forEach((unit) => {
                //     callback(unit);
                // });
                callback(boss);
              });
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).TargetType.BossEnemy:
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).bossManager.bosses.forEach(boss => {
                // boss.getUnits().forEach((unit) => {
                //     callback(unit);
                // });
                callback(boss);
              });
              break;

            case (_crd && res === void 0 ? (_reportPossibleCrUseOfres({
              error: Error()
            }), res) : res).TargetType.NormalEnemy:
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).enemyManager.enemies.forEach(plane => {
                callback(plane);
              });
              break;

            default:
              break;
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=84bd37479281472410fda7eafb80144779ce7cb1.js.map