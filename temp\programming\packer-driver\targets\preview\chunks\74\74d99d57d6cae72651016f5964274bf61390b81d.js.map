{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/move/Movable.ts"], "names": ["_decorator", "misc", "Component", "Enum", "Vec2", "Vec3", "UITransform", "BulletSystem", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "eSpriteDefaultFacing", "Movable", "type", "displayName", "isFacingMoveDir", "isTrackingTarget", "speed", "speedAngle", "turnSpeed", "acceleration", "accelerationAngle", "tiltSpeed", "tiltOffset", "target", "arrivalDistance", "_selfSize", "_position", "_tiltTime", "_basePosition", "_isVisible", "onBecomeVisibleCallback", "onBecomeInvisibleCallback", "isVisible", "onLoad", "uiTransform", "node", "getComponent", "self_size", "contentSize", "width", "height", "set", "getPosition", "tick", "dt", "velocityX", "Math", "cos", "velocityY", "sin", "targetPos", "currentPos", "directionX", "x", "directionY", "y", "distance", "sqrt", "desiredAngle", "atan2", "angleDiff", "normalizedAngleDiff", "trackingStrength", "maxTurnRate", "turnAmount", "min", "abs", "sign", "accelerationX", "accelerationY", "newVelocityX", "newVelocityY", "moveAngleRad", "perpX", "perpY", "tiltAmount", "setPosition", "movementAngle", "finalAngle", "defaultFacing", "setRotationFromEuler", "checkVisibility", "visibleSize", "worldBounds", "xMin", "xMax", "yMin", "yMax", "setVisible", "visible", "<PERSON><PERSON><PERSON><PERSON>", "Up"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,W,OAAAA,W;;AAI3DC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAHH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCR,I;OACzC;AAAES,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CZ,U;;sCAMrCa,oB,0BAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;eAAAA,oB;;;yBASCC,O,WAFZJ,OAAO,CAAC,SAAD,C,UAIHC,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAEZ,IAAI,CAACU,oBAAD,CAAX;AAAmCG,QAAAA,WAAW,EAAE;AAAhD,OAAD,C,gBAHZJ,iB,qBADD,MAEaE,OAFb,SAE6BZ,SAF7B,CAE2D;AAAA;AAAA;;AAAA;;AAAA,eAKhDe,eALgD,GAKrB,KALqB;AAKT;AALS,eAMhDC,gBANgD,GAMpB,KANoB;AAMT;AANS,eAOhDC,KAPgD,GAOhC,CAPgC;AAOT;AAPS,eAQhDC,UARgD,GAQ3B,CAR2B;AAQT;AARS,eAShDC,SATgD,GAS5B,EAT4B;AAST;AATS,eAUhDC,YAVgD,GAUzB,CAVyB;AAUT;AAVS,eAWhDC,iBAXgD,GAWpB,CAXoB;AAWT;AAE9C;AAbuD,eAchDC,SAdgD,GAc5B,CAd4B;AAcT;AAC9C;AAfuD,eAgBhDC,UAhBgD,GAgB3B,GAhB2B;AAgBP;AAhBO,eAkBhDC,MAlBgD,GAkB1B,IAlB0B;AAkBT;AAlBS,eAmBhDC,eAnBgD,GAmBtB,EAnBsB;AAmBT;AAnBS,eAqB/CC,SArB+C,GAqB7B,IAAIxB,IAAJ,EArB6B;AAAA,eAsB/CyB,SAtB+C,GAsB7B,IAAIxB,IAAJ,EAtB6B;AAAA,eAuB/CyB,SAvB+C,GAuB3B,CAvB2B;AAuBT;AAvBS,eAwB/CC,aAxB+C,GAwBzB,IAAI1B,IAAJ,EAxByB;AAwBT;AAxBS,eA0B/C2B,UA1B+C,GA0BzB,IA1ByB;AA6BvD;AA7BuD,eA8BhDC,uBA9BgD,GA8BL,IA9BK;AAAA,eA+BhDC,yBA/BgD,GA+BH,IA/BG;AAAA;;AA0BT;AAC1B,YAATC,SAAS,GAAG;AAAE,iBAAO,KAAKH,UAAZ;AAAyB;;AAKlD;AAEAI,QAAAA,MAAM,GAAG;AACL,cAAMC,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuBjC,WAAvB,CAApB;AACA,cAAMkC,SAAS,GAAGH,WAAW,GAAGA,WAAW,CAACI,WAAf,GAA6B;AAACC,YAAAA,KAAK,EAAE,CAAR;AAAWC,YAAAA,MAAM,EAAE;AAAnB,WAA1D;;AACA,eAAKf,SAAL,CAAegB,GAAf,CAAmBJ,SAAS,CAACE,KAAV,GAAkB,CAArC,EAAwCF,SAAS,CAACG,MAAV,GAAmB,CAA3D,EAHK,CAKL;;;AACA,eAAKL,IAAL,CAAUO,WAAV,CAAsB,KAAKd,aAA3B;AACH;;AAEMe,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B;AACA,eAAKT,IAAL,CAAUO,WAAV,CAAsB,KAAKhB,SAA3B,EAF0B,CAI1B;;AACA,cAAImB,SAAS,GAAG,KAAK7B,KAAL,GAAa8B,IAAI,CAACC,GAAL,CAAS1C,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAA7B;AACA,cAAI+B,SAAS,GAAG,KAAKhC,KAAL,GAAa8B,IAAI,CAACG,GAAL,CAAS5C,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAA7B;;AAEA,cAAI,KAAKF,gBAAL,IAAyB,KAAKQ,MAAlC,EAA0C;AACtC,gBAAM2B,SAAS,GAAG,KAAK3B,MAAL,CAAYmB,WAAZ,EAAlB;AACA,gBAAMS,UAAU,GAAG,KAAKhB,IAAL,CAAUO,WAAV,EAAnB,CAFsC,CAItC;;AACA,gBAAMU,UAAU,GAAGF,SAAS,CAACG,CAAV,GAAcF,UAAU,CAACE,CAA5C;AACA,gBAAMC,UAAU,GAAGJ,SAAS,CAACK,CAAV,GAAcJ,UAAU,CAACI,CAA5C;AACA,gBAAMC,QAAQ,GAAGV,IAAI,CAACW,IAAL,CAAUL,UAAU,GAAGA,UAAb,GAA0BE,UAAU,GAAGA,UAAjD,CAAjB;;AAEA,gBAAIE,QAAQ,GAAG,CAAf,EAAkB;AACd;AACA,kBAAME,YAAY,GAAGpD,gBAAgB,CAACwC,IAAI,CAACa,KAAL,CAAWL,UAAX,EAAuBF,UAAvB,CAAD,CAArC,CAFc,CAId;;AACA,kBAAMQ,SAAS,GAAGF,YAAY,GAAG,KAAKzC,UAAtC,CALc,CAMd;;AACA,kBAAM4C,mBAAmB,GAAI,CAACD,SAAS,GAAG,GAAb,IAAoB,GAArB,GAA4B,GAAxD,CAPc,CASd;;AACA,kBAAME,gBAAgB,GAAG,GAAzB,CAVc,CAUgB;;AAC9B,kBAAMC,WAAW,GAAG,KAAK7C,SAAzB,CAXc,CAWsB;;AACpC,kBAAM8C,UAAU,GAAGlB,IAAI,CAACmB,GAAL,CAASnB,IAAI,CAACoB,GAAL,CAASL,mBAAT,CAAT,EAAwCE,WAAW,GAAGnB,EAAtD,IAA4DE,IAAI,CAACqB,IAAL,CAAUN,mBAAV,CAA/E;AAEA,mBAAK5C,UAAL,IAAmB+C,UAAU,GAAGF,gBAAhC,CAdc,CAgBd;;AACAjB,cAAAA,SAAS,GAAG,KAAK7B,KAAL,GAAa8B,IAAI,CAACC,GAAL,CAAS1C,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAAzB;AACA+B,cAAAA,SAAS,GAAG,KAAKhC,KAAL,GAAa8B,IAAI,CAACG,GAAL,CAAS5C,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAAzB;AACH;AACJ,WArCyB,CAuC1B;;;AACA,cAAMmD,aAAa,GAAG,KAAKjD,YAAL,GAAoB2B,IAAI,CAACC,GAAL,CAAS1C,gBAAgB,CAAC,KAAKe,iBAAN,CAAzB,CAA1C;AACA,cAAMiD,aAAa,GAAG,KAAKlD,YAAL,GAAoB2B,IAAI,CAACG,GAAL,CAAS5C,gBAAgB,CAAC,KAAKe,iBAAN,CAAzB,CAA1C,CAzC0B,CA2C1B;;AACA,cAAMkD,YAAY,GAAGzB,SAAS,GAAGuB,aAAa,GAAGxB,EAAjD;AACA,cAAM2B,YAAY,GAAGvB,SAAS,GAAGqB,aAAa,GAAGzB,EAAjD,CA7C0B,CA+C1B;;AACA,eAAK5B,KAAL,GAAa8B,IAAI,CAACW,IAAL,CAAUa,YAAY,GAAGA,YAAf,GAA8BC,YAAY,GAAGA,YAAvD,CAAb;AACA,eAAKtD,UAAL,GAAkBX,gBAAgB,CAACwC,IAAI,CAACa,KAAL,CAAWY,YAAX,EAAyBD,YAAzB,CAAD,CAAlC,CAjD0B,CAmD1B;;AACA,cAAIA,YAAY,KAAK,CAAjB,IAAsBC,YAAY,KAAK,CAA3C,EAA8C;AAC1C;AACA,iBAAK3C,aAAL,CAAmByB,CAAnB,IAAwBiB,YAAY,GAAG1B,EAAvC;AACA,iBAAKhB,aAAL,CAAmB2B,CAAnB,IAAwBgB,YAAY,GAAG3B,EAAvC,CAH0C,CAK1C;;AACA,iBAAKlB,SAAL,CAAee,GAAf,CAAmB,KAAKb,aAAxB,EAN0C,CAQ1C;;;AACA,gBAAI,KAAKP,SAAL,GAAiB,CAAjB,IAAsB,KAAKC,UAAL,GAAkB,CAA5C,EAA+C;AAC3C;AACA,mBAAKK,SAAL,IAAkBiB,EAAlB,CAF2C,CAI3C;AACA;;AACA,kBAAM4B,YAAY,GAAGnE,gBAAgB,CAAC,KAAKY,UAAN,CAArC;AACA,kBAAMwD,KAAK,GAAG,CAAC3B,IAAI,CAACG,GAAL,CAASuB,YAAT,CAAf;AACA,kBAAME,KAAK,GAAG5B,IAAI,CAACC,GAAL,CAASyB,YAAT,CAAd,CAR2C,CAU3C;;AACA,kBAAMG,UAAU,GAAG7B,IAAI,CAACG,GAAL,CAAS,KAAKtB,SAAL,GAAiB,KAAKN,SAA/B,IAA4C,KAAKC,UAApE,CAX2C,CAa3C;;AACA,mBAAKI,SAAL,CAAe2B,CAAf,IAAoBoB,KAAK,GAAGE,UAA5B;AACA,mBAAKjD,SAAL,CAAe6B,CAAf,IAAoBmB,KAAK,GAAGC,UAA5B;AACH;;AAED,iBAAKxC,IAAL,CAAUyC,WAAV,CAAsB,KAAKlD,SAA3B,EA3B0C,CA4B1C;AACH;;AAED,cAAI,KAAKZ,eAAL,IAAwB,KAAKE,KAAL,GAAa,CAAzC,EAA4C;AACxC,gBAAM6D,aAAa,GAAGvE,gBAAgB,CAACwC,IAAI,CAACa,KAAL,CAAWY,YAAX,EAAyBD,YAAzB,CAAD,CAAtC;AACA,gBAAMQ,UAAU,GAAGD,aAAa,GAAG,KAAKE,aAAxC;AACA,iBAAK5C,IAAL,CAAU6C,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCF,UAArC;AACH;AACJ;;AAEMG,QAAAA,eAAe,GAAS;AAC3B;AACA;AACA,cAAMC,WAAW,GAAG;AAAA;AAAA,4CAAaC,WAAjC;AACA,cAAMnD,SAAS,GAAI,KAAKN,SAAL,CAAe2B,CAAf,GAAmB,KAAK5B,SAAL,CAAe4B,CAAnC,IAAyC6B,WAAW,CAACE,IAArD,IACC,KAAK1D,SAAL,CAAe2B,CAAf,GAAmB,KAAK5B,SAAL,CAAe4B,CAAnC,IAAyC6B,WAAW,CAACG,IADrD,IAEC,KAAK3D,SAAL,CAAe6B,CAAf,GAAmB,KAAK9B,SAAL,CAAe8B,CAAnC,IAAyC2B,WAAW,CAACI,IAFrD,IAGC,KAAK5D,SAAL,CAAe6B,CAAf,GAAmB,KAAK9B,SAAL,CAAe8B,CAAnC,IAAyC2B,WAAW,CAACK,IAHvE;AAKA,eAAKC,UAAL,CAAgBxD,SAAhB;AACH;;AAEMwD,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,cAAI,KAAK5D,UAAL,KAAoB4D,OAAxB,EAAiC;AAEjC,eAAK5D,UAAL,GAAkB4D,OAAlB;;AACA,cAAIA,OAAO,IAAI,KAAK3D,uBAApB,EAA6C;AACzC,iBAAKA,uBAAL;AACH,WAFD,MAEO,IAAI,CAAC2D,OAAD,IAAY,KAAK1D,yBAArB,EAAgD;AACnD,iBAAKA,yBAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACW2D,QAAAA,SAAS,CAACnE,MAAD,EAA4B;AACxC,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKR,gBAAL,GAAwBQ,MAAM,KAAK,IAAnC;AACH;;AAlKsD,O;;;;;iBAGVb,oBAAoB,CAACiF,E", "sourcesContent": ["import { _decorator, misc, size, Component, Enum, Vec2, Vec3, Node, UITransform } from 'cc';\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { IMovable } from './IMovable';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\nimport FCollider, { ColliderGroupType } from 'db://assets/scripts/Game/collider-system/FCollider';\r\nimport Entity from 'db://assets/scripts/Game/ui/base/Entity';\r\n\r\nexport enum eSpriteDefaultFacing {\r\n    Right = 0,    // →\r\n    Up = -90,     // ↑\r\n    Down = 90,    // ↓\r\n    Left = 180    // ←\r\n}\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    @property({type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向'})\r\n    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;\r\n\r\n    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向\r\n    public isTrackingTarget: boolean = false;     // 是否正在追踪目标\r\n    public speed: number = 1;                     // 速度\r\n    public speedAngle: number = 0;                // 速度方向 (用角度表示)\r\n    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）\r\n    public acceleration: number = 0;              // 加速度\r\n    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)\r\n\r\n    // @property({displayName: '倾斜速度', tooltip: '控制倾斜振荡的频率'})\r\n    public tiltSpeed: number = 5;                 // 偏移速度\r\n    // @property({displayName: '倾斜幅度', tooltip: '控制倾斜振荡的幅度'})\r\n    public tiltOffset: number = 100;                // 偏移距离\r\n\r\n    public target: Node | null = null;            // 追踪的目标节点\r\n    public arrivalDistance: number = 10;          // 到达目标的距离\r\n\r\n    private _selfSize: Vec2 = new Vec2();\r\n    private _position: Vec3 = new Vec3();\r\n    private _tiltTime: number = 0;                // 用于计算倾斜偏移的累积时间\r\n    private _basePosition: Vec3 = new Vec3();     // 基础位置（不包含倾斜偏移）\r\n\r\n    private _isVisible: boolean = true;           // 是否可见\r\n    public get isVisible() { return this._isVisible; }\r\n\r\n    // Callbacks:\r\n    public onBecomeVisibleCallback: Function | null = null;\r\n    public onBecomeInvisibleCallback: Function | null = null;\r\n    // public onCollideCallback: Function | null = null;\r\n\r\n    onLoad() {\r\n        const uiTransform = this.node.getComponent(UITransform);\r\n        const self_size = uiTransform ? uiTransform.contentSize : {width: 0, height: 0};\r\n        this._selfSize.set(self_size.width / 2, self_size.height / 2);\r\n\r\n        // Initialize base position to current node position\r\n        this.node.getPosition(this._basePosition);\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        // 根据移动属性更新位置\r\n        this.node.getPosition(this._position);\r\n        \r\n        // Convert speed and angle to velocity vector\r\n        let velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n        let velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n\r\n        if (this.isTrackingTarget && this.target) {\r\n            const targetPos = this.target.getPosition();\r\n            const currentPos = this.node.getPosition();\r\n            \r\n            // Calculate direction to target\r\n            const directionX = targetPos.x - currentPos.x;\r\n            const directionY = targetPos.y - currentPos.y;\r\n            const distance = Math.sqrt(directionX * directionX + directionY * directionY);\r\n            \r\n            if (distance > 0) {\r\n                // Calculate desired angle to target\r\n                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));\r\n                \r\n                // Smoothly adjust speedAngle toward target\r\n                const angleDiff = desiredAngle - this.speedAngle;\r\n                // Normalize angle difference to [-180, 180] range\r\n                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;\r\n                \r\n                // Apply tracking adjustment (you can add a trackingStrength property to control this)\r\n                const trackingStrength = 1.0; // Can be made configurable\r\n                const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable\r\n                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);\r\n                \r\n                this.speedAngle += turnAmount * trackingStrength;\r\n                \r\n                // Recalculate velocity with new angle\r\n                velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n                velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n            }\r\n        }\r\n\r\n        // Convert acceleration and angle to acceleration vector\r\n        const accelerationX = this.acceleration * Math.cos(degreesToRadians(this.accelerationAngle));\r\n        const accelerationY = this.acceleration * Math.sin(degreesToRadians(this.accelerationAngle));\r\n\r\n        // Update velocity vector: v = v + a * dt\r\n        const newVelocityX = velocityX + accelerationX * dt;\r\n        const newVelocityY = velocityY + accelerationY * dt;\r\n\r\n        // Convert back to speed and angle\r\n        this.speed = Math.sqrt(newVelocityX * newVelocityX + newVelocityY * newVelocityY);\r\n        this.speedAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));\r\n\r\n        // Update position: p = p + v * dt\r\n        if (newVelocityX !== 0 || newVelocityY !== 0) {\r\n            // Update base position (main movement path)\r\n            this._basePosition.x += newVelocityX * dt;\r\n            this._basePosition.y += newVelocityY * dt;\r\n\r\n            // Start with base position\r\n            this._position.set(this._basePosition);\r\n\r\n            // Apply tilting behavior if enabled\r\n            if (this.tiltSpeed > 0 && this.tiltOffset > 0) {\r\n                // Update tilt time\r\n                this._tiltTime += dt;\r\n\r\n                // Calculate perpendicular direction to movement\r\n                // If moving in direction (cos(angle), sin(angle)), perpendicular is (-sin(angle), cos(angle))\r\n                const moveAngleRad = degreesToRadians(this.speedAngle);\r\n                const perpX = -Math.sin(moveAngleRad);\r\n                const perpY = Math.cos(moveAngleRad);\r\n\r\n                // Calculate tilt offset using sine wave\r\n                const tiltAmount = Math.sin(this._tiltTime * this.tiltSpeed) * this.tiltOffset;\r\n\r\n                // Apply tilt offset in perpendicular direction (as position offset, not velocity)\r\n                this._position.x += perpX * tiltAmount;\r\n                this._position.y += perpY * tiltAmount;\r\n            }\r\n\r\n            this.node.setPosition(this._position);\r\n            // this.checkVisibility();\r\n        }\r\n        \r\n        if (this.isFacingMoveDir && this.speed > 0) {\r\n            const movementAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));\r\n            const finalAngle = movementAngle + this.defaultFacing;\r\n            this.node.setRotationFromEuler(0, 0, finalAngle);\r\n        }\r\n    }\r\n\r\n    public checkVisibility(): void {\r\n        // 这里目前的检查逻辑没有考虑旋转和缩放\r\n        // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的\r\n        const visibleSize = BulletSystem.worldBounds;\r\n        const isVisible = (this._position.x + this._selfSize.x) >= visibleSize.xMin &&\r\n                          (this._position.x - this._selfSize.x) <= visibleSize.xMax &&\r\n                          (this._position.y - this._selfSize.y) >= visibleSize.yMin && \r\n                          (this._position.y + this._selfSize.y) <= visibleSize.yMax;\r\n\r\n        this.setVisible(isVisible);\r\n    }\r\n\r\n    public setVisible(visible: boolean) {\r\n        if (this._isVisible === visible) return;\r\n\r\n        this._isVisible = visible;\r\n        if (visible && this.onBecomeVisibleCallback) {\r\n            this.onBecomeVisibleCallback();\r\n        } else if (!visible && this.onBecomeInvisibleCallback) {\r\n            this.onBecomeInvisibleCallback();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the target to track\r\n     */\r\n    public setTarget(target: Node | null): void {\r\n        this.target = target;\r\n        this.isTrackingTarget = target !== null;\r\n    }\r\n}"]}