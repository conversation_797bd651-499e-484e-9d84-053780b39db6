System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Animation, Label, Node, BaseUI, UILayer, UIMgr, BundleName, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, ToastUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIOpt(extras) {
    _reporterNs.report("UIOpt", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "../../../../Bundle", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Animation = _cc.Animation;
      Label = _cc.Label;
      Node = _cc.Node;
    }, function (_unresolved_2) {
      BaseUI = _unresolved_2.BaseUI;
      UILayer = _unresolved_2.UILayer;
      UIMgr = _unresolved_2.UIMgr;
    }, function (_unresolved_3) {
      BundleName = _unresolved_3.BundleName;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "e870f2JX0JKtbSM2Q28/qg+", "ToastUI", undefined);

      __checkObsolete__(['_decorator', 'Animation', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("ToastUI", ToastUI = (_dec = ccclass('ToastUI'), _dec2 = property(Label), _dec3 = property(Node), _dec(_class = (_class2 = class ToastUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "label", _descriptor, this);

          _initializerDefineProperty(this, "info", _descriptor2, this);
        }

        static getUrl() {
          return "prefab/ui/ToastUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Top;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home;
        }

        static getUIOption() {
          return {
            isClickBgCloseUI: false
          };
        }

        onLoad() {}

        finish() {
          console.log("finish");
        }

        onShow(message) {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this.label.string = message;

            _this.info.getComponent(Animation).play('ToastUIClose');

            _this.info.getComponent(Animation).on(Animation.EventType.FINISHED, _this.onAnimationFinished, _this);
          })();
        }

        onAnimationFinished(type, state) {
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).closeUI(ToastUI);
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "label", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "info", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=9627b239d0abd0bca658ce62cc377fb45a1526cc.js.map