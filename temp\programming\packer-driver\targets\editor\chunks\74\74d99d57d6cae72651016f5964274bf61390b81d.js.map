{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/move/Movable.ts"], "names": ["_decorator", "misc", "Component", "Enum", "Vec2", "Vec3", "UITransform", "BulletSystem", "degreesToRadians", "radiansToDegrees", "ccclass", "property", "executeInEditMode", "eSpriteDefaultFacing", "Movable", "type", "displayName", "isFacingMoveDir", "isTrackingTarget", "speed", "speedAngle", "turnSpeed", "acceleration", "accelerationAngle", "tiltSpeed", "tiltOffset", "target", "arrivalDistance", "_selfSize", "_position", "_isVisible", "onBecomeVisibleCallback", "onBecomeInvisibleCallback", "isVisible", "onLoad", "uiTransform", "node", "getComponent", "self_size", "contentSize", "width", "height", "set", "tick", "dt", "getPosition", "velocityX", "Math", "cos", "velocityY", "sin", "targetPos", "currentPos", "directionX", "x", "directionY", "y", "distance", "sqrt", "desiredAngle", "atan2", "angleDiff", "normalizedAngleDiff", "trackingStrength", "maxTurnRate", "turnAmount", "min", "abs", "sign", "accelerationX", "accelerationY", "newVelocityX", "newVelocityY", "setPosition", "movementAngle", "finalAngle", "defaultFacing", "setRotationFromEuler", "checkVisibility", "visibleSize", "worldBounds", "xMin", "xMax", "yMin", "yMax", "setVisible", "visible", "<PERSON><PERSON><PERSON><PERSON>", "Up"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAYC,MAAAA,W,OAAAA,W;;AAI3DC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAHH;AAAEC,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyCR,I;OACzC;AAAES,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CZ,U;;sCAMrCa,oB,0BAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;AAAAA,QAAAA,oB,CAAAA,oB;eAAAA,oB;;;yBASCC,O,WAFZJ,OAAO,CAAC,SAAD,C,UAIHC,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAEZ,IAAI,CAACU,oBAAD,CAAX;AAAmCG,QAAAA,WAAW,EAAE;AAAhD,OAAD,C,gBAHZJ,iB,qBADD,MAEaE,OAFb,SAE6BZ,SAF7B,CAE2D;AAAA;AAAA;;AAAA;;AAAA,eAKhDe,eALgD,GAKrB,KALqB;AAKT;AALS,eAMhDC,gBANgD,GAMpB,KANoB;AAMT;AANS,eAOhDC,KAPgD,GAOhC,CAPgC;AAOT;AAPS,eAQhDC,UARgD,GAQ3B,CAR2B;AAQT;AARS,eAShDC,SATgD,GAS5B,EAT4B;AAST;AATS,eAUhDC,YAVgD,GAUzB,CAVyB;AAUT;AAVS,eAWhDC,iBAXgD,GAWpB,CAXoB;AAWT;AAE9C;AAbuD,eAchDC,SAdgD,GAc5B,CAd4B;AAcT;AAdS,eAehDC,UAfgD,GAe3B,CAf2B;AAeT;AAfS,eAiBhDC,MAjBgD,GAiB1B,IAjB0B;AAiBT;AAjBS,eAkBhDC,eAlBgD,GAkBtB,EAlBsB;AAkBT;AAlBS,eAoB/CC,SApB+C,GAoB7B,IAAIxB,IAAJ,EApB6B;AAAA,eAqB/CyB,SArB+C,GAqB7B,IAAIxB,IAAJ,EArB6B;AAAA,eAuB/CyB,UAvB+C,GAuBzB,IAvByB;AA0BvD;AA1BuD,eA2BhDC,uBA3BgD,GA2BL,IA3BK;AAAA,eA4BhDC,yBA5BgD,GA4BH,IA5BG;AAAA;;AAuBT;AAC1B,YAATC,SAAS,GAAG;AAAE,iBAAO,KAAKH,UAAZ;AAAyB;;AAKlD;AAEAI,QAAAA,MAAM,GAAG;AACL,gBAAMC,WAAW,GAAG,KAAKC,IAAL,CAAUC,YAAV,CAAuB/B,WAAvB,CAApB;AACA,gBAAMgC,SAAS,GAAGH,WAAW,GAAGA,WAAW,CAACI,WAAf,GAA6B;AAACC,YAAAA,KAAK,EAAE,CAAR;AAAWC,YAAAA,MAAM,EAAE;AAAnB,WAA1D;;AACA,eAAKb,SAAL,CAAec,GAAf,CAAmBJ,SAAS,CAACE,KAAV,GAAkB,CAArC,EAAwCF,SAAS,CAACG,MAAV,GAAmB,CAA3D;AACH;;AAEME,QAAAA,IAAI,CAACC,EAAD,EAAmB;AAC1B;AACA,eAAKR,IAAL,CAAUS,WAAV,CAAsB,KAAKhB,SAA3B,EAF0B,CAI1B;;AACA,cAAIiB,SAAS,GAAG,KAAK3B,KAAL,GAAa4B,IAAI,CAACC,GAAL,CAASxC,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAA7B;AACA,cAAI6B,SAAS,GAAG,KAAK9B,KAAL,GAAa4B,IAAI,CAACG,GAAL,CAAS1C,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAA7B;;AAEA,cAAI,KAAKF,gBAAL,IAAyB,KAAKQ,MAAlC,EAA0C;AACtC,kBAAMyB,SAAS,GAAG,KAAKzB,MAAL,CAAYmB,WAAZ,EAAlB;AACA,kBAAMO,UAAU,GAAG,KAAKhB,IAAL,CAAUS,WAAV,EAAnB,CAFsC,CAItC;;AACA,kBAAMQ,UAAU,GAAGF,SAAS,CAACG,CAAV,GAAcF,UAAU,CAACE,CAA5C;AACA,kBAAMC,UAAU,GAAGJ,SAAS,CAACK,CAAV,GAAcJ,UAAU,CAACI,CAA5C;AACA,kBAAMC,QAAQ,GAAGV,IAAI,CAACW,IAAL,CAAUL,UAAU,GAAGA,UAAb,GAA0BE,UAAU,GAAGA,UAAjD,CAAjB;;AAEA,gBAAIE,QAAQ,GAAG,CAAf,EAAkB;AACd;AACA,oBAAME,YAAY,GAAGlD,gBAAgB,CAACsC,IAAI,CAACa,KAAL,CAAWL,UAAX,EAAuBF,UAAvB,CAAD,CAArC,CAFc,CAId;;AACA,oBAAMQ,SAAS,GAAGF,YAAY,GAAG,KAAKvC,UAAtC,CALc,CAMd;;AACA,oBAAM0C,mBAAmB,GAAI,CAACD,SAAS,GAAG,GAAb,IAAoB,GAArB,GAA4B,GAAxD,CAPc,CASd;;AACA,oBAAME,gBAAgB,GAAG,GAAzB,CAVc,CAUgB;;AAC9B,oBAAMC,WAAW,GAAG,KAAK3C,SAAzB,CAXc,CAWsB;;AACpC,oBAAM4C,UAAU,GAAGlB,IAAI,CAACmB,GAAL,CAASnB,IAAI,CAACoB,GAAL,CAASL,mBAAT,CAAT,EAAwCE,WAAW,GAAGpB,EAAtD,IAA4DG,IAAI,CAACqB,IAAL,CAAUN,mBAAV,CAA/E;AAEA,mBAAK1C,UAAL,IAAmB6C,UAAU,GAAGF,gBAAhC,CAdc,CAgBd;;AACAjB,cAAAA,SAAS,GAAG,KAAK3B,KAAL,GAAa4B,IAAI,CAACC,GAAL,CAASxC,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAAzB;AACA6B,cAAAA,SAAS,GAAG,KAAK9B,KAAL,GAAa4B,IAAI,CAACG,GAAL,CAAS1C,gBAAgB,CAAC,KAAKY,UAAN,CAAzB,CAAzB;AACH;AACJ,WArCyB,CAuC1B;;;AACA,gBAAMiD,aAAa,GAAG,KAAK/C,YAAL,GAAoByB,IAAI,CAACC,GAAL,CAASxC,gBAAgB,CAAC,KAAKe,iBAAN,CAAzB,CAA1C;AACA,gBAAM+C,aAAa,GAAG,KAAKhD,YAAL,GAAoByB,IAAI,CAACG,GAAL,CAAS1C,gBAAgB,CAAC,KAAKe,iBAAN,CAAzB,CAA1C,CAzC0B,CA2C1B;;AACA,gBAAMgD,YAAY,GAAGzB,SAAS,GAAGuB,aAAa,GAAGzB,EAAjD;AACA,gBAAM4B,YAAY,GAAGvB,SAAS,GAAGqB,aAAa,GAAG1B,EAAjD,CA7C0B,CA+C1B;;AACA,eAAKzB,KAAL,GAAa4B,IAAI,CAACW,IAAL,CAAUa,YAAY,GAAGA,YAAf,GAA8BC,YAAY,GAAGA,YAAvD,CAAb;AACA,eAAKpD,UAAL,GAAkBX,gBAAgB,CAACsC,IAAI,CAACa,KAAL,CAAWY,YAAX,EAAyBD,YAAzB,CAAD,CAAlC,CAjD0B,CAmD1B;;AACA,cAAIA,YAAY,KAAK,CAAjB,IAAsBC,YAAY,KAAK,CAA3C,EAA8C;AAC1C,iBAAK3C,SAAL,CAAeyB,CAAf,IAAoBiB,YAAY,GAAG3B,EAAnC;AACA,iBAAKf,SAAL,CAAe2B,CAAf,IAAoBgB,YAAY,GAAG5B,EAAnC;AACA,iBAAKR,IAAL,CAAUqC,WAAV,CAAsB,KAAK5C,SAA3B,EAH0C,CAI1C;AACH;;AAED,cAAI,KAAKZ,eAAL,IAAwB,KAAKE,KAAL,GAAa,CAAzC,EAA4C;AACxC,kBAAMuD,aAAa,GAAGjE,gBAAgB,CAACsC,IAAI,CAACa,KAAL,CAAWY,YAAX,EAAyBD,YAAzB,CAAD,CAAtC;AACA,kBAAMI,UAAU,GAAGD,aAAa,GAAG,KAAKE,aAAxC;AACA,iBAAKxC,IAAL,CAAUyC,oBAAV,CAA+B,CAA/B,EAAkC,CAAlC,EAAqCF,UAArC;AACH;AACJ;;AAEMG,QAAAA,eAAe,GAAS;AAC3B;AACA;AACA,gBAAMC,WAAW,GAAG;AAAA;AAAA,4CAAaC,WAAjC;AACA,gBAAM/C,SAAS,GAAI,KAAKJ,SAAL,CAAeyB,CAAf,GAAmB,KAAK1B,SAAL,CAAe0B,CAAnC,IAAyCyB,WAAW,CAACE,IAArD,IACC,KAAKpD,SAAL,CAAeyB,CAAf,GAAmB,KAAK1B,SAAL,CAAe0B,CAAnC,IAAyCyB,WAAW,CAACG,IADrD,IAEC,KAAKrD,SAAL,CAAe2B,CAAf,GAAmB,KAAK5B,SAAL,CAAe4B,CAAnC,IAAyCuB,WAAW,CAACI,IAFrD,IAGC,KAAKtD,SAAL,CAAe2B,CAAf,GAAmB,KAAK5B,SAAL,CAAe4B,CAAnC,IAAyCuB,WAAW,CAACK,IAHvE;AAKA,eAAKC,UAAL,CAAgBpD,SAAhB;AACH;;AAEMoD,QAAAA,UAAU,CAACC,OAAD,EAAmB;AAChC,cAAI,KAAKxD,UAAL,KAAoBwD,OAAxB,EAAiC;AAEjC,eAAKxD,UAAL,GAAkBwD,OAAlB;;AACA,cAAIA,OAAO,IAAI,KAAKvD,uBAApB,EAA6C;AACzC,iBAAKA,uBAAL;AACH,WAFD,MAEO,IAAI,CAACuD,OAAD,IAAY,KAAKtD,yBAArB,EAAgD;AACnD,iBAAKA,yBAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACWuD,QAAAA,SAAS,CAAC7D,MAAD,EAA4B;AACxC,eAAKA,MAAL,GAAcA,MAAd;AACA,eAAKR,gBAAL,GAAwBQ,MAAM,KAAK,IAAnC;AACH;;AApIsD,O;;;;;iBAGVb,oBAAoB,CAAC2E,E", "sourcesContent": ["import { _decorator, misc, size, Component, Enum, Vec2, Vec3, Node, UITransform } from 'cc';\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { IMovable } from './IMovable';\r\nimport { BulletSystem } from '../bullet/BulletSystem';\r\nimport FCollider, { ColliderGroupType } from 'db://assets/scripts/Game/collider-system/FCollider';\r\nimport Entity from 'db://assets/scripts/Game/ui/base/Entity';\r\n\r\nexport enum eSpriteDefaultFacing {\r\n    Right = 0,    // →\r\n    Up = -90,     // ↑\r\n    Down = 90,    // ↓\r\n    Left = 180    // ←\r\n}\r\n\r\n@ccclass('Movable')\r\n@executeInEditMode\r\nexport class Movable extends Component implements IMovable {\r\n\r\n    @property({type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向'})\r\n    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;\r\n\r\n    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向\r\n    public isTrackingTarget: boolean = false;     // 是否正在追踪目标\r\n    public speed: number = 1;                     // 速度\r\n    public speedAngle: number = 0;                // 速度方向 (用角度表示)\r\n    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）\r\n    public acceleration: number = 0;              // 加速度\r\n    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)\r\n\r\n    // TODO: \r\n    public tiltSpeed: number = 0;                 // 偏移速度\r\n    public tiltOffset: number = 0;                // 偏移距离\r\n\r\n    public target: Node | null = null;            // 追踪的目标节点\r\n    public arrivalDistance: number = 10;          // 到达目标的距离\r\n\r\n    private _selfSize: Vec2 = new Vec2();\r\n    private _position: Vec3 = new Vec3();\r\n\r\n    private _isVisible: boolean = true;           // 是否可见\r\n    public get isVisible() { return this._isVisible; }\r\n\r\n    // Callbacks:\r\n    public onBecomeVisibleCallback: Function | null = null;\r\n    public onBecomeInvisibleCallback: Function | null = null;\r\n    // public onCollideCallback: Function | null = null;\r\n\r\n    onLoad() {\r\n        const uiTransform = this.node.getComponent(UITransform);\r\n        const self_size = uiTransform ? uiTransform.contentSize : {width: 0, height: 0};\r\n        this._selfSize.set(self_size.width / 2, self_size.height / 2);\r\n    }\r\n\r\n    public tick(dt: number): void {\r\n        // 根据移动属性更新位置\r\n        this.node.getPosition(this._position);\r\n        \r\n        // Convert speed and angle to velocity vector\r\n        let velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n        let velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n\r\n        if (this.isTrackingTarget && this.target) {\r\n            const targetPos = this.target.getPosition();\r\n            const currentPos = this.node.getPosition();\r\n            \r\n            // Calculate direction to target\r\n            const directionX = targetPos.x - currentPos.x;\r\n            const directionY = targetPos.y - currentPos.y;\r\n            const distance = Math.sqrt(directionX * directionX + directionY * directionY);\r\n            \r\n            if (distance > 0) {\r\n                // Calculate desired angle to target\r\n                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));\r\n                \r\n                // Smoothly adjust speedAngle toward target\r\n                const angleDiff = desiredAngle - this.speedAngle;\r\n                // Normalize angle difference to [-180, 180] range\r\n                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;\r\n                \r\n                // Apply tracking adjustment (you can add a trackingStrength property to control this)\r\n                const trackingStrength = 1.0; // Can be made configurable\r\n                const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable\r\n                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);\r\n                \r\n                this.speedAngle += turnAmount * trackingStrength;\r\n                \r\n                // Recalculate velocity with new angle\r\n                velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));\r\n                velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));\r\n            }\r\n        }\r\n\r\n        // Convert acceleration and angle to acceleration vector\r\n        const accelerationX = this.acceleration * Math.cos(degreesToRadians(this.accelerationAngle));\r\n        const accelerationY = this.acceleration * Math.sin(degreesToRadians(this.accelerationAngle));\r\n\r\n        // Update velocity vector: v = v + a * dt\r\n        const newVelocityX = velocityX + accelerationX * dt;\r\n        const newVelocityY = velocityY + accelerationY * dt;\r\n\r\n        // Convert back to speed and angle\r\n        this.speed = Math.sqrt(newVelocityX * newVelocityX + newVelocityY * newVelocityY);\r\n        this.speedAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));\r\n\r\n        // Update position: p = p + v * dt\r\n        if (newVelocityX !== 0 || newVelocityY !== 0) {\r\n            this._position.x += newVelocityX * dt;\r\n            this._position.y += newVelocityY * dt;\r\n            this.node.setPosition(this._position);    \r\n            // this.checkVisibility();\r\n        }\r\n        \r\n        if (this.isFacingMoveDir && this.speed > 0) {\r\n            const movementAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));\r\n            const finalAngle = movementAngle + this.defaultFacing;\r\n            this.node.setRotationFromEuler(0, 0, finalAngle);\r\n        }\r\n    }\r\n\r\n    public checkVisibility(): void {\r\n        // 这里目前的检查逻辑没有考虑旋转和缩放\r\n        // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的\r\n        const visibleSize = BulletSystem.worldBounds;\r\n        const isVisible = (this._position.x + this._selfSize.x) >= visibleSize.xMin &&\r\n                          (this._position.x - this._selfSize.x) <= visibleSize.xMax &&\r\n                          (this._position.y - this._selfSize.y) >= visibleSize.yMin && \r\n                          (this._position.y + this._selfSize.y) <= visibleSize.yMax;\r\n\r\n        this.setVisible(isVisible);\r\n    }\r\n\r\n    public setVisible(visible: boolean) {\r\n        if (this._isVisible === visible) return;\r\n\r\n        this._isVisible = visible;\r\n        if (visible && this.onBecomeVisibleCallback) {\r\n            this.onBecomeVisibleCallback();\r\n        } else if (!visible && this.onBecomeInvisibleCallback) {\r\n            this.onBecomeInvisibleCallback();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set the target to track\r\n     */\r\n    public setTarget(target: Node | null): void {\r\n        this.target = target;\r\n        this.isTrackingTarget = target !== null;\r\n    }\r\n}"]}