System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, MainPlaneData, _crd;

  _export("MainPlaneData", void 0);

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "4fa2a7vdW1Mf6EJ3egxNnWk", "MainPlaneFightData", undefined);

      _export("MainPlaneData", MainPlaneData = class MainPlaneData {
        constructor() {
          this.hp = 0;
          this.maxhp = 0;
          this.screenLv = 0;
          this.die = false;
          this.relifeNum = 0;
          this.lifeNum = 0;
          this.atkAddRatio = 0;
          this.intensifyAtk = [];
          this.revive = false;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7488200ff64035440d127a60058d0eb6fc1e1e4d.js.map