System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, SingletonBase, GameEnum, GameMapRun, EnemyPlane, SceneManager, _crd;

  function _reportPossibleCrUseOfSingletonBase(extras) {
    _reporterNs.report("SingletonBase", "../../core/base/SingletonBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameMapRun(extras) {
    _reporterNs.report("GameMapRun", "../ui/map/GameMapRun", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlane(extras) {
    _reporterNs.report("EnemyPlane", "../ui/plane/enemy/EnemyPlane", _context.meta, extras);
  }

  _export("SceneManager", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_unresolved_2) {
      SingletonBase = _unresolved_2.SingletonBase;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.GameEnum;
    }, function (_unresolved_4) {
      GameMapRun = _unresolved_4.default;
    }, function (_unresolved_5) {
      EnemyPlane = _unresolved_5.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d1494I3VGBMAYFzCg6LqGIE", "SceneManager", undefined);

      _export("SceneManager", SceneManager = class SceneManager extends (_crd && SingletonBase === void 0 ? (_reportPossibleCrUseOfSingletonBase({
        error: Error()
      }), SingletonBase) : SingletonBase) {
        /**
         * 获取场景中的位置
         * @param {Entity} entity 场景中的实体
         * @returns {Object} 包含 x 和 y 的位置对象
         */
        getScenePos(entity) {
          if (entity instanceof (_crd && EnemyPlane === void 0 ? (_reportPossibleCrUseOfEnemyPlane({
            error: Error()
          }), EnemyPlane) : EnemyPlane)) {
            let x = entity.node.position.x;
            let y = entity.node.position.y;
            let parentName = "enemyPlane";

            if (entity.type === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyType.Missile) {
              parentName = "enemyBullet";
            }

            let parent = entity.node.parent;
            let scaleX = 1;
            let scaleY = 1;
            let lastParent = null;

            while (parent && parent.name !== parentName) {
              scaleX *= parent.scale.x;
              scaleY *= parent.scale.y;
              x += parent.x;
              y += parent.y;
              lastParent = parent;
              parent = parent.parent;
            }

            if (lastParent) {
              x -= lastParent.x;
              y -= lastParent.y;
              x *= scaleX;
              y *= scaleY;
              x += lastParent.x;
              y += lastParent.y;
            } else {
              x *= scaleX;
              y *= scaleY;
            }

            return {
              x,
              y
            };
          } // if (entity instanceof BossUnitBase) {
          //     const scenePos = entity.getScenePos();
          //     return { x: scenePos.x, y: scenePos.y };
          // }
          // if (entity instanceof WinePlane) {
          //     return entity.scenePos;
          // }


          return {
            x: entity.node.x,
            y: entity.node.y
          };
        }
        /**
         * 获取场景层的速度
         * @param {Entity} entity 场景中的实体
         * @returns {number} 场景层的速度
         */


        getLayerSpeed(entity) {
          // if (entity instanceof EnemyBuild) {
          //     const sceneLayer = entity.sceneLayer;
          //     return this.getMapSpeed(sceneLayer);
          // }
          // if (entity instanceof EnemyTurret && entity.sceneLayer >= 0) {
          //     return this.getMapSpeed(entity.sceneLayer);
          // }
          return 0;
        }
        /**
         * 获取地图层的速度
         * @param {number} layer 地图层索引
         * @returns {number} 地图层的速度
         */


        getMapSpeed() {
          return (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
            error: Error()
          }), GameMapRun) : GameMapRun).instance.MapSpeed;
        }
        /**
         * 将地图坐标转换为战斗场景坐标
         * @param {number} x 地图坐标 x
         * @param {number} y 地图坐标 y
         * @param {number} layer 地图层索引
         * @returns {Object} 包含 x 和 y 的战斗场景坐标
         */


        mapToBattleScene(x, y, layer) {
          return {
            x,
            y: y - (_crd && GameMapRun === void 0 ? (_reportPossibleCrUseOfGameMapRun({
              error: Error()
            }), GameMapRun) : GameMapRun).instance.ViewTop
          };
        }
        /**
         * 获取场景中的实体
         * @param {Entity} entity 场景中的实体
         * @returns {Entity} 实体对象
         */


        getSceneEntity(entity) {
          let result = entity;
          return result;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1812b9d101ddd6cf689e0ebdf218f9b10d0e339d.js.map