{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TopBlockInputUI.ts"], "names": ["_decorator", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "BundleName", "ccclass", "property", "TopBlockInputUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Common", "onLoad", "onShow", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;AACRC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;iCAGjBM,e,WADZF,OAAO,CAAC,iBAAD,C,gBAAR,MACaE,eADb;AAAA;AAAA,4BAC4C;AACpB,eAANC,MAAM,GAAW;AAAE,iBAAO,2BAAP;AAAqC;;AAChD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAqB;;AAC9B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,MAAlB;AAA2B;;AAEzDC,QAAAA,MAAM,GAAS,CACxB;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAAG;;AAXM,O", "sourcesContent": ["import { _decorator } from 'cc';\nimport { BaseUI, UILayer } from 'db://assets/scripts/ui/UIMgr';\nimport { BundleName } from '../../../../Bundle';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('TopBlockInputUI')\nexport class TopBlockInputUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/TopBlockInputUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top; }\n    public static getBundleName(): string { return BundleName.Common; }\n\n    protected onLoad(): void {\n    }\n    async onShow(): Promise<void> {\n    }\n    async onHide(): Promise<void> {\n    }\n    async onClose(): Promise<void> { }\n}"]}