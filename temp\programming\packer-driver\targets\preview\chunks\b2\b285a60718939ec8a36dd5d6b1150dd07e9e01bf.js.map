{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts"], "names": ["_decorator", "Label", "Node", "BundleName", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "DataEvent", "EventMgr", "ButtonPlus", "HomeUI", "RogueSelectIcon", "ccclass", "property", "RogueUI", "freshTimes", "excludeTimes", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "Home", "onLoad", "on", "RogueSelectClick", "onRogueSelectClick", "btnClose", "addClick", "closeUI", "nodeFresh", "getComponentInChildren", "onFresh", "nodeExclude", "onCancel", "index", "rogueSelectIcons", "for<PERSON>ach", "element", "updateActive", "btn", "string", "updateStatus", "updateFreshTimes", "currentText", "match", "count", "parseInt", "updateCancelTimes", "openUI", "onDestroy", "targetOff", "onShow", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AACnBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,e,iBAAAA,e;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;yBAGjBc,O,WADZF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACX,IAAD,C,UAERW,QAAQ,CAACX,IAAD,C,UAERW,QAAQ,CAACX,IAAD,C,UAERW,QAAQ,CAAC;AAAA;AAAA,6CAAD,C,2BAXb,MACaC,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAahCC,UAbgC,GAaL,IAbK;AAAA,eAchCC,YAdgC,GAcH,IAdG;AAAA;;AAgBZ,eAANC,MAAM,GAAW;AAAE,iBAAO,yBAAP;AAAmC;;AAC9C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACtDC,QAAAA,MAAM,GAAS;AACrB;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,sCAAUC,gBAAtB,EAAwC,KAAKC,kBAA7C,EAAiE,IAAjE;AACA,eAAKC,QAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACA,eAAKC,SAAL,CAAgBC,sBAAhB;AAAA;AAAA,wCAAoDH,QAApD,CAA6D,KAAKI,OAAlE,EAA2E,IAA3E;AACA,eAAKC,WAAL,CAAkBF,sBAAlB;AAAA;AAAA,wCAAsDH,QAAtD,CAA+D,KAAKM,QAApE,EAA8E,IAA9E;AACA,eAAKlB,UAAL,GAAkB,KAAKc,SAAL,CAAgBC,sBAAhB,CAAuC7B,KAAvC,CAAlB;AACA,eAAKe,YAAL,GAAoB,KAAKgB,WAAL,CAAkBF,sBAAlB,CAAyC7B,KAAzC,CAApB;AAEH;;AACOwB,QAAAA,kBAAkB,CAACS,KAAD,EAAgB;AACtC,eAAKC,gBAAL,CAAsBC,OAAtB,CAA8BC,OAAO,IAAI;AACrCA,YAAAA,OAAO,CAACC,YAAR,CAAqBJ,KAArB;AACH,WAFD;AAGH;;AAEDH,QAAAA,OAAO,GAAG;AACN,eAAKI,gBAAL,CAAsBC,OAAtB,CAA8BC,OAAO,IAAI;AACrCA,YAAAA,OAAO,CAACC,YAAR,CAAqB,CAArB;AACH,WAFD;AAGH;;AACDL,QAAAA,QAAQ,GAAG;AACP,eAAKE,gBAAL,CAAsBC,OAAtB,CAA8BC,OAAO,IAAI;AACrCA,YAAAA,OAAO,CAACC,YAAR,CAAqB,CAArB;AACH,WAFD;AAGA,cAAIC,GAAG,GAAG,KAAKP,WAAL,CAAkBF,sBAAlB;AAAA;AAAA,uCAAV;;AACA,cAAIS,GAAG,CAAET,sBAAL,CAA4B7B,KAA5B,EAAoCuC,MAApC,IAA8C,IAAlD,EAAwD;AACpDD,YAAAA,GAAG,CAAET,sBAAL,CAA4B7B,KAA5B,EAAoCuC,MAApC,GAA6C,IAA7C;AACA,iBAAKL,gBAAL,CAAsBC,OAAtB,CAA8BC,OAAO,IAAI;AACrCA,cAAAA,OAAO,CAACI,YAAR,CAAqB,CAArB;AACH,aAFD;AAGH,WALD,MAKO;AACHF,YAAAA,GAAG,CAAET,sBAAL,CAA4B7B,KAA5B,EAAoCuC,MAApC,GAA6C,IAA7C;AACA,iBAAKL,gBAAL,CAAsBC,OAAtB,CAA8BC,OAAO,IAAI;AACrCA,cAAAA,OAAO,CAACI,YAAR,CAAqB,CAArB;AACH,aAFD;AAGH;AACJ;;AACDC,QAAAA,gBAAgB,GAAG;AACf,cAAMC,WAAW,GAAG,KAAK5B,UAAL,CAAiByB,MAArC;AACA,cAAMI,KAAK,GAAGD,WAAW,CAACC,KAAZ,CAAkB,KAAlB,CAAd;;AACA,cAAIA,KAAJ,EAAW;AACP,gBAAIC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAD,CAAN,EAAW,EAAX,CAApB;;AACA,gBAAIC,KAAK,GAAG,CAAZ,EAAe;AACXA,cAAAA,KAAK;AACL,mBAAK9B,UAAL,CAAiByB,MAAjB,sCAAkCK,KAAlC;AACH;AACJ;AACJ;;AACDE,QAAAA,iBAAiB,GAAG;AAChB,cAAMJ,WAAW,GAAG,KAAK3B,YAAL,CAAmBwB,MAAvC;AACA,cAAMI,KAAK,GAAGD,WAAW,CAACC,KAAZ,CAAkB,KAAlB,CAAd;;AACA,cAAIA,KAAJ,EAAW;AACP,gBAAIC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAD,CAAN,EAAW,EAAX,CAApB;;AACA,gBAAIC,KAAK,GAAG,CAAZ,EAAe;AACXA,cAAAA,KAAK;AACL,mBAAK7B,YAAL,CAAmBwB,MAAnB,sCAAoCK,KAApC;AACH;AACJ;AACJ;;AACKjB,QAAAA,OAAO,GAAG;AAAA;AACZ,kBAAM;AAAA;AAAA,gCAAMoB,MAAN;AAAA;AAAA,iCAAN;AACA;AAAA;AAAA,gCAAMpB,OAAN,CAAcd,OAAd;AAFY;AAGf;;AACSmC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAAG;;AACzCC,QAAAA,MAAM,GAAgC;AAAA;AAAG;;AACzCC,QAAAA,OAAO,GAAgC;AAAA;AAAG;;AAxFhB,O;;;;;iBAGF,I;;;;;;;iBAEL,I;;;;;;;iBAEE,I;;;;;;;iBAEA,I;;;;;;;iBAEW,E", "sourcesContent": ["import { _decorator, Label, Node } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/Bundle';\r\nimport { BaseUI, UILayer, UIMgr } from '../../../../../../scripts/ui/UIMgr';\r\nimport { DataEvent } from '../../../event/DataEvent';\r\nimport { EventMgr } from '../../../event/EventManager';\r\nimport { ButtonPlus } from '../../common/components/button/ButtonPlus';\r\nimport { HomeUI } from '../HomeUI';\r\nimport { RogueSelectIcon } from './RogueSelectIcon';\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"RogueUI\")\r\nexport class RogueUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus | null = null;\r\n    @property(Node)\r\n    nodeFresh: Node | null = null;\r\n    @property(Node)\r\n    nodeExclude: Node | null = null;\r\n    @property(Node)\r\n    nodeAbility: Node | null = null;\r\n    @property([RogueSelectIcon])\r\n    rogueSelectIcons: RogueSelectIcon[] = [];\r\n\r\n    freshTimes: Label | null = null;\r\n    excludeTimes: Label | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/fight/RogueUI\"; };\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    protected onLoad(): void {\r\n        EventMgr.on(DataEvent.RogueSelectClick, this.onRogueSelectClick, this);\r\n        this.btnClose!.addClick(this.closeUI, this);\r\n        this.nodeFresh!.getComponentInChildren(ButtonPlus)!.addClick(this.onFresh, this);\r\n        this.nodeExclude!.getComponentInChildren(ButtonPlus)!.addClick(this.onCancel, this);\r\n        this.freshTimes = this.nodeFresh!.getComponentInChildren(Label);\r\n        this.excludeTimes = this.nodeExclude!.getComponentInChildren(Label);\r\n\r\n    }\r\n    private onRogueSelectClick(index: number) {\r\n        this.rogueSelectIcons.forEach(element => {\r\n            element.updateActive(index);\r\n        });\r\n    }\r\n\r\n    onFresh() {\r\n        this.rogueSelectIcons.forEach(element => {\r\n            element.updateActive(0);\r\n        });\r\n    }\r\n    onCancel() {\r\n        this.rogueSelectIcons.forEach(element => {\r\n            element.updateActive(0);\r\n        });\r\n        let btn = this.nodeExclude!.getComponentInChildren(ButtonPlus);\r\n        if (btn!.getComponentInChildren(Label)!.string == \"排除\") {\r\n            btn!.getComponentInChildren(Label)!.string = \"取消\";\r\n            this.rogueSelectIcons.forEach(element => {\r\n                element.updateStatus(2);\r\n            });\r\n        } else {\r\n            btn!.getComponentInChildren(Label)!.string = \"排除\";\r\n            this.rogueSelectIcons.forEach(element => {\r\n                element.updateStatus(1);\r\n            });\r\n        }\r\n    }\r\n    updateFreshTimes() {\r\n        const currentText = this.freshTimes!.string;\r\n        const match = currentText.match(/\\d+/);\r\n        if (match) {\r\n            let count = parseInt(match[0], 10);\r\n            if (count > 0) {\r\n                count--;\r\n                this.freshTimes!.string = `剩余次数：${count}`;\r\n            }\r\n        }\r\n    }\r\n    updateCancelTimes() {\r\n        const currentText = this.excludeTimes!.string;\r\n        const match = currentText.match(/\\d+/);\r\n        if (match) {\r\n            let count = parseInt(match[0], 10);\r\n            if (count > 0) {\r\n                count--;\r\n                this.excludeTimes!.string = `剩余次数：${count}`;\r\n            }\r\n        }\r\n    }\r\n    async closeUI() {\r\n        await UIMgr.openUI(HomeUI)\r\n        UIMgr.closeUI(RogueUI)\r\n    }\r\n    protected onDestroy(): void {\r\n        EventMgr.targetOff(this)\r\n    }\r\n\r\n    async onShow(...args: any[]): Promise<void> { }\r\n    async onHide(...args: any[]): Promise<void> { }\r\n    async onClose(...args: any[]): Promise<void> { }\r\n}\r\n"]}