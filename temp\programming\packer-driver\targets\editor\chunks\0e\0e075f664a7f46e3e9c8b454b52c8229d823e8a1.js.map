{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts"], "names": ["_decorator", "Sprite", "tween", "UIOpacity", "Tools", "GameIns", "GameEnum", "EnemyShootComponent", "EnemyShootData", "TrackComponent", "EnemyPlaneRole", "PlaneBase", "FBoxCollider", "ColliderGroupType", "Bullet", "Movable", "ccclass", "property", "EnemyPlane", "_data", "_trackCom", "_shootCom", "_moveCom", "removeAble", "_curAction", "_removeCount", "_removeTime", "_rotateSpeed", "_leaveAct", "_roleIndex", "_curFormIndex", "_curTrackType", "_dieAnimEnd", "_hpWhiteTween", "bullets", "_countTime", "_bStandBy", "_standByTime", "_standByEnd", "moveCom", "onLoad", "addScript", "node", "collide<PERSON>omp", "addComponent", "init", "groupType", "ENEMY_NORMAL", "colliderEnabled", "setCollideAble", "isEnabled", "isEnable", "collideAble", "initPlane", "data", "_reset", "_refreshProperty", "EnemyAction", "Track", "curHp", "hp", "maxHp", "attack", "initComps", "setAtkStartCall", "setAction", "AttackPrepare", "setAtkOverCall", "AttackOver", "action", "setIsShooting", "setTrackAble", "<PERSON><PERSON><PERSON>", "hpBg", "getComponent", "opacity", "Transform", "stopShoot", "setNextShootAtOnce", "playAtkAnim", "AttackIng", "startShoot", "role", "playAnim", "setFormIndex", "index", "bAttackAbles", "shootData", "attackInterval", "attackNum", "attackPointArr", "attackArrNum", "length", "setShootData", "_updateAction", "deltaTime", "setNextAble", "isMoving", "bMoveAttack", "bStayAttack", "Leave", "die", "EnemyDestroyType", "TimeOver", "initTrack", "trackData", "trackParams", "offsetX", "offsetY", "rotateSpeed", "setTrackGroupOverCall", "setTrackOverCall", "TrackOver", "setTrackLeaveCall", "setTrackStartCall", "setFirstShoot<PERSON>elay", "delay", "startBattle", "_refreshHpBar", "startTrack", "updateGameLogic", "isDead", "checkStandby", "m_comps", "for<PERSON>ach", "comp", "update", "_checkRemoveAble", "tick", "onCollide", "collider", "entity", "getAttack", "hurtEffectManager", "createHurtNumByType", "getPosition", "hurt", "damage", "checkHp", "Die", "hpRatio", "isDecreasing", "hpSpr", "fill<PERSON><PERSON><PERSON>", "stop", "duration", "Math", "abs", "hpWhite", "to", "call", "start", "destroyType", "to<PERSON><PERSON>", "onDie", "will<PERSON><PERSON><PERSON>", "checkLiveAble", "addBullet", "bullet", "push", "removeBullet", "indexOf", "splice", "setStandByTime", "time", "setPos", "x", "y", "setPosition"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,S,OAAAA,S;;AACvCC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,mB;;AACkBC,MAAAA,c,iBAAAA,c;;AAClBC,MAAAA,c;;AACAC,MAAAA,c;;AAGAC,MAAAA,S;;AACAC,MAAAA,Y;;AACaC,MAAAA,iB,kBAAAA,iB;;AACbC,MAAAA,M;;AACEC,MAAAA,O,kBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBjB,U;;yBAGTkB,U,WADpBF,OAAO,CAAC,YAAD,C,UAEHC,QAAQ;AAAA;AAAA,2C,UAGRA,QAAQ,CAAChB,MAAD,C,UAERgB,QAAQ,CAAChB,MAAD,C,UAERgB,QAAQ,CAAChB,MAAD,C,2BATb,MACqBiB,UADrB;AAAA;AAAA,kCACkD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAW9CC,KAX8C,GAWf,IAXe;AAY9C;AAZ8C,eAa9CC,SAb8C,GAaX,IAbW;AAAA,eAc9CC,SAd8C,GAcN,IAdM;AAAA,eAe9CC,QAf8C,GAenB,IAfmB;AAAA,eAkB9CC,UAlB8C,GAkBjC,KAlBiC;AAAA,eAmB9CC,UAnB8C,GAmBzB,CAnByB;AAAA,eAoB9CC,YApB8C,GAoBvB,CApBuB;AAAA,eAqB9CC,WArB8C,GAqBxB,CArBwB;AAAA,eAuB9CC,YAvB8C,GAuBvB,CAvBuB;AAAA,eAwB9CC,SAxB8C,GAwB1B,CAAC,CAxByB;AAAA,eAyB9CC,UAzB8C,GAyBzB,CAzByB;AAAA,eA0B9CC,aA1B8C,GA0BtB,CA1BsB;AAAA,eA2B9CC,aA3B8C,GA2BtB,CAAC,CA3BqB;AAAA,eA4B9CC,WA5B8C,GA4BvB,KA5BuB;AAAA,eA6B9CC,aA7B8C,GA6BzB,IA7ByB;AAAA,eA8B9CC,OA9B8C,GA8B1B,EA9B0B;AAAA,eAgC9CC,UAhC8C,GAgCjC,CAhCiC;AAAA,eAiC9CC,SAjC8C,GAiClC,KAjCkC;AAAA,eAkC9CC,YAlC8C,GAkC/B,CAlC+B;AAAA,eAmC9CC,WAnC8C,GAmChC,KAnCgC;AAAA;;AAgB5B,YAAPC,OAAO,GAAG;AAAE,iBAAO,KAAKjB,QAAZ;AAAuB;;AAqBpCkB,QAAAA,MAAM,GAAS;AACrB,eAAKpB,SAAL,GAAiB;AAAA;AAAA,8BAAMqB,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,+CAAjB;AACA,eAAKrB,SAAL,GAAiB;AAAA;AAAA,8BAAMoB,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,yDAAjB,CAFqB,CAGrB;;AACA,eAAKC,WAAL,GAAmB,KAAKC,YAAL;AAAA;AAAA,+CAAkC,KAAKA,YAAL;AAAA;AAAA,2CAArD;AACA,eAAKD,WAAL,CAAkBE,IAAlB,CAAuB,IAAvB;AACA,eAAKF,WAAL,CAAkBG,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,YAAhD;AACA,eAAKC,eAAL,GAAuB,KAAvB,CAPqB,CAQrB;;AACA,eAAK1B,QAAL,GAAgB;AAAA;AAAA,8BAAMmB,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,iCAAhB;AACH;;AAEDO,QAAAA,cAAc,CAACC,SAAD,EAAqB;AAC/B,eAAKP,WAAL,CAAkBQ,QAAlB,GAA6BD,SAA7B;AACH;;AAEc,YAAXE,WAAW,GAAY;AACvB,iBAAO,KAAKT,WAAL,CAAkBQ,QAAzB;AACH;;AAEc,cAATE,SAAS,CAACC,IAAD,EAAuB;AAClC,gBAAMT,IAAN;;AACA,eAAKU,MAAL;;AACA,eAAKpC,KAAL,GAAamC,IAAb;;AACA,eAAKE,gBAAL;AACH;;AACDD,QAAAA,MAAM,GAAG;AACL,eAAK/B,UAAL,GAAkB;AAAA;AAAA,oCAASiC,WAAT,CAAqBC,KAAvC;AACH;;AAEDF,QAAAA,gBAAgB,GAAG;AACf,eAAKG,KAAL,GAAa,KAAKxC,KAAL,CAAYyC,EAAzB;AACA,eAAKC,KAAL,GAAa,KAAK1C,KAAL,CAAYyC,EAAzB;AACA,eAAKE,MAAL,GAAc,EAAd;AACH;;AAEDC,QAAAA,SAAS,GAAS;AACd;AACA,gBAAMA,SAAN,GAFc,CAId;;AACA,eAAK1C,SAAL,CAAgBwB,IAAhB,CAAqB,IAArB,EAA2B,KAAKH,IAAhC,EAAsC,IAAtC,EAA4C,KAA5C,EALc,CAOd;;;AACA,eAAKrB,SAAL,CAAgB2C,eAAhB,CAAgC,MAAM;AAClC,iBAAKC,SAAL,CAAe;AAAA;AAAA,sCAASR,WAAT,CAAqBS,aAApC;AACH,WAFD,EARc,CAYd;;;AACA,eAAK7C,SAAL,CAAgB8C,cAAhB,CAA+B,MAAM;AACjC,iBAAKF,SAAL,CAAe;AAAA;AAAA,sCAASR,WAAT,CAAqBW,UAApC;AACH,WAFD;AAGH;;AAEDH,QAAAA,SAAS,CAACI,MAAD,EAAiB;AACtB,cAAI,KAAK7C,UAAL,KAAoB6C,MAAxB,EAAgC;AAC5B,iBAAK7C,UAAL,GAAkB6C,MAAlB,CAD4B,CAG5B;;AACA,iBAAKhD,SAAL,CAAgBiD,aAAhB,CAA8B,KAA9B;;AACA,iBAAKlD,SAAL,CAAgBmD,YAAhB,CAA6B,IAA7B;;AAEA,oBAAQ,KAAK/C,UAAb;AACI,mBAAK;AAAA;AAAA,wCAASiC,WAAT,CAAqBe,KAA1B;AACI;AACA,qBAAKC,IAAL,CAAW/B,IAAX,CAAiBgC,YAAjB,CAA8BvE,SAA9B,EAA0CwE,OAA1C,GAAoD,CAApD,CAFJ,CAGI;;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAASlB,WAAT,CAAqBC,KAA1B;AACI;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAASD,WAAT,CAAqBmB,SAA1B;AACI;AACA,qBAAKxD,SAAL,CAAgBmD,YAAhB,CAA6B,KAA7B;;AACA,qBAAKlD,SAAL,CAAgBwD,SAAhB;;AACA,qBAAKhD,UAAL,GAJJ,CAKI;AACA;AACA;AACA;AACA;;AACA,qBAAKoC,SAAL,CAAe;AAAA;AAAA,0CAASR,WAAT,CAAqBC,KAApC;;AACA,qBAAKrC,SAAL,CAAgByD,kBAAhB;;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAASrB,WAAT,CAAqBS,aAA1B;AACI;AACA,qBAAKa,WAAL;AACA,qBAAKd,SAAL,CAAe;AAAA;AAAA,0CAASR,WAAT,CAAqBuB,SAApC;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAASvB,WAAT,CAAqBuB,SAA1B;AACI;AACA,qBAAK3D,SAAL,CAAgB4D,UAAhB;;AACA;;AAEJ,mBAAK;AAAA;AAAA,wCAASxB,WAAT,CAAqBW,UAA1B;AACI,qBAAKH,SAAL,CAAe;AAAA;AAAA,0CAASR,WAAT,CAAqBC,KAApC;AACA;;AAEJ;AACI;AAzCR;AA2CH;AACJ;AAED;AACJ;AACA;;;AACIqB,QAAAA,WAAW,GAAG;AACV,eAAKG,IAAL,CAAWC,QAAX,CAAqB,MAAK,KAAKtD,UAAW,EAA1C,EAA6C,KAA7C,EAAoD,MAAM;AACtD,iBAAKqD,IAAL,CAAWC,QAAX,CAAqB,OAAM,KAAKtD,UAAW,EAA3C;AACH,WAFD;AAGH;AAED;AACJ;AACA;AACA;;;AACIuD,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,eAAKvD,aAAL,GAAqBuD,KAArB;;AACA,cAAI,KAAKvD,aAAL,IAAsB,CAAtB,IAA2B,KAAKX,KAAL,CAAYmE,YAAZ,CAAyB,KAAKxD,aAA9B,CAA/B,EAA6E;AACzE,kBAAMyD,SAAS,GAAG;AAAA;AAAA,mDAAlB;AACAA,YAAAA,SAAS,CAACC,cAAV,GAA2B,KAAKrE,KAAL,CAAYqE,cAAvC;AACAD,YAAAA,SAAS,CAACE,SAAV,GAAsB,KAAKtE,KAAL,CAAYsE,SAAlC;AACAF,YAAAA,SAAS,CAACG,cAAV,GAA2B,KAAKvE,KAAL,CAAYuE,cAAZ,CAA2B,KAAK5D,aAAhC,CAA3B;AACAyD,YAAAA,SAAS,CAACI,YAAV,GAAyBJ,SAAS,CAACG,cAAV,CAAyBE,MAAlD;;AACA,iBAAKvE,SAAL,CAAgBwE,YAAhB,CAA6BN,SAA7B;AACH,WAPD,MAOO;AACH,iBAAKlE,SAAL,CAAgBwE,YAAhB,CAA6B,IAA7B;AACH;AACJ;;AAGDC,QAAAA,aAAa,CAACC,SAAD,EAAoB;AAC7B,eAAK1E,SAAL,CAAgB2E,WAAhB,CAA4B,KAA5B;;AAEA,kBAAQ,KAAKxE,UAAb;AACI,iBAAK;AAAA;AAAA,sCAASiC,WAAT,CAAqBe,KAA1B;AACI;AACA,mBAAKxB,eAAL,GAAuB,KAAvB;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAASS,WAAT,CAAqBC,KAA1B;AACI;AACA,mBAAKrC,SAAL,CAAgB2E,WAAhB,CACK,KAAK5E,SAAL,CAAgB6E,QAAhB,IAA4B,KAAK9E,KAAL,CAAY+E,WAAzC,IACC,CAAC,KAAK9E,SAAL,CAAgB6E,QAAjB,IAA6B,KAAK9E,KAAL,CAAYgF,WAF9C;;AAIA;;AAEJ,iBAAK;AAAA;AAAA,sCAAS1C,WAAT,CAAqBmB,SAA1B;AACI;;AAEJ,iBAAK;AAAA;AAAA,sCAASnB,WAAT,CAAqBS,aAA1B;AACA,iBAAK;AAAA;AAAA,sCAAST,WAAT,CAAqBuB,SAA1B;AACI;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAASvB,WAAT,CAAqB2C,KAA1B;AACI,kBAAI,KAAKxE,SAAL,KAAmB,CAAvB,EAA0B;AACtB,qBAAKyE,GAAL,CAAS;AAAA;AAAA,0CAASC,gBAAT,CAA0BC,QAAnC;AACH,eAFD,MAEO,IAAI,KAAK3E,SAAL,GAAiB,CAArB,EAAwB,CAC3B;AACH;;AACD;AA5BR;AA8BH;AAGD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACI4E,QAAAA,SAAS,CAACC,SAAD,EAA0BC,WAA1B,EAAiDC,OAAjD,EAAkEC,OAAlE,EAAmFC,WAAW,GAAG,CAAjG,EAAoG;AACzG,eAAKlF,YAAL,GAAoBkF,WAApB;;AACA,eAAKzF,SAAL,CAAgByB,IAAhB,CAAqB,IAArB,EAA2B4D,SAA3B,EAAsCC,WAAtC,EAAmDC,OAAnD,EAA4DC,OAA5D;;AAEA,eAAKxF,SAAL,CAAgB0F,qBAAhB,CAAuChE,SAAD,IAAuB,CAE5D,CAFD;;AAIA,eAAK1B,SAAL,CAAgB2F,gBAAhB,CAAiC,MAAM;AACnC,iBAAKV,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BU,SAAnC;AACH,WAFD;;AAIA,eAAK5F,SAAL,CAAgB6F,iBAAhB,CAAkC,MAAM;AACpC,iBAAKZ,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BF,KAAnC;AACH,WAFD;;AAIA,eAAKhF,SAAL,CAAgB8F,iBAAhB,CAAmCT,SAAD,IAA0B,CAE3D,CAFD;AAGH;AAED;AACJ;AACA;AACA;;;AACIU,QAAAA,kBAAkB,CAACC,KAAD,EAAgB;AAC9B,eAAK/F,SAAL,CAAgB8F,kBAAhB,CAAmCC,KAAnC;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,eAAKrE,eAAL,GAAuB,IAAvB;;AACA,eAAKsE,aAAL;;AACA,eAAKlG,SAAL,CAAgBmD,YAAhB,CAA6B,IAA7B;;AACA,eAAKnD,SAAL,CAAgBmG,UAAhB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,eAAe,CAACzB,SAAD,EAAoB;AAC/B,cAAI,CAAC,KAAK0B,MAAN,IAAgB,KAAKC,YAAL,CAAkB3B,SAAlB,CAApB,EAAkD;AAC9C;AACA,iBAAK4B,OAAL,CAAaC,OAAb,CAAsBC,IAAD,IAAU;AAC3BA,cAAAA,IAAI,CAACC,MAAL,CAAY/B,SAAZ;AACH,aAFD;AAGH;;AACD,cAAI,KAAK0B,MAAT,EAAiB;AACb,iBAAKM,gBAAL,CAAsBhC,SAAtB;AACH,WAFD,MAEO,IAAI,CAAC,KAAK3D,SAAV,EAAqB;AACxB,iBAAKhB,SAAL,CAAgBoG,eAAhB,CAAgCzB,SAAhC;;AACA,iBAAK1E,SAAL,CAAgBmG,eAAhB,CAAgCzB,SAAhC;;AACA,iBAAKzE,QAAL,CAAe0G,IAAf,CAAoBjC,SAApB;;AACA,iBAAKD,aAAL,CAAmBC,SAAnB;AACH;AACJ;;AAGDkC,QAAAA,SAAS,CAACC,QAAD,EAAsB;AAC3B,cAAI,CAAC,KAAKT,MAAV,EAAkB;AACd,gBAAIS,QAAQ,CAACC,MAAT;AAAA;AAAA,iCAAJ,EAAuC;AACnC,oBAAMrE,MAAM,GAAGoE,QAAQ,CAACC,MAAT,CAAgBC,SAAhB,EAAf;AACA;AAAA;AAAA,sCAAQC,iBAAR,CAA0BC,mBAA1B,CAA8CJ,QAAQ,CAACC,MAAT,CAAgBzF,IAAhB,CAAqB6F,WAArB,EAA9C,EAAkFzE,MAAlF;AACA,mBAAK0E,IAAL,CAAU,CAAC1E,MAAX;AACH;AACJ;AACJ;;AAED0E,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACjB,cAAI,KAAKhB,MAAT,EAAiB;AACb,mBAAO,KAAP;AACH;;AACD,eAAK9D,KAAL,IAAc8E,MAAd;;AACA,cAAI,KAAK9E,KAAL,GAAa,CAAjB,EAAoB;AAChB,iBAAKA,KAAL,GAAa,CAAb;AACH,WAFD,MAEO,IAAI,KAAKA,KAAL,GAAa,KAAKE,KAAtB,EAA6B;AAChC,iBAAKF,KAAL,GAAa,KAAKE,KAAlB;AACH;;AACD,eAAKyD,aAAL;;AACA,eAAKoB,OAAL;;AACA,cAAI,CAAC,KAAKjB,MAAV,EAAkB,CACd;AACH;AACJ;;AAEDiB,QAAAA,OAAO,GAAG;AACN,cAAI,KAAK/E,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAK0C,GAAL,CAAS;AAAA;AAAA,sCAASC,gBAAT,CAA0BqC,GAAnC;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAEDrB,QAAAA,aAAa,GAAG;AACZ,gBAAMsB,OAAO,GAAG,KAAKjF,KAAL,GAAa,KAAKE,KAAlC;AACA,gBAAMgF,YAAY,GAAGD,OAAO,GAAG,KAAKE,KAAL,CAAYC,SAA3C,CAFY,CAIZ;;AACA,eAAKD,KAAL,CAAYC,SAAZ,GAAwBH,OAAxB,CALY,CAOZ;;AACA,cAAI,KAAK3G,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB+G,IAAnB;;AACA,iBAAK/G,aAAL,GAAqB,IAArB;AACH,WAXW,CAaZ;;;AACA,cAAI4G,YAAJ,EAAkB;AACd,kBAAMI,QAAQ,GAAGC,IAAI,CAACC,GAAL,CAAS,KAAKC,OAAL,CAAcL,SAAd,GAA0B,KAAKD,KAAL,CAAYC,SAA/C,CAAjB;AACA,iBAAK9G,aAAL,GAAqB/B,KAAK,CAAC,KAAKkJ,OAAN,CAAL,CAChBC,EADgB,CACbJ,QADa,EACH;AAAEF,cAAAA,SAAS,EAAE,KAAKD,KAAL,CAAYC;AAAzB,aADG,EAEhBO,IAFgB,CAEX,MAAM;AACR,mBAAKrH,aAAL,GAAqB,IAArB;AACH,aAJgB,EAKhBsH,KALgB,EAArB;AAMH,WARD,MAQO;AACH,iBAAKH,OAAL,CAAcL,SAAd,GAA0BH,OAA1B;AACH;AACJ;AAID;AACJ;AACA;AACA;;;AACIvC,QAAAA,GAAG,CAACmD,WAAD,EAAyC;AACxC,cAAI,CAAC,MAAMC,KAAN,EAAL,EAAoB;AAChB,mBAAO,KAAP;AACH;;AACD,eAAKzG,eAAL,GAAuB,KAAvB;AAEA,eAAK0G,KAAL,CAAWF,WAAX;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,KAAK,CAACF,WAAD,EAAsB;AACvB,eAAK/E,IAAL,CAAW/B,IAAX,CAAiBgC,YAAjB,CAA8BvE,SAA9B,EAA0CwE,OAA1C,GAAoD,CAApD;AACA,eAAKgF,UAAL;;AAEA,kBAAQH,WAAR;AACI,iBAAK;AAAA;AAAA,sCAASlD,gBAAT,CAA0BqC,GAA/B;AACI;AACA;;AAEJ,iBAAK;AAAA;AAAA,sCAASrC,gBAAT,CAA0BF,KAA/B;AACA,iBAAK;AAAA;AAAA,sCAASE,gBAAT,CAA0BU,SAA/B;AACA,iBAAK;AAAA;AAAA,sCAASV,gBAAT,CAA0BC,QAA/B;AACI,mBAAKvE,WAAL,GAAmB,IAAnB;AACA;AATR;AAWH;AAGD;AACJ;AACA;;;AACI2H,QAAAA,UAAU,GAAG;AACT,cAAI,KAAK1H,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB+G,IAAnB;;AACA,iBAAK/G,aAAL,GAAqB,IAArB;AACH;;AAED,eAAKmH,OAAL,CAAcL,SAAd,GAA0B,CAA1B;AACH;AAED;AACJ;AACA;AACA;;;AACIa,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAKpI,UAAL,KAAoB;AAAA;AAAA,oCAASiC,WAAT,CAAqBC,KAA7C,EAAoD;AAChD,iBAAKO,SAAL,CAAe;AAAA;AAAA,sCAASR,WAAT,CAAqB2C,KAApC;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;AACD;AACJ;AACA;AACA;;;AACI2B,QAAAA,gBAAgB,CAAChC,SAAD,EAAoB;AAChC,eAAKxE,UAAL,GAAkB,IAAlB;AACH;;AAEDsI,QAAAA,SAAS,CAACC,MAAD,EAAiB;AACtB,cAAI,KAAK5H,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAa6H,IAAb,CAAkBD,MAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,YAAY,CAACF,MAAD,EAAiB;AACzB,cAAI,KAAK5H,OAAT,EAAkB;AACd,kBAAMmD,KAAK,GAAG,KAAKnD,OAAL,CAAa+H,OAAb,CAAqBH,MAArB,CAAd;;AACA,gBAAIzE,KAAK,IAAI,CAAb,EAAgB;AACZ,mBAAKnD,OAAL,CAAagI,MAAb,CAAoB7E,KAApB,EAA2B,CAA3B;AACH;AACJ;AACJ;AAED;AACJ;AACA;AACA;AACA;;;AACIqC,QAAAA,YAAY,CAAC3B,SAAD,EAAoB;AAC5B,eAAK5D,UAAL,IAAmB4D,SAAnB;;AACA,cAAI,KAAK3D,SAAT,EAAoB;AAChB,gBAAI,KAAKD,UAAL,GAAkB,KAAKE,YAA3B,EAAyC;AACrC,mBAAKD,SAAL,GAAiB,KAAjB;AACA,mBAAKD,UAAL,GAAkB,CAAlB;AACA,mBAAKG,WAAL,GAAmB,IAAnB;AACA,mBAAKI,IAAL,CAAWgC,YAAX,CAAwBvE,SAAxB,EAAoCwE,OAApC,GAA8C,GAA9C;AACA,mBAAK0C,WAAL;AACH;;AACD,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAED8C,QAAAA,cAAc,CAACC,IAAD,EAAe;AACzB,eAAKhI,SAAL,GAAiB,IAAjB;AACA,eAAKC,YAAL,GAAoB+H,IAApB;AACA,eAAK1H,IAAL,CAAWgC,YAAX,CAAwBvE,SAAxB,EAAoCwE,OAApC,GAA8C,CAA9C;AACH;;AAED0F,QAAAA,MAAM,CAACC,CAAD,EAAYC,CAAZ,EAAuB;AACzB,eAAK7H,IAAL,CAAU8H,WAAV,CAAsBF,CAAtB,EAAyBC,CAAzB;AACH;;AAvc6C,O;;;;;iBAEhB,I;;;;;;;iBAGR,I;;;;;;;iBAEC,I;;;;;;;iBAEE,I", "sourcesContent": ["import { _decorator, Node, Sprite, Vec2, tween, UIOpacity } from 'cc';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { GameEnum } from '../../../const/GameEnum';\r\nimport EnemyShootComponent from './EnemyShootComponent';\r\nimport { EnemyPlaneData, EnemyShootData } from '../../../data/EnemyData';\r\nimport TrackComponent from '../../base/TrackComponent';\r\nimport EnemyPlaneRole from './EnemyPlaneRole';\r\nimport { TrackGroup } from '../../../data/EnemyWave';\r\nimport { TrackData } from '../../../data/TrackData';\r\nimport PlaneBase from '../PlaneBase';\r\nimport FBoxCollider from '../../../collider-system/FBoxCollider';\r\nimport FCollider, { ColliderGroupType } from '../../../collider-system/FCollider';\r\nimport Bullet from '../../bullet/Bullet';\r\nimport { Movable } from 'db://assets/scripts/Game/move/Movable';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyPlane')\r\nexport default class EnemyPlane extends PlaneBase {\r\n    @property(EnemyPlaneRole)\r\n    role: EnemyPlaneRole | null = null;\r\n\r\n    @property(Sprite)\r\n    hpBg: Sprite | null = null;\r\n    @property(Sprite)\r\n    hpSpr: Sprite | null = null;\r\n    @property(Sprite)\r\n    hpWhite: Sprite | null = null;\r\n\r\n    _data: EnemyPlaneData | null = null;\r\n    // 这个将要废弃\r\n    _trackCom: TrackComponent | null = null;\r\n    _shootCom: EnemyShootComponent | null = null;\r\n    _moveCom: Movable | null = null;\r\n    public get moveCom() { return this._moveCom; }\r\n\r\n    removeAble = false;\r\n    _curAction: number = 0;\r\n    _removeCount: number = 0;\r\n    _removeTime: number = 0;\r\n\r\n    _rotateSpeed: number = 0;\r\n    _leaveAct: number = -1;\r\n    _roleIndex: number = 1;\r\n    _curFormIndex: number = 0;\r\n    _curTrackType: number = -1;\r\n    _dieAnimEnd: boolean = false;\r\n    _hpWhiteTween: any = null;\r\n    bullets: Bullet[] = [];\r\n\r\n    _countTime = 0;\r\n    _bStandBy = false;\r\n    _standByTime = 0;\r\n    _standByEnd = false;\r\n\r\n    protected onLoad(): void {\r\n        this._trackCom = Tools.addScript(this.node, TrackComponent);\r\n        this._shootCom = Tools.addScript(this.node, EnemyShootComponent);\r\n        // 添加碰撞组件并初始化\r\n        this.collideComp = this.addComponent(FBoxCollider)|| this.addComponent(FBoxCollider);\r\n        this.collideComp!.init(this);\r\n        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;\r\n        this.colliderEnabled = false;\r\n        // 添加移动组件\r\n        this._moveCom = Tools.addScript(this.node, Movable);\r\n    }\r\n\r\n    setCollideAble(isEnabled: boolean) {\r\n        this.collideComp!.isEnable = isEnabled;\r\n    }\r\n\r\n    get collideAble(): boolean {\r\n        return this.collideComp!.isEnable;\r\n    }\r\n\r\n    async initPlane(data: EnemyPlaneData) {\r\n        super.init();\r\n        this._reset();\r\n        this._data = data;\r\n        this._refreshProperty();\r\n    }\r\n    _reset() {\r\n        this._curAction = GameEnum.EnemyAction.Track;\r\n    }\r\n\r\n    _refreshProperty() {\r\n        this.curHp = this._data!.hp;\r\n        this.maxHp = this._data!.hp;\r\n        this.attack = 10;\r\n    }\r\n\r\n    initComps(): void {\r\n        // 调用父类的组件初始化方法\r\n        super.initComps();\r\n\r\n        // 初始化射击组件\r\n        this._shootCom!.init(this, this.node, null, false);\r\n\r\n        // 设置攻击开始的回调\r\n        this._shootCom!.setAtkStartCall(() => {\r\n            this.setAction(GameEnum.EnemyAction.AttackPrepare);\r\n        });\r\n\r\n        // 设置攻击结束的回调\r\n        this._shootCom!.setAtkOverCall(() => {\r\n            this.setAction(GameEnum.EnemyAction.AttackOver);\r\n        });\r\n    }\r\n\r\n    setAction(action: number) {\r\n        if (this._curAction !== action) {\r\n            this._curAction = action;\r\n\r\n            // 停止射击并启用轨迹\r\n            this._shootCom!.setIsShooting(false);\r\n            this._trackCom!.setTrackAble(true);\r\n\r\n            switch (this._curAction) {\r\n                case GameEnum.EnemyAction.Sneak:\r\n                    // 潜行行为\r\n                    this.hpBg!.node!.getComponent(UIOpacity)!.opacity = 0;\r\n                    // this.role.playSneakAnim();\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.Track:\r\n                    // 跟踪行为\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.Transform:\r\n                    // 变形行为\r\n                    this._trackCom!.setTrackAble(false);\r\n                    this._shootCom!.stopShoot();\r\n                    this._roleIndex++;\r\n                    // this.role!.playAnim(\"transform\", () => {\r\n                    //     this.role!.playAnim(\"idle\" + this._roleIndex);\r\n                    //     this.setAction(GameEnum.EnemyAction.Track);\r\n                    //     this._shootCom!.setNextShootAtOnce();\r\n                    // }) || (\r\n                    this.setAction(GameEnum.EnemyAction.Track)\r\n                    this._shootCom!.setNextShootAtOnce();\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackPrepare:\r\n                    // 准备攻击行为\r\n                    this.playAtkAnim();\r\n                    this.setAction(GameEnum.EnemyAction.AttackIng);\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackIng:\r\n                    // 攻击中行为\r\n                    this._shootCom!.startShoot();\r\n                    break;\r\n\r\n                case GameEnum.EnemyAction.AttackOver:\r\n                    this.setAction(GameEnum.EnemyAction.Track);\r\n                    break;\r\n\r\n                default:\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 播放攻击动画\r\n     */\r\n    playAtkAnim() {\r\n        this.role!.playAnim(`atk${this._roleIndex}`, false, () => {\r\n            this.role!.playAnim(`idle${this._roleIndex}`);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * 设置形态索引\r\n     * @param {number} index 形态索引\r\n     */\r\n    setFormIndex(index: number) {\r\n        this._curFormIndex = index;\r\n        if (this._curFormIndex >= 0 && this._data!.bAttackAbles[this._curFormIndex]) {\r\n            const shootData = new EnemyShootData();\r\n            shootData.attackInterval = this._data!.attackInterval;\r\n            shootData.attackNum = this._data!.attackNum;\r\n            shootData.attackPointArr = this._data!.attackPointArr[this._curFormIndex];\r\n            shootData.attackArrNum = shootData.attackPointArr.length;\r\n            this._shootCom!.setShootData(shootData);\r\n        } else {\r\n            this._shootCom!.setShootData(null);\r\n        }\r\n    }\r\n\r\n\r\n    _updateAction(deltaTime: number) {\r\n        this._shootCom!.setNextAble(false);\r\n\r\n        switch (this._curAction) {\r\n            case GameEnum.EnemyAction.Sneak:\r\n                // this._updateCurDir(deltaTime);\r\n                this.colliderEnabled = false;\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Track:\r\n                // this._updateCurDir(deltaTime);\r\n                this._shootCom!.setNextAble(\r\n                    (this._trackCom!.isMoving && this._data!.bMoveAttack) ||\r\n                    (!this._trackCom!.isMoving && this._data!.bStayAttack)\r\n                );\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Transform:\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.AttackPrepare:\r\n            case GameEnum.EnemyAction.AttackIng:\r\n                // this._updateCurDir(deltaTime);\r\n                break;\r\n\r\n            case GameEnum.EnemyAction.Leave:\r\n                if (this._leaveAct === 0) {\r\n                    this.die(GameEnum.EnemyDestroyType.TimeOver);\r\n                } else if (this._leaveAct > 0) {\r\n                    // this._updateCurDir(deltaTime);\r\n                }\r\n                break;\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 初始化轨迹\r\n     * @param {TrackData} trackData 轨迹数据\r\n     * @param {number} offsetX X 轴偏移\r\n     * @param {number} offsetY Y 轴偏移\r\n     * @param {boolean} isLoop 是否循环\r\n     * @param {number} rotateSpeed 旋转速度\r\n     */\r\n    initTrack(trackData: TrackGroup[], trackParams: number[], offsetX: number, offsetY: number, rotateSpeed = 0) {\r\n        this._rotateSpeed = rotateSpeed;\r\n        this._trackCom!.init(this, trackData, trackParams, offsetX, offsetY);\r\n\r\n        this._trackCom!.setTrackGroupOverCall((groupType: number) => {\r\n\r\n        });\r\n\r\n        this._trackCom!.setTrackOverCall(() => {\r\n            this.die(GameEnum.EnemyDestroyType.TrackOver);\r\n        });\r\n\r\n        this._trackCom!.setTrackLeaveCall(() => {\r\n            this.die(GameEnum.EnemyDestroyType.Leave);\r\n        });\r\n\r\n        this._trackCom!.setTrackStartCall((trackData: TrackData) => {\r\n\r\n        });\r\n    }\r\n\r\n    /**\r\n * 设置首次射击的延迟时间\r\n * @param {number} delay 延迟时间\r\n */\r\n    setFirstShootDelay(delay: number) {\r\n        this._shootCom!.setFirstShootDelay(delay);\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    startBattle() {\r\n        this.colliderEnabled = true;\r\n        this._refreshHpBar();\r\n        this._trackCom!.setTrackAble(true);\r\n        this._trackCom!.startTrack();\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        if (!this.isDead && this.checkStandby(deltaTime)) {\r\n            // 更新所有组件\r\n            this.m_comps.forEach((comp) => {\r\n                comp.update(deltaTime);\r\n            });\r\n        }\r\n        if (this.isDead) {\r\n            this._checkRemoveAble(deltaTime);\r\n        } else if (!this._bStandBy) {\r\n            this._trackCom!.updateGameLogic(deltaTime);\r\n            this._shootCom!.updateGameLogic(deltaTime);\r\n            this._moveCom!.tick(deltaTime);\r\n            this._updateAction(deltaTime);\r\n        }\r\n    }\r\n\r\n\r\n    onCollide(collider: FCollider) {\r\n        if (!this.isDead) {\r\n            if (collider.entity instanceof Bullet) {\r\n                const attack = collider.entity.getAttack();\r\n                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), attack);\r\n                this.hurt(-attack)\r\n            }\r\n        }\r\n    }\r\n\r\n    hurt(damage: number) {\r\n        if (this.isDead) {\r\n            return false;\r\n        }\r\n        this.curHp += damage;\r\n        if (this.curHp < 0) {\r\n            this.curHp = 0;\r\n        } else if (this.curHp > this.maxHp) {\r\n            this.curHp = this.maxHp;\r\n        }\r\n        this._refreshHpBar();\r\n        this.checkHp();\r\n        if (!this.isDead) {\r\n            // this.role.winkWhite();\r\n        }\r\n    }\r\n\r\n    checkHp() {\r\n        if (this.curHp <= 0) {\r\n            this.die(GameEnum.EnemyDestroyType.Die);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    _refreshHpBar() {\r\n        const hpRatio = this.curHp / this.maxHp;\r\n        const isDecreasing = hpRatio < this.hpSpr!.fillRange;\r\n\r\n        // 更新血条显示\r\n        this.hpSpr!.fillRange = hpRatio;\r\n\r\n        // 停止之前的血条动画\r\n        if (this._hpWhiteTween) {\r\n            this._hpWhiteTween.stop();\r\n            this._hpWhiteTween = null;\r\n        }\r\n\r\n        // 如果血量减少，播放白色血条的动画\r\n        if (isDecreasing) {\r\n            const duration = Math.abs(this.hpWhite!.fillRange - this.hpSpr!.fillRange);\r\n            this._hpWhiteTween = tween(this.hpWhite!)\r\n                .to(duration, { fillRange: this.hpSpr!.fillRange })\r\n                .call(() => {\r\n                    this._hpWhiteTween = null;\r\n                })\r\n                .start();\r\n        } else {\r\n            this.hpWhite!.fillRange = hpRatio;\r\n        }\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * 处理敌人死亡逻辑\r\n     * @param {GameEnum.EnemyDestroyType} destroyType 敌人销毁类型\r\n     */\r\n    die(destroyType: GameEnum.EnemyDestroyType) {\r\n        if (!super.toDie()) {\r\n            return false;\r\n        }\r\n        this.colliderEnabled = false;\r\n\r\n        this.onDie(destroyType);\r\n    }\r\n\r\n    /**\r\n     * 敌机死亡时的处理\r\n     * @param {number} destroyType 销毁类型\r\n     */\r\n    onDie(destroyType: number) {\r\n        this.hpBg!.node!.getComponent(UIOpacity)!.opacity = 0;\r\n        this.willRemove();\r\n\r\n        switch (destroyType) {\r\n            case GameEnum.EnemyDestroyType.Die:\r\n                // this.playDieAnim();\r\n                break;\r\n\r\n            case GameEnum.EnemyDestroyType.Leave:\r\n            case GameEnum.EnemyDestroyType.TrackOver:\r\n            case GameEnum.EnemyDestroyType.TimeOver:\r\n                this._dieAnimEnd = true;\r\n                break;\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 准备移除敌机\r\n     */\r\n    willRemove() {\r\n        if (this._hpWhiteTween) {\r\n            this._hpWhiteTween.stop();\r\n            this._hpWhiteTween = null;\r\n        }\r\n\r\n        this.hpWhite!.fillRange = 0;\r\n    }\r\n\r\n    /**\r\n     * 检查是否可以存活\r\n     * @returns {boolean} 是否可以存活\r\n     */\r\n    checkLiveAble() {\r\n        if (this._curAction === GameEnum.EnemyAction.Track) {\r\n            this.setAction(GameEnum.EnemyAction.Leave);\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n    /**\r\n     * 检查是否可以移除\r\n     * @param {number} deltaTime 帧间隔时间\r\n     */\r\n    _checkRemoveAble(deltaTime: number) {\r\n        this.removeAble = true;\r\n    }\r\n\r\n    addBullet(bullet: Bullet) {\r\n        if (this.bullets) {\r\n            this.bullets.push(bullet);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 从敌人移除子弹\r\n     * @param {Bullet} bullet 子弹对象\r\n     */\r\n    removeBullet(bullet: Bullet) {\r\n        if (this.bullets) {\r\n            const index = this.bullets.indexOf(bullet);\r\n            if (index >= 0) {\r\n                this.bullets.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 检查待机状态\r\n     * @param {number} deltaTime 帧间隔时间\r\n     * @returns {boolean} 是否处于待机状态\r\n     */\r\n    checkStandby(deltaTime: number) {\r\n        this._countTime += deltaTime;\r\n        if (this._bStandBy) {\r\n            if (this._countTime > this._standByTime) {\r\n                this._bStandBy = false;\r\n                this._countTime = 0;\r\n                this._standByEnd = true;\r\n                this.node!.getComponent(UIOpacity)!.opacity = 255;\r\n                this.startBattle();\r\n            }\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    setStandByTime(time: number) {\r\n        this._bStandBy = true;\r\n        this._standByTime = time;\r\n        this.node!.getComponent(UIOpacity)!.opacity = 0;\r\n    }\r\n\r\n    setPos(x: number, y: number) {\r\n        this.node.setPosition(x, y);\r\n    }\r\n}"]}