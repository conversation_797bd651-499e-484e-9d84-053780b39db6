{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/skill/SkillComp.ts"], "names": ["<PERSON>llComp", "MyApp", "GameIns", "BaseComp", "logInfo", "log<PERSON>arn", "res", "Cast", "caster", "skillID", "skillData", "lubanTables", "Tbskill", "get", "ApplyBuffs", "for<PERSON>ach", "applyBuff", "forEachByTargetType", "target", "entity", "buff<PERSON><PERSON>p", "A<PERSON><PERSON><PERSON><PERSON>", "buff<PERSON>", "targetType", "callback", "TargetType", "Self", "Main", "mainPlaneManager", "mainPlane", "MainFriendly", "Enemy", "enemyManager", "enemies", "plane", "boss<PERSON><PERSON><PERSON>", "bosses", "boss", "BossEnemy", "NormalEnemy"], "mappings": ";;;+EAQqBA,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AARZC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,Q;;AACEC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,O,iBAAAA,O;;AAETC,MAAAA,G,iBAAAA,G;;;;;;;yBAGYN,S,GAAN,MAAMA,SAAN;AAAA;AAAA,gCAAiC;AAC5CO,QAAAA,IAAI,CAACC,MAAD,EAAeC,OAAf,EAA+B;AAC/B;AAAA;AAAA,kCAAQ,OAAR,EAAkB,cAAaA,OAAQ,EAAvC;AACA,cAAIC,SAAS,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,GAA1B,CAA8BJ,OAA9B,CAAhB;;AACA,cAAI,CAACC,SAAL,EAAgB;AACZ;AAAA;AAAA,oCAAQ,OAAR,EAAkB,cAAaD,OAAQ,uBAAvC;AACA;AACH;;AACDC,UAAAA,SAAS,CAACI,UAAV,CAAqBC,OAArB,CAA8BC,SAAD,IAAe;AACxChB,YAAAA,SAAS,CAACiB,mBAAV,CAA8BT,MAA9B,EAAsCQ,SAAS,CAACE,MAAhD,EAAyDC,MAAD,IAAY;AAC/DA,cAAAA,MAAD,CAAkBC,QAAlB,CAA2BC,SAA3B,CAAqCL,SAAS,CAACM,MAA/C;AACH,aAFD;AAGH,WAJD;AAKH;;AAEyB,eAAnBL,mBAAmB,CAACT,MAAD,EAAee,UAAf,EAA2CC,QAA3C,EAA8E;AACpG,kBAAQD,UAAR;AACI,iBAAK;AAAA;AAAA,4BAAIE,UAAJ,CAAeC,IAApB;AACIF,cAAAA,QAAQ,CAAChB,MAAD,CAAR;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIiB,UAAJ,CAAeE,IAApB;AACIH,cAAAA,QAAQ,CAAC;AAAA;AAAA,sCAAQI,gBAAR,CAAyBC,SAA1B,CAAR;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIJ,UAAJ,CAAeK,YAApB;AACIN,cAAAA,QAAQ,CAAC;AAAA;AAAA,sCAAQI,gBAAR,CAAyBC,SAA1B,CAAR;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIJ,UAAJ,CAAeM,KAApB;AACI;AAAA;AAAA,sCAAQC,YAAR,CAAqBC,OAArB,CAA6BlB,OAA7B,CAAsCmB,KAAD,IAAW;AAC5CV,gBAAAA,QAAQ,CAACU,KAAD,CAAR;AACH,eAFD;AAGA;AAAA;AAAA,sCAAQC,WAAR,CAAoBC,MAApB,CAA2BrB,OAA3B,CAAoCsB,IAAD,IAAU;AACzC;AACA;AACA;AACAb,gBAAAA,QAAQ,CAACa,IAAD,CAAR;AACH,eALD;AAMA;;AACJ,iBAAK;AAAA;AAAA,4BAAIZ,UAAJ,CAAea,SAApB;AACI;AAAA;AAAA,sCAAQH,WAAR,CAAoBC,MAApB,CAA2BrB,OAA3B,CAAoCsB,IAAD,IAAU;AACzC;AACA;AACA;AACAb,gBAAAA,QAAQ,CAACa,IAAD,CAAR;AACH,eALD;AAMA;;AACJ,iBAAK;AAAA;AAAA,4BAAIZ,UAAJ,CAAec,WAApB;AACI;AAAA;AAAA,sCAAQP,YAAR,CAAqBC,OAArB,CAA6BlB,OAA7B,CAAsCmB,KAAD,IAAW;AAC5CV,gBAAAA,QAAQ,CAACU,KAAD,CAAR;AACH,eAFD;AAGA;;AACJ;AACI;AAnCR;AAqCH;;AArD2C,O", "sourcesContent": ["import { MyApp } from \"db://assets/scripts/MyApp\";\r\nimport { GameIns } from \"../../../GameIns\";\r\nimport BaseComp from \"../../base/BaseComp\";\r\nimport { logInfo, logWarn } from \"db://assets/scripts/Utils/Logger\";\r\nimport Entity from \"../../base/Entity\";\r\nimport { res } from \"db://assets/scripts/AutoGen/Luban/schema\";\r\nimport Plane from \"../PlaneBase\";\r\n\r\nexport default class SkillComp extends BaseComp {\r\n    Cast(caster:Plane, skillID:number) {\r\n        logInfo(\"Skill\", `cast skill ${skillID}`);\r\n        let skillData = MyApp.lubanTables.Tbskill.get(skillID);\r\n        if (!skillData) {\r\n            logWarn(\"Skill\", `cast skill ${skillID} but config not found`)\r\n            return;\r\n        }\r\n        skillData.ApplyBuffs.forEach((applyBuff) => {\r\n            SkillComp.forEachByTargetType(caster, applyBuff.target, (entity) => {\r\n                (entity as Plane).buffComp.ApplyBuff(applyBuff.buffID);\r\n            })\r\n        })\r\n    }\r\n    \r\n    static forEachByTargetType(caster:Plane, targetType: res.TargetType, callback: (entity: Plane) => void) {\r\n        switch (targetType) {\r\n            case res.TargetType.Self:\r\n                callback(caster as Plane);\r\n                break;\r\n            case res.TargetType.Main:\r\n                callback(GameIns.mainPlaneManager.mainPlane!);\r\n                break;\r\n            case res.TargetType.MainFriendly:\r\n                callback(GameIns.mainPlaneManager.mainPlane!);\r\n                break;\r\n            case res.TargetType.Enemy:\r\n                GameIns.enemyManager.enemies.forEach((plane) => {\r\n                    callback(plane);\r\n                });\r\n                GameIns.bossManager.bosses.forEach((boss) => {\r\n                    // boss.getUnits().forEach((unit) => {\r\n                    //     callback(unit);\r\n                    // });\r\n                    callback(boss)\r\n                });\r\n                break;\r\n            case res.TargetType.BossEnemy:\r\n                GameIns.bossManager.bosses.forEach((boss) => {\r\n                    // boss.getUnits().forEach((unit) => {\r\n                    //     callback(unit);\r\n                    // });\r\n                    callback(boss)\r\n                });\r\n                break;\r\n            case res.TargetType.NormalEnemy:\r\n                GameIns.enemyManager.enemies.forEach((plane) => {\r\n                    callback(plane);\r\n                });\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n}"]}