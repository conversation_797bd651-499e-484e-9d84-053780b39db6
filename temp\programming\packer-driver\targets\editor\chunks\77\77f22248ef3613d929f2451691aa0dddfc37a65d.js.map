{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyShootComponent.ts"], "names": ["_decorator", "Component", "v2", "Tools", "ccclass", "property", "EnemyShootComponent", "_target", "_atkNode", "_shootAble", "_nextAble", "bulletTurn", "_bShooting", "_shootInterval", "_shootTime", "_shootCount", "_attackNum", "_attackArrIndex", "_attackArrOver", "_attackPointArr", "_firstShootDelay", "_isFirstShoot", "_atkOverCall", "_atkStartCall", "init", "target", "atkNode", "shootData", "reset", "setShootData", "_initAtkPoints", "attackInterval", "attackNum", "attackPointArr", "setFirstShoot<PERSON>elay", "delay", "getFirstShootDelay", "setIsShooting", "isShooting", "setNextAble", "nextAble", "startShoot", "stopShoot", "setAtkStartCall", "callback", "setAtkOverCall", "updateGameLogic", "deltaTime", "sceneLayer", "isPlaneOutScreen", "node", "position", "x", "y", "_updateShoot", "_updateNextShoot", "shootPrepare", "setNextShootAtOnce", "_isShootLoop", "_getShootLoop", "setNextShoot", "_setNextShootArr"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA8BC,MAAAA,E,OAAAA,E;;AAC1CC,MAAAA,K,iBAAAA,K;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;yBAGTM,mB,WADpBF,OAAO,CAAC,qBAAD,C,gBAAR,MACqBE,mBADrB,SACiDL,SADjD,CAC2D;AAAA;AAAA;AAAA,eACvDM,OADuD,GACxC,IADwC;AAAA,eAEvDC,QAFuD,GAEjC,IAFiC;AAAA,eAGvDC,UAHuD,GAGjC,KAHiC;AAAA,eAIvDC,SAJuD,GAIlC,KAJkC;AAAA,eAKvDC,UALuD,GAKjC,KALiC;AAAA,eAMvDC,UANuD,GAMjC,KANiC;AAAA,eAOvDC,cAPuD,GAO9B,CAP8B;AAAA,eAQvDC,UARuD,GAQlC,CARkC;AAAA,eASvDC,WATuD,GASjC,CATiC;AAAA,eAUvDC,UAVuD,GAUlC,CAVkC;AAAA,eAWvDC,eAXuD,GAW7B,CAX6B;AAAA,eAYvDC,cAZuD,GAY7B,IAZ6B;AAAA,eAavDC,eAbuD,GAa9B,EAb8B;AAAA,eAcvDC,gBAduD,GAc5B,CAAC,CAd2B;AAAA,eAevDC,aAfuD,GAe9B,IAf8B;AAAA,eAgBvDC,YAhBuD,GAgBzB,IAhByB;AAAA,eAiBvDC,aAjBuD,GAiBxB,IAjBwB;AAAA;;AAmBvD;AACJ;AACA;AACA;AACA;AACA;AACA;AACIC,QAAAA,IAAI,CAACC,MAAD,EAAcC,OAAd,EAA6BC,SAA7B,EAA6ChB,UAAmB,GAAG,KAAnE,EAA0E;AAC1E,eAAKiB,KAAL;AACA,eAAKjB,UAAL,GAAkBA,UAAlB;AACA,eAAKJ,OAAL,GAAekB,MAAf;AACA,eAAKjB,QAAL,GAAgBkB,OAAhB;AACA,eAAKG,YAAL,CAAkBF,SAAlB;;AACA,eAAKG,cAAL;AACH;AAED;AACJ;AACA;;;AACIF,QAAAA,KAAK,GAAG;AACJ,eAAKlB,SAAL,GAAiB,KAAjB;AACA,eAAKE,UAAL,GAAkB,KAAlB;AACA,eAAKC,cAAL,GAAsB,CAAtB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,WAAL,GAAmB,CAAnB;AACA,eAAKC,UAAL,GAAkB,CAAlB;AACA,eAAKC,eAAL,GAAuB,CAAvB;AACA,eAAKC,cAAL,GAAsB,IAAtB;AACA,eAAKC,eAAL,GAAuB,EAAvB;AACA,eAAKE,aAAL,GAAqB,IAArB;AACA,eAAKD,gBAAL,GAAwB,CAAC,CAAzB;AACA,eAAKG,aAAL,GAAqB,IAArB;AACA,eAAKD,YAAL,GAAoB,IAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIO,QAAAA,YAAY,CAACF,SAAD,EAAiB;AACzB,cAAIA,SAAJ,EAAe;AACX,iBAAKlB,UAAL,GAAkB,IAAlB;AACA,iBAAKQ,eAAL,GAAuB,CAAvB;AACA,iBAAKJ,cAAL,GAAsBc,SAAS,CAACI,cAAhC;AACA,iBAAKf,UAAL,GAAkBW,SAAS,CAACK,SAA5B;AACA,iBAAKb,eAAL,GAAuBQ,SAAS,CAACM,cAAjC;AACH,WAND,MAMO;AACH,iBAAKxB,UAAL,GAAkB,KAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIyB,QAAAA,kBAAkB,CAACC,KAAD,EAAgB;AAC9B,eAAKf,gBAAL,GAAwBe,KAAxB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,kBAAkB,GAAG;AACjB,iBAAO,KAAKhB,gBAAZ;AACH;AAED;AACJ;AACA;AACA;;;AACIiB,QAAAA,aAAa,CAACC,UAAD,EAAsB;AAC/B,eAAK1B,UAAL,GAAkB0B,UAAlB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,WAAW,CAACC,QAAD,EAAoB;AAC3B,eAAK9B,SAAL,GAAiB8B,QAAjB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT,eAAK7B,UAAL,GAAkB,IAAlB;AACH;AAED;AACJ;AACA;;;AACI8B,QAAAA,SAAS,GAAG;AACR,eAAKxB,cAAL,GAAsB,IAAtB;AACA,eAAKJ,UAAL,GAAkB,CAAlB;AACA,eAAKG,eAAL,GAAuB,CAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACI0B,QAAAA,eAAe,CAACC,QAAD,EAAqB;AAChC,eAAKrB,aAAL,GAAqBqB,QAArB;AACH;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,cAAc,CAACD,QAAD,EAAqB;AAC/B,eAAKtB,YAAL,GAAoBsB,QAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAI,KAAKtC,UAAT,EAAqB;AACjB,gBAAI,KAAKF,OAAL,CAAayC,UAAb,GAA0B,CAA1B,IAA+B;AAAA;AAAA,gCAAMC,gBAAN,CAAuB/C,EAAE,CAAC,KAAKgD,IAAL,CAAUC,QAAV,CAAmBC,CAApB,EAAuB,KAAKF,IAAL,CAAUC,QAAV,CAAmBE,CAA1C,CAAzB,CAAnC,EAA2G;AACvG;AACH;;AACD,iBAAKC,YAAL,CAAkBP,SAAlB;;AACA,gBAAI,KAAKrC,SAAT,EAAoB;AAChB,mBAAK6C,gBAAL,CAAsBR,SAAtB;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACIjB,QAAAA,cAAc,GAAG,CAEhB;AAED;AACJ;AACA;;;AACI0B,QAAAA,YAAY,GAAG;AACX,eAAK1C,UAAL,GAAkB,CAAlB;;AACA,cAAI,KAAKS,aAAT,EAAwB;AACpB,iBAAKA,aAAL;AACH;AACJ;AAED;AACJ;AACA;;;AACIkC,QAAAA,kBAAkB,GAAG;AACjB,cAAI,CAAC,KAAKpC,aAAN,IAAuB,KAAKD,gBAAL,GAAwB,CAAnD,EAAsD;AAClD,iBAAKN,UAAL,GAAkB,KAAKD,cAAvB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACsB,cAAZyC,YAAY,CAACP,SAAD,EAAoB,CAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACIQ,QAAAA,gBAAgB,CAACR,SAAD,EAAoB;AAChC,cAAI,CAAC,KAAKnC,UAAN,IAAoB,KAAKM,cAA7B,EAA6C;AACzC,iBAAKJ,UAAL,IAAmBiC,SAAnB;;AACA,gBAAI,KAAKW,YAAL,MAAuB,KAAK3C,WAAL,GAAmB,KAAK4C,aAAL,EAA9C,EAAoE;AAChE,kBAAI,KAAKtC,aAAL,IAAsB,KAAKD,gBAAL,IAAyB,CAAnD,EAAsD;AAClD,oBAAI,KAAKN,UAAL,GAAkB,KAAKM,gBAA3B,EAA6C;AACzC,uBAAKwC,YAAL;AACH;AACJ,eAJD,MAIO,IAAI,KAAK9C,UAAL,GAAkB,KAAKD,cAA3B,EAA2C;AAC9C,qBAAK+C,YAAL;AACH;AACJ;AACJ;AACJ;AAED;AACJ;AACA;;;AACIC,QAAAA,gBAAgB,GAAG,CACf;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;;;AACID,QAAAA,YAAY,GAAG;AACX,eAAK7C,WAAL;AACA,eAAKE,eAAL,GAAuB,CAAvB;;AACA,eAAK4C,gBAAL;;AACA,eAAKL,YAAL;AACH;AAED;AACJ;AACA;AACA;;;AACIE,QAAAA,YAAY,GAAG;AACX,iBAAO,KAAK1C,UAAL,KAAoB,CAAC,CAA5B;AACH;AAED;AACJ;AACA;AACA;;;AACI2C,QAAAA,aAAa,GAAG;AACZ,iBAAO,KAAK3C,UAAZ;AACH;;AApRsD,O", "sourcesContent": ["import { _decorator, Component, Node, UITransform, v2 } from 'cc';\r\nimport { Tools } from '../../../utils/Tools';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('EnemyShootComponent')\r\nexport default class EnemyShootComponent extends Component {\r\n    _target: any = null;\r\n    _atkNode: Node|null = null;\r\n    _shootAble: boolean = false;\r\n    _nextAble: boolean = false;\r\n    bulletTurn: boolean = false;\r\n    _bShooting: boolean = false;\r\n    _shootInterval: number = 0;\r\n    _shootTime: number = 0;\r\n    _shootCount: number = 0;\r\n    _attackNum: number = 0;\r\n    _attackArrIndex: number = 0;\r\n    _attackArrOver: boolean = true;\r\n    _attackPointArr: any[] = [];\r\n    _firstShootDelay: number = -1;\r\n    _isFirstShoot: boolean = true;\r\n    _atkOverCall: Function|null = null;\r\n    _atkStartCall: Function|null = null;\r\n\r\n    /**\r\n     * 初始化射击组件\r\n     * @param target 目标对象\r\n     * @param atkNode 攻击节点\r\n     * @param shootData 射击数据\r\n     * @param bulletTurn 是否允许子弹转向\r\n     */\r\n    init(target: any, atkNode: Node, shootData: any, bulletTurn: boolean = false) {\r\n        this.reset();\r\n        this.bulletTurn = bulletTurn;\r\n        this._target = target;\r\n        this._atkNode = atkNode;\r\n        this.setShootData(shootData);\r\n        this._initAtkPoints();\r\n    }\r\n\r\n    /**\r\n     * 重置射击组件\r\n     */\r\n    reset() {\r\n        this._nextAble = false;\r\n        this._bShooting = false;\r\n        this._shootInterval = 0;\r\n        this._shootTime = 0;\r\n        this._shootCount = 0;\r\n        this._attackNum = 0;\r\n        this._attackArrIndex = 0;\r\n        this._attackArrOver = true;\r\n        this._attackPointArr = [];\r\n        this._isFirstShoot = true;\r\n        this._firstShootDelay = -1;\r\n        this._atkStartCall = null;\r\n        this._atkOverCall = null;\r\n    }\r\n\r\n    /**\r\n     * 设置射击数据\r\n     * @param shootData 射击数据\r\n     */\r\n    setShootData(shootData: any) {\r\n        if (shootData) {\r\n            this._shootAble = true;\r\n            this._attackArrIndex = 0;\r\n            this._shootInterval = shootData.attackInterval;\r\n            this._attackNum = shootData.attackNum;\r\n            this._attackPointArr = shootData.attackPointArr;\r\n        } else {\r\n            this._shootAble = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置首次射击延迟\r\n     * @param delay 延迟时间\r\n     */\r\n    setFirstShootDelay(delay: number) {\r\n        this._firstShootDelay = delay;\r\n    }\r\n\r\n    /**\r\n     * 获取首次射击延迟\r\n     * @returns {number} 首次射击延迟\r\n     */\r\n    getFirstShootDelay() {\r\n        return this._firstShootDelay;\r\n    }\r\n\r\n    /**\r\n     * 设置是否正在射击\r\n     * @param isShooting 是否射击\r\n     */\r\n    setIsShooting(isShooting: boolean) {\r\n        this._bShooting = isShooting;\r\n    }\r\n\r\n    /**\r\n     * 设置是否可以进行下一次射击\r\n     * @param nextAble 是否可以进行下一次射击\r\n     */\r\n    setNextAble(nextAble: boolean) {\r\n        this._nextAble = nextAble;\r\n    }\r\n\r\n    /**\r\n     * 开始射击\r\n     */\r\n    startShoot() {\r\n        this._bShooting = true;\r\n    }\r\n\r\n    /**\r\n     * 停止射击\r\n     */\r\n    stopShoot() {\r\n        this._attackArrOver = true;\r\n        this._shootTime = 0;\r\n        this._attackArrIndex = 0;\r\n    }\r\n\r\n    /**\r\n     * 设置攻击开始的回调\r\n     * @param callback 回调函数\r\n     */\r\n    setAtkStartCall(callback: Function) {\r\n        this._atkStartCall = callback;\r\n    }\r\n\r\n    /**\r\n     * 设置攻击结束的回调\r\n     * @param callback 回调函数\r\n     */\r\n    setAtkOverCall(callback: Function) {\r\n        this._atkOverCall = callback;\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 帧间隔时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        if (this._shootAble) {\r\n            if (this._target.sceneLayer < 0 && Tools.isPlaneOutScreen(v2(this.node.position.x, this.node.position.y))) {\r\n                return;\r\n            }\r\n            this._updateShoot(deltaTime);\r\n            if (this._nextAble) {\r\n                this._updateNextShoot(deltaTime);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 初始化攻击点\r\n     */\r\n    _initAtkPoints() {\r\n\r\n    }\r\n\r\n    /**\r\n     * 准备射击\r\n     */\r\n    shootPrepare() {\r\n        this._shootTime = 0;\r\n        if (this._atkStartCall) {\r\n            this._atkStartCall();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 立即进行下一次射击\r\n     */\r\n    setNextShootAtOnce() {\r\n        if (!this._isFirstShoot || this._firstShootDelay < 0) {\r\n            this._shootTime = this._shootInterval;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新射击逻辑\r\n     * @param deltaTime 帧间隔时间\r\n     */\r\n    async _updateShoot(deltaTime: number) {\r\n        // if (this._bShooting) {\r\n        //     let allOver = true;\r\n        //     for (const point of this._attackPoints) {\r\n        //         await point.updateGameLogic(deltaTime);\r\n        //         if (!point.isAttackOver()) {\r\n        //             allOver = false;\r\n        //         }\r\n        //     }\r\n        //     if (allOver) {\r\n        //         this._shootTime = 0;\r\n        //         this._bShooting = false;\r\n        //         this._attackArrOver = true;\r\n        //         this._attackArrIndex++;\r\n        //         if (this._attackArrIndex < this._attackPointArr.length) {\r\n        //             this._setNextShootArr();\r\n        //             this.shootPrepare();\r\n        //         } else if (this._atkOverCall) {\r\n        //             this._atkOverCall();\r\n        //         }\r\n        //     }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 更新下一次射击逻辑\r\n     * @param deltaTime 帧间隔时间\r\n     */\r\n    _updateNextShoot(deltaTime: number) {\r\n        if (!this._bShooting && this._attackArrOver) {\r\n            this._shootTime += deltaTime;\r\n            if (this._isShootLoop() || this._shootCount < this._getShootLoop()) {\r\n                if (this._isFirstShoot && this._firstShootDelay >= 0) {\r\n                    if (this._shootTime > this._firstShootDelay) {\r\n                        this.setNextShoot();\r\n                    }\r\n                } else if (this._shootTime > this._shootInterval) {\r\n                    this.setNextShoot();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 设置下一次射击的攻击点数组\r\n     */\r\n    _setNextShootArr() {\r\n        // this._attackArrOver = false;\r\n        // this._attackPoints.splice(0);\r\n\r\n        // const attackPoints = this._attackPointArr[this._attackArrIndex];\r\n        // if (attackPoints && attackPoints.length > 0) {\r\n        //     for (let i = 0; i < attackPoints.length; i++) {\r\n        //         let atkPoint = this._atkPointsPool[i];\r\n        //         if (!atkPoint) {\r\n        //             const node = new Node();\r\n        //             node.addComponent(UITransform);\r\n        //             node.angle = -180;\r\n        //             this._atkNode!.addChild(node);\r\n        //             atkPoint = node.addComponent(AttackPoint);\r\n        //             this._atkPointsPool.push(atkPoint);\r\n        //         }\r\n        //         atkPoint.initForEnemy(attackPoints[i], this._target);\r\n        //         atkPoint.bulletTurn = this.bulletTurn;\r\n        //         this._attackPoints.push(atkPoint);\r\n        //     }\r\n        //     this._isFirstShoot = false;\r\n        // } else {\r\n        //     Tools.log('shoot error');\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 立即进行下一次射击\r\n     */\r\n    setNextShoot() {\r\n        this._shootCount++;\r\n        this._attackArrIndex = 0;\r\n        this._setNextShootArr();\r\n        this.shootPrepare();\r\n    }\r\n\r\n    /**\r\n     * 是否为循环射击\r\n     * @returns {boolean} 是否循环\r\n     */\r\n    _isShootLoop() {\r\n        return this._attackNum === -1;\r\n    }\r\n\r\n    /**\r\n     * 获取射击循环次数\r\n     * @returns {number} 循环次数\r\n     */\r\n    _getShootLoop() {\r\n        return this._attackNum;\r\n    }\r\n}"]}