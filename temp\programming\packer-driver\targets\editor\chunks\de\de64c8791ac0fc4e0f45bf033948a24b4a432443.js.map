{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/Tabs.ts"], "names": ["_decorator", "Component", "Sprite", "SpriteFrame", "EventMgr", "PlaneUIEvent", "ButtonPlus", "TabStatus", "ccclass", "property", "Tabs", "_tabStatus", "None", "onLoad", "tabBagBtn", "addClick", "onClick", "tabMergeBtn", "init", "setTabStatus", "node", "event", "btnNode", "target", "Bag", "getComponent", "spriteFrame", "pressSpriteFrame", "releaseSpriteFrame", "<PERSON><PERSON>", "emit", "TabChange"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAA6BC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,W,OAAAA,W;;AACjDC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,S,iBAAAA,S;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;sBAGjBU,I,WADZF,OAAO,CAAC,MAAD,C,UAEHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ,CAACN,WAAD,C,UAERM,QAAQ,CAACN,WAAD,C,2BATb,MACaO,IADb,SAC0BT,SAD1B,CACoC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAWxBU,UAXwB,GAWA;AAAA;AAAA,sCAAUC,IAXV;AAAA;;AAatBC,QAAAA,MAAM,GAAS;AACrB,eAAKC,SAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACA,eAAKC,WAAL,CAAiBF,QAAjB,CAA0B,KAAKC,OAA/B,EAAwC,IAAxC;AACH;;AAEDE,QAAAA,IAAI,GAAG;AACH,eAAKC,YAAL,CAAkB,KAAKL,SAAL,CAAeM,IAAjC;AACH;;AAEOJ,QAAAA,OAAO,CAACK,KAAD,EAA0B;AACrC,gBAAMC,OAAO,GAAGD,KAAK,CAACE,MAAtB;AACA,eAAKJ,YAAL,CAAkBG,OAAlB;AACH;;AAEOH,QAAAA,YAAY,CAACG,OAAD,EAAgB;AAChC,kBAAQA,OAAR;AACI,iBAAK,KAAKR,SAAL,CAAeM,IAApB;AACI,mBAAKT,UAAL,GAAkB;AAAA;AAAA,0CAAUa,GAA5B;AACA,mBAAKV,SAAL,CAAeW,YAAf,CAA4BvB,MAA5B,EAAqCwB,WAArC,GAAmD,KAAKC,gBAAxD;AACA,mBAAKV,WAAL,CAAiBQ,YAAjB,CAA8BvB,MAA9B,EAAuCwB,WAAvC,GAAqD,KAAKE,kBAA1D;AACA;;AACJ,iBAAK,KAAKX,WAAL,CAAiBG,IAAtB;AACI,mBAAKT,UAAL,GAAkB;AAAA;AAAA,0CAAUkB,KAA5B;AACA,mBAAKZ,WAAL,CAAiBQ,YAAjB,CAA8BvB,MAA9B,EAAuCwB,WAAvC,GAAqD,KAAKC,gBAA1D;AACA,mBAAKb,SAAL,CAAeW,YAAf,CAA4BvB,MAA5B,EAAqCwB,WAArC,GAAmD,KAAKE,kBAAxD;AACA;;AACJ;AACI;AACA;AAbR;;AAeA;AAAA;AAAA,oCAASE,IAAT,CAAc;AAAA;AAAA,4CAAaC,SAA3B,EAAsC,KAAKpB,UAA3C;AACH;;AA5C+B,O;;;;;iBAER,I;;;;;;;iBAEE,I;;;;;;;iBAGM,I;;;;;;;iBAEE,I", "sourcesContent": ["import { _decorator, Component, EventTouch, Node, Sprite, SpriteFrame } from 'cc';\nimport { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';\n\nimport { PlaneUIEvent } from 'db://assets/bundles/common/script/event/PlaneUIEvent';\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\nimport { TabStatus } from '../../PlaneTypes';\nconst { ccclass, property } = _decorator;\n\n@ccclass('Tabs')\nexport class Tabs extends Component {\n    @property(ButtonPlus)\n    tabBagBtn: ButtonPlus = null!;\n    @property(ButtonPlus)\n    tabMergeBtn: ButtonPlus = null!;\n\n    @property(SpriteFrame)\n    pressSpriteFrame: SpriteFrame = null!;\n    @property(SpriteFrame)\n    releaseSpriteFrame: SpriteFrame = null!;\n\n    private _tabStatus: TabStatus = TabStatus.None;\n\n    protected onLoad(): void {\n        this.tabBagBtn.addClick(this.onClick, this);\n        this.tabMergeBtn.addClick(this.onClick, this);\n    }\n\n    init() {\n        this.setTabStatus(this.tabBagBtn.node);\n    }\n\n    private onClick(event: EventTouch): void {\n        const btnNode = event.target as Node;\n        this.setTabStatus(btnNode);\n    }\n\n    private setTabStatus(btnNode: Node) {\n        switch (btnNode) {\n            case this.tabBagBtn.node:\n                this._tabStatus = TabStatus.Bag;\n                this.tabBagBtn.getComponent(Sprite)!.spriteFrame = this.pressSpriteFrame;\n                this.tabMergeBtn.getComponent(Sprite)!.spriteFrame = this.releaseSpriteFrame;\n                break;\n            case this.tabMergeBtn.node:\n                this._tabStatus = TabStatus.Merge;\n                this.tabMergeBtn.getComponent(Sprite)!.spriteFrame = this.pressSpriteFrame;\n                this.tabBagBtn.getComponent(Sprite)!.spriteFrame = this.releaseSpriteFrame;\n                break;\n            default:\n                //logError(\"PlaneUI\", `Tabs setTabStatus error ${btnNode.name}`)\n                return;\n        }\n        EventMgr.emit(PlaneUIEvent.TabChange, this._tabStatus);\n    }\n}"]}