{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts"], "names": ["DataEvent", "ItemsRefresh", "EquipSlotRefresh", "GamePvpGetAward", "GamePvpGetList", "GamePvpMatchSuc", "BattleItemClick", "RogueSelectClick"], "mappings": ";;;;;;;;;;;;;;2BAAaA,S,GAAY;AACrBC,QAAAA,YAAY,EAAE,wBADO;AAErBC,QAAAA,gBAAgB,EAAE,4BAFG;AAGrBC,QAAAA,eAAe,EAAE,2BAHI;AAIrBC,QAAAA,cAAc,EAAE,0BAJK;AAKrBC,QAAAA,eAAe,EAAE,2BALI;AAMrBC,QAAAA,eAAe,EAAE,2BANI;AAOrBC,QAAAA,gBAAgB,EAAE;AAPG,O", "sourcesContent": ["export const DataEvent = {\n    ItemsRefresh: 'DataEvent_ItemsRefresh',\n    EquipSlotRefresh: 'DataEvent_EquipSlotRefresh',\n    GamePvpGetAward: 'DataEvent_GamePvpGetAward',\n    GamePvpGetList: 'DataEvent_GamePvpGetList',\n    GamePvpMatchSuc: 'DataEvent_GamePvpMatchSuc',\n    BattleItemClick: 'DataEvent_BattleItemClick',\n    RogueSelectClick: 'DataEvent_RogueSelectClick',\n}"]}