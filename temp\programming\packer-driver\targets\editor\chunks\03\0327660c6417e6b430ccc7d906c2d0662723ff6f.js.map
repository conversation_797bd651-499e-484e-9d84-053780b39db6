{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts"], "names": ["_decorator", "GameIns", "Entity", "FCollider", "ColliderGroupType", "ccclass", "property", "Bullet", "enemy", "attack", "onLoad", "collide<PERSON>omp", "isEnable", "initData", "isEnemy", "colliderGroupType", "BULLET_SELF", "groupType", "getAttack", "playHurtAudio", "onCollide", "collider", "remove", "onOutScreen", "force", "will<PERSON><PERSON><PERSON>", "bulletManager", "removeBullet", "dieRemove"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,M;;AACAC,MAAAA,S;AAAaC,MAAAA,iB,iBAAAA,iB;;;;;;;;;OAEd;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;yBAGTO,M,WADpBF,OAAO,CAAC,QAAD,C,UAGHC,QAAQ;AAAA;AAAA,iC,2BAHb,MACqBC,MADrB;AAAA;AAAA,4BAC2C;AAAA;AAAA;;AAAA;;AAAA,eAKvCC,KALuC,GAK/B,KAL+B;AAAA,eAMvCC,MANuC,GAMvB,CANuB;AAAA;;AAQ7BC,QAAAA,MAAM,GAAS;AACrB,eAAKC,WAAL,CAAkBC,QAAlB,GAA6B,IAA7B;AACH;;AAGDC,QAAAA,QAAQ,CAACC,OAAD,EAAkBL,MAAa,GAAG,CAAlC,EAAoCM,iBAAwB,GAAG;AAAA;AAAA,oDAAkBC,WAAjF,EAA8F;AAClG,eAAKR,KAAL,GAAaM,OAAb;AACA,eAAKL,MAAL,GAAcA,MAAd;AACA,eAAKE,WAAL,CAAkBM,SAAlB,GAA8BF,iBAA9B;AACH;;AAEDG,QAAAA,SAAS,GAAU;AACf,iBAAO,KAAKT,MAAZ;AACH;AAED;AACJ;AACA;;;AACIU,QAAAA,aAAa,GAAG,CACZ;AACA;AACA;AACH;;AAEDC,QAAAA,SAAS,CAACC,QAAD,EAAqB;AAC1B,eAAKC,MAAL,CAAY,IAAZ;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,WAAW,GAAG;AACV,eAAKD,MAAL;AACH;AAED;AACJ;AACA;AACA;;;AACIA,QAAAA,MAAM,CAACE,KAAK,GAAG,KAAT,EAAgB;AAClB,eAAKC,UAAL;AACA;AAAA;AAAA,kCAAQC,aAAR,CAAsBC,YAAtB,CAAmC,IAAnC;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,SAAS,GAAG;AACR,eAAKH,UAAL;AACH;AAED;AACJ;AACA;;;AACIA,QAAAA,UAAU,GAAG;AACT,cAAI,KAAKd,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBC,QAAjB,GAA4B,KAA5B;AACH;AACJ;;AAlEsC,O;;;;;iBAGR,I", "sourcesContent": ["import { _decorator} from 'cc';\r\nimport { GameIns } from '../../GameIns';\r\nimport Entity from '../base/Entity';\r\nimport FCollider, { ColliderGroupType } from '../../collider-system/FCollider';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('Bullet')\r\nexport default class Bullet extends Entity {\r\n\r\n    @property(FCollider)\r\n    collideComp:FCollider|  null = null;\r\n\r\n    enemy = false;\r\n    attack:number = 0;\r\n\r\n    protected onLoad(): void {\r\n        this.collideComp!.isEnable = true;\r\n    }\r\n\r\n\r\n    initData(isEnemy:boolean, attack:number = 0,colliderGroupType:number = ColliderGroupType.BULLET_SELF) {\r\n        this.enemy = isEnemy;\r\n        this.attack = attack;\r\n        this.collideComp!.groupType = colliderGroupType;\r\n    }\r\n\r\n    getAttack():number {\r\n        return this.attack;\r\n    }\r\n    \r\n    /**\r\n     * 播放子弹命中音效\r\n     */\r\n    playHurtAudio() {\r\n        // if (this.m_config.hit.length > 0) {\r\n        //     Bullet.playAudio('hit2');\r\n        // }\r\n    }\r\n\r\n    onCollide(collider:<PERSON>ollider) {\r\n        this.remove(true);\r\n    }\r\n\r\n    /**\r\n     * 子弹超出屏幕处理\r\n     */\r\n    onOutScreen() {\r\n        this.remove();\r\n    }\r\n\r\n    /**\r\n     * 移除子弹\r\n     * @param {boolean} force 是否强制移除\r\n     */\r\n    remove(force = false) {\r\n        this.willRemove();\r\n        GameIns.bulletManager.removeBullet(this);\r\n    }\r\n\r\n    /**\r\n     * 子弹死亡移除\r\n     */\r\n    dieRemove() {\r\n        this.willRemove();\r\n    }\r\n\r\n    /**\r\n     * 子弹移除前的清理操作\r\n     */\r\n    willRemove() {\r\n        if (this.collideComp) {\r\n            this.collideComp.isEnable = false;\r\n        }\r\n    }\r\n}"]}