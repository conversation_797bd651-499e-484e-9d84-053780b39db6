System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Button, Label, Node, Sprite, BundleName, EventMgr, HomeUI, BaseUI, UILayer, UIMgr, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _dec8, _dec9, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _descriptor6, _descriptor7, _descriptor8, _crd, ccclass, property, DialogueUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "db://assets/bundles/common/script/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUI(extras) {
    _reporterNs.report("HomeUI", "db://assets/bundles/common/script/ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Button = _cc.Button;
      Label = _cc.Label;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      EventMgr = _unresolved_3.EventMgr;
    }, function (_unresolved_4) {
      HomeUI = _unresolved_4.HomeUI;
    }, function (_unresolved_5) {
      BaseUI = _unresolved_5.BaseUI;
      UILayer = _unresolved_5.UILayer;
      UIMgr = _unresolved_5.UIMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "9297diwgAlCdK6VR7ziSeNu", "DialogueUI", undefined);

      __checkObsolete__(['_decorator', 'Button', 'Label', 'Node', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("DialogueUI", DialogueUI = (_dec = ccclass('DialogueUI'), _dec2 = property(Node), _dec3 = property(Sprite), _dec4 = property(Label), _dec5 = property(Node), _dec6 = property(Sprite), _dec7 = property(Label), _dec8 = property(Label), _dec9 = property(Button), _dec(_class = (_class2 = class DialogueUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "nodeLeft", _descriptor, this);

          _initializerDefineProperty(this, "characterImageLeft", _descriptor2, this);

          _initializerDefineProperty(this, "characterNameLeft", _descriptor3, this);

          _initializerDefineProperty(this, "nodeRight", _descriptor4, this);

          _initializerDefineProperty(this, "characterImageRight", _descriptor5, this);

          _initializerDefineProperty(this, "characterNameRight", _descriptor6, this);

          _initializerDefineProperty(this, "dialogueContent", _descriptor7, this);

          _initializerDefineProperty(this, "btnClick", _descriptor8, this);

          //data
          this.dialogueID = 0;
          this.dialogueContentList = [];
          this.dialogueIndex = 0;
          this.currentIndex = 0;
          this.doing = false;
          this.doingFast = false;
          this.text = void 0;
        }

        static getUrl() {
          return "prefab/ui/dialogue/DialogueUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home;
        }

        onLoad() {
          var randomDialogues = ["这组坐标指向的星球不该存在大气层。工程师敲着全息屏，眉头紧锁，但探测器却传回了植物光合作用的光谱。", "会不会是仪器故障？船长调出航行日志，毕竟上次校准已经是三个月前的事了。", "不，数据太规律了。科学家放大图像，那些绿色斑块每天固定时间收缩扩张，像在呼吸。", "立刻准备小型登陆舱。船长眼神锐利起来，如果真有生命，我们可能是第一批发现者——或者入侵者。"];

          for (var i = 0; i < randomDialogues.length; i++) {
            this.dialogueContentList.push(randomDialogues[i]);
          }

          this.text = this.dialogueContentList[this.dialogueIndex];
          this.startTypewriter();
          this.btnClick.node.on('click', this.onClick, this);
          this.nodeRight.active = false;
        }

        onClick() {
          var _this = this;

          return _asyncToGenerator(function* () {
            if (_this.doingFast) {
              return;
            }

            if (_this.doing) {
              _this.startTypewriterFast();

              return;
            }

            _this.nextDialogue();
          })();
        }

        nextDialogue() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            _this2.dialogueIndex++;

            if (_this2.dialogueIndex >= _this2.dialogueContentList.length) {
              _this2.dialogueIndex = 0;
              _this2.btnClick.node.active = false;
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).closeUI(DialogueUI);
              yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
                error: Error()
              }), HomeUI) : HomeUI);
              return;
            }

            if (_this2.dialogueIndex % 2 === 0) {
              _this2.nodeRight.active = false;
              _this2.nodeLeft.active = true;
            } else {
              _this2.nodeLeft.active = false;
              _this2.nodeRight.active = true;
            }

            _this2.text = _this2.dialogueContentList[_this2.dialogueIndex];

            _this2.startTypewriter();
          })();
        }

        startTypewriter() {
          if (this.text == "") return;
          this.currentIndex = 0;
          this.dialogueContent.string = "";
          this.doing = true;
          this.unschedule(this.startTypewriter);
          this.unschedule(this.startTypewriterFast);
          this.schedule(() => {
            if (this.currentIndex < this.text.length) {
              this.dialogueContent.string += this.text.charAt(this.currentIndex);
              this.currentIndex++;
            } else {
              this.stopTypewriter();
              this.text = "";
            }
          }, 0.05);
        }

        startTypewriterFast() {
          this.doingFast = true;
          this.unschedule(this.startTypewriter);
          this.unschedule(this.startTypewriterFast);
          this.schedule(() => {
            if (this.currentIndex < this.text.length) {
              this.dialogueContent.string += this.text.charAt(this.currentIndex);
              this.currentIndex++;
            } else {
              this.stopTypewriterFast();
              this.text = "";
            }
          }, 0.01);
        }

        stopTypewriter() {
          this.unschedule(this.startTypewriter);
          this.doing = false;
        }

        stopTypewriterFast() {
          this.unschedule(this.startTypewriterFast);
          this.doingFast = false;
        }

        onShow(dialogueID) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            _this3.dialogueID = dialogueID;
          })();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "nodeLeft", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "characterImageLeft", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "characterNameLeft", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "nodeRight", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "characterImageRight", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class2.prototype, "characterNameRight", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor7 = _applyDecoratedDescriptor(_class2.prototype, "dialogueContent", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor8 = _applyDecoratedDescriptor(_class2.prototype, "btnClick", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0a229576f85b8e9f292ce0e200faa9f76f182eac.js.map