System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, DataMgr, DataEvent, EventMgr, MyApp, UIMgr, PlaneUIEvent, ButtonPlus, PlaneEquipInfoUI, OpenEquipInfoUISource, TabStatus, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, EquipDisplay;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "db://assets/bundles/common/script/data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "db://assets/bundles/common/script/event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "db://assets/bundles/common/script/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../../../../../../scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUIEvent(extras) {
    _reporterNs.report("PlaneUIEvent", "../../../../event/PlaneUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneEquipInfoUI(extras) {
    _reporterNs.report("PlaneEquipInfoUI", "../../PlaneEquipInfoUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfOpenEquipInfoUISource(extras) {
    _reporterNs.report("OpenEquipInfoUISource", "../../PlaneTypes", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabStatus(extras) {
    _reporterNs.report("TabStatus", "../../PlaneTypes", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
    }, function (_unresolved_2) {
      DataMgr = _unresolved_2.DataMgr;
    }, function (_unresolved_3) {
      DataEvent = _unresolved_3.DataEvent;
    }, function (_unresolved_4) {
      EventMgr = _unresolved_4.EventMgr;
    }, function (_unresolved_5) {
      MyApp = _unresolved_5.MyApp;
    }, function (_unresolved_6) {
      UIMgr = _unresolved_6.UIMgr;
    }, function (_unresolved_7) {
      PlaneUIEvent = _unresolved_7.PlaneUIEvent;
    }, function (_unresolved_8) {
      ButtonPlus = _unresolved_8.ButtonPlus;
    }, function (_unresolved_9) {
      PlaneEquipInfoUI = _unresolved_9.PlaneEquipInfoUI;
    }, function (_unresolved_10) {
      OpenEquipInfoUISource = _unresolved_10.OpenEquipInfoUISource;
      TabStatus = _unresolved_10.TabStatus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "afe02lrJfFNhJQRTXUrYJ/Y", "EquipDisplay", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("EquipDisplay", EquipDisplay = (_dec = ccclass('EquipDisplay'), _dec2 = property([_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus]), _dec(_class = (_class2 = class EquipDisplay extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "equipBtns", _descriptor, this);
        }

        onLoad() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).TabChange, this.onTabChange, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).EquipSlotRefresh, this.onEquipSlotRefresh, this);
          this.equipBtns.forEach(v => v.addClick(this.onClickEquip, this));
        }

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

        onTabChange(tabStatus) {
          if (tabStatus == (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).Bag) {
            this.node.active = true;
            this.onEquipDisplayRefresh();
          } else {
            this.node.active = false;
          }
        }

        onEquipSlotRefresh() {
          this.onEquipDisplayRefresh();
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).bag.refreshItems();
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).hideUI(_crd && PlaneEquipInfoUI === void 0 ? (_reportPossibleCrUseOfPlaneEquipInfoUI({
            error: Error()
          }), PlaneEquipInfoUI) : PlaneEquipInfoUI);
        }

        onEquipDisplayRefresh() {
          var TbEquip = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbEquip;
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqSlots.slots.forEach(v => {
            var btn = this.equipBtns[v.slot_id - 1];

            if (v.guid && v.guid.gt(0)) {
              var _TbEquip$get;

              btn.getComponentInChildren(Label).string = ((_TbEquip$get = TbEquip.get(v.equip_id)) == null ? void 0 : _TbEquip$get.name) || '未知';
            } else {
              btn.getComponentInChildren(Label).string = '空';
            }
          });
        }

        onClickEquip(event) {
          var btnNode = event.target;
          var slotID = parseInt(btnNode.name);
          var info = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqSlots.getEquipSlotInfo(slotID);
          if (!info || !info.guid || info.guid.lte(0)) return;
          (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
            error: Error()
          }), UIMgr) : UIMgr).openUI(_crd && PlaneEquipInfoUI === void 0 ? (_reportPossibleCrUseOfPlaneEquipInfoUI({
            error: Error()
          }), PlaneEquipInfoUI) : PlaneEquipInfoUI, info, (_crd && OpenEquipInfoUISource === void 0 ? (_reportPossibleCrUseOfOpenEquipInfoUISource({
            error: Error()
          }), OpenEquipInfoUISource) : OpenEquipInfoUISource).DisPlay);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "equipBtns", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=6eec49bf5ae9e4658372fe8aca5870a2418f6ce8.js.map