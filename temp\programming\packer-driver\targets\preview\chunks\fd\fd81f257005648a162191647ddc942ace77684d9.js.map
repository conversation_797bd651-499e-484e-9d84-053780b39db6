{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueSelectIcon.ts"], "names": ["_decorator", "CCInteger", "Label", "Sprite", "BundleName", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "DataEvent", "EventMgr", "ButtonPlus", "ccclass", "property", "RogueSelectIcon", "status", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "Home", "onLoad", "selectIcon", "node", "active", "delIcon", "getComponent", "addClick", "onSelect", "getComponentInChildren", "onExcludeClick", "emit", "RogueSelectClick", "index", "updateStatus", "updateActive", "selectIndex", "onDestroy", "onShow", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAC9BC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;AACRC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAGH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;iCAGjBY,e,WADZF,OAAO,CAAC,iBAAD,C,UAGHC,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACT,KAAD,C,UAERS,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ,CAACV,SAAD,C,2BAfb,MACaW,eADb;AAAA;AAAA,4BAC4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAgBxCC,MAhBwC,GAgBvB,CAhBuB;AAAA;;AAkBpB,eAANC,MAAM,GAAW;AAAE,iBAAO,iCAAP;AAA2C;;AACtD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACtDC,QAAAA,MAAM,GAAS;AACrB,eAAKC,UAAL,CAAiBC,IAAjB,CAAsBC,MAAtB,GAA+B,KAA/B;AACA,eAAKC,OAAL,CAAcF,IAAd,CAAmBC,MAAnB,GAA4B,KAA5B;AACA,eAAKE,YAAL;AAAA;AAAA,wCAA+BC,QAA/B,CAAwC,KAAKC,QAA7C,EAAuD,IAAvD;AACA,eAAKH,OAAL,CAAcI,sBAAd;AAAA;AAAA,wCAAkDF,QAAlD,CAA2D,KAAKG,cAAhE,EAAgF,IAAhF;AACH;;AACDA,QAAAA,cAAc,GAAG;AACb,eAAKP,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACH;;AACOI,QAAAA,QAAQ,GAAG;AACf,cAAI,KAAKb,MAAL,IAAe,CAAnB,EAAsB;AAClB,gBAAI,KAAKO,UAAL,CAAiBC,IAAjB,CAAsBC,MAA1B,EAAkC;AAC9B;AAAA;AAAA,wCAASO,IAAT,CAAc;AAAA;AAAA,0CAAUC,gBAAxB,EAA0C,CAA1C;AACH,aAFD,MAEO;AACH;AAAA;AAAA,wCAASD,IAAT,CAAc;AAAA;AAAA,0CAAUC,gBAAxB,EAA0C,KAAKC,KAA/C;AACH;AACJ,WAND,MAMO,IAAI,KAAKlB,MAAL,IAAe,CAAnB,EAAsB;AACzB,gBAAI,KAAKU,OAAL,CAAcF,IAAd,CAAmBC,MAAvB,EAA+B;AAC3B;AAAA;AAAA,wCAASO,IAAT,CAAc;AAAA;AAAA,0CAAUC,gBAAxB,EAA0C,CAA1C;AACH,aAFD,MAEO;AACH;AAAA;AAAA,wCAASD,IAAT,CAAc;AAAA;AAAA,0CAAUC,gBAAxB,EAA0C,KAAKC,KAA/C;AACH;AAEJ;AACJ;;AAEMC,QAAAA,YAAY,CAACnB,MAAD,EAAiB;AAChC,eAAKA,MAAL,GAAcA,MAAd;AACH;;AAEMoB,QAAAA,YAAY,CAACC,WAAD,EAAsB;AACrC,cAAI,KAAKrB,MAAL,IAAe,CAAnB,EAAsB;AAClB,iBAAKU,OAAL,CAAcF,IAAd,CAAmBC,MAAnB,GAA4B,KAA5B;;AACA,gBAAIY,WAAW,IAAI,KAAKH,KAAxB,EAA+B;AAC3B,mBAAKX,UAAL,CAAiBC,IAAjB,CAAsBC,MAAtB,GAA+B,IAA/B;AACH,aAFD,MAEO;AACH,mBAAKF,UAAL,CAAiBC,IAAjB,CAAsBC,MAAtB,GAA+B,KAA/B;AACH;AACJ,WAPD,MAOO,IAAI,KAAKT,MAAL,IAAe,CAAnB,EAAsB;AACzB,iBAAKO,UAAL,CAAiBC,IAAjB,CAAsBC,MAAtB,GAA+B,KAA/B;;AACA,gBAAIY,WAAW,IAAI,KAAKH,KAAxB,EAA+B;AAC3B,mBAAKR,OAAL,CAAcF,IAAd,CAAmBC,MAAnB,GAA4B,IAA5B;AACH,aAFD,MAEO;AACH,mBAAKC,OAAL,CAAcF,IAAd,CAAmBC,MAAnB,GAA4B,KAA5B;AACH;AACJ;AACJ;;AAESa,QAAAA,SAAS,GAAS,CAAG;;AAEzBC,QAAAA,MAAM,GAAgC;AAAA;AAAG;;AAEzCC,QAAAA,MAAM,GAAgC;AAAA;AAAG;;AAEzCC,QAAAA,OAAO,GAAgC;AAAA;AAAG;;AA3ER,O;;;;;iBAGb,I;;;;;;;iBAED,I;;;;;;;iBAEA,I;;;;;;;iBAED,I;;;;;;;iBAEG,I;;;;;;;iBAEH,I;;;;;;;iBAET,C", "sourcesContent": ["import { _decorator, CCInteger, Label, Sprite } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/Bundle';\r\nimport { BaseUI, UILayer } from '../../../../../../scripts/ui/UIMgr';\r\nimport { DataEvent } from '../../../event/DataEvent';\r\nimport { EventMgr } from '../../../event/EventManager';\r\nimport { ButtonPlus } from '../../common/components/button/ButtonPlus';\r\n\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"RogueSelectIcon\")\r\nexport class RogueSelectIcon extends BaseUI {\r\n\r\n    @property(Sprite)\r\n    rogueIcon: Sprite | null = null;\r\n    @property(Label)\r\n    rogueName: Label | null = null;\r\n    @property(Label)\r\n    rogueDesc: Label | null = null;\r\n    @property(Label)\r\n    rogueQua: Label | null = null;\r\n    @property(Sprite)\r\n    selectIcon: Sprite | null = null;\r\n    @property(Sprite)\r\n    delIcon: Sprite | null = null;\r\n    @property(CCInteger)\r\n    index: number = 0;\r\n    status: number = 1;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/fight/RogueSelectIcon\"; };\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    protected onLoad(): void {\r\n        this.selectIcon!.node.active = false;\r\n        this.delIcon!.node.active = false;\r\n        this.getComponent(ButtonPlus)!.addClick(this.onSelect, this);\r\n        this.delIcon!.getComponentInChildren(ButtonPlus)!.addClick(this.onExcludeClick, this);\r\n    }\r\n    onExcludeClick() {\r\n        this.node.active = false;\r\n    }\r\n    private onSelect() {\r\n        if (this.status == 1) {\r\n            if (this.selectIcon!.node.active) {\r\n                EventMgr.emit(DataEvent.RogueSelectClick, 0);\r\n            } else {\r\n                EventMgr.emit(DataEvent.RogueSelectClick, this.index);\r\n            }\r\n        } else if (this.status == 2) {\r\n            if (this.delIcon!.node.active) {\r\n                EventMgr.emit(DataEvent.RogueSelectClick, 0);\r\n            } else {\r\n                EventMgr.emit(DataEvent.RogueSelectClick, this.index);\r\n            }\r\n\r\n        }\r\n    }\r\n\r\n    public updateStatus(status: number) {\r\n        this.status = status;\r\n    }\r\n\r\n    public updateActive(selectIndex: number) {\r\n        if (this.status == 1) {\r\n            this.delIcon!.node.active = false;\r\n            if (selectIndex == this.index) {\r\n                this.selectIcon!.node.active = true;\r\n            } else {\r\n                this.selectIcon!.node.active = false;\r\n            }\r\n        } else if (this.status == 2) {\r\n            this.selectIcon!.node.active = false;\r\n            if (selectIndex == this.index) {\r\n                this.delIcon!.node.active = true;\r\n            } else {\r\n                this.delIcon!.node.active = false;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected onDestroy(): void { }\r\n\r\n    async onShow(...args: any[]): Promise<void> { }\r\n\r\n    async onHide(...args: any[]): Promise<void> { }\r\n\r\n    async onClose(...args: any[]): Promise<void> { }\r\n\r\n}\r\n"]}