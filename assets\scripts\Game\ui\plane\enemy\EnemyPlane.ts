import { _decorator, Node, Sprite, Vec2, tween, UIOpacity } from 'cc';
import { Tools } from '../../../utils/Tools';
import { GameIns } from '../../../GameIns';
import { GameEnum } from '../../../const/GameEnum';
import EnemyShootComponent from './EnemyShootComponent';
import { EnemyPlaneData, EnemyShootData } from '../../../data/EnemyData';
import TrackComponent from '../../base/TrackComponent';
import EnemyPlaneRole from './EnemyPlaneRole';
import { TrackGroup } from '../../../data/EnemyWave';
import { TrackData } from '../../../data/TrackData';
import PlaneBase from '../PlaneBase';
import FBoxCollider from '../../../collider-system/FBoxCollider';
import FCollider, { ColliderGroupType } from '../../../collider-system/FCollider';
import Bullet from '../../bullet/Bullet';
import { Movable } from 'db://assets/scripts/Game/move/Movable';

const { ccclass, property } = _decorator;

@ccclass('EnemyPlane')
export default class EnemyPlane extends PlaneBase {
    @property(EnemyPlaneRole)
    role: EnemyPlaneRole | null = null;

    @property(Sprite)
    hpBg: Sprite | null = null;
    @property(Sprite)
    hpSpr: Sprite | null = null;
    @property(Sprite)
    hpWhite: Sprite | null = null;

    _data: EnemyPlaneData | null = null;
    // 这个将要废弃
    _trackCom: TrackComponent | null = null;
    _shootCom: EnemyShootComponent | null = null;
    _moveCom: Movable | null = null;
    public get moveCom() { return this._moveCom; }

    removeAble = false;
    _curAction: number = 0;
    _removeCount: number = 0;
    _removeTime: number = 0;

    _rotateSpeed: number = 0;
    _leaveAct: number = -1;
    _roleIndex: number = 1;
    _curFormIndex: number = 0;
    _curTrackType: number = -1;
    _dieAnimEnd: boolean = false;
    _hpWhiteTween: any = null;
    bullets: Bullet[] = [];

    _countTime = 0;
    _bStandBy = false;
    _standByTime = 0;
    _standByEnd = false;

    protected onLoad(): void {
        this._trackCom = Tools.addScript(this.node, TrackComponent);
        this._shootCom = Tools.addScript(this.node, EnemyShootComponent);
        // 添加碰撞组件并初始化
        this.collideComp = this.addComponent(FBoxCollider)|| this.addComponent(FBoxCollider);
        this.collideComp!.init(this);
        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;
        this.colliderEnabled = false;
        // 添加移动组件
        this._moveCom = Tools.addScript(this.node, Movable);
    }

    setCollideAble(isEnabled: boolean) {
        this.collideComp!.isEnable = isEnabled;
    }

    get collideAble(): boolean {
        return this.collideComp!.isEnable;
    }

    async initPlane(data: EnemyPlaneData) {
        super.init();
        this._reset();
        this._data = data;
        this._refreshProperty();
    }
    _reset() {
        this._curAction = GameEnum.EnemyAction.Track;
    }

    _refreshProperty() {
        this.curHp = this._data!.hp;
        this.maxHp = this._data!.hp;
        this.attack = 10;
    }

    initComps(): void {
        // 调用父类的组件初始化方法
        super.initComps();

        // 初始化射击组件
        this._shootCom!.init(this, this.node, null, false);

        // 设置攻击开始的回调
        this._shootCom!.setAtkStartCall(() => {
            this.setAction(GameEnum.EnemyAction.AttackPrepare);
        });

        // 设置攻击结束的回调
        this._shootCom!.setAtkOverCall(() => {
            this.setAction(GameEnum.EnemyAction.AttackOver);
        });
    }

    setAction(action: number) {
        if (this._curAction !== action) {
            this._curAction = action;

            // 停止射击并启用轨迹
            this._shootCom!.setIsShooting(false);
            this._trackCom!.setTrackAble(true);

            switch (this._curAction) {
                case GameEnum.EnemyAction.Sneak:
                    // 潜行行为
                    this.hpBg!.node!.getComponent(UIOpacity)!.opacity = 0;
                    // this.role.playSneakAnim();
                    break;

                case GameEnum.EnemyAction.Track:
                    // 跟踪行为
                    break;

                case GameEnum.EnemyAction.Transform:
                    // 变形行为
                    this._trackCom!.setTrackAble(false);
                    this._shootCom!.stopShoot();
                    this._roleIndex++;
                    // this.role!.playAnim("transform", () => {
                    //     this.role!.playAnim("idle" + this._roleIndex);
                    //     this.setAction(GameEnum.EnemyAction.Track);
                    //     this._shootCom!.setNextShootAtOnce();
                    // }) || (
                    this.setAction(GameEnum.EnemyAction.Track)
                    this._shootCom!.setNextShootAtOnce();
                    break;

                case GameEnum.EnemyAction.AttackPrepare:
                    // 准备攻击行为
                    this.playAtkAnim();
                    this.setAction(GameEnum.EnemyAction.AttackIng);
                    break;

                case GameEnum.EnemyAction.AttackIng:
                    // 攻击中行为
                    this._shootCom!.startShoot();
                    break;

                case GameEnum.EnemyAction.AttackOver:
                    this.setAction(GameEnum.EnemyAction.Track);
                    break;

                default:
                    break;
            }
        }
    }

    /**
     * 播放攻击动画
     */
    playAtkAnim() {
        this.role!.playAnim(`atk${this._roleIndex}`, false, () => {
            this.role!.playAnim(`idle${this._roleIndex}`);
        });
    }

    /**
     * 设置形态索引
     * @param {number} index 形态索引
     */
    setFormIndex(index: number) {
        this._curFormIndex = index;
        if (this._curFormIndex >= 0 && this._data!.bAttackAbles[this._curFormIndex]) {
            const shootData = new EnemyShootData();
            shootData.attackInterval = this._data!.attackInterval;
            shootData.attackNum = this._data!.attackNum;
            shootData.attackPointArr = this._data!.attackPointArr[this._curFormIndex];
            shootData.attackArrNum = shootData.attackPointArr.length;
            this._shootCom!.setShootData(shootData);
        } else {
            this._shootCom!.setShootData(null);
        }
    }


    _updateAction(deltaTime: number) {
        this._shootCom!.setNextAble(false);

        switch (this._curAction) {
            case GameEnum.EnemyAction.Sneak:
                // this._updateCurDir(deltaTime);
                this.colliderEnabled = false;
                break;

            case GameEnum.EnemyAction.Track:
                // this._updateCurDir(deltaTime);
                this._shootCom!.setNextAble(
                    (this._trackCom!.isMoving && this._data!.bMoveAttack) ||
                    (!this._trackCom!.isMoving && this._data!.bStayAttack)
                );
                break;

            case GameEnum.EnemyAction.Transform:
                break;

            case GameEnum.EnemyAction.AttackPrepare:
            case GameEnum.EnemyAction.AttackIng:
                // this._updateCurDir(deltaTime);
                break;

            case GameEnum.EnemyAction.Leave:
                if (this._leaveAct === 0) {
                    this.die(GameEnum.EnemyDestroyType.TimeOver);
                } else if (this._leaveAct > 0) {
                    // this._updateCurDir(deltaTime);
                }
                break;
        }
    }


    /**
     * 初始化轨迹
     * @param {TrackData} trackData 轨迹数据
     * @param {number} offsetX X 轴偏移
     * @param {number} offsetY Y 轴偏移
     * @param {boolean} isLoop 是否循环
     * @param {number} rotateSpeed 旋转速度
     */
    initTrack(trackData: TrackGroup[], trackParams: number[], offsetX: number, offsetY: number, rotateSpeed = 0) {
        this._rotateSpeed = rotateSpeed;
        this._trackCom!.init(this, trackData, trackParams, offsetX, offsetY);

        this._trackCom!.setTrackGroupOverCall((groupType: number) => {

        });

        this._trackCom!.setTrackOverCall(() => {
            this.die(GameEnum.EnemyDestroyType.TrackOver);
        });

        this._trackCom!.setTrackLeaveCall(() => {
            this.die(GameEnum.EnemyDestroyType.Leave);
        });

        this._trackCom!.setTrackStartCall((trackData: TrackData) => {

        });
    }

    /**
 * 设置首次射击的延迟时间
 * @param {number} delay 延迟时间
 */
    setFirstShootDelay(delay: number) {
        this._shootCom!.setFirstShootDelay(delay);
    }

    /**
     * 开始战斗
     */
    startBattle() {
        this.colliderEnabled = true;
        this._refreshHpBar();
        this._trackCom!.setTrackAble(true);
        this._trackCom!.startTrack();
    }

    /**
     * 更新游戏逻辑
     * @param {number} deltaTime 帧间隔时间
     */
    updateGameLogic(deltaTime: number) {
        if (!this.isDead && this.checkStandby(deltaTime)) {
            // 更新所有组件
            this.m_comps.forEach((comp) => {
                comp.update(deltaTime);
            });
        }
        if (this.isDead) {
            this._checkRemoveAble(deltaTime);
        } else if (!this._bStandBy) {
            this._trackCom!.updateGameLogic(deltaTime);
            this._shootCom!.updateGameLogic(deltaTime);
            this._moveCom!.tick(deltaTime);
            this._updateAction(deltaTime);
        }
    }


    onCollide(collider: FCollider) {
        if (!this.isDead) {
            if (collider.entity instanceof Bullet) {
                const attack = collider.entity.getAttack();
                GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), attack);
                this.hurt(-attack)
            }
        }
    }

    hurt(damage: number) {
        if (this.isDead) {
            return false;
        }
        this.curHp += damage;
        if (this.curHp < 0) {
            this.curHp = 0;
        } else if (this.curHp > this.maxHp) {
            this.curHp = this.maxHp;
        }
        this._refreshHpBar();
        this.checkHp();
        if (!this.isDead) {
            // this.role.winkWhite();
        }
    }

    checkHp() {
        if (this.curHp <= 0) {
            this.die(GameEnum.EnemyDestroyType.Die);
            return true;
        }
        return false;
    }

    _refreshHpBar() {
        const hpRatio = this.curHp / this.maxHp;
        const isDecreasing = hpRatio < this.hpSpr!.fillRange;

        // 更新血条显示
        this.hpSpr!.fillRange = hpRatio;

        // 停止之前的血条动画
        if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();
            this._hpWhiteTween = null;
        }

        // 如果血量减少，播放白色血条的动画
        if (isDecreasing) {
            const duration = Math.abs(this.hpWhite!.fillRange - this.hpSpr!.fillRange);
            this._hpWhiteTween = tween(this.hpWhite!)
                .to(duration, { fillRange: this.hpSpr!.fillRange })
                .call(() => {
                    this._hpWhiteTween = null;
                })
                .start();
        } else {
            this.hpWhite!.fillRange = hpRatio;
        }
    }



    /**
     * 处理敌人死亡逻辑
     * @param {GameEnum.EnemyDestroyType} destroyType 敌人销毁类型
     */
    die(destroyType: GameEnum.EnemyDestroyType) {
        if (!super.toDie()) {
            return false;
        }
        this.colliderEnabled = false;

        this.onDie(destroyType);
    }

    /**
     * 敌机死亡时的处理
     * @param {number} destroyType 销毁类型
     */
    onDie(destroyType: number) {
        this.hpBg!.node!.getComponent(UIOpacity)!.opacity = 0;
        this.willRemove();

        switch (destroyType) {
            case GameEnum.EnemyDestroyType.Die:
                // this.playDieAnim();
                break;

            case GameEnum.EnemyDestroyType.Leave:
            case GameEnum.EnemyDestroyType.TrackOver:
            case GameEnum.EnemyDestroyType.TimeOver:
                this._dieAnimEnd = true;
                break;
        }
    }


    /**
     * 准备移除敌机
     */
    willRemove() {
        if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();
            this._hpWhiteTween = null;
        }

        this.hpWhite!.fillRange = 0;
    }

    /**
     * 检查是否可以存活
     * @returns {boolean} 是否可以存活
     */
    checkLiveAble() {
        if (this._curAction === GameEnum.EnemyAction.Track) {
            this.setAction(GameEnum.EnemyAction.Leave);
            return true;
        }
        return false;
    }
    /**
     * 检查是否可以移除
     * @param {number} deltaTime 帧间隔时间
     */
    _checkRemoveAble(deltaTime: number) {
        this.removeAble = true;
    }

    addBullet(bullet: Bullet) {
        if (this.bullets) {
            this.bullets.push(bullet);
        }
    }

    /**
     * 从敌人移除子弹
     * @param {Bullet} bullet 子弹对象
     */
    removeBullet(bullet: Bullet) {
        if (this.bullets) {
            const index = this.bullets.indexOf(bullet);
            if (index >= 0) {
                this.bullets.splice(index, 1);
            }
        }
    }

    /**
     * 检查待机状态
     * @param {number} deltaTime 帧间隔时间
     * @returns {boolean} 是否处于待机状态
     */
    checkStandby(deltaTime: number) {
        this._countTime += deltaTime;
        if (this._bStandBy) {
            if (this._countTime > this._standByTime) {
                this._bStandBy = false;
                this._countTime = 0;
                this._standByEnd = true;
                this.node!.getComponent(UIOpacity)!.opacity = 255;
                this.startBattle();
            }
            return true;
        }
        return false;
    }

    setStandByTime(time: number) {
        this._bStandBy = true;
        this._standByTime = time;
        this.node!.getComponent(UIOpacity)!.opacity = 0;
    }

    setPos(x: number, y: number) {
        this.node.setPosition(x, y);
    }
}