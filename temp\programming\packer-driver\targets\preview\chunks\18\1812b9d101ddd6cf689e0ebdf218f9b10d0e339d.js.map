{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts"], "names": ["SceneManager", "SingletonBase", "GameEnum", "GameMapRun", "EnemyPlane", "getScenePos", "entity", "x", "node", "position", "y", "parentName", "type", "EnemyType", "Missile", "parent", "scaleX", "scaleY", "lastParent", "name", "scale", "getLayerSpeed", "getMapSpeed", "instance", "MapSpeed", "mapToBattleScene", "layer", "ViewTop", "getSceneEntity", "result"], "mappings": ";;;+EAMaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;AANJC,MAAAA,a,iBAAAA,a;;AACDC,MAAAA,Q,iBAAAA,Q;;AACDC,MAAAA,U;;AACAC,MAAAA,U;;;;;;;8BAGMJ,Y,GAAN,MAAMA,YAAN;AAAA;AAAA,0CAAuD;AAE1D;AACJ;AACA;AACA;AACA;AACIK,QAAAA,WAAW,CAACC,MAAD,EAAa;AAEpB,cAAIA,MAAM;AAAA;AAAA,uCAAV,EAAkC;AAC9B,gBAAIC,CAAC,GAAGD,MAAM,CAACE,IAAP,CAAYC,QAAZ,CAAqBF,CAA7B;AACA,gBAAIG,CAAC,GAAGJ,MAAM,CAACE,IAAP,CAAYC,QAAZ,CAAqBC,CAA7B;AACA,gBAAIC,UAAU,GAAG,YAAjB;;AACA,gBAAIL,MAAM,CAACM,IAAP,KAAgB;AAAA;AAAA,sCAASC,SAAT,CAAmBC,OAAvC,EAAgD;AAC5CH,cAAAA,UAAU,GAAG,aAAb;AACH;;AAED,gBAAII,MAAM,GAAGT,MAAM,CAACE,IAAP,CAAYO,MAAzB;AACA,gBAAIC,MAAM,GAAG,CAAb;AACA,gBAAIC,MAAM,GAAG,CAAb;AACA,gBAAIC,UAAU,GAAG,IAAjB;;AAEA,mBAAOH,MAAM,IAAIA,MAAM,CAACI,IAAP,KAAgBR,UAAjC,EAA6C;AACzCK,cAAAA,MAAM,IAAID,MAAM,CAACK,KAAP,CAAab,CAAvB;AACAU,cAAAA,MAAM,IAAIF,MAAM,CAACK,KAAP,CAAaV,CAAvB;AACAH,cAAAA,CAAC,IAAIQ,MAAM,CAACR,CAAZ;AACAG,cAAAA,CAAC,IAAIK,MAAM,CAACL,CAAZ;AACAQ,cAAAA,UAAU,GAAGH,MAAb;AACAA,cAAAA,MAAM,GAAGA,MAAM,CAACA,MAAhB;AACH;;AAED,gBAAIG,UAAJ,EAAgB;AACZX,cAAAA,CAAC,IAAIW,UAAU,CAACX,CAAhB;AACAG,cAAAA,CAAC,IAAIQ,UAAU,CAACR,CAAhB;AACAH,cAAAA,CAAC,IAAIS,MAAL;AACAN,cAAAA,CAAC,IAAIO,MAAL;AACAV,cAAAA,CAAC,IAAIW,UAAU,CAACX,CAAhB;AACAG,cAAAA,CAAC,IAAIQ,UAAU,CAACR,CAAhB;AACH,aAPD,MAOO;AACHH,cAAAA,CAAC,IAAIS,MAAL;AACAN,cAAAA,CAAC,IAAIO,MAAL;AACH;;AAED,mBAAO;AAAEV,cAAAA,CAAF;AAAKG,cAAAA;AAAL,aAAP;AACH,WArCmB,CAuCpB;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAEA,iBAAO;AAAEH,YAAAA,CAAC,EAAED,MAAM,CAACE,IAAP,CAAYD,CAAjB;AAAoBG,YAAAA,CAAC,EAAEJ,MAAM,CAACE,IAAP,CAAYE;AAAnC,WAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIW,QAAAA,aAAa,CAACf,MAAD,EAAa;AACtB;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA,iBAAO,CAAP;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACIgB,QAAAA,WAAW,GAAG;AACV,iBAAO;AAAA;AAAA,wCAAWC,QAAX,CAAqBC,QAA5B;AACH;AAED;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACIC,QAAAA,gBAAgB,CAAClB,CAAD,EAAWG,CAAX,EAAqBgB,KAArB,EAAgC;AAC5C,iBAAO;AACHnB,YAAAA,CADG;AAEHG,YAAAA,CAAC,EAAEA,CAAC,GAAG;AAAA;AAAA,0CAAWa,QAAX,CAAqBI;AAFzB,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;;;AACIC,QAAAA,cAAc,CAACtB,MAAD,EAAa;AACvB,cAAIuB,MAAM,GAAGvB,MAAb;AAEA,iBAAOuB,MAAP;AACH;;AA5GyD,O", "sourcesContent": ["import { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport {GameEnum} from \"../const/GameEnum\";\r\nimport GameMapRun from \"../ui/map/GameMapRun\";\r\nimport EnemyPlane from \"../ui/plane/enemy/EnemyPlane\";\r\n\r\n\r\nexport class SceneManager extends SingletonBase<SceneManager> {\r\n\r\n    /**\r\n     * 获取场景中的位置\r\n     * @param {Entity} entity 场景中的实体\r\n     * @returns {Object} 包含 x 和 y 的位置对象\r\n     */\r\n    getScenePos(entity:any) {\r\n\r\n        if (entity instanceof EnemyPlane) {\r\n            let x = entity.node.position.x;\r\n            let y = entity.node.position.y;\r\n            let parentName = \"enemyPlane\";\r\n            if (entity.type === GameEnum.EnemyType.Missile) {\r\n                parentName = \"enemyBullet\";\r\n            }\r\n\r\n            let parent = entity.node.parent;\r\n            let scaleX = 1;\r\n            let scaleY = 1;\r\n            let lastParent = null;\r\n\r\n            while (parent && parent.name !== parentName) {\r\n                scaleX *= parent.scale.x;\r\n                scaleY *= parent.scale.y;\r\n                x += parent.x;\r\n                y += parent.y;\r\n                lastParent = parent;\r\n                parent = parent.parent;\r\n            }\r\n\r\n            if (lastParent) {\r\n                x -= lastParent.x;\r\n                y -= lastParent.y;\r\n                x *= scaleX;\r\n                y *= scaleY;\r\n                x += lastParent.x;\r\n                y += lastParent.y;\r\n            } else {\r\n                x *= scaleX;\r\n                y *= scaleY;\r\n            }\r\n\r\n            return { x, y };\r\n        }\r\n\r\n        // if (entity instanceof BossUnitBase) {\r\n        //     const scenePos = entity.getScenePos();\r\n        //     return { x: scenePos.x, y: scenePos.y };\r\n        // }\r\n\r\n        // if (entity instanceof WinePlane) {\r\n        //     return entity.scenePos;\r\n        // }\r\n\r\n        return { x: entity.node.x, y: entity.node.y };\r\n    }\r\n\r\n    /**\r\n     * 获取场景层的速度\r\n     * @param {Entity} entity 场景中的实体\r\n     * @returns {number} 场景层的速度\r\n     */\r\n    getLayerSpeed(entity:any) {\r\n        // if (entity instanceof EnemyBuild) {\r\n        //     const sceneLayer = entity.sceneLayer;\r\n        //     return this.getMapSpeed(sceneLayer);\r\n        // }\r\n\r\n        // if (entity instanceof EnemyTurret && entity.sceneLayer >= 0) {\r\n        //     return this.getMapSpeed(entity.sceneLayer);\r\n        // }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * 获取地图层的速度\r\n     * @param {number} layer 地图层索引\r\n     * @returns {number} 地图层的速度\r\n     */\r\n    getMapSpeed() {\r\n        return GameMapRun.instance!.MapSpeed;\r\n    }\r\n\r\n    /**\r\n     * 将地图坐标转换为战斗场景坐标\r\n     * @param {number} x 地图坐标 x\r\n     * @param {number} y 地图坐标 y\r\n     * @param {number} layer 地图层索引\r\n     * @returns {Object} 包含 x 和 y 的战斗场景坐标\r\n     */\r\n    mapToBattleScene(x:number, y:number, layer:any) {\r\n        return {\r\n            x,\r\n            y: y - GameMapRun.instance!.ViewTop,\r\n        };\r\n    }\r\n\r\n    /**\r\n     * 获取场景中的实体\r\n     * @param {Entity} entity 场景中的实体\r\n     * @returns {Entity} 实体对象\r\n     */\r\n    getSceneEntity(entity:any) {\r\n        let result = entity;\r\n\r\n        return result;\r\n    }\r\n}"]}