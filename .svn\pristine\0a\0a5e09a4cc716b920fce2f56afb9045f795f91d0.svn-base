import { _decorator, Label } from 'cc';
import { BundleName } from 'db://assets/bundles/Bundle';

import { BaseUI, UILayer } from 'db://assets/scripts/ui/UIMgr';
import List from '../common/components/list/List';
const { ccclass, property } = _decorator;

@ccclass('TaskUI')
export class TaskUI extends BaseUI {
    public static getUrl(): string { return "prefab/ui/TaskUI"; }
    public static getLayer(): UILayer { return UILayer.Background }
    public static getBundleName(): string { return BundleName.HomeTask }
    @property(Label)
    title: Label | null = null;
    @property(List)
    TaskList: List | null = null;
    @property(List)
    achievementList: List | null = null;

    protected onLoad(): void {

    }

    async onShow(...args: any[]): Promise<void> {
    }
    async onHide(...args: any[]): Promise<void> {
    }
    async onClose(...args: any[]): Promise<void> {
    }
    protected update(dt: number): void {
    }

}

