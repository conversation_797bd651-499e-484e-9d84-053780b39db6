{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts"], "names": ["_decorator", "BundleName", "BottomTab", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BagGrid", "SortTypeDropdown", "Tabs", "CombineDisplay", "EquipDisplay", "ccclass", "property", "PlaneUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getBundleName", "HomePlane", "onLoad", "onShow", "tabs", "init", "onHide", "onClose", "onTabOpen", "tab", "Plane", "openUI", "onTabHide", "hideUI", "onTabClose", "closeUI", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,c,iBAAAA,c;;AACAC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBAGjBa,O,WADZF,OAAO,CAAC,SAAD,C,UAMHC,QAAQ;AAAA;AAAA,6B,UAERA,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ;AAAA;AAAA,+C,UAIRA,QAAQ;AAAA;AAAA,uC,UAERA,QAAQ;AAAA;AAAA,2C,2BAhBb,MACaC,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAIhC;AAJgC;;AAMA;AANA;;AAQN;AARM;;AAUiB;;AAEjD;AAZgC;;AAcU;AAdV;AAAA;;AACZ,eAANC,MAAM,GAAW;AAAE,iBAAO,mBAAP;AAA6B;;AACxC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AACpC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA6B;;AAavB;AAEpCC,QAAAA,MAAM,GAAS,CAAG;;AAEtBC,QAAAA,MAAM,GAAgC;AAAA;;AAAA;AACxC,YAAA,KAAI,CAACC,IAAL,CAAWC,IAAX;AADwC;AAE3C;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AAEKC,QAAAA,OAAO,GAAgC;AAAA;AAE5C;;AAEKC,QAAAA,SAAS,CAACC,GAAD,EAAiB;AAAA;AAC5B,gBAAIA,GAAG,IAAI;AAAA;AAAA,wCAAUC,KAArB,EAA4B;AACxB;AAAA;AAAA,kCAAMC,MAAN,CAAaf,OAAb;AACH;AAH2B;AAI/B;;AACKgB,QAAAA,SAAS,CAACH,GAAD,EAAiB;AAAA;AAC5B,gBAAIA,GAAG,IAAI;AAAA;AAAA,wCAAUC,KAArB,EAA4B;AACxB;AAAA;AAAA,kCAAMG,MAAN,CAAajB,OAAb;AACH;AAH2B;AAI/B;;AACKkB,QAAAA,UAAU,CAACL,GAAD,EAAiB;AAAA;AAC7B,gBAAIA,GAAG,IAAI;AAAA;AAAA,wCAAUC,KAArB,EAA4B;AACxB;AAAA;AAAA,kCAAMK,OAAN,CAAcnB,OAAd;AACH;AAH4B;AAIhC;;AACSoB,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AA/C+B,O;;;;;iBAMN,I;;;;;;;iBAEN,I;;;;;;;iBAEwB,I;;;;;;;iBAIR,I;;;;;;;iBAEI,I", "sourcesContent": ["import { _decorator } from 'cc';\n\nimport { BundleName } from 'db://assets/bundles/Bundle';\nimport { BottomTab } from 'db://assets/bundles/common/script/ui/home/<USER>';\nimport { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/ui/UIMgr';\nimport { BagGrid } from './components/back_pack/BagGrid';\nimport { SortTypeDropdown } from './components/back_pack/SortTypeDropdown';\nimport { Tabs } from './components/back_pack/Tabs';\nimport { CombineDisplay } from './components/display/CombineDisplay';\nimport { EquipDisplay } from './components/display/EquipDisplay';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('PlaneUI')\nexport class PlaneUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/PlaneUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    public static getBundleName(): string { return BundleName.HomePlane }\n    /**背包区域组件 */\n    @property(BagGrid)\n    bagGrid: BagGrid | null = null; //背包格子\n    @property(Tabs)\n    tabs: Tabs | null = null; //标签页\n    @property(SortTypeDropdown)\n    sortTypeDropDown: SortTypeDropdown | null = null;//排序下拉列表\n\n    /**展示区域组件 */\n    @property(EquipDisplay)\n    equipDisplay: EquipDisplay | null = null; //装备展示区域\n    @property(CombineDisplay)\n    combineDisplay: CombineDisplay | null = null; //合成展示区域\n\n    protected onLoad(): void { }\n\n    async onShow(...args: any[]): Promise<void> {\n        this.tabs!.init();\n    }\n\n    async onHide(...args: any[]): Promise<void> {\n    }\n\n    async onClose(...args: any[]): Promise<void> {\n\n    }\n\n    async onTabOpen(tab: BottomTab) {\n        if (tab == BottomTab.Plane) {\n            UIMgr.openUI(PlaneUI)\n        }\n    }\n    async onTabHide(tab: BottomTab) {\n        if (tab == BottomTab.Plane) {\n            UIMgr.hideUI(PlaneUI)\n        }\n    }\n    async onTabClose(tab: BottomTab) {\n        if (tab == BottomTab.Plane) {\n            UIMgr.closeUI(PlaneUI)\n        }\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}