{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/FriendUI.ts"], "names": ["_decorator", "instantiate", "Label", "Node", "Prefab", "BundleName", "MyApp", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "EventMgr", "PlaneUIEvent", "ButtonPlus", "Tabs", "TabStatus", "HomeUI", "PopupUI", "ccclass", "property", "FriendUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "Home", "onLoad", "tabs", "init", "updateLabelAfterColon", "LabelTimes", "tabBagBtn", "getComponentInChildren", "LabelUpdate", "timestamp", "date", "Date", "hours", "getHours", "toString", "padStart", "minutes", "getMinutes", "now", "btnClose", "addClick", "closeUI", "btnGet", "onPower", "btnIgnoreAll", "onIgnore", "btnAgreeAll", "onAgree", "btnRefreshAll", "onRefresh", "on", "TabChange", "onTabChange", "prefab", "resMgr", "loadAsync", "panel1", "<PERSON><PERSON><PERSON><PERSON>", "panel2", "panel3", "active", "node2", "tabStatus", "Bag", "node1", "label", "originalText", "string", "colonIndex", "indexOf", "formattedValue", "args", "length", "join", "prefix", "substring", "openUI", "onShow", "onHide", "onClose", "onDestroy", "targetOff"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACtCC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,I,iBAAAA,I;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,O,kBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBlB,U;;0BAGjBmB,Q,WADZF,OAAO,CAAC,UAAD,C,UAGHC,QAAQ;AAAA;AAAA,mC,UAGRA,QAAQ;AAAA;AAAA,uB,UAGRA,QAAQ,CAACf,IAAD,C,UAERe,QAAQ,CAACf,IAAD,C,UAERe,QAAQ,CAACf,IAAD,C,UAGRe,QAAQ,CAACf,IAAD,C,UAERe,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAAChB,KAAD,C,WAERgB,QAAQ,CAAChB,KAAD,C,WAGRgB,QAAQ,CAACf,IAAD,C,WAERe,QAAQ;AAAA;AAAA,mC,WAERA,QAAQ;AAAA;AAAA,mC,WAERA,QAAQ;AAAA;AAAA,mC,2BA/Bb,MACaC,QADb;AAAA;AAAA,4BACqC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAiCb,eAANC,MAAM,GAAW;AAAE,iBAAO,2BAAP;AAAqC;;AAChD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAyB;;AAClC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAyB;;AACjDC,QAAAA,MAAM,GAAkB;AAAA;;AAAA;AACpC,YAAA,KAAI,CAACC,IAAL,CAAWC,IAAX;;AACA,YAAA,KAAI,CAACC,qBAAL,CAA2B,KAAI,CAACC,UAAhC,EAA6C,IAA7C,EAAmD,KAAnD;;AACA,YAAA,KAAI,CAACD,qBAAL,CAA2B,KAAI,CAACF,IAAL,CAAWI,SAAX,CAAsBC,sBAAtB,CAA6C7B,KAA7C,CAA3B,EAAiF,IAAjF,EAAuF,KAAvF;;AACA,YAAA,KAAI,CAAC0B,qBAAL,CAA2B,KAAI,CAACI,WAAhC,EACI,CAAEC,SAAD,IAAuB;AACpB,kBAAMC,IAAI,GAAG,IAAIC,IAAJ,CAASF,SAAT,CAAb;AACA,kBAAMG,KAAK,GAAGF,IAAI,CAACG,QAAL,GAAgBC,QAAhB,GAA2BC,QAA3B,CAAoC,CAApC,EAAuC,GAAvC,CAAd;AACA,kBAAMC,OAAO,GAAGN,IAAI,CAACO,UAAL,GAAkBH,QAAlB,GAA6BC,QAA7B,CAAsC,CAAtC,EAAyC,GAAzC,CAAhB;AACA,qBAAUH,KAAV,cAAmBI,OAAnB;AACH,aALD,EAKGL,IAAI,CAACO,GAAL,KAAa,IAAI,EAAJ,GAAS,EAAT,GAAc,IAL9B,CADJ;;AAQA,YAAA,KAAI,CAACC,QAAL,CAAeC,QAAf,CAAwB,KAAI,CAACC,OAA7B,EAAsC,KAAtC;;AACA,YAAA,KAAI,CAACC,MAAL,CAAaF,QAAb,CAAsB,KAAI,CAACG,OAA3B,EAAoC,KAApC;;AACA,YAAA,KAAI,CAACC,YAAL,CAAmBJ,QAAnB,CAA4B,KAAI,CAACK,QAAjC,EAA2C,KAA3C;;AACA,YAAA,KAAI,CAACC,WAAL,CAAkBN,QAAlB,CAA2B,KAAI,CAACO,OAAhC,EAAyC,KAAzC;;AACA,YAAA,KAAI,CAACC,aAAL,CAAoBR,QAApB,CAA6B,KAAI,CAACS,SAAlC,EAA6C,KAA7C;;AACA;AAAA;AAAA,sCAASC,EAAT,CAAY;AAAA;AAAA,8CAAaC,SAAzB,EAAoC,KAAI,CAACC,WAAzC,EAAsD,KAAtD;AAEA,gBAAIC,MAAM,SAAS;AAAA;AAAA,gCAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,0CAAWnC,IAAlC,EAAwC,+BAAxC,EAAyEpB,MAAzE,CAAnB;;AACA,YAAA,KAAI,CAACwD,MAAL,CAAaC,QAAb,CAAsB5D,WAAW,CAACwD,MAAD,CAAjC;;AACAA,YAAAA,MAAM,SAAS;AAAA;AAAA,gCAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,0CAAWnC,IAAlC,EAAwC,8BAAxC,EAAwEpB,MAAxE,CAAf;;AACA,YAAA,KAAI,CAAC0D,MAAL,CAAaD,QAAb,CAAsB5D,WAAW,CAACwD,MAAD,CAAjC;;AACAA,YAAAA,MAAM,SAAS;AAAA;AAAA,gCAAMC,MAAN,CAAaC,SAAb,CAAuB;AAAA;AAAA,0CAAWnC,IAAlC,EAAwC,mCAAxC,EAA6EpB,MAA7E,CAAf;;AACA,YAAA,KAAI,CAAC2D,MAAL,CAAaF,QAAb,CAAsB5D,WAAW,CAACwD,MAAD,CAAjC;;AAEA,YAAA,KAAI,CAACK,MAAL,CAAaE,MAAb,GAAsB,KAAtB;AACA,YAAA,KAAI,CAACD,MAAL,CAAaC,MAAb,GAAsB,KAAtB;AACA,YAAA,KAAI,CAACC,KAAL,CAAYD,MAAZ,GAAqB,KAArB;AA5BoC;AA6BvC;;AACOR,QAAAA,WAAW,CAACU,SAAD,EAAuB;AACtC,cAAIA,SAAS,IAAI;AAAA;AAAA,sCAAUC,GAA3B,EAAgC;AAC5B,iBAAKC,KAAL,CAAYJ,MAAZ,GAAqB,IAArB;AACA,iBAAKC,KAAL,CAAYD,MAAZ,GAAqB,KAArB;AACA,iBAAKJ,MAAL,CAAaI,MAAb,GAAsB,IAAtB;AACA,iBAAKF,MAAL,CAAaE,MAAb,GAAsB,KAAtB;AACA,iBAAKD,MAAL,CAAaC,MAAb,GAAsB,KAAtB;AACH,WAND,MAMO;AACH,iBAAKI,KAAL,CAAYJ,MAAZ,GAAqB,KAArB;AACA,iBAAKC,KAAL,CAAYD,MAAZ,GAAqB,IAArB;AACA,iBAAKJ,MAAL,CAAaI,MAAb,GAAsB,KAAtB;AACA,iBAAKF,MAAL,CAAaE,MAAb,GAAsB,IAAtB;AACA,iBAAKD,MAAL,CAAaC,MAAb,GAAsB,IAAtB;AACH;AACJ;;AACMpC,QAAAA,qBAAqB,CAACyC,KAAD,EAAwC;AAChE,cAAMC,YAAY,GAAGD,KAAK,CAACE,MAA3B;AACA,cAAIC,UAAU,GAAGF,YAAY,CAACG,OAAb,CAAqB,GAArB,CAAjB;;AACA,cAAID,UAAU,KAAK,CAAC,CAApB,EAAuB;AACnBA,YAAAA,UAAU,GAAGF,YAAY,CAACG,OAAb,CAAqB,GAArB,CAAb,CADmB,CACqB;AAC3C;;AACD,cAAIC,cAAJ;;AANgE,4CAAtBC,IAAsB;AAAtBA,YAAAA,IAAsB;AAAA;;AAOhE,cAAIA,IAAI,CAACC,MAAL,KAAgB,CAApB,EAAuB;AACnBF,YAAAA,cAAc,GAAGC,IAAI,CAAC,CAAD,CAArB;AACH,WAFD,MAEO,IAAIA,IAAI,CAACC,MAAL,KAAgB,CAApB,EAAuB;AAC1BF,YAAAA,cAAc,GAAMC,IAAI,CAAC,CAAD,CAAV,SAAiBA,IAAI,CAAC,CAAD,CAAnC;AACH,WAFM,MAEA,IAAIA,IAAI,CAACC,MAAL,GAAc,CAAlB,EAAqB;AACxBF,YAAAA,cAAc,GAAGC,IAAI,CAACE,IAAL,CAAU,GAAV,CAAjB;AACH,WAFM,MAEA;AACHH,YAAAA,cAAc,GAAG,EAAjB;AACH;;AACD,cAAIF,UAAU,KAAK,CAAC,CAApB,EAAuB;AACnBH,YAAAA,KAAK,CAACE,MAAN,GAAkBD,YAAlB,SAAkCI,cAAlC;AACA;AACH;;AACD,cAAMI,MAAM,GAAGR,YAAY,CAACS,SAAb,CAAuB,CAAvB,EAA0BP,UAAU,GAAG,CAAvC,CAAf,CApBgE,CAoBN;;AAC1DH,UAAAA,KAAK,CAACE,MAAN,QAAkBO,MAAlB,GAA2BJ,cAA3B;AACH;;AAEK7B,QAAAA,OAAO,GAAG;AAAA;AACZ;AAAA;AAAA,gCAAMA,OAAN,CAAc1B,QAAd;AACA,kBAAM;AAAA;AAAA,gCAAM6D,MAAN;AAAA;AAAA,iCAAN;AAFY;AAGf;;AACOjC,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,8BAAMiC,MAAN;AAAA;AAAA,kCAAsB,MAAtB;AACH;;AACO/B,QAAAA,QAAQ,GAAG;AACf;AAAA;AAAA,8BAAM+B,MAAN;AAAA;AAAA,kCAAsB,MAAtB;AACH;;AACO7B,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,8BAAM6B,MAAN;AAAA;AAAA,kCAAsB,MAAtB;AACH;;AACO3B,QAAAA,SAAS,GAAG;AAChB;AAAA;AAAA,8BAAM2B,MAAN;AAAA;AAAA,kCAAsB,OAAtB;AACH;;AAEKC,QAAAA,MAAM,GAAkB;AAAA;AAE7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AACSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAnIgC,O;;;;;iBAGH,I;;;;;;;iBAGV,I;;;;;;;iBAGE,I;;;;;;;iBAEA,I;;;;;;;iBAEA,I;;;;;;;iBAGD,I;;;;;;;iBAEO,I;;;;;;;iBAED,I;;;;;;;iBAEC,I;;;;;;;iBAGP,I;;;;;;;iBAEa,I;;;;;;;iBAED,I;;;;;;;iBAEE,I", "sourcesContent": ["import { _decorator, instantiate, Label, Node, Prefab } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/Bundle';\r\nimport { MyApp } from '../../../../../../scripts/MyApp';\r\nimport { <PERSON><PERSON>, UILayer, UIMgr } from '../../../../../../scripts/ui/UIMgr';\r\nimport { EventMgr } from '../../../event/EventManager';\r\nimport { PlaneUIEvent } from '../../../event/PlaneUIEvent';\r\nimport { ButtonPlus } from '../../common/components/button/ButtonPlus';\r\nimport { Tabs } from '../../plane/components/back_pack/Tabs';\r\nimport { TabStatus } from '../../plane/PlaneTypes';\r\nimport { HomeUI } from '../HomeUI';\r\nimport { PopupUI } from '../PopupUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('FriendUI')\r\nexport class FriendUI extends BaseUI {\r\n\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus | null = null;\r\n\r\n    @property(Tabs)\r\n    tabs: Tabs | null = null;\r\n\r\n    @property(Node)\r\n    panel1: Node | null = null;\r\n    @property(Node)\r\n    panel2: Node | null = null;\r\n    @property(Node)\r\n    panel3: Node | null = null;\r\n\r\n    @property(Node)\r\n    node1: Node | null = null;\r\n    @property(ButtonPlus)\r\n    btnGet: ButtonPlus | null = null;\r\n    @property(Label)\r\n    LabelTimes: Label | null = null;\r\n    @property(Label)\r\n    LabelUpdate: Label | null = null;\r\n\r\n    @property(Node)\r\n    node2: Node | null = null;\r\n    @property(ButtonPlus)\r\n    btnIgnoreAll: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnAgreeAll: ButtonPlus | null = null;\r\n    @property(ButtonPlus)\r\n    btnRefreshAll: ButtonPlus | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/friend/FriendUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default; }\r\n    public static getBundleName(): string { return BundleName.Home; }\r\n    protected async onLoad(): Promise<void> {\r\n        this.tabs!.init();\r\n        this.updateLabelAfterColon(this.LabelTimes!, \"50\", \"100\");\r\n        this.updateLabelAfterColon(this.tabs!.tabBagBtn!.getComponentInChildren(Label)!, \"30\", \"100\");\r\n        this.updateLabelAfterColon(this.LabelUpdate!,\r\n            ((timestamp: number) => {\r\n                const date = new Date(timestamp);\r\n                const hours = date.getHours().toString().padStart(2, '0');\r\n                const minutes = date.getMinutes().toString().padStart(2, '0');\r\n                return `${hours}时${minutes}分`;\r\n            })(Date.now() + 3 * 60 * 60 * 1000)\r\n        );\r\n        this.btnClose!.addClick(this.closeUI, this);\r\n        this.btnGet!.addClick(this.onPower, this);\r\n        this.btnIgnoreAll!.addClick(this.onIgnore, this);\r\n        this.btnAgreeAll!.addClick(this.onAgree, this);\r\n        this.btnRefreshAll!.addClick(this.onRefresh, this);\r\n        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this);\r\n\r\n        let prefab = await MyApp.resMgr.loadAsync(BundleName.Home, \"prefab/ui/friend/FriendListUI\", Prefab);\r\n        this.panel1!.addChild(instantiate(prefab));\r\n        prefab = await MyApp.resMgr.loadAsync(BundleName.Home, \"prefab/ui/friend/FriendAddUI\", Prefab);\r\n        this.panel2!.addChild(instantiate(prefab));\r\n        prefab = await MyApp.resMgr.loadAsync(BundleName.Home, \"prefab/ui/friend/FriendStrangerUI\", Prefab);\r\n        this.panel3!.addChild(instantiate(prefab));\r\n\r\n        this.panel2!.active = false;\r\n        this.panel3!.active = false;\r\n        this.node2!.active = false;\r\n    }\r\n    private onTabChange(tabStatus: TabStatus) {\r\n        if (tabStatus == TabStatus.Bag) {\r\n            this.node1!.active = true;\r\n            this.node2!.active = false;\r\n            this.panel1!.active = true;\r\n            this.panel2!.active = false;\r\n            this.panel3!.active = false;\r\n        } else {\r\n            this.node1!.active = false;\r\n            this.node2!.active = true;\r\n            this.panel1!.active = false;\r\n            this.panel2!.active = true;\r\n            this.panel3!.active = true;\r\n        }\r\n    }\r\n    public updateLabelAfterColon(label: Label, ...args: string[]): void {\r\n        const originalText = label.string;\r\n        let colonIndex = originalText.indexOf(\":\");\r\n        if (colonIndex === -1) {\r\n            colonIndex = originalText.indexOf(\"：\"); // 中文冒号\r\n        }\r\n        let formattedValue: string;\r\n        if (args.length === 1) {\r\n            formattedValue = args[0];\r\n        } else if (args.length === 2) {\r\n            formattedValue = `${args[0]}/${args[1]}`;\r\n        } else if (args.length > 2) {\r\n            formattedValue = args.join(\",\");\r\n        } else {\r\n            formattedValue = \"\";\r\n        }\r\n        if (colonIndex === -1) {\r\n            label.string = `${originalText}:${formattedValue}`;\r\n            return;\r\n        }\r\n        const prefix = originalText.substring(0, colonIndex + 1); // 包含冒号\r\n        label.string = `${prefix}${formattedValue}`;\r\n    }\r\n\r\n    async closeUI() {\r\n        UIMgr.closeUI(FriendUI);\r\n        await UIMgr.openUI(HomeUI)\r\n    }\r\n    private onPower() {\r\n        UIMgr.openUI(PopupUI, \"一键收赠\");\r\n    }\r\n    private onIgnore() {\r\n        UIMgr.openUI(PopupUI, \"全部忽略\");\r\n    }\r\n    private onAgree() {\r\n        UIMgr.openUI(PopupUI, \"全部同意\");\r\n    }\r\n    private onRefresh() {\r\n        UIMgr.openUI(PopupUI, \"刷新陌生人\");\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n        EventMgr.targetOff(this);\r\n    }\r\n}"]}