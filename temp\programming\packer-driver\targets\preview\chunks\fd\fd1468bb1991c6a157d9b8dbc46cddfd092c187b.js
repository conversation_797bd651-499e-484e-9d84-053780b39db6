System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Input, UITransform, v2, v3, Vec3, view, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, DragButton;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Input = _cc.Input;
      UITransform = _cc.UITransform;
      v2 = _cc.v2;
      v3 = _cc.v3;
      Vec3 = _cc.Vec3;
      view = _cc.view;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "24abezLM5JNPYccs3EJAaPn", "DragButton", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'Input', 'UITransform', 'v2', 'v3', 'Vec2', 'Vec3', 'view']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("DragButton", DragButton = (_dec = ccclass('DragButton'), _dec2 = property({
        tooltip: '长按触发时间（秒）'
      }), _dec(_class = (_class2 = class DragButton extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "longPressDuration", _descriptor, this);

          this.isTouching = false;
          this.startPos = v3();
          this.startTouchPos = v2();
          this.longPressTimer = 0;
        }

        onLoad() {
          this.node.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
          this.node.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
          this.node.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
          this.node.on(Input.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        }

        addClick(callback, target) {
          this.node.off('click');
          this.node.on('click', callback, target);
        }

        get isDragging() {
          return new Date().getTime() - this.longPressTimer >= this.longPressDuration * 1000;
        }

        onTouchStart(event) {
          var _event$touch;

          this.isTouching = true;
          this.longPressTimer = new Date().getTime();
          (_event$touch = event.touch) == null || _event$touch.getUILocation(this.startTouchPos);
          Vec3.copy(this.startPos, this.node.position);
        }

        onTouchMove(event) {
          var _event$touch2, _this$node$parent;

          if (!this.isTouching || !this.isDragging) return;
          var newPos = v2();
          (_event$touch2 = event.touch) == null || _event$touch2.getUILocation(newPos);
          var localPos = (_this$node$parent = this.node.parent) == null ? void 0 : _this$node$parent.inverseTransformPoint(newPos.toVec3(), newPos.toVec3());
          this.node.position = localPos; // 在updatePosition中添加限制

          var trans = this.node.getComponent(UITransform);
          var width = view.getVisibleSize().width / 2;
          var height = view.getVisibleSize().height / 2;
          var validX = Math.max(-width + trans.width / 2, Math.min(width - trans.width / 2, localPos.x));
          var validY = Math.max(-height + trans.height / 2, Math.min(height - trans.height / 2, localPos.y));
          this.node.position = v3(validX, validY, localPos.z);
        }

        onTouchEnd(event) {
          if (!this.isDragging) {
            this.onTouchMove(event);
            this.node.emit('click', event);
          }

          this.resetState();
        }

        onTouchCancel() {
          this.resetState();
        }

        resetState() {
          this.isTouching = false;
          this.longPressTimer = 0;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "longPressDuration", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 0.5;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fd1468bb1991c6a157d9b8dbc46cddfd092c187b.js.map