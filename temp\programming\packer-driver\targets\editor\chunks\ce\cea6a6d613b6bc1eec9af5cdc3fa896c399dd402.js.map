{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FBoxCollider.ts"], "names": ["_decorator", "v2", "Size", "BoxCollider2D", "size", "FCollider", "ColliderType", "GameConst", "ccclass", "property", "menu", "FBoxCollider", "worldPoints", "worldEdge", "isConvex", "type", "Box", "_size", "value", "width", "height", "onLoad", "collider", "node", "getComponent", "offset", "x", "y", "init", "entity", "initBaseData", "draw", "ColliderDraw", "addComponent"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAkBC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,a,OAAAA,a;AAAeC,MAAAA,I,OAAAA,I;;AAG7CC,MAAAA,S;AAAaC,MAAAA,Y,iBAAAA,Y;;AACXC,MAAAA,S,iBAAAA,S;;;;;;;;;OAHH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA8BV,U;;yBAQfW,Y,WAFpBH,OAAO,CAAC,cAAD,C,UACPE,IAAI,CAAC,qBAAD,C,UASAD,QAAQ,CAACP,IAAD,C,0CAVb,MAEqBS,YAFrB;AAAA;AAAA,kCAEoD;AAAA;AAAA;AAAA,eACzCC,WADyC,GACnB,CAACX,EAAE,EAAH,EAAOA,EAAE,EAAT,EAAaA,EAAE,EAAf,EAAmBA,EAAE,EAArB,CADmB;AAAA,eAEzCY,SAFyC,GAErB,EAFqB;AAAA,eAGzCC,QAHyC,GAGrB,IAHqB;;AAAA;AAAA;;AAIjC,YAAJC,IAAI,GAAG;AACd,iBAAO;AAAA;AAAA,4CAAaC,GAApB;AACH;;AAGoC;AAGtB,YAAJZ,IAAI,GAAS;AACpB,iBAAO,KAAKa,KAAZ;AACH;;AACc,YAAJb,IAAI,CAACc,KAAD,EAAc;AACzB,eAAKD,KAAL,GAAa,IAAIf,IAAJ,CACTgB,KAAK,CAACC,KAAN,GAAc,CAAd,GAAkB,CAAlB,GAAsBD,KAAK,CAACC,KADnB,EAETD,KAAK,CAACE,MAAN,GAAe,CAAf,GAAmB,CAAnB,GAAuBF,KAAK,CAACE,MAFpB,CAAb;AAIH;;AAEDC,QAAAA,MAAM,GAAS;AACX,cAAIC,QAAQ,GAAK,KAAKC,IAAL,CAAUC,YAAV,CAAuBrB,aAAvB,CAAjB;;AACA,cAAImB,QAAJ,EAAc;AACV,iBAAKlB,IAAL,GAAYkB,QAAQ,CAAClB,IAArB;AACA,iBAAKqB,MAAL,GAAcxB,EAAE,CAACqB,QAAQ,CAACG,MAAT,CAAgBC,CAAjB,EAAoBJ,QAAQ,CAACG,MAAT,CAAgBE,CAApC,CAAhB;AACH;AACJ;;AAEDC,QAAAA,IAAI,CAACC,MAAD,EAAgBzB,IAAe,GAAG,IAAlC,EAAwCqB,MAAY,GAAGxB,EAAE,CAAC,CAAD,EAAI,CAAJ,CAAzD,EAAiE;AACjE,eAAK6B,YAAL,CAAkBD,MAAlB,EAA0BJ,MAA1B;;AACA,cAAIrB,IAAJ,EAAU;AACN,iBAAKA,IAAL,GAAYA,IAAZ;AACH;AACJ;;AAED2B,QAAAA,IAAI,GAAG;AACH,cAAI,CAAC;AAAA;AAAA,sCAAUC,YAAf,EAA6B;AACzB;AACH;;AAED,cAAIV,QAAQ,GAAK,KAAKC,IAAL,CAAUC,YAAV,CAAuBrB,aAAvB,CAAjB;;AACA,cAAI,CAACmB,QAAL,EAAe;AACXA,YAAAA,QAAQ,GAAG,KAAKC,IAAL,CAAUU,YAAV,CAAuB9B,aAAvB,CAAX;AACAmB,YAAAA,QAAQ,CAAClB,IAAT,GAAgB,KAAKA,IAArB;AACAkB,YAAAA,QAAQ,CAACG,MAAT,CAAgBC,CAAhB,GAAoB,KAAKD,MAAL,CAAYC,CAAhC;AACAJ,YAAAA,QAAQ,CAACG,MAAT,CAAgBE,CAAhB,GAAoB,KAAKF,MAAL,CAAYE,CAAhC;AACH;AACJ;;AAjD+C,O;;;;;iBAS1BvB,IAAI,CAAC,GAAD,EAAM,GAAN,C;;gEAEzBK,Q", "sourcesContent": ["\nimport { _decorator, Vec2, v2, Size, BoxCollider2D, size } from 'cc';\nconst { ccclass, property, menu } = _decorator;\n\nimport FCollider, { ColliderType } from \"./FCollider\";\nimport { GameConst } from '../const/GameConst';\nimport Entity from '../ui/base/Entity';\n\n@ccclass('FBoxCollider')\n@menu(\"碰撞组件Ex/FBoxCollider\")\nexport default class FBoxCollider extends FCollider {\n    public worldPoints: Vec2[] = [v2(), v2(), v2(), v2()];\n    public worldEdge: Vec2[] = [];\n    public isConvex: boolean = true;\n    public get type() {\n        return ColliderType.Box;\n    }\n\n    @property(Size)\n    private _size: Size = size(100, 100);//默认100x100\n\n    @property\n    public get size(): Size {\n        return this._size;\n    }\n    public set size(value: Size) {\n        this._size = new Size(\n            value.width < 0 ? 0 : value.width,\n            value.height < 0 ? 0 : value.height\n        );\n    }\n\n    onLoad(): void {\n        let collider  =  this.node.getComponent(BoxCollider2D);\n        if (collider) {\n            this.size = collider.size;\n            this.offset = v2(collider.offset.x, collider.offset.y);\n        }\n    }\n\n    init(entity:Entity, size: Size|null = null, offset: Vec2 = v2(0, 0)) {\n        this.initBaseData(entity, offset);\n        if (size) {\n            this.size = size;\n        }\n    }\n\n    draw() {\n        if (!GameConst.ColliderDraw) {\n            return;\n        }\n\n        let collider  =  this.node.getComponent(BoxCollider2D);\n        if (!collider) {\n            collider = this.node.addComponent(BoxCollider2D);\n            collider.size = this.size;\n            collider.offset.x = this.offset.x;\n            collider.offset.y = this.offset.y;\n        }\n    }\n}\n"]}