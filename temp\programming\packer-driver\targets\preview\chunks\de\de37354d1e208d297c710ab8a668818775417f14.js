System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, DataMgr, DataEvent, EventMgr, MyApp, logDebug, PlaneUIEvent, ButtonPlus, DropDown, BagSortType, TabStatus, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, SortTypeDropdown;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "db://assets/bundles/common/script/data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "db://assets/bundles/common/script/event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "db://assets/bundles/common/script/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcs(extras) {
    _reporterNs.report("cs", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUIEvent(extras) {
    _reporterNs.report("PlaneUIEvent", "../../../../event/PlaneUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDropDown(extras) {
    _reporterNs.report("DropDown", "../../../common/components/dropdown/DropDown", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBagSortType(extras) {
    _reporterNs.report("BagSortType", "../../PlaneTypes", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabStatus(extras) {
    _reporterNs.report("TabStatus", "../../PlaneTypes", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
    }, function (_unresolved_2) {
      DataMgr = _unresolved_2.DataMgr;
    }, function (_unresolved_3) {
      DataEvent = _unresolved_3.DataEvent;
    }, function (_unresolved_4) {
      EventMgr = _unresolved_4.EventMgr;
    }, function (_unresolved_5) {
      MyApp = _unresolved_5.MyApp;
    }, function (_unresolved_6) {
      logDebug = _unresolved_6.logDebug;
    }, function (_unresolved_7) {
      PlaneUIEvent = _unresolved_7.PlaneUIEvent;
    }, function (_unresolved_8) {
      ButtonPlus = _unresolved_8.ButtonPlus;
    }, function (_unresolved_9) {
      DropDown = _unresolved_9.DropDown;
    }, function (_unresolved_10) {
      BagSortType = _unresolved_10.BagSortType;
      TabStatus = _unresolved_10.TabStatus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "6d394OVySFL67dLlH0tWDpM", "SortTypeDropdown", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("SortTypeDropdown", SortTypeDropdown = (_dec = ccclass('SortTypeDropdown'), _dec2 = property(_crd && DropDown === void 0 ? (_reportPossibleCrUseOfDropDown({
        error: Error()
      }), DropDown) : DropDown), _dec(_class = (_class2 = class SortTypeDropdown extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "dropDown", _descriptor, this);

          this._tabStatus = (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).None;
          this._sortType = (_crd && BagSortType === void 0 ? (_reportPossibleCrUseOfBagSortType({
            error: Error()
          }), BagSortType) : BagSortType).None;
          this._bagTabOptions = [{
            key: (_crd && BagSortType === void 0 ? (_reportPossibleCrUseOfBagSortType({
              error: Error()
            }), BagSortType) : BagSortType).Quality,
            label: '按品质排序'
          }, {
            key: (_crd && BagSortType === void 0 ? (_reportPossibleCrUseOfBagSortType({
              error: Error()
            }), BagSortType) : BagSortType).Part,
            label: '按部位排序'
          }];
          this._mergeTabOptions = [{
            key: (_crd && BagSortType === void 0 ? (_reportPossibleCrUseOfBagSortType({
              error: Error()
            }), BagSortType) : BagSortType).Merge,
            label: '按合成排序'
          }];
        }

        onLoad() {
          this.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onDropDownOptionClick, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).TabChange, this.onTabChangeEvent, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).UpdateBagGrids, this.sortBag, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).ItemsRefresh, this.sortBag, this);
        }

        onTabChangeEvent(tabStatus) {
          if (tabStatus === this._tabStatus) {
            return;
          }

          this._tabStatus = tabStatus;
          var optionKeyList;

          if (tabStatus === (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).Bag) {
            optionKeyList = this._bagTabOptions.map(v => v.key);
          } else {
            optionKeyList = this._mergeTabOptions.map(v => v.key);
          }

          this.dropDown.init(optionKeyList, this.onOptionRender.bind(this), this.onDropDownOptionClick.bind(this));
          this.sortBag();
        }

        onOptionRender(optNode, optKey) {
          var label = optNode.getComponentInChildren(Label);
          var opt;

          switch (this._tabStatus) {
            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Bag:
              opt = this._bagTabOptions.find(v => v.key == optKey);
              label.string = opt.label;
              break;

            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Merge:
              opt = this._mergeTabOptions.find(v => v.key == optKey);
              label.string = opt.label;
              break;

            default:
              //logError("PlaneUI", `onOptionRender error ${this._tabStatus}`)
              break;
          }
        }

        onDropDownOptionClick(optKey) {
          var opt;

          switch (this._tabStatus) {
            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Bag:
              opt = this._bagTabOptions.find(v => v.key == optKey);
              break;

            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Merge:
              opt = this._mergeTabOptions.find(v => v.key == optKey);
              break;

            default:
              //logError("PlaneUI", `Dropdown onClickDropDownOption error ${this._tabStatus}`)
              return;
          }

          this.sortBag();
        }

        sortBag() {
          var items = [];

          switch (this._tabStatus) {
            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Bag:
              items = [...this.sortEquipsInBagTabStatus(), ...this.sortItems()];
              break;

            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Merge:
              items = this.sortEquipsInCombineTabStatus();
              break;

            default:
              //logError("PlaneUI", `Dropdown onDisPlayRefresh error ${this._tabStatus}`)
              return;
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).SortTypeChange, this._tabStatus, items);
        }

        sortItems() {
          var sorted = [];
          (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).bag.items.forEach(v => {
            var cfg = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanMgr.table.TbItem.get(v.item_id);

            if (cfg) {
              sorted.push(v);
            }
          });
          sorted.sort((a, b) => {
            return b.add_time - a.add_time;
          });
          return sorted;
        }

        sortEquipsInBagTabStatus() {
          var tbEquip = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanMgr.table.TbEquip; // 1. 找出所有空部位

          var emptySlots = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqSlots.getEmptySlots();
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("PlaneUI", "sortEquipsInBagTabStatus item_total:" + (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).bag.items.length + ", empty_slots:" + emptySlots.join(',')); // 2. 将装备分为三部分：
          //    - emptySlotEquips: 对应空部位的装备（最高优先级）
          //    - unequippedEquips: 未装备的其他装备

          var emptySlotEquips = [];
          var unequippedEquips = [];

          var _loop = function _loop() {
            var cfg = tbEquip.get(item.item_id);
            if (!cfg) return 1; // continue
            // 3. 检查是否对应空部位

            var isEmptyEquipClass = emptySlots.some(e => e.equip_class === cfg.equipClass);

            if (isEmptyEquipClass) {
              emptySlotEquips.push(item);
            } else {
              unequippedEquips.push(item);
            }
          };

          for (var item of (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).bag.items) {
            if (_loop()) continue;
          } // 4. 排序函数


          var sortFn = (a, b) => {
            var aCfg = tbEquip.get(a.item_id);
            var bCfg = tbEquip.get(b.item_id);

            if (this._sortType === (_crd && BagSortType === void 0 ? (_reportPossibleCrUseOfBagSortType({
              error: Error()
            }), BagSortType) : BagSortType).Part) {
              // 按部位排序：先按部位类型，再按品质（从高到低）
              return aCfg.equipClass - bCfg.equipClass || bCfg.quality - aCfg.quality;
            } else {
              // 按品质排序：先按品质（从高到低），再按部位类型
              return bCfg.quality - aCfg.quality || aCfg.equipClass - bCfg.equipClass;
            }
          }; // 4. 分别排序三部分


          var sortedEmptySlotEquips = emptySlotEquips.sort(sortFn);
          var sortedUnequippedEquips = unequippedEquips.sort(sortFn); // 5. 合并结果：空部位装备 →  其他装备

          var sorted = [...sortedEmptySlotEquips, ...sortedUnequippedEquips]; // 6. 调试输出

          sorted.forEach((e, index) => {
            var cfg = tbEquip.get(e.item_id);
            var equipType = sortedEmptySlotEquips.includes(e) ? "[空部位]" : "[已装备部位]";
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("PlaneUI", index + 1 + ". " + equipType + " ID:" + e.item_id + " \u90E8\u4F4D:" + cfg.equipClass + " \u54C1\u8D28:" + cfg.quality + " \u6570\u91CF:" + e.count);
          });
          return sorted;
        }
        /**
        * 材料排序函数（支持主材料格子优先）
        * 功能：对背包中的材料进行智能排序
        * 排序规则：
        * 1. 如果主材料格子有放主材料，优先排该主材料相关的副材料
        * 2. 如果主材料格子没放主材料，按合成后品质排可合成主材料,然后也按品质排其他副材料
        * @returns 排序后的材料列表
        */


        sortEquipsInCombineTabStatus() {
          var _equip$eqCombine$getB;

          var items = [...(_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).bag.items, ...(_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqSlots.getEquippedSlots().map(v => ({
            item_id: v.equip_id,
            count: 1,
            guid: v.guid
          }))];
          var combineConfigs = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.calculateAllCombinePossible(items);
          var materialInfos = []; // 获取主材料格子上的材料

          var combineMainMaterial = (_equip$eqCombine$getB = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.getByPos(0)) == null ? void 0 : _equip$eqCombine$getB.item;
          items.forEach(v => {
            var _lubanTables$TbItem$g;

            var info = {
              item: v,
              isMainMaterial: false,
              isSubMaterial: false,
              canSynthesize: false,
              combineQuality: 0,
              isRelatedToSlotMain: false,
              materialQuality: 0
            };
            var itemQuality = (_lubanTables$TbItem$g = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbItem.get(v.item_id)) == null ? void 0 : _lubanTables$TbItem$g.quality;

            if (!itemQuality) {
              var _lubanTables$TbEquip$;

              var eqQuality = (_lubanTables$TbEquip$ = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).lubanTables.TbEquip.get(v.item_id)) == null ? void 0 : _lubanTables$TbEquip$.quality;

              if (!eqQuality) {
                return;
              }

              info.materialQuality = eqQuality;
            } else {
              info.materialQuality = itemQuality;
            } //info.isRelatedToSlotMain = combineMainMaterial?.item_id == v.item_id


            materialInfos.push(info);
          });
          combineConfigs.forEach(cfg => {
            var mainMaterial = cfg.consumeItems[0];
            var subMaterials = cfg.consumeItems.slice(1);
            materialInfos.forEach(v => {
              if (mainMaterial.id == v.item.item_id) {
                v.isMainMaterial = true;
                v.canSynthesize = true;

                if ((combineMainMaterial == null ? void 0 : combineMainMaterial.item_id) == v.item.item_id) {
                  v.isRelatedToSlotMain = true;
                }
              }
            });
            subMaterials.forEach(v => {
              materialInfos.forEach(m => {
                if ((_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
                  error: Error()
                }), DataMgr) : DataMgr).equip.eqCombine.isSameMatType(v.id, m.item.item_id)) {
                  m.isSubMaterial = true;
                  m.canSynthesize = true;
                  m.combineQuality = cfg.quality || 0;

                  if ((combineMainMaterial == null ? void 0 : combineMainMaterial.item_id) == mainMaterial.id) {
                    m.isRelatedToSlotMain = true;
                  }
                }
              });
            });
          });
          materialInfos.sort((a, b) => {
            // 场景1: 主材料格子有材料
            if (combineMainMaterial) {
              // 1. 优先排与主材料格子相关的材料（包括主材料和副材料）
              if (a.isRelatedToSlotMain && !b.isRelatedToSlotMain) return -1;
              if (!a.isRelatedToSlotMain && b.isRelatedToSlotMain) return 1; // 2. 都是相关材料时，主材料优先于副材料

              if (a.isRelatedToSlotMain && b.isRelatedToSlotMain) {
                if (a.isMainMaterial && !b.isMainMaterial) return -1;
                if (!a.isMainMaterial && b.isMainMaterial) return 1; // 都是副材料时，按合成后品质排序

                if (b.combineQuality !== a.combineQuality) {
                  return b.combineQuality - a.combineQuality;
                }
              } // 3. 非相关材料按自身品质排序


              if (b.materialQuality !== a.materialQuality) {
                return b.materialQuality - a.materialQuality;
              }
            } // 场景2: 主材料格子没有材料
            else {
              // 1. 可合成材料优先于不可合成材料
              if (a.canSynthesize && !b.canSynthesize) return -1;
              if (!a.canSynthesize && b.canSynthesize) return 1; // 2. 都可合成时，按合成后品质排序（品质高的优先）

              if (a.canSynthesize && b.canSynthesize) {
                if (b.combineQuality !== a.combineQuality) {
                  return b.combineQuality - a.combineQuality;
                } // 同一合成优先级时，主材料优先于副材料


                if (a.isMainMaterial && !b.isMainMaterial) return -1;
                if (!a.isMainMaterial && b.isMainMaterial) return 1;
              } // 3. 都不可合成时，按材料自身品质排序


              if (b.materialQuality !== a.materialQuality) {
                return b.materialQuality - a.materialQuality;
              }
            } // 最后按ID排序确保稳定性


            return a.item.item_id - b.item.item_id;
          }); // 调试输出

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("PlaneUI", "\u6750\u6599\u6392\u5E8F\u7ED3\u679C - \u4E3B\u6750\u6599\u683C\u5B50: " + (combineMainMaterial ? "ID " + combineMainMaterial.item_id : '空'));
          materialInfos.forEach((material, index) => {
            var _material$item;

            var type = material.isMainMaterial ? '主材料' : '副材料';
            var status = material.canSynthesize ? "\u53EF\u5408\u6210(\u54C1\u8D28" + material.combineQuality + ")" : '不可合成';
            var related = material.isRelatedToSlotMain ? '[格子相关]' : '';
            var quality = "\u54C1\u8D28" + material.materialQuality;
            (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
              error: Error()
            }), logDebug) : logDebug)("PlaneUI", index + 1 + ". ID:" + ((_material$item = material.item) == null ? void 0 : _material$item.item_id) + " \u6570\u91CF:" + material.item.count + " " + type + " " + status + " " + quality + " " + related);
          });
          return materialInfos.map(v => v.item);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "dropDown", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=de37354d1e208d297c710ab8a668818775417f14.js.map