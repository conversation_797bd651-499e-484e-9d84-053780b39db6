System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCFloat, Component, Node, CCString, assetManager, instantiate, UITransform, view, Graphics, Color, Rect, Vec2, LayerType, LevelDataBackgroundLayer, LevelDataLayer, LevelDataRandTerrains, LevelDataScroll, LevelEditorLayerUI, LevelBackgroundLayer, LevelEditorUtils, LevelLayer, LevelScrollLayerUI, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _dec7, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, executeInEditMode, BackgroundsNodeName, LevelEditorBaseUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLayerType(extras) {
    _reporterNs.report("LayerType", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelData(extras) {
    _reporterNs.report("LevelData", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataBackgroundLayer(extras) {
    _reporterNs.report("LevelDataBackgroundLayer", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataLayer(extras) {
    _reporterNs.report("LevelDataLayer", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataRandTerrain(extras) {
    _reporterNs.report("LevelDataRandTerrain", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataRandTerrains(extras) {
    _reporterNs.report("LevelDataRandTerrains", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelDataScroll(extras) {
    _reporterNs.report("LevelDataScroll", "../../scripts/leveldata/leveldata", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorLayerUI(extras) {
    _reporterNs.report("LevelEditorLayerUI", "./LevelEditorLayerUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelBackgroundLayer(extras) {
    _reporterNs.report("LevelBackgroundLayer", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelEditorUtils(extras) {
    _reporterNs.report("LevelEditorUtils", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelLayer(extras) {
    _reporterNs.report("LevelLayer", "./utils", _context.meta, extras);
  }

  function _reportPossibleCrUseOfLevelScrollLayerUI(extras) {
    _reporterNs.report("LevelScrollLayerUI", "./utils", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCFloat = _cc.CCFloat;
      Component = _cc.Component;
      Node = _cc.Node;
      CCString = _cc.CCString;
      assetManager = _cc.assetManager;
      instantiate = _cc.instantiate;
      UITransform = _cc.UITransform;
      view = _cc.view;
      Graphics = _cc.Graphics;
      Color = _cc.Color;
      Rect = _cc.Rect;
      Vec2 = _cc.Vec2;
    }, function (_unresolved_2) {
      LayerType = _unresolved_2.LayerType;
      LevelDataBackgroundLayer = _unresolved_2.LevelDataBackgroundLayer;
      LevelDataLayer = _unresolved_2.LevelDataLayer;
      LevelDataRandTerrains = _unresolved_2.LevelDataRandTerrains;
      LevelDataScroll = _unresolved_2.LevelDataScroll;
    }, function (_unresolved_3) {
      LevelEditorLayerUI = _unresolved_3.LevelEditorLayerUI;
    }, function (_unresolved_4) {
      LevelBackgroundLayer = _unresolved_4.LevelBackgroundLayer;
      LevelEditorUtils = _unresolved_4.LevelEditorUtils;
      LevelLayer = _unresolved_4.LevelLayer;
      LevelScrollLayerUI = _unresolved_4.LevelScrollLayerUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "a4bf2J2KGJJV7RbX1jwoQWJ", "LevelEditorBaseUI", undefined);

      __checkObsolete__(['_decorator', 'CCFloat', 'Component', 'Node', 'CCString', 'Prefab', 'assetManager', 'instantiate', 'UITransform', 'view', 'Graphics', 'Color', 'Rect', 'Vec2']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);
      BackgroundsNodeName = "backgrounds";

      _export("LevelEditorBaseUI", LevelEditorBaseUI = (_dec = ccclass('LevelEditorBaseUI'), _dec2 = executeInEditMode(), _dec3 = property(CCString), _dec4 = property({
        type: CCFloat,
        displayName: "关卡时长"
      }), _dec5 = property(_crd && LevelBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelBackgroundLayer({
        error: Error()
      }), LevelBackgroundLayer) : LevelBackgroundLayer), _dec6 = property({
        type: [_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
          error: Error()
        }), LevelLayer) : LevelLayer],
        displayName: "地面层"
      }), _dec7 = property({
        type: [_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
          error: Error()
        }), LevelLayer) : LevelLayer],
        displayName: "天空层"
      }), _dec(_class = _dec2(_class = (_class2 = class LevelEditorBaseUI extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "levelname", _descriptor, this);

          _initializerDefineProperty(this, "totalTime", _descriptor2, this);

          this._totalHeight = 0;

          _initializerDefineProperty(this, "backgroundLayer", _descriptor3, this);

          _initializerDefineProperty(this, "floorLayers", _descriptor4, this);

          _initializerDefineProperty(this, "skyLayers", _descriptor5, this);

          this.backgroundLayerNode = null;
          this.floorLayersNode = null;
          this.skyLayersNode = null;
          this.drawNode = null;
          this.graphics = null;
        }

        onLoad() {
          var _this$floorLayersNode;

          console.log("LevelEditorBaseUI start.");
          this.backgroundLayerNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "BackgroundLayer");
          this.floorLayersNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "FloorLayers");
          this.skyLayersNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "SkyLayers");
          this.drawNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "DrawNode");

          if (!this.graphics) {
            this.graphics = this.drawNode.getComponent(Graphics) || this.drawNode.addComponent(Graphics);
          }

          console.log("LevelEditorBaseUI start " + ((_this$floorLayersNode = this.floorLayersNode) == null ? void 0 : _this$floorLayersNode.uuid));
        }

        update(dt) {
          this.checkLayerNode(this.floorLayersNode, this.floorLayers);
          this.checkLayerNode(this.skyLayersNode, this.skyLayers);
          this.floorLayers.forEach(layer => {
            this._checkScrollNode(layer, layer.node);

            this._checkRandTerrainNode(layer, layer.node);
          });
          this.skyLayers.forEach(layer => {
            this._checkScrollNode(layer, layer.node);

            this._checkRandTerrainNode(layer, layer.node);
          });
        }

        setBackgroundNodePosition(node, yOff) {
          var height = node.getComponent(UITransform).contentSize.height;
          node.setPosition(0, yOff - view.getVisibleSize().height / 2 + height / 2);
          return height;
        }

        tick(progress) {
          var yOff = 0;

          for (var i = 0; i < this.backgroundLayer.backgroundsNode.children.length; i++) {
            var bg = this.backgroundLayer.backgroundsNode.children[i];
            yOff += this.setBackgroundNodePosition(bg, yOff);
          }

          while (this.backgroundLayer.backgrounds.length > 0 && yOff < this._totalHeight) {
            var _bg = null;
            var bgIndex = this.backgroundLayer.backgroundsNode.children.length % this.backgroundLayer.backgrounds.length;
            var prefab = this.backgroundLayer.backgrounds[bgIndex];

            if (prefab != null) {
              _bg = instantiate(prefab);
            }

            if (_bg == null) {
              _bg = new Node("empty");
              _bg.addComponent(UITransform).height = 1024;
            }

            this.backgroundLayer.backgroundsNode.addChild(_bg);
            yOff += this.setBackgroundNodePosition(_bg, yOff);
          }

          for (var _i = this.backgroundLayer.backgroundsNode.children.length - 1; _i >= 0; _i--) {
            var _bg2 = this.backgroundLayer.backgroundsNode.children[_i];

            if (_bg2.position.y - _bg2.getComponent(UITransform).height / 2 > this._totalHeight) {
              _bg2.removeFromParent();
            } else {
              break;
            }
          }

          this.backgroundLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, this.backgroundLayer.speed);
          this.floorLayers.forEach(layer => {
            layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);
          });
          this.skyLayers.forEach(layer => {
            layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI).tick(progress, this.totalTime, layer.speed);
          });
        }

        static addLayer(parentNode, name) {
          var layerNode = new Node(name);
          var layerCom = layerNode.addComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI);
          parentNode.addChild(layerNode);
          return layerCom;
        }

        checkLayerNode(parentNode, layers) {
          var removeLayerNodes = [];
          parentNode.children.forEach(node => {
            var layerCom = node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI);

            if (layerCom == null) {
              console.log("Level checkLayerNode remove " + node.name + " because layerCom == null\"");
              removeLayerNodes.push(node);
              return;
            }

            if (layers.find(layer => layer.node == node) == null) {
              console.log("Level checkLayerNode remove " + node.name + " because not in layers\"");
              removeLayerNodes.push(node);
              return;
            }
          });
          removeLayerNodes.forEach(element => {
            element.removeFromParent();
          });
          layers.forEach((layer, i) => {
            if (layer.node == null || layer.node.isValid == false) {
              console.log("Level checkLayerNode add because layer == null");
              layer.node = LevelEditorBaseUI.addLayer(parentNode, "layer_" + i).node;
            }
          });
        }

        _checkScrollNode(data, parentNode) {
          var scrollsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(parentNode, "scrolls");

          if (data.type != (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Scroll) {
            scrollsNode.removeAllChildren();
            return;
          }

          var isCountMatch = scrollsNode.children.length === data.scrollLayers.length;
          var isUUIDMatch = true;

          for (var i = 0; i < scrollsNode.children.length; i++) {
            var _data$scrollLayers$i;

            var scrollNode = scrollsNode.children[i];
            var scrollPrefabUUID = "";
            var firstChild;

            if (scrollNode != null) {
              firstChild = scrollNode.children[0];

              if (firstChild != null) {
                var _firstChild$_prefab;

                // @ts-ignore
                scrollPrefabUUID = (_firstChild$_prefab = firstChild._prefab) == null || (_firstChild$_prefab = _firstChild$_prefab.asset) == null ? void 0 : _firstChild$_prefab._uuid;
              }
            } // @ts-ignore


            var scrollUUID = (_data$scrollLayers$i = data.scrollLayers[i]) == null || (_data$scrollLayers$i = _data$scrollLayers$i.scrollPrefab) == null ? void 0 : _data$scrollLayers$i._uuid;

            if (scrollPrefabUUID != scrollUUID) {
              console.log("LevelEditorBaseUI _checkScrollNode scrollPrefabUUID != scrollUUID", scrollPrefabUUID, scrollUUID);
              isUUIDMatch = false;
              break;
            }
          }

          if (!isCountMatch || !isUUIDMatch) {
            scrollsNode.removeAllChildren();
            data.scrollLayers.forEach((scroll, index) => {
              if (scroll.scrollPrefab != null) {
                var _scroll$scrollPrefab;

                assetManager.loadAny({
                  uuid: (_scroll$scrollPrefab = scroll.scrollPrefab) == null ? void 0 : _scroll$scrollPrefab.uuid
                }, (err, prefab) => {
                  if (err) {
                    console.error("LevelEditorBaseUI _checkScrollNode load scroll prefab err", err);
                    return;
                  }

                  var scrollNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
                    error: Error()
                  }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(scrollsNode, "scroll_" + index);
                  var totalHeight = data.speed * this.totalTime;
                  console.log("LevelEditorBaseUI totalHeight", totalHeight);
                  var childCount = totalHeight / data.speed;
                  var posOffsetY = 0;

                  for (var _i2 = 0; _i2 < childCount; _i2++) {
                    var child = instantiate(prefab);
                    child.setPosition(0, posOffsetY, 0);
                    var offY = child.getComponent(UITransform).contentSize.height;
                    scrollNode.addChild(child);
                    posOffsetY += offY;
                  }
                });
              }
            });
          }
        }

        _checkRandTerrainNode(data, parentNode) {
          var dynamicNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(parentNode, "dynamic");

          if (data.type != (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Random) {
            dynamicNode.removeAllChildren();
            return;
          }

          var isCountMatch = dynamicNode.children.length === data.randomLayers.length;
          var dynaNode;
          data.randomLayers.forEach((randTerrains, index) => {
            dynaNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
              error: Error()
            }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(dynamicNode, "dyna_" + index);

            if (dynaNode === null || dynaNode.children.length != randTerrains.dynamicTerrains.length) {
              isCountMatch = false;
            }
          });

          if (!isCountMatch) {
            dynamicNode.removeAllChildren();
            data.randomLayers.forEach(randTerrains => {
              if (randTerrains.dynamicTerrains.length > 0) {
                randTerrains.dynamicTerrains.forEach(terrains => {
                  terrains.dynamicTerrains.forEach(terrain => {
                    var _terrain$terrainEleme;

                    assetManager.loadAny({
                      uuid: terrain == null || (_terrain$terrainEleme = terrain.terrainElements) == null ? void 0 : _terrain$terrainEleme.uuid
                    }, (err, prefab) => {
                      if (err) {
                        return;
                      }

                      var node = instantiate(prefab);
                      dynaNode.addChild(node);
                    });
                  });
                });
              }
            });
          }
        }

        initByLevelData(data) {
          var _data$backgroundLayer, _data$backgroundLayer2;

          this.levelname = data.name;
          this.totalTime = data.totalTime;
          this.backgroundLayerNode.removeAllChildren();
          this.backgroundLayer = new (_crd && LevelBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelBackgroundLayer({
            error: Error()
          }), LevelBackgroundLayer) : LevelBackgroundLayer)();
          this.backgroundLayer.backgrounds = [];
          (_data$backgroundLayer = data.backgroundLayer) == null || (_data$backgroundLayer = _data$backgroundLayer.backgrounds) == null || _data$backgroundLayer.forEach(background => {
            assetManager.loadAny({
              uuid: background
            }, (err, prefab) => {
              if (err) {
                console.error("LevelEditorBaseUI initByLevelData load background prefab err", err);
                return;
              }

              this.backgroundLayer.backgrounds.push(prefab);
            });
          });
          this.backgroundLayer.speed = (_data$backgroundLayer2 = data.backgroundLayer) == null ? void 0 : _data$backgroundLayer2.speed;
          this._totalHeight = this.backgroundLayer.speed * this.totalTime;
          this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode, "layer").node;
          this.backgroundLayer.backgroundsNode = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);
          this.backgroundLayer.backgroundsNode.setSiblingIndex(0);
          this.backgroundLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).initByLevelData(data.backgroundLayer);

          if (data.backgroundLayer && data.backgroundLayer.scrolls) {
            data.backgroundLayer.scrolls.forEach(scrollData => {
              var scrollLayer = new (_crd && LevelScrollLayerUI === void 0 ? (_reportPossibleCrUseOfLevelScrollLayerUI({
                error: Error()
              }), LevelScrollLayerUI) : LevelScrollLayerUI)();
              assetManager.loadAny({
                uuid: scrollData.uuid
              }, (err, prefab) => {
                if (err) {
                  console.error("LevelEditorBaseUI initByLevelData load background scroll layer prefab err", err);
                  return;
                }

                scrollLayer.scrollPrefab = prefab;
                this.backgroundLayer.scrollLayers.push(scrollLayer);
              });
            });
          }

          this.floorLayers = [];
          this.skyLayers = [];
          LevelEditorBaseUI.initLayers(this.floorLayersNode, this.floorLayers, data.floorLayers);
          LevelEditorBaseUI.initLayers(this.skyLayersNode, this.skyLayers, data.skyLayers);
        }

        static initLayers(parentNode, layers, dataLayers) {
          parentNode.removeAllChildren();
          dataLayers.forEach((layer, i) => {
            var levelLayer = new (_crd && LevelLayer === void 0 ? (_reportPossibleCrUseOfLevelLayer({
              error: Error()
            }), LevelLayer) : LevelLayer)();
            levelLayer.speed = layer.speed;
            levelLayer.type = layer.type;
            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, "layer_" + i).node;
            var levelEditorLayerUI = levelLayer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
              error: Error()
            }), LevelEditorLayerUI) : LevelEditorLayerUI);
            levelEditorLayerUI.initByLevelData(layer);
            levelEditorLayerUI.initScorllsByLevelData(levelLayer, layer);
            layers.push(levelLayer);
          });
        }

        static fillLevelLayerData(layer, dataLayer) {
          dataLayer.speed = layer.speed;
          dataLayer.type = layer.type;

          if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Scroll) {
            layer.scrollLayers.forEach(scrollLayer => {
              var _scrollLayer$scrollPr;

              var data = new (_crd && LevelDataScroll === void 0 ? (_reportPossibleCrUseOfLevelDataScroll({
                error: Error()
              }), LevelDataScroll) : LevelDataScroll)();
              data.uuid = (_scrollLayer$scrollPr = scrollLayer.scrollPrefab) == null ? void 0 : _scrollLayer$scrollPr.uuid;
              data.weight = scrollLayer.weight;
              data.position = Vec2.ZERO;
              data.scale = Vec2.ONE;
              data.rotation = 0;
              dataLayer.scrolls.push(data);
              console.log("LevelEditorBaseUI fill scrollLayersData", dataLayer);
            });
          } else if (layer.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Random) {
            layer.randomLayers.forEach(randomLayer => {
              randomLayer.dynamicTerrains.forEach(terrains => {
                var data = new (_crd && LevelDataRandTerrains === void 0 ? (_reportPossibleCrUseOfLevelDataRandTerrains({
                  error: Error()
                }), LevelDataRandTerrains) : LevelDataRandTerrains)();
                data.terrains = [];
                data.weight = terrains.weight;
                terrains.dynamicTerrains.forEach(terrainElement => {
                  var _terrainElement$terra;

                  var terrainData = {
                    weight: terrainElement.weight,
                    uuid: (_terrainElement$terra = terrainElement.terrainElements) == null ? void 0 : _terrainElement$terra.uuid
                  };
                  data.terrains.push(terrainData);
                });
                dataLayer.dynamics.push(data);
              });
            });
          }

          layer.node.getComponent(_crd && LevelEditorLayerUI === void 0 ? (_reportPossibleCrUseOfLevelEditorLayerUI({
            error: Error()
          }), LevelEditorLayerUI) : LevelEditorLayerUI).fillLevelData(dataLayer);
        }

        static fillLevelLayersData(layers, dataLayers) {
          layers.forEach(layer => {
            var levelLayer = new (_crd && LevelDataLayer === void 0 ? (_reportPossibleCrUseOfLevelDataLayer({
              error: Error()
            }), LevelDataLayer) : LevelDataLayer)();
            LevelEditorBaseUI.fillLevelLayerData(layer, levelLayer);
            dataLayers.push(levelLayer);
          });
        }

        fillLevelData(data) {
          data.name = this.levelname;
          data.totalTime = this.totalTime;
          data.backgroundLayer = new (_crd && LevelDataBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelDataBackgroundLayer({
            error: Error()
          }), LevelDataBackgroundLayer) : LevelDataBackgroundLayer)();

          for (var i = 0; i < this.backgroundLayer.backgrounds.length; i++) {
            var prefab = this.backgroundLayer.backgrounds[i];

            if (prefab == null) {
              continue;
            }

            data.backgroundLayer.backgrounds.push(prefab.uuid);
          }

          LevelEditorBaseUI.fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);
          data.floorLayers = [];
          data.skyLayers = [];
          LevelEditorBaseUI.fillLevelLayersData(this.floorLayers, data.floorLayers);
          LevelEditorBaseUI.fillLevelLayersData(this.skyLayers, data.skyLayers);
        }

        drawViewport() {
          if (!this.graphics) return;
          var drawTransform = this.drawNode.getComponent(UITransform); // 计算视口矩形

          var viewport = new Rect(this.drawNode.getPosition().x - drawTransform.contentSize.width / 2, this.drawNode.getPosition().y - drawTransform.contentSize.height / 2, drawTransform.contentSize.width, drawTransform.contentSize.height); // Draw viewport rectangle

          this.graphics.strokeColor = Color.RED;
          this.graphics.lineWidth = 10;
          this.graphics.rect(viewport.x, viewport.y, viewport.width, viewport.height);
          this.graphics.stroke(); // 绘制4个填充矩形表示视口边界

          var maskWidth = 10000;
          var maskHeight = drawTransform.contentSize.height;
          this.graphics.fillColor = Color.BLACK; // 顶部矩形

          this.graphics.fillRect(-maskWidth / 2, this.drawNode.getPosition().y + drawTransform.contentSize.height - maskHeight / 2, maskWidth, maskHeight); // 底部矩形

          this.graphics.fillRect(-maskWidth / 2, this.drawNode.getPosition().y - drawTransform.contentSize.height - maskHeight / 2, maskWidth, maskHeight); // 左侧矩形

          this.graphics.fillRect(-maskWidth - drawTransform.contentSize.width / 2, this.drawNode.getPosition().y - drawTransform.contentSize.height / 2, maskWidth, maskHeight); // 右侧矩形

          this.graphics.fillRect(drawTransform.contentSize.width / 2, this.drawNode.getPosition().y - drawTransform.contentSize.height / 2, maskWidth, maskHeight);
        }

        drawClear() {
          if (!this.graphics) return;
          this.graphics.clear();
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "levelname", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return "";
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "totalTime", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 10;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "backgroundLayer", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return new (_crd && LevelBackgroundLayer === void 0 ? (_reportPossibleCrUseOfLevelBackgroundLayer({
            error: Error()
          }), LevelBackgroundLayer) : LevelBackgroundLayer)();
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "floorLayers", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "skyLayers", [_dec7], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7faf2e43a321c3c1a125f57fa1812daa6a9e334f.js.map