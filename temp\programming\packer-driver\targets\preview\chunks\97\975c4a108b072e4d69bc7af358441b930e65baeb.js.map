{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts"], "names": ["_decorator", "BundleName", "IBundleEntry", "MyApp", "UIMgr", "logDebug", "EventMgr", "HomeUIEvent", "BottomTab", "BottomUI", "HomeUI", "TopUI", "PlaneUI", "ShopUI", "SkyIslandUI", "TalentUI", "StoryUI", "ccclass", "CommonEntry", "initEntry", "resMgr", "loadBundle", "Home", "openUI", "on", "Leave", "closeUI", "emit", "BottomTabRegister", "get", "preloadHomeSubBundles", "HomePlane", "then", "loadUI", "Plane", "HomeTalent", "Talent", "HomeShop", "Shop", "HomeSkyIsland", "SkyIsLand", "HomeStory"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,Y,iBAAAA,Y;;AACZC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,K,kBAAAA,K;;AACAC,MAAAA,O,kBAAAA,O;;AACAC,MAAAA,M,kBAAAA,M;;AACAC,MAAAA,W,kBAAAA,W;;AACAC,MAAAA,Q,kBAAAA,Q;;AACAC,MAAAA,O,kBAAAA,O;;;;;;;;;OACH;AAAEC,QAAAA;AAAF,O,GAAcjB,U;;6BAEPkB,W,WADZD,OAAO,CAAC,aAAD,C,gBAAR,MACaC,WADb;AAAA;AAAA,wCAC8C;AAC7BC,QAAAA,SAAS,GAAgC;AAAA;;AAAA;AAClD;AAAA;AAAA,sCAAS,aAAT,EAAwB,WAAxB;AACA,kBAAM;AAAA;AAAA,gCAAMC,MAAN,CAAaC,UAAb,CAAwB;AAAA;AAAA,0CAAWC,IAAnC,CAAN;AACA,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,iCAAN;AACA,kBAAM;AAAA;AAAA,gCAAMA,MAAN;AAAA;AAAA,qCAAN;AACA,kBAAM;AAAA;AAAA,gCAAMA,MAAN;AAAA;AAAA,+BAAN,CALkD,CAMlD;;AACA;AAAA;AAAA,sCAASC,EAAT,CAAY;AAAA;AAAA,4CAAYC,KAAxB,EAA+B,MAAM;AACjC;AAAA;AAAA,kCAAMC,OAAN;AAAA;AAAA;AACA;AAAA;AAAA,kCAAMA,OAAN;AAAA;AAAA,wCAFiC,CAET;;AACxB;AAAA;AAAA,kCAAMA,OAAN;AAAA;AAAA;AACH,aAJD;AAKA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUN,IAAvD,EAA6D;AAAA;AAAA,gCAAMO,GAAN;AAAA;AAAA,iCAA7D;;AACA,YAAA,KAAI,CAACC,qBAAL;AAbkD;AAcrD;;AAEOA,QAAAA,qBAAqB,GAAG;AAC5B;AACA;AAAA;AAAA,8BAAMV,MAAN,CAAaC,UAAb,CAAwB;AAAA;AAAA,wCAAWU,SAAnC,EAA8CC,IAA9C,iCAAmD,aAAY;AAC3D,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,mCAAN;AACA;AAAA;AAAA,sCAASN,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUM,KAAvD,EAA8D;AAAA;AAAA,gCAAML,GAAN;AAAA;AAAA,mCAA9D;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMT,MAAN,CAAaC,UAAb,CAAwB;AAAA;AAAA,wCAAWc,UAAnC,EAA+CH,IAA/C,iCAAoD,aAAY;AAC5D,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,qCAAN;AACA;AAAA;AAAA,sCAASN,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUQ,MAAvD,EAA+D;AAAA;AAAA,gCAAMP,GAAN;AAAA;AAAA,qCAA/D;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMT,MAAN,CAAaC,UAAb,CAAwB;AAAA;AAAA,wCAAWgB,QAAnC,EAA6CL,IAA7C,iCAAkD,aAAY;AAC1D,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,iCAAN;AACA;AAAA;AAAA,sCAASN,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUU,IAAvD,EAA6D;AAAA;AAAA,gCAAMT,GAAN;AAAA;AAAA,iCAA7D;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMT,MAAN,CAAaC,UAAb,CAAwB;AAAA;AAAA,wCAAWkB,aAAnC,EAAkDP,IAAlD,iCAAuD,aAAY;AAC/D,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,2CAAN;AACA;AAAA;AAAA,sCAASN,IAAT,CAAc;AAAA;AAAA,4CAAYC,iBAA1B,EAA6C;AAAA;AAAA,wCAAUY,SAAvD,EAAkE;AAAA;AAAA,gCAAMX,GAAN;AAAA;AAAA,2CAAlE;AACH,WAHD;AAIA;AAAA;AAAA,8BAAMT,MAAN,CAAaC,UAAb,CAAwB;AAAA;AAAA,wCAAWoB,SAAnC,EAA8CT,IAA9C,iCAAmD,aAAY;AAC3D,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,mCAAN;AACH,WAFD;AAGH;;AAtCyC,O", "sourcesContent": ["import { _decorator } from \"cc\";\nimport { BundleName, IBundleEntry } from \"db://assets/bundles/Bundle\";\nimport { MyApp } from \"db://assets/scripts/MyApp\";\nimport { UIMgr } from \"db://assets/scripts/ui/UIMgr\";\nimport { logDebug } from \"db://assets/scripts/Utils/Logger\";\nimport { EventMgr } from \"./event/EventManager\";\nimport { HomeUIEvent } from \"./event/HomeUIEvent\";\nimport { BottomTab } from \"./ui/home/<USER>\";\nimport { BottomUI } from \"./ui/home/<USER>\";\nimport { HomeUI } from \"./ui/home/<USER>\";\nimport { TopUI } from \"./ui/home/<USER>\";\nimport { PlaneUI } from \"./ui/plane/PlaneUI\";\nimport { ShopUI } from \"./ui/shop/ShopUI\";\nimport { SkyIslandUI } from \"./ui/skyisland/SkyIslandUI\";\nimport { TalentUI } from \"./ui/talent/TalentUI\";\nimport { StoryUI } from \"./ui/story/StoryUI\";\nconst { ccclass } = _decorator;\n@ccclass('CommonEntry')\nexport class CommonEntry extends IBundleEntry {\n    public async initEntry(...args: any[]): Promise<void> {\n        logDebug(\"CommonEntry\", \"initEntry\")\n        await MyApp.resMgr.loadBundle(BundleName.Home)\n        await UIMgr.openUI(HomeUI)\n        await UIMgr.openUI(BottomUI)\n        await UIMgr.openUI(TopUI)\n        //暂时这样 后面再优化\n        EventMgr.on(HomeUIEvent.Leave, () => {\n            UIMgr.closeUI(HomeUI)\n            UIMgr.closeUI(BottomUI) //这里会把下面的主界面都关闭  \n            UIMgr.closeUI(TopUI)\n        })\n        EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Home, UIMgr.get(HomeUI))\n        this.preloadHomeSubBundles();\n    }\n\n    private preloadHomeSubBundles() {\n        //异步加载其他bundle\n        MyApp.resMgr.loadBundle(BundleName.HomePlane).then(async () => {\n            await UIMgr.loadUI(PlaneUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Plane, UIMgr.get(PlaneUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeTalent).then(async () => {\n            await UIMgr.loadUI(TalentUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Talent, UIMgr.get(TalentUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeShop).then(async () => {\n            await UIMgr.loadUI(ShopUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.Shop, UIMgr.get(ShopUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeSkyIsland).then(async () => {\n            await UIMgr.loadUI(SkyIslandUI)\n            EventMgr.emit(HomeUIEvent.BottomTabRegister, BottomTab.SkyIsLand, UIMgr.get(SkyIslandUI))\n        })\n        MyApp.resMgr.loadBundle(BundleName.HomeStory).then(async () => {\n            await UIMgr.loadUI(StoryUI)\n        })\n    }\n}"]}