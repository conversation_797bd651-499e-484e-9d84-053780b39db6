{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneCombineResultUI.ts"], "names": ["_decorator", "Label", "Node", "MyApp", "logDebug", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "BundleName", "List", "ListItem", "ccclass", "property", "PlaneCombineResultUI", "_results", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "PopUp", "getBundleName", "HomePlane", "getUIOption", "isClickBgCloseUI", "onLoad", "onShow", "results", "length", "list", "node", "active", "singleResult", "numItems", "equipInfo", "lubanTables", "TbEquip", "get", "equip_id", "getComponentInChildren", "string", "name", "quality", "onHide", "onClose", "onList<PERSON>ender", "item", "index", "getComponent", "listId"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAGnBC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;AACRC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,I;;AACAC,MAAAA,Q;;;;;;;;;OACD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBX,U;;sCAGjBY,oB,WADZF,OAAO,CAAC,sBAAD,C,UAQHC,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ,CAACT,IAAD,C,2BAVb,MACaU,oBADb;AAAA;AAAA,4BACiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAWrCC,QAXqC,GAWa,EAXb;AAAA;;AACzB,eAANC,MAAM,GAAW;AAAE,iBAAO,gCAAP;AAA0C;;AACrD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,KAAf;AAAsB;;AAC/B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA6B;;AAC5C,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAQSC,QAAAA,MAAM,GAAS,CACxB;;AAEKC,QAAAA,MAAM,CAACC,OAAD,EAAgE;AAAA;;AAAA;AACxE,YAAA,KAAI,CAACV,QAAL,GAAgBU,OAAhB;;AACA,gBAAIA,OAAO,CAACC,MAAR,GAAiB,CAArB,EAAwB;AACpB,cAAA,KAAI,CAACC,IAAL,CAAWC,IAAX,CAAgBC,MAAhB,GAAyB,IAAzB;AACA,cAAA,KAAI,CAACC,YAAL,CAAmBD,MAAnB,GAA4B,KAA5B;AACA,cAAA,KAAI,CAACF,IAAL,CAAWI,QAAX,GAAsBN,OAAO,CAACC,MAA9B;AACA;AACH;;AACD,YAAA,KAAI,CAACC,IAAL,CAAWC,IAAX,CAAgBC,MAAhB,GAAyB,KAAzB;AACA,YAAA,KAAI,CAACC,YAAL,CAAmBD,MAAnB,GAA4B,IAA5B;AACA,gBAAMG,SAAS,GAAG;AAAA;AAAA,gCAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,GAA1B,CAA8B,KAAI,CAACpB,QAAL,CAAc,CAAd,EAAkBqB,QAAhD,CAAlB;;AACA,gBAAIJ,SAAJ,EAAe;AACX,cAAA,KAAI,CAACF,YAAL,CAAmBO,sBAAnB,CAA0ClC,KAA1C,EAAkDmC,MAAlD,GAA2DN,SAAS,CAACO,IAAV,GAAiB,MAAjB,GAA0BP,SAAS,CAACQ,OAApC,GAA8C,GAAzG;AACH,aAFD,MAEO;AACH,cAAA,KAAI,CAACV,YAAL,CAAmBO,sBAAnB,CAA0ClC,KAA1C,EAAkDmC,MAAlD,GAA2D,IAA3D;AACH;AAfuE;AAgB3E;;AACKG,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AAEDC,QAAAA,YAAY,CAACC,IAAD,EAAaC,KAAb,EAA4B;AACpC;AAAA;AAAA,oCAAS,SAAT,aAA6BA,KAA7B,YAAyCD,IAAI,CAACE,YAAL;AAAA;AAAA,oCAA6BC,MAAtE;AACA,cAAMf,SAAS,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,GAA1B,CAA8B,KAAKpB,QAAL,CAAc8B,KAAd,EAAsBT,QAApD,CAAlB;;AACA,cAAIJ,SAAJ,EAAe;AACXY,YAAAA,IAAI,CAACP,sBAAL,CAA4BlC,KAA5B,EAAoCmC,MAApC,GAA6CN,SAAS,CAACO,IAAV,GAAiB,MAAjB,GAA0BP,SAAS,CAACQ,OAApC,GAA8C,GAA3F;AACH,WAFD,MAEO;AACHI,YAAAA,IAAI,CAACP,sBAAL,CAA4BlC,KAA5B,EAAoCmC,MAApC,GAA6C,IAA7C;AACH;AACJ;;AA/C4C,O;;;;;iBAQzB,I;;;;;;;iBAEQ,I", "sourcesContent": ["import { _decorator, Label, Node } from 'cc';\n\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { MyApp } from '../../../../../scripts/MyApp';\nimport { logDebug } from '../../../../../scripts/Utils/Logger';\nimport { BaseUI, UILayer, UIOpt } from '../../../../../scripts/ui/UIMgr';\nimport { BundleName } from '../../../../Bundle';\nimport List from '../common/components/list/List';\nimport ListItem from '../common/components/list/ListItem';\nconst { ccclass, property } = _decorator;\n\n@ccclass('PlaneCombineResultUI')\nexport class PlaneCombineResultUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/PlaneCombineResultUI\"; }\n    public static getLayer(): UILayer { return UILayer.PopUp }\n    public static getBundleName(): string { return BundleName.HomePlane }\n    public static getUIOption(): UIOpt {\n        return { isClickBgCloseUI: true }\n    }\n    @property(List)\n    list: List | null = null;\n    @property(Node)\n    singleResult: Node | null = null;\n    private _results: csproto.cs.ICSEquipCombineResultOne[] = [];\n\n\n    protected onLoad(): void {\n    }\n\n    async onShow(results: csproto.cs.ICSEquipCombineResultOne[]): Promise<void> {\n        this._results = results\n        if (results.length > 1) {\n            this.list!.node.active = true;\n            this.singleResult!.active = false;\n            this.list!.numItems = results.length;\n            return\n        }\n        this.list!.node.active = false;\n        this.singleResult!.active = true;\n        const equipInfo = MyApp.lubanTables.TbEquip.get(this._results[0]!.equip_id!)\n        if (equipInfo) {\n            this.singleResult!.getComponentInChildren(Label)!.string = equipInfo.name + \"(品质:\" + equipInfo.quality + \")\";\n        } else {\n            this.singleResult!.getComponentInChildren(Label)!.string = \"未知\";\n        }\n    }\n    async onHide(): Promise<void> {\n    }\n    async onClose(): Promise<void> {\n    }\n\n    onListRender(item: Node, index: number) {\n        logDebug(\"PlaneUI\", `index:${index} id:${item.getComponent(ListItem)!.listId}`)\n        const equipInfo = MyApp.lubanTables.TbEquip.get(this._results[index]!.equip_id!)\n        if (equipInfo) {\n            item.getComponentInChildren(Label)!.string = equipInfo.name + \"(品质:\" + equipInfo.quality + \")\";\n        } else {\n            item.getComponentInChildren(Label)!.string = \"未知\";\n        }\n    }\n}\n"]}