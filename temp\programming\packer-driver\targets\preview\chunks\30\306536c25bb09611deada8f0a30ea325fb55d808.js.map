{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/ButtonPlus.ts"], "names": ["_decorator", "<PERSON><PERSON>", "EventHandler", "ccclass", "property", "executeInEditMode", "menu", "help", "inspector", "ButtonPlus", "tooltip", "type", "multiline", "formerlySerializedAs", "continuous", "_continuousTimer", "_pressed", "longPressFlag", "longPressTimer", "onEnable", "clickDefZoomScale", "transition", "zoomScale", "duration", "onDisable", "clearTimeout", "_onTouchBegan", "event", "interactable", "enabledInHierarchy", "openLongPress", "setTimeout", "node", "emit", "bind", "longPressTime", "_updateState", "propagationStopped", "_onTouchEnded", "openContinuous", "emitEvents", "clickEvents", "continuousTime", "_onTouchCancel", "addClick", "callback", "target", "off", "on", "addLongClick", "startFunc", "endFunc"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,Y,OAAAA,Y;;;;;;;;;OAEvB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,iBAArB;AAAwCC,QAAAA,IAAxC;AAA8CC,QAAAA,IAA9C;AAAoDC,QAAAA;AAApD,O,GAAkER,U;;4BAM3DS,U,WAERL,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAERN,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE,MAAX;AAAmBC,QAAAA,IAAI,EAAE,EAAzB;AAA6BC,QAAAA,SAAS,EAAE,IAAxC;AAA8CC,QAAAA,oBAAoB,EAAE;AAApE,OAAD,C,UAERT,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAERN,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAaRN,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,UAGRN,QAAQ,CAAC;AAAEM,QAAAA,OAAO,EAAE;AAAX,OAAD,C,EA7BZP,O,UAEAE,iB,qBAFD,MAKaI,UALb,SAKgCR,MALhC,CAKuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAYnC;AAZmC,eAanCa,UAbmC,GAab,KAba;AAcnC;AAdmC,eAenCC,gBAfmC,GAeI,IAfJ;AAAA,eAiB3BC,QAjB2B,GAiBhB,KAjBgB;;AAoBnC;AApBmC;;AAuBnC;AAvBmC;;AAAA,eA0BnCC,aA1BmC,GA0BnB,KA1BmB;AAAA,eA4B3BC,cA5B2B,GA4BU,IA5BV;AAAA;;AA8BnCC,QAAAA,QAAQ,GAAG;AACP,eAAKL,UAAL,GAAkB,KAAlB;AACA,gBAAMK,QAAN,GAFO,CAGP;AACA;;AACA,cAAI,KAAKC,iBAAT,EAA4B;AACxB,iBAAKC,UAAL,GAAkB,CAAlB;AACA,iBAAKC,SAAL,GAAiB,IAAjB;AACA,iBAAKC,QAAL,GAAgB,GAAhB;AACH;AACJ;;AACDC,QAAAA,SAAS,GAAG;AACR,cAAI,KAAKT,gBAAT,EAA2B;AACvBU,YAAAA,YAAY,CAAC,KAAKV,gBAAN,CAAZ;AACA,iBAAKA,gBAAL,GAAwB,IAAxB;AACH;;AACD,cAAI,KAAKG,cAAT,EAAyB;AACrBO,YAAAA,YAAY,CAAC,KAAKP,cAAN,CAAZ;AACA,iBAAKA,cAAL,GAAsB,IAAtB;AACH;;AACD,gBAAMM,SAAN;AACH;AAED;;;AACUE,QAAAA,aAAa,CAACC,KAAD,EAAoB;AACvC,cAAI,CAAC,KAAKC,YAAN,IAAsB,CAAC,KAAKC,kBAAhC,EAAoD;;AAEpD,cAAI,KAAKC,aAAL,IAAsB,CAAC,KAAKb,aAAhC,EAA+C;AAAK;AAChD,gBAAI,KAAKC,cAAT,EAAyBO,YAAY,CAAC,KAAKP,cAAN,CAAZ;AACzB,iBAAKA,cAAL,GAAsBa,UAAU,CAAC,YAA2B;AACxD;AACA,kBAAI,KAAKf,QAAT,EAAmB;AACf,qBAAKgB,IAAL,CAAUC,IAAV,CAAe,gBAAf,EAAiC,IAAjC;AACA,qBAAKhB,aAAL,GAAqB,IAArB;AACH;AACJ,aANgC,CAM/BiB,IAN+B,CAM1B,IAN0B,CAAD,EAMlB,KAAKC,aAAL,GAAqB,IANH,CAAhC;AAOH;;AAED,eAAKnB,QAAL,GAAgB,IAAhB;;AACA,eAAKoB,YAAL,GAfuC,CAgBvC;;;AACAT,UAAAA,KAAK,CAACU,kBAAN,GAA2B,IAA3B;AACH;;AACSC,QAAAA,aAAa,CAACX,KAAD,EAAoB;AACvC,cAAI,CAAC,KAAKC,YAAN,IAAsB,CAAC,KAAKC,kBAAhC,EAAoD;;AACpD,cAAI,KAAKb,QAAL,IAAiB,KAAKC,aAA1B,EAAyC;AACrC,iBAAKe,IAAL,CAAUC,IAAV,CAAe,cAAf,EAA+B,IAA/B;AACA,iBAAKhB,aAAL,GAAqB,KAArB;AACH,WAHD,MAGO,IAAI,KAAKD,QAAL,IAAiB,CAAC,KAAKF,UAA3B,EAAuC;AAC1C,iBAAKA,UAAL,GAAkB,KAAKyB,cAAL,GAAsB,IAAtB,GAA6B,KAA/C;AACArC,YAAAA,YAAY,CAACsC,UAAb,CAAwB,KAAKC,WAA7B,EAA0Cd,KAA1C;AACA,iBAAKK,IAAL,CAAUC,IAAV,CAAe,OAAf,EAAwBN,KAAxB,EAH0C,CAI1C;;AACA,gBAAI,KAAKY,cAAT,EAAyB;AACrB,mBAAKxB,gBAAL,GAAwBgB,UAAU,CAAC,YAA2B;AAC1D,qBAAKjB,UAAL,GAAkB,KAAlB;AACH,eAFkC,CAEjCoB,IAFiC,CAE5B,IAF4B,CAAD,EAEpB,KAAKQ,cAAL,GAAsB,IAFF,CAAlC;AAGH;AACJ;;AACD,eAAK1B,QAAL,GAAgB,KAAhB;;AACA,eAAKoB,YAAL;;AACAT,UAAAA,KAAK,CAACU,kBAAN,GAA2B,IAA3B,CAlBuC,CAmBvC;AACH;;AACSM,QAAAA,cAAc,GAAG;AACvB,cAAI,CAAC,KAAKf,YAAN,IAAsB,CAAC,KAAKC,kBAAhC,EAAoD;;AACpD,cAAI,KAAKb,QAAL,IAAiB,KAAKC,aAA1B,EAAyC;AACrC,iBAAKe,IAAL,CAAUC,IAAV,CAAe,cAAf,EAA+B,IAA/B;AACA,iBAAKhB,aAAL,GAAqB,KAArB;AACH;;AACD,eAAKD,QAAL,GAAgB,KAAhB;;AACA,eAAKoB,YAAL;AACH;AACD;;;AACAQ,QAAAA,QAAQ,CAACC,QAAD,EAAqBC,MAArB,EAAqC;AACzC,eAAKd,IAAL,CAAUe,GAAV,CAAc,OAAd;AACA,eAAKf,IAAL,CAAUgB,EAAV,CAAa,OAAb,EAAsBH,QAAtB,EAAgCC,MAAhC;AACH;AACD;;;AACAG,QAAAA,YAAY,CAACC,SAAD,EAAsBC,OAAtB,EAAyCL,MAAzC,EAAyD;AACjE,eAAKd,IAAL,CAAUe,GAAV,CAAc,gBAAd;AACA,eAAKf,IAAL,CAAUe,GAAV,CAAc,cAAd;AACA,eAAKf,IAAL,CAAUgB,EAAV,CAAa,gBAAb,EAA+BE,SAA/B,EAA0CJ,MAA1C;AACA,eAAKd,IAAL,CAAUgB,EAAV,CAAa,cAAb,EAA6BG,OAA7B,EAAsCL,MAAtC;AACH;;AAlHkC,O;;;;;iBAGf,I;;;;;;;iBAET,E;;;;;;;iBAEM,I;;;;;;;iBAEA,G;;;;;;;iBAaD,K;;;;;;;iBAGA,C", "sourcesContent": ["import { _decorator, <PERSON><PERSON>, <PERSON>Hand<PERSON>, EventTouch } from 'cc';\n\nconst { ccclass, property, executeInEditMode, menu, help, inspector } = _decorator;\n@ccclass\n//@menu('i18n:MAIN_MENU.component.ui/ButtonPlus')\n@executeInEditMode\n//@help('i18n:COMPONENT.help_url.button')\n//@inspector('packages://buttonplus/inspector.js')\nexport class ButtonPlus extends Button {\n\n    @property({ tooltip: \"点击放大默认倍数\" })\n    clickDefZoomScale = true;\n    @property({ tooltip: \"音效路径\", type: '', multiline: true, formerlySerializedAs: '_N$string' })\n    audioUrl = '';\n    @property({ tooltip: \"屏蔽连续点击\" })\n    openContinuous = true;\n    @property({ tooltip: \"屏蔽时间, 单位:秒\" })\n    continuousTime = 0.5;\n\n\n    // false表示可以点击\n    continuous: boolean = false;\n    // 定时器\n    _continuousTimer:NodeJS.Timeout|null = null;\n\n    private _pressed = false;\n\n\n    // 长按触发\n    @property({ tooltip: \"是否开启长按事件\" })\n    openLongPress = false;\n    // 触发时间\n    @property({ tooltip: \"长按时间\" })\n    longPressTime = 1;\n    longPressFlag = false;\n\n    private longPressTimer:NodeJS.Timeout|null = null;\n\n    onEnable() {\n        this.continuous = false;\n        super.onEnable();\n        // if (!CC_EDITOR) {\n        // }\n        if (this.clickDefZoomScale) {\n            this.transition = 3\n            this.zoomScale = 1.05\n            this.duration = 0.1\n        }\n    }\n    onDisable() {\n        if (this._continuousTimer) {\n            clearTimeout(this._continuousTimer);\n            this._continuousTimer = null;\n        }\n        if (this.longPressTimer) {\n            clearTimeout(this.longPressTimer);\n            this.longPressTimer = null;\n        }\n        super.onDisable();\n    }\n\n    /** 重写 */\n    protected _onTouchBegan(event: EventTouch) {\n        if (!this.interactable || !this.enabledInHierarchy) return;\n\n        if (this.openLongPress && !this.longPressFlag) {    // 开启长按\n            if (this.longPressTimer) clearTimeout(this.longPressTimer);\n            this.longPressTimer = setTimeout(function (this:ButtonPlus) {\n                // 还在触摸中 触发事件\n                if (this._pressed) {\n                    this.node.emit('longclickStart', this);\n                    this.longPressFlag = true;\n                }\n            }.bind(this), this.longPressTime * 1000);\n        }\n\n        this._pressed = true;\n        this._updateState();\n        //event.stopPropagation();\n        event.propagationStopped = true\n    }\n    protected _onTouchEnded(event: EventTouch) {\n        if (!this.interactable || !this.enabledInHierarchy) return;\n        if (this._pressed && this.longPressFlag) {\n            this.node.emit('longclickEnd', this);\n            this.longPressFlag = false;\n        } else if (this._pressed && !this.continuous) {\n            this.continuous = this.openContinuous ? true : false;\n            EventHandler.emitEvents(this.clickEvents, event);\n            this.node.emit('click', event);\n            //SoundMgr.inst.playEffect(this.audioUrl)\n            if (this.openContinuous) {\n                this._continuousTimer = setTimeout(function (this:ButtonPlus) {\n                    this.continuous = false;\n                }.bind(this), this.continuousTime * 1000);\n            }\n        }\n        this._pressed = false;\n        this._updateState();\n        event.propagationStopped = true\n        //event.stopPropagation();\n    }\n    protected _onTouchCancel() {\n        if (!this.interactable || !this.enabledInHierarchy) return;\n        if (this._pressed && this.longPressFlag) {\n            this.node.emit('longclickEnd', this);\n            this.longPressFlag = false;\n        }\n        this._pressed = false;\n        this._updateState();\n    }\n    /** 添加点击事件 */\n    addClick(callback: Function, target: Object) {\n        this.node.off('click');\n        this.node.on('click', callback, target);\n    }\n    /** 添加一个长按事件 */\n    addLongClick(startFunc: Function, endFunc: Function, target: Object) {\n        this.node.off('longclickStart');\n        this.node.off('longclickEnd');\n        this.node.on('longclickStart', startFunc, target);\n        this.node.on('longclickEnd', endFunc, target);\n    }\n}"]}