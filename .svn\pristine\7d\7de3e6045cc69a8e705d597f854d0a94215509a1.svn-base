import { _decorator, Layout, Node, ScrollView, UITransform } from 'cc';
import { BundleName } from 'db://assets/bundles/Bundle';
import { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/ui/UIMgr';
import { ButtonPlus } from '../common/components/button/ButtonPlus';

const { ccclass, property } = _decorator;

@ccclass("BuildingInfoUI")
export class BuildingInfoUI extends BaseUI {

    @property(ScrollView)
    scroll: ScrollView | null = null;
    @property(Node)
    scrollContent: Node | null = null;
    @property(Layout)
    scrollLayout: Layout | null = null;
    @property(ButtonPlus)
    closeBtn: ButtonPlus | null = null;

    public static getUrl(): string { return "prefab/ui/BuildingInfoUI"; };
    public static getLayer(): UILayer { return UILayer.PopUp }
    public static getBundleName(): string { return BundleName.HomeStory }
    public static getUIOption(): UIOpt { return { isClickBgCloseUI: true } }
    protected onLoad(): void {
        let len = this.scrollLayout!.node.children.length;
        if (len > 0) {
            let wid = this.scrollLayout!.node!.children[0]!.getComponent(UITransform)!.width;
            let sx = this.scrollLayout!.spacingX;
            let newWid = (len - 1) * sx + wid * len;
            let hei = this.scrollContent!.getComponent(UITransform)!.height;
            this.scrollContent!.getComponent(UITransform)!.setContentSize(newWid, hei);
        }
        this.closeBtn!.addClick(this.closeUI, this);
    }
    async closeUI() {
        UIMgr.closeUI(BuildingInfoUI);
    }

    protected onDestroy(): void {

    }

    async onShow(...args: any[]): Promise<void> {

    }

    protected update(dt: number): void {

    }

    async onHide(...args: any[]): Promise<void> { }

    async onClose(...args: any[]): Promise<void> { }

}
