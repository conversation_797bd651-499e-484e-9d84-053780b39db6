{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagGrid.ts"], "names": ["_decorator", "Component", "Node", "UITransform", "DataMgr", "EventMgr", "MyApp", "logDebug", "UIMgr", "PlaneUIEvent", "List", "PlaneEquipInfoUI", "OpenEquipInfoUISource", "TabStatus", "BagItem", "ccclass", "property", "BagGrid", "_sortedItems", "_sortedEquips", "_lineGridNum", "_separatorRow", "_tabStatus", "None", "onLoad", "separator", "removeFromParent", "mergeSelectMaskBg", "active", "on", "SortTypeChange", "onSortTypeChange", "BagItemClick", "onBagItemClick", "UpdateMergeEquipStatus", "onUpdateMergeEquipStatus", "onDestroy", "targetOff", "bagList", "updateAll", "item", "Bag", "openUI", "<PERSON><PERSON>", "equip", "eqCombine", "isFull", "isCanCombine", "tabStatus", "items", "_customSize", "_resizeContent", "listNum", "filter", "v", "lubanTables", "TbItem", "get", "item_id", "TbEquip", "Math", "ceil", "length", "itemRowNum", "numItems", "scrollTo", "onListRenderInBagStatus", "listItem", "row", "name", "normalSize", "tmpNode", "getComponent", "contentSize", "children", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "setContentSize", "width", "height", "bagItems", "getComponentsInChildren", "index", "dataIndex", "node", "onBagTabStatusRender", "onListRenderInCombineStatus", "onCombineTabStatusRender", "onList<PERSON>ender"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,W,OAAAA,W;;AAC7BC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Y,iBAAAA,Y;;AACFC,MAAAA,I;;AACEC,MAAAA,gB,iBAAAA,gB;;AACAC,MAAAA,qB,kBAAAA,qB;AAAuBC,MAAAA,S,kBAAAA,S;;AACvBC,MAAAA,O,kBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;yBAGjBiB,O,WADZF,OAAO,CAAC,SAAD,C,UAEHC,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACd,IAAD,C,2BANb,MACae,OADb,SAC6BhB,SAD7B,CACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAQ3BiB,YAR2B,GAQU,EARV;AAAA,eAS3BC,aAT2B,GASW,EATX;AAAA,eAU3BC,YAV2B,GAUJ,CAVI;AAAA,eAW3BC,aAX2B,GAWH,CAXG;AAAA,eAY3BC,UAZ2B,GAYH;AAAA;AAAA,sCAAUC,IAZP;AAAA;;AAcnCC,QAAAA,MAAM,GAAG;AACL,eAAKC,SAAL,CAAgBC,gBAAhB;AACA,eAAKC,iBAAL,CAAwBC,MAAxB,GAAiC,KAAjC;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,cAAzB,EAAyC,KAAKC,gBAA9C,EAAgE,IAAhE;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,YAAzB,EAAuC,KAAKC,cAA5C,EAA4D,IAA5D;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,4CAAaK,sBAAzB,EAAiD,KAAKC,wBAAtD,EAAgF,IAAhF;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAEOF,QAAAA,wBAAwB,GAAG;AAC/B,eAAKG,OAAL,CAAcC,SAAd;AACH;AAED;;;AACQN,QAAAA,cAAc,CAACO,IAAD,EAA2B;AAC7C,kBAAQ,KAAKlB,UAAb;AACI,iBAAK;AAAA;AAAA,wCAAUmB,GAAf;AACI,mBAAKd,iBAAL,CAAwBC,MAAxB,GAAiC,KAAjC;AACA;AAAA;AAAA,wCAAS,SAAT,2BAA2CY,IAA3C;AACA;AAAA;AAAA,kCAAME,MAAN;AAAA;AAAA,wDAA+BF,IAA/B,EAAqC;AAAA;AAAA,kEAAsBvB,OAA3D;AACA;;AACJ,iBAAK;AAAA;AAAA,wCAAU0B,KAAf;AACI,kBAAI;AAAA;AAAA,sCAAQC,KAAR,CAAcC,SAAd,CAAwBC,MAAxB,MACA,CAAC;AAAA;AAAA,sCAAQF,KAAR,CAAcC,SAAd,CAAwBE,YAAxB,CAAqCP,IAArC,CADL,EACiD;AAC7C;AACH;;AACD,mBAAKL,wBAAL;AACA;AAZR;AAcH;;AAEOJ,QAAAA,gBAAgB,CAACiB,SAAD,EAAuBC,KAAvB,EAAoD;AACxE,eAAK3B,UAAL,GAAkB0B,SAAlB;AACA,eAAKrB,iBAAL,CAAwBC,MAAxB,GAAiC,KAAjC;AACA,eAAKH,SAAL,CAAgBG,MAAhB,GAAyB,KAAzB;AACA,eAAKH,SAAL,CAAgBC,gBAAhB;AACA,eAAKY,OAAL,CAAcY,WAAd,GAA4B,EAA5B;;AACA,eAAKZ,OAAL,CAAca,cAAd;;AACA,cAAIC,OAAO,GAAG,CAAd;;AACA,kBAAQJ,SAAR;AACI,iBAAK;AAAA;AAAA,wCAAUP,GAAf;AACI,mBAAKvB,YAAL,GAAoB+B,KAAK,CAACI,MAAN,CAAaC,CAAC,IAAI;AAAA;AAAA,kCAAMC,WAAN,CAAkBC,MAAlB,CAAyBC,GAAzB,CAA6BH,CAAC,CAACI,OAA/B,KAA4C,IAA9D,CAApB;AACA,mBAAKvC,aAAL,GAAqB8B,KAAK,CAACI,MAAN,CAAaC,CAAC,IAAI;AAAA;AAAA,kCAAMC,WAAN,CAAkBI,OAAlB,CAA0BF,GAA1B,CAA8BH,CAAC,CAACI,OAAhC,KAA6C,IAA/D,CAArB;AACA,mBAAKrC,aAAL,GAAqBuC,IAAI,CAACC,IAAL,CAAU,KAAK1C,aAAL,CAAmB2C,MAAnB,GAA4B,KAAK1C,YAA3C,CAArB;AACA,kBAAM2C,UAAU,GAAGH,IAAI,CAACC,IAAL,CAAU,KAAK3C,YAAL,CAAkB4C,MAAlB,GAA2B,KAAK1C,YAA1C,CAAnB;AACAgC,cAAAA,OAAO,GAAG,KAAK/B,aAAL,GAAqB0C,UAArB,GAAkC,CAA5C;AACA;;AACJ,iBAAK;AAAA;AAAA,wCAAUpB,KAAf;AACI,mBAAKtB,aAAL,GAAqB,CAAC,CAAtB;AACA,mBAAKH,YAAL,GAAoB+B,KAApB;AACAG,cAAAA,OAAO,GAAGQ,IAAI,CAACC,IAAL,CAAU,KAAK3C,YAAL,CAAkB4C,MAAlB,GAA2B,KAAK1C,YAA1C,CAAV;AACA;AAZR;;AAcA;AAAA;AAAA,oCAAS,SAAT,iCAAiD,KAAKkB,OAAL,CAAc0B,QAA/D,4BAA8F,KAAK3C,aAAnG;AACA,eAAKiB,OAAL,CAAc0B,QAAd,GAAyBZ,OAAzB;AACA,eAAKd,OAAL,CAAc2B,QAAd,CAAuB,CAAvB,EAA0B,CAA1B;AACH;;AAEDC,QAAAA,uBAAuB,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AACjDD,UAAAA,QAAQ,CAACE,IAAT,QAAmBD,GAAnB;;AACA,cAAIA,GAAG,IAAI,KAAK/C,aAAhB,EAA+B;AAC3B,gBAAMiD,UAAU,GAAG,KAAKhC,OAAL,CAAciC,OAAd,CAAuBC,YAAvB,CAAoCrE,WAApC,EAAkDsE,WAArE;AACAN,YAAAA,QAAQ,CAACO,QAAT,CAAkBC,OAAlB,CAA0BrB,CAAC,IAAIA,CAAC,CAAC1B,MAAF,GAAW,KAA1C;AACA,iBAAKH,SAAL,CAAgBC,gBAAhB;AACA,iBAAKD,SAAL,CAAgBG,MAAhB,GAAyB,IAAzB;AACAuC,YAAAA,QAAQ,CAACS,QAAT,CAAkB,KAAKnD,SAAvB;AACA0C,YAAAA,QAAQ,CAACK,YAAT,CAAsBrE,WAAtB,EAAoC0E,cAApC,CAAmDP,UAAU,CAACQ,KAA9D,EAAqER,UAAU,CAACS,MAAX,GAAoB,CAAzF;AACA;AACH;;AAED,cAAIZ,QAAQ,CAACO,QAAT,CAAkBZ,MAAlB,GAA2B,CAA/B,EAAkC;AAC9B,iBAAKrC,SAAL,CAAgBC,gBAAhB;AACA,iBAAKD,SAAL,CAAgBG,MAAhB,GAAyB,KAAzB;AACH;;AAED,cAAMoD,QAAQ,GAAGb,QAAQ,CAACc,uBAAT;AAAA;AAAA,iCAAjB;;AACA,cAAIb,GAAG,GAAG,KAAK/C,aAAf,EAA8B;AAC1B,iBAAK,IAAI6D,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,QAAQ,CAAClB,MAArC,EAA6CoB,KAAK,EAAlD,EAAsD;AAClD,kBAAM1C,IAAI,GAAGwC,QAAQ,CAACE,KAAD,CAArB;AACA,kBAAMC,SAAS,GAAGf,GAAG,GAAG,KAAKhD,YAAX,GAA0B8D,KAA5C;;AACA,kBAAIC,SAAS,IAAI,KAAKhE,aAAL,CAAmB2C,MAApC,EAA4C;AACxCtB,gBAAAA,IAAI,CAAC4C,IAAL,CAAUxD,MAAV,GAAmB,KAAnB;AACA;AAAA;AAAA,0CAAS,SAAT,kCAAkDsD,KAAlD,mBAAqEC,SAArE,aAAsFf,GAAtF,mBAAuG,KAAKjD,aAAL,CAAmB2C,MAA1H;AACA;AACH;;AACDtB,cAAAA,IAAI,CAAC4C,IAAL,CAAUxD,MAAV,GAAmB,IAAnB;AACAY,cAAAA,IAAI,CAAC6C,oBAAL,CAA0B,KAAKlE,aAAL,CAAmBgE,SAAnB,CAA1B;AACH;AACJ,WAZD,MAYO;AACH,iBAAK,IAAID,MAAK,GAAG,CAAjB,EAAoBA,MAAK,GAAGF,QAAQ,CAAClB,MAArC,EAA6CoB,MAAK,EAAlD,EAAsD;AAClD,kBAAM1C,KAAI,GAAGwC,QAAQ,CAACE,MAAD,CAArB;;AACA,kBAAMC,UAAS,GAAG,CAACf,GAAG,GAAG,KAAK/C,aAAX,GAA2B,CAA5B,IAAiC,KAAKD,YAAtC,GAAqD8D,MAAvE;;AACA,kBAAIC,UAAS,IAAI,KAAKjE,YAAL,CAAkB4C,MAAnC,EAA2C;AACvCtB,gBAAAA,KAAI,CAAC4C,IAAL,CAAUxD,MAAV,GAAmB,KAAnB;AACA;AACH;;AACDY,cAAAA,KAAI,CAAC4C,IAAL,CAAUxD,MAAV,GAAmB,IAAnB;;AACAY,cAAAA,KAAI,CAAC6C,oBAAL,CAA0B,KAAKnE,YAAL,CAAkBiE,UAAlB,CAA1B;AACH;AACJ;AACJ;;AAEOG,QAAAA,2BAA2B,CAACnB,QAAD,EAAiBC,GAAjB,EAA8B;AAC7D,cAAMY,QAAQ,GAAGb,QAAQ,CAACc,uBAAT;AAAA;AAAA,iCAAjB;;AACA,eAAK,IAAIC,KAAK,GAAG,CAAjB,EAAoBA,KAAK,GAAGF,QAAQ,CAAClB,MAArC,EAA6CoB,KAAK,EAAlD,EAAsD;AAClD,gBAAM1C,IAAI,GAAGwC,QAAQ,CAACE,KAAD,CAArB;AACA,gBAAMC,SAAS,GAAGf,GAAG,GAAG,KAAKhD,YAAX,GAA0B8D,KAA5C;;AACA,gBAAIC,SAAS,IAAI,KAAKjE,YAAL,CAAkB4C,MAAnC,EAA2C;AACvCtB,cAAAA,IAAI,CAAC4C,IAAL,CAAUxD,MAAV,GAAmB,KAAnB;AACA;AACH;;AACDY,YAAAA,IAAI,CAAC4C,IAAL,CAAUxD,MAAV,GAAmB,IAAnB;AACAY,YAAAA,IAAI,CAAC+C,wBAAL,CAA8B,KAAKrE,YAAL,CAAkBiE,SAAlB,CAA9B;AACH;AACJ;;AAEDK,QAAAA,YAAY,CAACrB,QAAD,EAAiBC,GAAjB,EAA8B;AACtCD,UAAAA,QAAQ,CAACE,IAAT,gBAA2BD,GAA3B;;AACA,cAAI,KAAK9C,UAAL,IAAmB;AAAA;AAAA,sCAAUmB,GAAjC,EAAsC;AAClC,iBAAKyB,uBAAL,CAA6BC,QAA7B,EAAuCC,GAAvC;AACH,WAFD,MAEO;AACH,iBAAKkB,2BAAL,CAAiCnB,QAAjC,EAA2CC,GAA3C;AACH;AACJ;;AA5IkC,O;;;;;iBAEZ,I;;;;;;;iBAEE,I;;;;;;;iBAEQ,I", "sourcesContent": ["import { _decorator, Component, Node, UITransform } from 'cc';\nimport { DataMgr } from 'db://assets/bundles/common/script/data/DataManager';\nimport { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { MyApp } from 'db://assets/scripts/MyApp';\nimport { logDebug } from 'db://assets/scripts/Utils/Logger';\nimport { UIMgr } from '../../../../../../../scripts/ui/UIMgr';\nimport { PlaneUIEvent } from '../../../../event/PlaneUIEvent';\nimport List from '../../../common/components/list/List';\nimport { PlaneEquipInfoUI } from '../../PlaneEquipInfoUI';\nimport { OpenEquipInfoUISource, TabStatus } from '../../PlaneTypes';\nimport { BagItem } from './BagItem';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('BagGrid')\nexport class BagGrid extends Component {\n    @property(List)\n    bagList: List | null = null;\n    @property(Node)\n    separator: Node | null = null;\n    @property(Node)\n    mergeSelectMaskBg: Node | null = null;\n\n    private _sortedItems: csproto.cs.ICSItem[] = [];\n    private _sortedEquips: csproto.cs.ICSItem[] = [];\n    private _lineGridNum: number = 5;\n    private _separatorRow: number = 0;\n    private _tabStatus: TabStatus = TabStatus.None;\n\n    onLoad() {\n        this.separator!.removeFromParent();\n        this.mergeSelectMaskBg!.active = false;\n        EventMgr.on(PlaneUIEvent.SortTypeChange, this.onSortTypeChange, this);\n        EventMgr.on(PlaneUIEvent.BagItemClick, this.onBagItemClick, this)\n        EventMgr.on(PlaneUIEvent.UpdateMergeEquipStatus, this.onUpdateMergeEquipStatus, this)\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this)\n    }\n\n    private onUpdateMergeEquipStatus() {\n        this.bagList!.updateAll();\n    }\n\n    /*暂时只有装备点击*/\n    private onBagItemClick(item: csproto.cs.ICSItem) {\n        switch (this._tabStatus) {\n            case TabStatus.Bag:\n                this.mergeSelectMaskBg!.active = false;\n                logDebug(\"PlaneUI\", `onBagItemClick item:${item}`)\n                UIMgr.openUI(PlaneEquipInfoUI, item, OpenEquipInfoUISource.BagGrid)\n                break;\n            case TabStatus.Merge:\n                if (DataMgr.equip.eqCombine.isFull() ||\n                    !DataMgr.equip.eqCombine.isCanCombine(item)) {\n                    return\n                }\n                this.onUpdateMergeEquipStatus();\n                break;\n        }\n    }\n\n    private onSortTypeChange(tabStatus: TabStatus, items: csproto.cs.ICSItem[]) {\n        this._tabStatus = tabStatus;\n        this.mergeSelectMaskBg!.active = false;\n        this.separator!.active = false\n        this.separator!.removeFromParent();\n        this.bagList!._customSize = {}\n        this.bagList!._resizeContent();\n        let listNum = 0;\n        switch (tabStatus) {\n            case TabStatus.Bag:\n                this._sortedItems = items.filter(v => MyApp.lubanTables.TbItem.get(v.item_id!) != null)\n                this._sortedEquips = items.filter(v => MyApp.lubanTables.TbEquip.get(v.item_id!) != null)\n                this._separatorRow = Math.ceil(this._sortedEquips.length / this._lineGridNum)\n                const itemRowNum = Math.ceil(this._sortedItems.length / this._lineGridNum)\n                listNum = this._separatorRow + itemRowNum + 1;\n                break;\n            case TabStatus.Merge:\n                this._separatorRow = -1\n                this._sortedItems = items\n                listNum = Math.ceil(this._sortedItems.length / this._lineGridNum)\n                break;\n        }\n        logDebug(\"PlaneUI\", `onSortTypeChange list num:${this.bagList!.numItems} maxPlanePartRowNum:${this._separatorRow}`)\n        this.bagList!.numItems = listNum\n        this.bagList!.scrollTo(0, 1)\n    }\n\n    onListRenderInBagStatus(listItem: Node, row: number) {\n        listItem.name = `${row}`\n        if (row == this._separatorRow) {\n            const normalSize = this.bagList!.tmpNode!.getComponent(UITransform)!.contentSize\n            listItem.children.forEach(v => v.active = false)\n            this.separator!.removeFromParent();\n            this.separator!.active = true;\n            listItem.addChild(this.separator!)\n            listItem.getComponent(UITransform)!.setContentSize(normalSize.width, normalSize.height / 2)\n            return\n        }\n\n        if (listItem.children.length > 5) {\n            this.separator!.removeFromParent();\n            this.separator!.active = false;\n        }\n\n        const bagItems = listItem.getComponentsInChildren(BagItem)\n        if (row < this._separatorRow) {\n            for (let index = 0; index < bagItems.length; index++) {\n                const item = bagItems[index];\n                const dataIndex = row * this._lineGridNum + index;\n                if (dataIndex >= this._sortedEquips.length) {\n                    item.node.active = false;\n                    logDebug(\"PlaneUI\", `onListRender bagItem index:${index} dataIndex:${dataIndex} row:${row} sortedLen:${this._sortedEquips.length}`)\n                    continue\n                }\n                item.node.active = true;\n                item.onBagTabStatusRender(this._sortedEquips[dataIndex]);\n            }\n        } else {\n            for (let index = 0; index < bagItems.length; index++) {\n                const item = bagItems[index];\n                const dataIndex = (row - this._separatorRow - 1) * this._lineGridNum + index;\n                if (dataIndex >= this._sortedItems.length) {\n                    item.node.active = false;\n                    continue\n                }\n                item.node.active = true;\n                item.onBagTabStatusRender(this._sortedItems[dataIndex]);\n            }\n        }\n    }\n\n    private onListRenderInCombineStatus(listItem: Node, row: number) {\n        const bagItems = listItem.getComponentsInChildren(BagItem)\n        for (let index = 0; index < bagItems.length; index++) {\n            const item = bagItems[index];\n            const dataIndex = row * this._lineGridNum + index;\n            if (dataIndex >= this._sortedItems.length) {\n                item.node.active = false;\n                continue\n            }\n            item.node.active = true;\n            item.onCombineTabStatusRender(this._sortedItems[dataIndex]);\n        }\n    }\n\n    onListRender(listItem: Node, row: number) {\n        listItem.name = `listItem${row}`\n        if (this._tabStatus == TabStatus.Bag) {\n            this.onListRenderInBagStatus(listItem, row)\n        } else {\n            this.onListRenderInCombineStatus(listItem, row)\n        }\n    }\n}"]}