{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/leveldata/leveldata.ts"], "names": ["LevelDataTerrain", "LevelDataScroll", "LevelDataRandTerrains", "LevelDataElem", "LevelDataWave", "LevelDataEvent", "LevelDataLayer", "LevelDataBackgroundLayer", "LevelData", "Vec2", "newCondition", "newTrigger", "LayerType", "uuid", "position", "scale", "rotation", "weight", "terrains", "elemID", "name", "waveUUID", "planeID", "params", "fromJSON", "json", "wave", "Object", "assign", "conditions", "triggers", "event", "map", "condition", "trigger", "totalTime", "speed", "type", "scrolls", "dynamics", "waves", "events", "terrain", "scroll", "dynamic", "layer", "backgrounds", "background<PERSON>ayer", "floorLayers", "skyLayers", "levelData"], "mappings": ";;;+GAYaA,gB,EAOAC,e,EASAC,qB,EAKAC,a,EAMAC,a,EAaAC,c,EAoBAC,c,EAmCAC,wB,EAaAC,S;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAxHJC,MAAAA,I,OAAAA,I;;AAGAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;AAJkB;;;2BAMfC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;;kCAMCZ,gB,GAAN,MAAMA,gBAAN,CAAuB;AAAA;AAAA,eACnBa,IADmB,GACJ,EADI;AAAA,eAEnBC,QAFmB,GAEF,IAAIL,IAAJ,EAFE;AAAA,eAGnBM,KAHmB,GAGL,IAAIN,IAAJ,EAHK;AAAA,eAInBO,QAJmB,GAIA,CAJA;AAAA;;AAAA,O;;iCAOjBf,e,GAAN,MAAMA,eAAN,SAA8BD,gBAA9B,CAA+C;AAAA;AAAA;AAAA,eAC3CiB,MAD2C,GAC1B,GAD0B;AAAA;;AAAA,O;;uCASzCf,qB,GAAN,MAAMA,qBAAN,SAAoCF,gBAApC,CAAqD;AAAA;AAAA;AAAA,eACjDiB,MADiD,GAChC,GADgC;AAAA,eAEjDC,QAFiD,GAEd,EAFc;AAAA;;AAAA,O;;+BAK/Cf,a,GAAN,MAAMA,aAAN,CAAoB;AAAA;AAAA,eAChBgB,MADgB,GACC,EADD;AAAA,eAEhBL,QAFgB,GAEC,IAAIL,IAAJ,EAFD;AAAA,eAGhBW,IAHgB,GAGD,SAHC;AAAA;;AAAA,O;;+BAMdhB,a,GAAN,MAAMA,aAAN,SAA4BD,aAA5B,CAA0C;AAAA;AAAA;AAAA,eACtCkB,QADsC,GACnB,EADmB;AAAA,eAEtCC,OAFsC,GAEpB,CAFoB;AAAA,eAGtCC,MAHsC,GAGL,EAHK;AAAA;;AAK9B,eAARC,QAAQ,CAACC,IAAD,EAA2B;AACtC,cAAMC,IAAI,GAAG,IAAItB,aAAJ,EAAb;AACA,cAAI,CAACqB,IAAL,EAAW,OAAOC,IAAP;AACXC,UAAAA,MAAM,CAACC,MAAP,CAAcF,IAAd,EAAoBD,IAApB;AACA,iBAAOC,IAAP;AACH;;AAV4C,O;;gCAapCrB,c,GAAN,MAAMA,cAAN,SAA6BF,aAA7B,CAA2C;AAAA;AAAA;AAAA,eACvC0B,UADuC,GACA,EADA;AAAA,eAEvCC,QAFuC,GAEH,EAFG;AAAA;;AAI/B,eAARN,QAAQ,CAACC,IAAD,EAA4B;AAAA;;AACvC,cAAMM,KAAK,GAAG,IAAI1B,cAAJ,EAAd;AACA,cAAI,CAACoB,IAAL,EAAW,OAAOM,KAAP;AAEXJ,UAAAA,MAAM,CAACC,MAAP,CAAcG,KAAd,EAAqBN,IAArB;AACAM,UAAAA,KAAK,CAACF,UAAN,GAAmB,qBAAAJ,IAAI,CAACI,UAAL,sCAAiBG,GAAjB,CAAsBC,SAAD,IAAoB;AACxD,mBAAO;AAAA;AAAA,8CAAaA,SAAb,CAAP;AACH,WAFkB,MAEb,EAFN;AAGAF,UAAAA,KAAK,CAACD,QAAN,GAAiB,mBAAAL,IAAI,CAACK,QAAL,oCAAeE,GAAf,CAAoBE,OAAD,IAAkB;AAClD,mBAAO;AAAA;AAAA,0CAAWA,OAAX,CAAP;AACH,WAFgB,MAEX,EAFN;AAIA,iBAAOH,KAAP;AACH;;AAjB6C,O;;gCAoBrCzB,c,GAAN,MAAMA,cAAN,CAAqB;AAAA;AAAA,eACjB6B,SADiB,GACG,EADH;AAAA,eAEjBC,KAFiB,GAED,GAFC;AAAA,eAGjBC,IAHiB,GAGF,CAHE;AAGC;AAHD,eAIjBnB,QAJiB,GAIc,EAJd;AAAA,eAKjBoB,OALiB,GAKY,EALZ;AAAA,eAMjBC,QANiB,GAMmB,EANnB;AAAA,eAOjBC,KAPiB,GAOQ,EAPR;AAAA,eAQjBC,MARiB,GAQU,EARV;AAAA;;AAUdb,QAAAA,MAAM,CAACH,IAAD,EAAiB;AAAA;;AAC7BE,UAAAA,MAAM,CAACC,MAAP,CAAc,IAAd,EAAoBH,IAApB;AAEA,eAAKP,QAAL,GAAgB,mBAAAO,IAAI,CAACP,QAAL,oCAAec,GAAf,CAAoBU,OAAD,IAC/Bf,MAAM,CAACC,MAAP,CAAc,IAAI5B,gBAAJ,EAAd,EAAsC0C,OAAtC,CADY,MACuC,EADvD;AAEA,eAAKJ,OAAL,GAAe,kBAAAb,IAAI,CAACa,OAAL,mCAAcN,GAAd,CAAmBW,MAAD,IAC7BhB,MAAM,CAACC,MAAP,CAAc,IAAI5B,gBAAJ,EAAd,EAAsC2C,MAAtC,CADW,MACuC,EADtD;AAEA,eAAKJ,QAAL,GAAgB,mBAAAd,IAAI,CAACc,QAAL,oCAAeP,GAAf,CAAoBY,OAAD,IAC/BjB,MAAM,CAACC,MAAP,CAAc,IAAI1B,qBAAJ,EAAd,EAA2C0C,OAA3C,CADY,MAC4C,EAD5D;AAEA,eAAKJ,KAAL,GAAa,gBAAAf,IAAI,CAACe,KAAL,iCAAYR,GAAZ,CAAiBN,IAAD,IACzBtB,aAAa,CAACoB,QAAd,CAAuBE,IAAvB,CADS,MACwB,EADrC;AAEA,eAAKe,MAAL,GAAc,iBAAAhB,IAAI,CAACgB,MAAL,kCAAaT,GAAb,CAAkBD,KAAD,IAC3B1B,cAAc,CAACmB,QAAf,CAAwBO,KAAxB,CADU,MACyB,EADvC;AAEH;;AAEc,eAARP,QAAQ,CAACC,IAAD,EAA4B;AACvC,cAAMoB,KAAK,GAAG,IAAIvC,cAAJ,EAAd;AACA,cAAI,CAACmB,IAAL,EAAW,OAAOoB,KAAP;AAEXA,UAAAA,KAAK,CAACjB,MAAN,CAAaH,IAAb;AAEA,iBAAOoB,KAAP;AACH;;AAhCuB,O;;0CAmCftC,wB,GAAN,MAAMA,wBAAN,SAAuCD,cAAvC,CAAsD;AAAA;AAAA;AAAA,eAClDwC,WADkD,GAC1B,EAD0B;AAAA;;AAG1C,eAARtB,QAAQ,CAACC,IAAD,EAAsC;AACjD,cAAMoB,KAAK,GAAG,IAAItC,wBAAJ,EAAd;AACA,cAAI,CAACkB,IAAL,EAAW,OAAOoB,KAAP;AAEXA,UAAAA,KAAK,CAACjB,MAAN,CAAaH,IAAb;AAEA,iBAAOoB,KAAP;AACH;;AAVwD,O;;2BAahDrC,S,GAAN,MAAMA,SAAN,CAAgB;AAAA;AAAA,eACZY,IADY,GACG,EADH;AAAA,eAEZe,SAFY,GAEQ,EAFR;AAAA,eAGZE,IAHY,GAGG,CAHH;AAAA,eAIZU,eAJY,GAIgC,IAAIxC,wBAAJ,EAJhC;AAAA,eAKZyC,WALY,GAKoB,EALpB;AAAA,eAMZC,SANY,GAMkB,EANlB;AAAA;;AAQJ,eAARzB,QAAQ,CAACC,IAAD,EAAuB;AAAA;;AAClC,cAAMyB,SAAS,GAAG,IAAI1C,SAAJ,EAAlB;AACA,cAAI,CAACiB,IAAL,EAAW,OAAOyB,SAAP;AAEXvB,UAAAA,MAAM,CAACC,MAAP,CAAcsB,SAAd,EAAyBzB,IAAzB;AACAyB,UAAAA,SAAS,CAACH,eAAV,GAA4BxC,wBAAwB,CAACiB,QAAzB,CAAkCC,IAAI,CAACsB,eAAvC,CAA5B;AACAG,UAAAA,SAAS,CAACF,WAAV,GAAwB,sBAAAvB,IAAI,CAACuB,WAAL,uCAAkBhB,GAAlB,CAAuBa,KAAD,IAC1CvC,cAAc,CAACkB,QAAf,CAAwBqB,KAAxB,CADoB,MACe,EADvC;AAEAK,UAAAA,SAAS,CAACD,SAAV,GAAsB,oBAAAxB,IAAI,CAACwB,SAAL,qCAAgBjB,GAAhB,CAAqBa,KAAD,IACtCvC,cAAc,CAACkB,QAAf,CAAwBqB,KAAxB,CADkB,MACiB,EADvC;AAGA,iBAAOK,SAAP;AACH;;AApBkB,O", "sourcesContent": ["import { Vec2 } from \"cc\"; // 注意：这里的分号是必须的，因为下面的代码是压缩过的\r\nimport { LevelDataEventCondtion } from \"./condition/LevelDataEventCondtion\";\r\nimport { LevelDataEventTrigger } from \"./trigger/LevelDataEventTrigger\";\r\nimport { newCondition } from \"./condition/newCondition\";\r\nimport { newTrigger } from \"./trigger/newTrigger\";\r\n\r\nexport enum LayerType {\r\n    Background = 1,\r\n    Random,\r\n    Scroll\r\n}\r\n\r\nexport class LevelDataTerrain {\r\n    public uuid: string = \"\";\r\n    public position: Vec2 = new Vec2();\r\n    public scale: Vec2 = new Vec2();\r\n    public rotation: number = 0;\r\n}\r\n\r\nexport class LevelDataScroll extends LevelDataTerrain {\r\n    public weight: number = 100;\r\n}\r\n\r\nexport interface LevelDataRandTerrain {\r\n    weight: number;\r\n    uuid: string;\r\n}\r\n\r\nexport class LevelDataRandTerrains extends LevelDataTerrain {\r\n    public weight: number = 100;\r\n    public terrains: LevelDataRandTerrain[] = [];\r\n}\r\n\r\nexport class LevelDataElem {\r\n    public elemID: string = \"\";\r\n    public position: Vec2 = new Vec2();\r\n    public name: string = \"default\";\r\n}\r\n\r\nexport class LevelDataWave extends LevelDataElem {\r\n    public waveUUID: string = \"\";\r\n    public planeID: number = 0;\r\n    public params: {[key:string]:number;} = {};\r\n\r\n    static fromJSON(json: any): LevelDataWave {\r\n        const wave = new LevelDataWave();\r\n        if (!json) return wave;\r\n        Object.assign(wave, json);\r\n        return wave;\r\n    }\r\n}\r\n\r\nexport class LevelDataEvent extends LevelDataElem {\r\n    public conditions: LevelDataEventCondtion[] = [];\r\n    public triggers: LevelDataEventTrigger[] = [];\r\n\r\n    static fromJSON(json: any): LevelDataEvent {\r\n        const event = new LevelDataEvent();\r\n        if (!json) return event;\r\n        \r\n        Object.assign(event, json);\r\n        event.conditions = json.conditions?.map((condition: any) => {\r\n            return newCondition(condition);\r\n        }) || [];\r\n        event.triggers = json.triggers?.map((trigger: any) => {\r\n            return newTrigger(trigger);\r\n        }) || [];\r\n        \r\n        return event;\r\n    }\r\n}\r\n\r\nexport class LevelDataLayer {\r\n    public totalTime: number = 60;\r\n    public speed: number = 200;\r\n    public type: number = 0; // 对应LayerType\r\n    public terrains: LevelDataTerrain[] = [];\r\n    public scrolls: LevelDataScroll[] = [];\r\n    public dynamics: LevelDataRandTerrains[] = [];\r\n    public waves: LevelDataWave[] = [];\r\n    public events: LevelDataEvent[] = [];\r\n\r\n    protected assign(json: any):void {\r\n        Object.assign(this, json);\r\n\r\n        this.terrains = json.terrains?.map((terrain: any) => \r\n            Object.assign(new LevelDataTerrain(), terrain)) || [];\r\n        this.scrolls = json.scrolls?.map((scroll: any) => \r\n            Object.assign(new LevelDataTerrain(), scroll)) || [];\r\n        this.dynamics = json.dynamics?.map((dynamic: any) => \r\n            Object.assign(new LevelDataRandTerrains(), dynamic)) || [];\r\n        this.waves = json.waves?.map((wave: any) => \r\n            LevelDataWave.fromJSON(wave)) || [];\r\n        this.events = json.events?.map((event: any) => \r\n            LevelDataEvent.fromJSON(event)) || [];\r\n    }\r\n\r\n    static fromJSON(json: any): LevelDataLayer {\r\n        const layer = new LevelDataLayer();\r\n        if (!json) return layer;\r\n\r\n        layer.assign(json);\r\n        \r\n        return layer;\r\n    }\r\n}\r\n\r\nexport class LevelDataBackgroundLayer extends LevelDataLayer {\r\n    public backgrounds: string[] = [];\r\n    \r\n    static fromJSON(json: any): LevelDataBackgroundLayer {\r\n        const layer = new LevelDataBackgroundLayer();\r\n        if (!json) return layer;\r\n       \r\n        layer.assign(json);\r\n        \r\n        return layer;\r\n    }\r\n}\r\n\r\nexport class LevelData {\r\n    public name: string = \"\";\r\n    public totalTime: number = 59;\r\n    public type: number = 0;\r\n    public backgroundLayer: LevelDataBackgroundLayer = new LevelDataBackgroundLayer();\r\n    public floorLayers: LevelDataLayer[] = [];\r\n    public skyLayers: LevelDataLayer[] = [];\r\n\r\n    static fromJSON(json: any): LevelData {\r\n        const levelData = new LevelData();\r\n        if (!json) return levelData;\r\n        \r\n        Object.assign(levelData, json);\r\n        levelData.backgroundLayer = LevelDataBackgroundLayer.fromJSON(json.backgroundLayer);\r\n        levelData.floorLayers = json.floorLayers?.map((layer: any) =>\r\n            LevelDataLayer.fromJSON(layer)) || [];\r\n        levelData.skyLayers = json.skyLayers?.map((layer: any) => \r\n            LevelDataLayer.fromJSON(layer)) || [];\r\n        \r\n        return levelData;\r\n    }\r\n}\r\n"]}