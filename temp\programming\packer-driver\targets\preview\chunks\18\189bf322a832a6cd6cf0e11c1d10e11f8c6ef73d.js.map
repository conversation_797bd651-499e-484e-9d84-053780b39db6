{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/PlaneBase.ts"], "names": ["_decorator", "Label", "Sprite", "tween", "Tween", "Entity", "<PERSON>llComp", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "res", "AttributeData", "AttributeConst", "ccclass", "property", "PlaneBase", "enemy", "isDead", "type", "maxHp", "curHp", "attack", "collide<PERSON>omp", "_skillComp", "_buffComp", "_attributeData", "init", "addComp", "skillComp", "buff<PERSON><PERSON>p", "attribute", "colliderEnabled", "value", "isEnable", "CastSkill", "skillID", "Cast", "addHp", "heal", "Math", "min", "updateHpUI", "hurt", "damage", "cutHp", "playHurtAnim", "to<PERSON><PERSON>", "newHp", "max", "hpBar", "fill<PERSON><PERSON><PERSON>", "hpAniSprite", "duration", "abs", "stopAllByTarget", "to", "call", "start", "hpfont", "string", "toFixed", "ApplyBuffEffect", "buff", "effectData", "EffectType", "Kill", "Hurt", "param", "length", "AttrMaxHPPer", "ApplyBuffAttributeEffect", "MaxHP", "AttrMaxHPAdd", "AttrHPRecoveryPer", "HPRecovery", "AttrHPRecoveryAdd", "AttrHPRecoveryMaxHPPerAdd", "HPRecoveryRate", "AttrAttackPer", "Attack", "AttrAttackAdd", "AttrAttackBossPer", "AttackBoss", "AttrAttackNormalPer", "AttackNormal", "AttrFortunatePer", "Fortunate", "AttrFortunateAdd", "AttrMissAdd", "MissRate", "AttrBulletHurtResistancePer", "BulletHurtResistance", "AttrBulletHurtResistanceAdd", "AttrCollisionHurtResistancePer", "CollisionHurtResistance", "AttrCollisionHurtResistanceAdd", "AttrFinalScoreAdd", "FinalScoreRate", "AttrKillScoreAdd", "KillScoreRate", "AttrEnergyRecoveryPerAdd", "EnergyRecoveryRate", "AttrEnergyRecoveryAdd", "EnergyRecovery", "AttrPickRadiusPer", "PickRadius", "AttrPickRadius", "AttrBombMax", "BombMax", "AttrBombHurtAdd", "BombHurt", "AttrBombHurtPer", "key", "isPer", "addModify", "id", "RemoveBuffEffect", "removeModify"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,K,OAAAA,K;;AAGpCC,MAAAA,M;;AACAC,MAAAA,S;;AACAC,MAAAA,Q;;AACWC,MAAAA,G,iBAAAA,G;;AACTC,MAAAA,a,iBAAAA,a;;AACAC,MAAAA,c,iBAAAA,c;;;;;;;;;OAPH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;yBAcTa,S,WADpBF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACV,MAAD,C,UAERU,QAAQ,CAACV,MAAD,C,UAERU,QAAQ,CAACX,KAAD,C,2BAPb,MACqBY,SADrB;AAAA;AAAA,4BAC8C;AAAA;AAAA;;AAAA;;AAGb;AAHa;;AAKP;AALO;;AAOb;AAPa,eAS1CC,KAT0C,GASlC,IATkC;AAS5B;AAT4B,eAU1CC,MAV0C,GAUjC,KAViC;AAU1B;AAV0B,eAW1CC,IAX0C,GAWnC,CAXmC;AAWhC;AAXgC,eAa1CC,KAb0C,GAa1B,CAb0B;AAAA,eAc1CC,KAd0C,GAc1B,CAd0B;AAAA,eAe1CC,MAf0C,GAezB,CAfyB;AAAA,eAgB1CC,WAhB0C,GAgB8B,IAhB9B;AAgBoC;AAhBpC,eAkBlCC,UAlBkC,GAkBH,IAlBG;AAAA,eAmBlCC,SAnBkC,GAmBL,IAnBK;AAqB1C;AArB0C,eAsBlCC,cAtBkC,GAsBF;AAAA;AAAA,+CAtBE;AAAA;;AAyB1CC,QAAAA,IAAI,GAAG;AACH,eAAKH,UAAL,GAAkB;AAAA;AAAA,uCAAlB;AACA,eAAKI,OAAL,CAAa,OAAb,EAAsB,KAAKJ,UAA3B;AACA,eAAKC,SAAL,GAAiB;AAAA;AAAA,qCAAjB;AACA,eAAKG,OAAL,CAAa,MAAb,EAAqB,KAAKH,SAA1B;AACA,gBAAME,IAAN;AACH;;AAEY,YAATE,SAAS,GAAG;AACZ,iBAAO,KAAKL,UAAZ;AACH;;AAEW,YAARM,QAAQ,GAAG;AACX,iBAAO,KAAKL,SAAZ;AACH;;AAEY,YAATM,SAAS,GAAkB;AAC3B,iBAAO,KAAKL,cAAZ;AACH;;AAEkB,YAAfM,eAAe,CAACC,KAAD,EAAiB;AAChC,cAAI,KAAKV,WAAT,EAAsB;AAClB,iBAAKA,WAAL,CAAiBW,QAAjB,GAA4BD,KAA5B;AACH;AACJ;;AACkB,YAAfD,eAAe,GAAY;AAC3B,iBAAO,KAAKT,WAAL,GAAmB,KAAKA,WAAL,CAAiBW,QAApC,GAA+C,KAAtD;AACH;;AAEDC,QAAAA,SAAS,CAACC,OAAD,EAAkB;AACvB,eAAKP,SAAL,CAAeQ,IAAf,CAAoB,IAApB,EAA0BD,OAA1B;AACH;;AAEDE,QAAAA,KAAK,CAACC,IAAD,EAAe;AAChB,eAAKlB,KAAL,GAAamB,IAAI,CAACC,GAAL,CACT,KAAKrB,KADI,EAET,KAAKC,KAAL,GAAakB,IAFJ,CAAb;AAIA,eAAKG,UAAL;AAAkB;AACrB;;AAEDC,QAAAA,IAAI,CAACC,MAAD,EAAiB;AACjB,cAAI,KAAK1B,MAAT,EAAiB;AACb;AACH;;AACD,eAAK2B,KAAL,CAAWD,MAAX;AACA,eAAKE,YAAL;;AACA,cAAI,KAAKzB,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAK0B,KAAL;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIF,QAAAA,KAAK,CAACD,MAAD,EAAiB;AAClB,cAAMI,KAAK,GAAG,KAAK3B,KAAL,GAAauB,MAA3B;AACA,eAAKvB,KAAL,GAAamB,IAAI,CAACS,GAAL,CAAS,CAAT,EAAYD,KAAZ,CAAb;AAEA,eAAKN,UAAL;AACH;;AAEDK,QAAAA,KAAK,GAAY;AACb,cAAI,KAAK7B,MAAT,EAAiB;AACb,mBAAO,KAAP;AACH;;AACD,eAAKA,MAAL,GAAc,IAAd;AACA,eAAKc,eAAL,GAAuB,KAAvB;AACA,iBAAO,IAAP;AACH;AACD;AACJ;AACA;;;AACIU,QAAAA,UAAU,GAAG;AACT,cAAI,KAAKQ,KAAT,EAAgB;AACZ;AACA,iBAAKA,KAAL,CAAWC,SAAX,GAAuB,KAAK9B,KAAL,GAAa,KAAKD,KAAzC;;AAEA,gBAAI,KAAKgC,WAAT,EAAsB;AAClB;AACA,kBAAMC,QAAQ,GAAGb,IAAI,CAACc,GAAL,CAAS,KAAKF,WAAL,CAAiBD,SAAjB,GAA6B,KAAKD,KAAL,CAAWC,SAAjD,CAAjB;AAEA5C,cAAAA,KAAK,CAACgD,eAAN,CAAsB,KAAKH,WAA3B,EAJkB,CAKlB;;AACA9C,cAAAA,KAAK,CAAC,KAAK8C,WAAN,CAAL,CACKI,EADL,CACQH,QADR,EACkB;AAAEF,gBAAAA,SAAS,EAAE,KAAKD,KAAL,CAAWC;AAAxB,eADlB,EAEKM,IAFL,CAEU,MAAM,CAEX,CAJL,EAKKC,KALL;AAMH;AACJ,WAlBQ,CAoBT;;;AACA,eAAKC,MAAL,KAAgB,KAAKA,MAAL,CAAaC,MAAb,GAAsB,KAAKvC,KAAL,CAAWwC,OAAX,CAAmB,CAAnB,CAAtC;AACH;;AAEDf,QAAAA,YAAY,GAAG,CACX;AACH;;AAEDgB,QAAAA,eAAe,CAACC,IAAD,EAAoBC,UAApB,EAAqD;AAChE,kBAAQA,UAAU,CAAC7C,IAAnB;AACI,iBAAK;AAAA;AAAA,4BAAI8C,UAAJ,CAAeC,IAApB;AACI,mBAAKnB,KAAL;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIkB,UAAJ,CAAeE,IAApB;AACI,kBAAIH,UAAU,CAACI,KAAX,CAAiBC,MAAjB,IAA2B,CAA/B,EAAkC;AAC9B,qBAAK1B,IAAL,CAAUqB,UAAU,CAACI,KAAX,CAAiB,CAAjB,CAAV;AACH;;AACD;;AACJ,iBAAK;AAAA;AAAA,4BAAIH,UAAJ,CAAeK,YAApB;AACI,mBAAKC,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAeS,KAAnD,EAA0DR,UAA1D,EAAsE,IAAtE;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAeQ,YAApB;AACI,mBAAKF,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAeS,KAAnD,EAA0DR,UAA1D,EAAsE,KAAtE;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAeS,iBAApB;AACI,mBAAKH,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAeY,UAAnD,EAA+DX,UAA/D,EAA2E,IAA3E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAeW,iBAApB;AACI,mBAAKL,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAeY,UAAnD,EAA+DX,UAA/D,EAA2E,KAA3E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAeY,yBAApB;AACI,mBAAKN,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAee,cAAnD,EAAmEd,UAAnE,EAA+E,KAA/E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAec,aAApB;AACI,mBAAKR,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAeiB,MAAnD,EAA2DhB,UAA3D,EAAuE,IAAvE;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAegB,aAApB;AACI,mBAAKV,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAeiB,MAAnD,EAA2DhB,UAA3D,EAAuE,KAAvE;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAeiB,iBAApB;AACI,mBAAKX,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAeoB,UAAnD,EAA+DnB,UAA/D,EAA2E,IAA3E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAemB,mBAApB;AACI,mBAAKb,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAesB,YAAnD,EAAiErB,UAAjE,EAA6E,IAA7E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAeqB,gBAApB;AACI,mBAAKf,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAewB,SAAnD,EAA8DvB,UAA9D,EAA0E,IAA1E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAeuB,gBAApB;AACI,mBAAKjB,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAewB,SAAnD,EAA8DvB,UAA9D,EAA0E,KAA1E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAewB,WAApB;AACI,mBAAKlB,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAe2B,QAAnD,EAA6D1B,UAA7D,EAAyE,KAAzE;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAe0B,2BAApB;AACI,mBAAKpB,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAe6B,oBAAnD,EAAyE5B,UAAzE,EAAqF,IAArF;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAe4B,2BAApB;AACI,mBAAKtB,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAe6B,oBAAnD,EAAyE5B,UAAzE,EAAqF,KAArF;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAe6B,8BAApB;AACI,mBAAKvB,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAegC,uBAAnD,EAA4E/B,UAA5E,EAAwF,IAAxF;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAe+B,8BAApB;AACI,mBAAKzB,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAegC,uBAAnD,EAA4E/B,UAA5E,EAAwF,KAAxF;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAegC,iBAApB;AACI,mBAAK1B,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAemC,cAAnD,EAAmElC,UAAnE,EAA+E,IAA/E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAekC,gBAApB;AACI,mBAAK5B,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAeqC,aAAnD,EAAkEpC,UAAlE,EAA8E,IAA9E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAeoC,wBAApB;AACI,mBAAK9B,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAeuC,kBAAnD,EAAuEtC,UAAvE,EAAmF,KAAnF;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAesC,qBAApB;AACI,mBAAKhC,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAeyC,cAAnD,EAAmExC,UAAnE,EAA+E,KAA/E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAewC,iBAApB;AACI,mBAAKlC,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAe2C,UAAnD,EAA+D1C,UAA/D,EAA2E,IAA3E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAe0C,cAApB;AACI,mBAAKpC,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAe2C,UAAnD,EAA+D1C,UAA/D,EAA2E,KAA3E;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAe2C,WAApB;AACI,mBAAKrC,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAe8C,OAAnD,EAA4D7C,UAA5D,EAAwE,KAAxE;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAe6C,eAApB;AACI,mBAAKvC,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAegD,QAAnD,EAA6D/C,UAA7D,EAAyE,KAAzE;AACA;;AACJ,iBAAK;AAAA;AAAA,4BAAIC,UAAJ,CAAe+C,eAApB;AACI,mBAAKzC,wBAAL,CAA8BR,IAA9B,EAAoC;AAAA;AAAA,oDAAegD,QAAnD,EAA6D/C,UAA7D,EAAyE,IAAzE;AACA;;AACJ;AACI;AArFR;AAuFH;;AACOO,QAAAA,wBAAwB,CAACR,IAAD,EAAoBkD,GAApB,EAAiCjD,UAAjC,EAAkEkD,KAAlE,EAAkF;AAC9G,cAAI,CAACnD,IAAL,EAAW;AACP;AACH;;AACD,cAAIC,UAAU,CAACI,KAAX,CAAiBC,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B;AACH;;AACD,eAAKtC,SAAL,CAAeoF,SAAf,CAAyBpD,IAAI,CAACqD,EAA9B,EAAkCH,GAAlC,EAAuCjD,UAAU,CAACI,KAAX,CAAiB,CAAjB,CAAvC,EAA4D8C,KAA5D;AACH;;AACDG,QAAAA,gBAAgB,CAACtD,IAAD,EAAaC,UAAb,EAA8C;AAC1D,eAAKjC,SAAL,CAAeuF,YAAf,CAA4BvD,IAAI,CAACqD,EAAjC;AACH;;AAnOyC,O;;;;;iBAGnB,I;;;;;;;iBAEM,I;;;;;;;iBAEN,I", "sourcesContent": ["import { _decorator, Label, Sprite, tween, Tween } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\nimport Entity from 'db://assets/scripts/Game/ui/base/Entity';\r\nimport SkillComp from './skill/SkillComp';\r\nimport BuffComp, { Buff } from './skill/BuffComp';\r\nimport { builtin, res } from '../../../AutoGen/Luban/schema';\r\nimport { AttributeData } from 'db://assets/bundles/common/script/data/base/AttributeData';\r\nimport { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';\r\nimport FCircleCollider from '../../collider-system/FCircleCollider';\r\nimport FBoxCollider from '../../collider-system/FBoxCollider';\r\nimport FPolygonCollider from '../../collider-system/FPolygonCollider';\r\n\r\n\r\n@ccclass('PlaneBase')\r\nexport default class PlaneBase extends Entity {\r\n\r\n    @property(Sprite)\r\n    hpBar: Sprite | null = null; // 血条\r\n    @property(Sprite)\r\n    hpAniSprite: Sprite | null = null; // 血条动画条\r\n    @property(Label)\r\n    hpfont: Label | null = null; // 血条文本\r\n\r\n    enemy = true; // 是否为敌机\r\n    isDead = false; // 是否死亡\r\n    type = 0; // 敌人类型\r\n\r\n    maxHp: number = 0;\r\n    curHp: number = 0;\r\n    attack: number = 0;\r\n    collideComp: FCircleCollider | FBoxCollider | FPolygonCollider | null = null; // 碰撞组件\r\n\r\n    private _skillComp: SkillComp | null = null;\r\n    private _buffComp: BuffComp | null = null;\r\n\r\n    // TODO 临时做法，后续应该挪到 PlaneBase\r\n    private _attributeData: AttributeData = new AttributeData();\r\n\r\n\r\n    init() {\r\n        this._skillComp = new SkillComp();\r\n        this.addComp(\"skill\", this._skillComp);\r\n        this._buffComp = new BuffComp();\r\n        this.addComp(\"buff\", this._buffComp)\r\n        super.init();\r\n    }\r\n\r\n    get skillComp() {\r\n        return this._skillComp!;\r\n    }\r\n\r\n    get buffComp() {\r\n        return this._buffComp!;\r\n    }\r\n\r\n    get attribute(): AttributeData {\r\n        return this._attributeData;\r\n    }\r\n\r\n    set colliderEnabled(value: boolean) {\r\n        if (this.collideComp) {\r\n            this.collideComp.isEnable = value;\r\n        }\r\n    }\r\n    get colliderEnabled(): boolean {\r\n        return this.collideComp ? this.collideComp.isEnable : false;\r\n    }\r\n\r\n    CastSkill(skillID: number) {\r\n        this.skillComp.Cast(this, skillID);\r\n    }\r\n\r\n    addHp(heal: number) {\r\n        this.curHp = Math.min(\r\n            this.maxHp,\r\n            this.curHp + heal\r\n        );\r\n        this.updateHpUI();;\r\n    }\r\n\r\n    hurt(damage: number) {\r\n        if (this.isDead) {\r\n            return;\r\n        }\r\n        this.cutHp(damage);\r\n        this.playHurtAnim();\r\n        if (this.curHp <= 0) {\r\n            this.toDie();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 减少血量\r\n     * @param {number} damage 受到的伤害值\r\n     */\r\n    cutHp(damage: number) {\r\n        const newHp = this.curHp - damage;\r\n        this.curHp = Math.max(0, newHp);\r\n\r\n        this.updateHpUI();\r\n    }\r\n\r\n    toDie(): boolean {\r\n        if (this.isDead) {\r\n            return false\r\n        }\r\n        this.isDead = true;\r\n        this.colliderEnabled = false;\r\n        return true\r\n    }\r\n    /**\r\n     * 更新血量显示\r\n     */\r\n    updateHpUI() {\r\n        if (this.hpBar) {\r\n            // 更新血条前景的填充范围\r\n            this.hpBar.fillRange = this.curHp / this.maxHp;\r\n\r\n            if (this.hpAniSprite) {\r\n                // 计算血条动画时间\r\n                const duration = Math.abs(this.hpAniSprite.fillRange - this.hpBar.fillRange);\r\n\r\n                Tween.stopAllByTarget(this.hpAniSprite);\r\n                // 血条中间部分的动画\r\n                tween(this.hpAniSprite)\r\n                    .to(duration, { fillRange: this.hpBar.fillRange })\r\n                    .call(() => {\r\n\r\n                    })\r\n                    .start();\r\n            }\r\n        }\r\n\r\n        // 更新血量文字\r\n        this.hpfont && (this.hpfont!.string = this.curHp.toFixed(0));\r\n    }\r\n\r\n    playHurtAnim() {\r\n        // 子类实现\r\n    }\r\n\r\n    ApplyBuffEffect(buff: Buff | null, effectData: builtin.EffectParam) {\r\n        switch (effectData.type) {\r\n            case res.EffectType.Kill:\r\n                this.toDie();\r\n                break;\r\n            case res.EffectType.Hurt:\r\n                if (effectData.param.length >= 1) {\r\n                    this.hurt(effectData.param[0]);\r\n                }\r\n                break;\r\n            case res.EffectType.AttrMaxHPPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHP, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrMaxHPAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHP, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrHPRecoveryPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecovery, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrHPRecoveryAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecovery, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrHPRecoveryMaxHPPerAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryRate, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrAttackPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.Attack, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrAttackAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.Attack, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrAttackBossPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackBoss, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrAttackNormalPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackNormal, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrFortunatePer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.Fortunate, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrFortunateAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.Fortunate, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrMissAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.MissRate, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrBulletHurtResistancePer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistance, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrBulletHurtResistanceAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistance, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrCollisionHurtResistancePer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistance, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrCollisionHurtResistanceAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistance, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrFinalScoreAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.FinalScoreRate, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrKillScoreAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.KillScoreRate, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrEnergyRecoveryPerAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryRate, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrEnergyRecoveryAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecovery, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrPickRadiusPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadius, effectData, true);\r\n                break;\r\n            case res.EffectType.AttrPickRadius:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadius, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrBombMax:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.BombMax, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrBombHurtAdd:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.BombHurt, effectData, false);\r\n                break;\r\n            case res.EffectType.AttrBombHurtPer:\r\n                this.ApplyBuffAttributeEffect(buff, AttributeConst.BombHurt, effectData, true);\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n    }\r\n    private ApplyBuffAttributeEffect(buff: Buff | null, key: number, effectData: builtin.EffectParam, isPer: boolean) {\r\n        if (!buff) {\r\n            return;\r\n        }\r\n        if (effectData.param.length < 1) {\r\n            return;\r\n        }\r\n        this.attribute.addModify(buff.id, key, effectData.param[0], isPer);\r\n    }\r\n    RemoveBuffEffect(buff: Buff, effectData: builtin.EffectParam) {\r\n        this.attribute.removeModify(buff.id);\r\n    }\r\n}"]}