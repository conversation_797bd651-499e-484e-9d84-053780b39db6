{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "Label", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "BundleName", "ccclass", "property", "PopupUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "onShow", "message", "label", "string", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AACZC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;AACRC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;yBAGjBO,O,WADZF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAACL,KAAD,C,2BAHb,MACaM,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAAA;AAAA;;AAKZ,eAANC,MAAM,GAAW;AAAE,iBAAO,mBAAP;AAA6B;;AACxC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS,CACxB;;AAEKC,QAAAA,MAAM,CAACC,OAAD,EAAiC;AAAA;;AAAA;AACzC,YAAA,KAAI,CAACC,KAAL,CAAYC,MAAZ,GAAqBF,OAArB;AADyC;AAE5C;;AACKG,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AArB+B,O;;;;;iBAGV,I", "sourcesContent": ["import { _decorator, Label } from 'cc';\nimport { Base<PERSON>, UILayer, UIOpt } from '../../../../../scripts/ui/UIMgr';\nimport { BundleName } from '../../../../Bundle';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('PopupUI')\nexport class PopupUI extends BaseUI {\n\n    @property(Label)\n    label: Label | null = null;\n\n    public static getUrl(): string { return \"prefab/ui/PopupUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top }\n    public static getBundleName(): string { return BundleName.Home }\n    public static getUIOption(): UIOpt {\n        return { isClickBgCloseUI: true }\n    }\n\n    protected onLoad(): void {\n    }\n\n    async onShow(message: string): Promise<void> {\n        this.label!.string = message;\n    }\n    async onHide(): Promise<void> {\n    }\n    async onClose(): Promise<void> {\n    }\n\n}\n"]}