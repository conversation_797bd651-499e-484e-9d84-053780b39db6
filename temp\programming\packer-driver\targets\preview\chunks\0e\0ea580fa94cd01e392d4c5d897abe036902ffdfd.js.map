{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/CombineDisplay.ts"], "names": ["_decorator", "Component", "Label", "Node", "DataMgr", "EventMgr", "csproto", "MyApp", "logError", "UIMgr", "PlaneUIEvent", "ButtonPlus", "TopBlockInputUI", "PlaneCombineResultUI", "TabStatus", "ccclass", "property", "CombineDisplay", "_tips", "onLoad", "on", "TabChange", "onTabChange", "BagItemClick", "onBagItemClick", "materialGridParentNode", "children", "for<PERSON>ach", "gridsNode", "getComponentsInChildren", "btn", "addClick", "onMatGridClick", "combineOnceBtn", "onCombineOnceClick", "combineAllBtn", "onCombineAllClick", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_EQUIP_COMBINE", "onCombineResultMsg", "onDestroy", "targetOff", "unregister<PERSON><PERSON><PERSON>", "tabStatus", "Bag", "node", "active", "equip", "eqCombine", "reset", "refreshDisplay", "combineSize", "Math", "max", "size", "combineGridsNode", "label", "index", "info", "getByPos", "equipInfo", "lubanTables", "TbEquip", "get", "item", "item_id", "string", "name", "quality", "itemInfo", "TbItem", "count", "tip", "currentNum", "isFull", "next<PERSON>ev", "getCombineResult", "resultGrid", "getComponentInChildren", "getByGuid", "guid", "deleteByPos", "pos", "add", "emit", "UpdateBagGrids", "event", "nd", "target", "parseInt", "msg", "bag", "refreshItems", "hideUI", "openUI", "body", "equip_combine", "results", "combine", "combineAll"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAC1CC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,O;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,e,kBAAAA,e;;AACAC,MAAAA,oB,kBAAAA,oB;;AACAC,MAAAA,S,kBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;gCAGjBiB,c,WADZF,OAAO,CAAC,gBAAD,C,UAEHC,QAAQ,CAACb,IAAD,C,UAERa,QAAQ,CAACb,IAAD,C,UAERa,QAAQ,CAACd,KAAD,C,UAERc,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,2BAVb,MACaC,cADb,SACoChB,SADpC,CAC8C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAWlCiB,KAXkC,GAWhB,CACtB,WADsB,EAEtB,WAFsB,EAGtB,WAHsB,EAItB,SAJsB,CAXgB;AAAA;;AAkB1CC,QAAAA,MAAM,GAAS;AACX;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,SAAzB,EAAoC,KAAKC,WAAzC,EAAsD,IAAtD;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,YAAzB,EAAuC,KAAKC,cAA5C,EAA4D,IAA5D,EAAkE,CAAlE;AACA,eAAKC,sBAAL,CAA6BC,QAA7B,CAAsCC,OAAtC,CAA8CC,SAAS,IAAI;AACvDA,YAAAA,SAAS,CAAEC,uBAAX;AAAA;AAAA,0CAA+CF,OAA/C,CAAuDG,GAAG,IAAI;AAC1DA,cAAAA,GAAG,CAACC,QAAJ,CAAa,KAAKC,cAAlB,EAAkC,IAAlC;AACH,aAFD;AAGH,WAJD;AAKA,eAAKC,cAAL,CAAqBF,QAArB,CAA8B,KAAKG,kBAAnC,EAAuD,IAAvD;AACA,eAAKC,aAAL,CAAoBJ,QAApB,CAA6B,KAAKK,iBAAlC,EAAqD,IAArD;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,oBAA/C,EAAqE,KAAKC,kBAA1E,EAA8F,IAA9F;AACH;;AAESC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACA;AAAA;AAAA,8BAAMP,MAAN,CAAaQ,iBAAb,CAA+B;AAAA;AAAA,kCAAQN,EAAR,CAAWC,MAAX,CAAkBC,oBAAjD,EAAuE,KAAKC,kBAA5E,EAAgG,IAAhG;AACH;;AAEOpB,QAAAA,WAAW,CAACwB,SAAD,EAAuB;AACtC,cAAIA,SAAS,IAAI;AAAA;AAAA,sCAAUC,GAA3B,EAAgC;AAC5B,iBAAKC,IAAL,CAAUC,MAAV,GAAmB,KAAnB;AACA;AACH;;AACD,eAAKD,IAAL,CAAUC,MAAV,GAAmB,IAAnB;AACA;AAAA;AAAA,kCAAQC,KAAR,CAAcC,SAAd,CAAwBC,KAAxB;AACA,eAAKC,cAAL;AACH;;AAEOA,QAAAA,cAAc,GAAG;AACrB,cAAIC,WAAW,GAAGC,IAAI,CAACC,GAAL,CAAS;AAAA;AAAA,kCAAQN,KAAR,CAAcC,SAAd,CAAwBM,IAAxB,EAAT,EAAyC,CAAzC,CAAlB;AACA,eAAKhC,sBAAL,CAA6BC,QAA7B,CAAsCC,OAAtC,CAA8CC,SAAS,IAAI;AACvDA,YAAAA,SAAS,CAACqB,MAAV,GAAmB,KAAnB;AACH,WAFD;AAGA,cAAMS,gBAAgB,GAAG,KAAKjC,sBAAL,CAA6BC,QAA7B,CAAsC4B,WAAW,GAAG,CAApD,CAAzB;AACAI,UAAAA,gBAAgB,CAACT,MAAjB,GAA0B,IAA1B;AACAS,UAAAA,gBAAgB,CAAC7B,uBAAjB,CAAyC3B,KAAzC,EAAgDyB,OAAhD,CAAwD,CAACgC,KAAD,EAAQC,KAAR,KAAkB;AACtE,gBAAMC,IAAI,GAAG;AAAA;AAAA,oCAAQX,KAAR,CAAcC,SAAd,CAAwBW,QAAxB,CAAiCF,KAAjC,CAAb;;AACA,gBAAIC,IAAJ,EAAU;AACN,kBAAME,SAAS,GAAG;AAAA;AAAA,kCAAMC,WAAN,CAAkBC,OAAlB,CAA0BC,GAA1B,CAA8BL,IAAI,CAACM,IAAL,CAAUC,OAAxC,CAAlB;;AACA,kBAAIL,SAAJ,EAAe;AACXJ,gBAAAA,KAAK,CAACU,MAAN,GAAeN,SAAS,CAACO,IAAV,GAAiB,MAAjB,GAA0BP,SAAS,CAACQ,OAApC,GAA8C,GAA7D;AACH,eAFD,MAEO;AACH,oBAAMC,QAAQ,GAAG;AAAA;AAAA,oCAAMR,WAAN,CAAkBS,MAAlB,CAAyBP,GAAzB,CAA6BL,IAAI,CAACM,IAAL,CAAUC,OAAvC,CAAjB;;AACA,oBAAII,QAAJ,EAAc;AACVb,kBAAAA,KAAK,CAACU,MAAN,GAAeG,QAAQ,CAACF,IAAT,GAAgB,MAAhB,GAAyBT,IAAI,CAACM,IAAL,CAAUO,KAAnC,GAA2C,GAA1D;AACH,iBAFD,MAEO;AACHf,kBAAAA,KAAK,CAACU,MAAN,GAAe,IAAf;AACH;AACJ;AACJ,aAZD,MAYO;AACHV,cAAAA,KAAK,CAACU,MAAN,GAAe,MAAf;AACH;AACJ,WAjBD;AAkBA,eAAKM,GAAL,CAAUN,MAAV,GAAmB,KAAKnD,KAAL,CAAW;AAAA;AAAA,kCAAQgC,KAAR,CAAcC,SAAd,CAAwByB,UAAxB,EAAX,CAAnB;;AACA,cAAI;AAAA;AAAA,kCAAQ1B,KAAR,CAAcC,SAAd,CAAwB0B,MAAxB,EAAJ,EAAsC;AAClC,gBAAMC,OAAO,GAAG;AAAA;AAAA,oCAAQ5B,KAAR,CAAcC,SAAd,CAAwB4B,gBAAxB,EAAhB;;AACA,gBAAID,OAAJ,EAAa;AACT,mBAAKE,UAAL,CAAiBC,sBAAjB,CAAwC/E,KAAxC,EAAgDmE,MAAhD,GAAyDS,OAAO,CAACR,IAAR,GAAe,MAAf,GAAwBQ,OAAO,CAACP,OAAhC,GAA0C,GAAnG;AACH,aAFD,MAEO;AACH;AAAA;AAAA,wCAAS,SAAT;AACH;AACJ,WAPD,MAOO;AACH,iBAAKS,UAAL,CAAiBC,sBAAjB,CAAwC/E,KAAxC,EAAgDmE,MAAhD,GAAyD,MAAzD;AACH;;AACD,eAAKpC,cAAL,CAAqBe,IAArB,CAA0BC,MAA1B,GAAmC;AAAA;AAAA,kCAAQC,KAAR,CAAcC,SAAd,CAAwB0B,MAAxB,EAAnC;AACA,eAAK1C,aAAL,CAAoBa,IAApB,CAAyBC,MAAzB,GAAkC,CAAC,KAAKhB,cAAL,CAAqBe,IAArB,CAA0BC,MAA7D;AACH;;AAEOzB,QAAAA,cAAc,CAAC2C,IAAD,EAA2B;AAC7C,cAAI,CAAC,KAAKnB,IAAL,CAAUC,MAAf,EAAuB;AACvB,cAAMY,IAAI,GAAG;AAAA;AAAA,kCAAQX,KAAR,CAAcC,SAAd,CAAwB+B,SAAxB,CAAkCf,IAAI,CAACgB,IAAvC,CAAb;;AACA,cAAItB,IAAJ,EAAU;AACN;AAAA;AAAA,oCAAQX,KAAR,CAAcC,SAAd,CAAwBiC,WAAxB,CAAoCvB,IAAI,CAACwB,GAAzC;AACH,WAFD,MAEO;AACH,gBAAI,CAAC;AAAA;AAAA,oCAAQnC,KAAR,CAAcC,SAAd,CAAwBmC,GAAxB,CAA4BnB,IAA5B,CAAL,EAAwC;AAC3C;;AACD;AAAA;AAAA,oCAASoB,IAAT,CAAc;AAAA;AAAA,4CAAaC,cAA3B;AACA,eAAKnC,cAAL;AACH;;AAEOrB,QAAAA,cAAc,CAACyD,KAAD,EAAoB;AACtC,cAAMC,EAAE,GAAGD,KAAK,CAACE,MAAjB;AACA,cAAMN,GAAG,GAAGO,QAAQ,CAACF,EAAE,CAACpB,IAAJ,CAApB;AACA,cAAI,CAAC;AAAA;AAAA,kCAAQpB,KAAR,CAAcC,SAAd,CAAwBW,QAAxB,CAAiCuB,GAAjC,CAAL,EAA4C;AAC5C;AAAA;AAAA,kCAAQnC,KAAR,CAAcC,SAAd,CAAwBiC,WAAxB,CAAoCC,GAApC;AACA,eAAKhC,cAAL;AACA;AAAA;AAAA,oCAASkC,IAAT,CAAc;AAAA;AAAA,4CAAaC,cAA3B;AACH;;AAEO9C,QAAAA,kBAAkB,CAACmD,GAAD,EAA0B;AAAA;;AAChD;AAAA;AAAA,kCAAQC,GAAR,CAAYC,YAAZ;AACA;AAAA;AAAA,kCAAQ7C,KAAR,CAAcC,SAAd,CAAwBC,KAAxB;AACA,eAAKC,cAAL;AACA;AAAA;AAAA,8BAAM2C,MAAN;AAAA;AAAA;AACA;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA,yEAAmCJ,GAAG,CAACK,IAAvC,0BAAmC,UAAUC,aAA7C,qBAAmC,UAAyBC,OAA5D;AACH;;AAEOlE,QAAAA,kBAAkB,GAAG;AACzB,cAAI,CAAC;AAAA;AAAA,kCAAQgB,KAAR,CAAcC,SAAd,CAAwB0B,MAAxB,EAAL,EAAuC;AACvC;AAAA;AAAA,8BAAMoB,MAAN;AAAA;AAAA;AACA;AAAA;AAAA,kCAAQ/C,KAAR,CAAcC,SAAd,CAAwBkD,OAAxB;AACH;;AAEOjE,QAAAA,iBAAiB,GAAG;AACxB,cAAI;AAAA;AAAA,kCAAQc,KAAR,CAAcC,SAAd,CAAwB0B,MAAxB,EAAJ,EAAsC;;AACtC,cAAI;AAAA;AAAA,kCAAQ3B,KAAR,CAAcC,SAAd,CAAwBmD,UAAxB,EAAJ,EAA0C;AACtC;AAAA;AAAA,gCAAML,MAAN;AAAA;AAAA;AACH;AACJ;;AA9HyC,O;;;;;iBAEhB,I;;;;;;;iBAEY,I;;;;;;;iBAElB,I;;;;;;;iBAEgB,I;;;;;;;iBAED,I", "sourcesContent": ["import { _decorator, Component, EventTouch, Label, Node } from 'cc';\nimport { DataMgr } from 'db://assets/bundles/common/script/data/DataManager';\nimport { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { MyApp } from 'db://assets/scripts/MyApp';\nimport { logError } from 'db://assets/scripts/Utils/Logger';\nimport { UIMgr } from '../../../../../../../scripts/ui/UIMgr';\nimport { PlaneUIEvent } from '../../../../event/PlaneUIEvent';\nimport { ButtonPlus } from '../../../common/components/button/ButtonPlus';\nimport { TopBlockInputUI } from '../../../common/TopBlockInputUI';\nimport { PlaneCombineResultUI } from '../../PlaneCombineResultUI';\nimport { TabStatus } from '../../PlaneTypes';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('CombineDisplay')\nexport class CombineDisplay extends Component {\n    @property(Node)\n    resultGrid: Node | null = null\n    @property(Node)\n    materialGridParentNode: Node | null = null\n    @property(Label)\n    tip: Label | null = null\n    @property(ButtonPlus)\n    combineOnceBtn: ButtonPlus | null = null\n    @property(ButtonPlus)\n    combineAllBtn: ButtonPlus | null = null\n    private _tips: string[] = [\n        \"选择你想合成的装备\",\n        \"还需要2件相同装备\",\n        \"还需要1件相同装备\",\n        \"一切准备就绪!\",\n    ]\n\n    onLoad(): void {\n        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChange, this)\n        EventMgr.on(PlaneUIEvent.BagItemClick, this.onBagItemClick, this, 1)\n        this.materialGridParentNode!.children.forEach(gridsNode => {\n            gridsNode!.getComponentsInChildren(ButtonPlus).forEach(btn => {\n                btn.addClick(this.onMatGridClick, this)\n            })\n        });\n        this.combineOnceBtn!.addClick(this.onCombineOnceClick, this)\n        this.combineAllBtn!.addClick(this.onCombineAllClick, this)\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, this.onCombineResultMsg, this)\n    }\n\n    protected onDestroy(): void {\n        EventMgr.targetOff(this);\n        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_COMBINE, this.onCombineResultMsg, this)\n    }\n\n    private onTabChange(tabStatus: TabStatus) {\n        if (tabStatus == TabStatus.Bag) {\n            this.node.active = false;\n            return\n        }\n        this.node.active = true;\n        DataMgr.equip.eqCombine.reset();\n        this.refreshDisplay();\n    }\n\n    private refreshDisplay() {\n        let combineSize = Math.max(DataMgr.equip.eqCombine.size(), 1)\n        this.materialGridParentNode!.children.forEach(gridsNode => {\n            gridsNode.active = false\n        })\n        const combineGridsNode = this.materialGridParentNode!.children[combineSize - 1]\n        combineGridsNode.active = true\n        combineGridsNode.getComponentsInChildren(Label).forEach((label, index) => {\n            const info = DataMgr.equip.eqCombine.getByPos(index)\n            if (info) {\n                const equipInfo = MyApp.lubanTables.TbEquip.get(info.item.item_id!)\n                if (equipInfo) {\n                    label.string = equipInfo.name + \"(品质:\" + equipInfo.quality + \")\"\n                } else {\n                    const itemInfo = MyApp.lubanTables.TbItem.get(info.item.item_id!)\n                    if (itemInfo) {\n                        label.string = itemInfo.name + \"(品质:\" + info.item.count + \")\"\n                    } else {\n                        label.string = \"未知\"\n                    }\n                }\n            } else {\n                label.string = \"合成材料\"\n            }\n        });\n        this.tip!.string = this._tips[DataMgr.equip.eqCombine.currentNum()]\n        if (DataMgr.equip.eqCombine.isFull()) {\n            const nextLev = DataMgr.equip.eqCombine.getCombineResult()\n            if (nextLev) {\n                this.resultGrid!.getComponentInChildren(Label)!.string = nextLev.name + \"(品质:\" + nextLev.quality + \")\"\n            } else {\n                logError(\"PlaneUI\", `cant get merge result no pos1 equip info`)\n            }\n        } else {\n            this.resultGrid!.getComponentInChildren(Label)!.string = \"合成结果\"\n        }\n        this.combineOnceBtn!.node.active = DataMgr.equip.eqCombine.isFull();\n        this.combineAllBtn!.node.active = !this.combineOnceBtn!.node.active;\n    }\n\n    private onBagItemClick(item: csproto.cs.ICSItem) {\n        if (!this.node.active) return\n        const info = DataMgr.equip.eqCombine.getByGuid(item.guid!)\n        if (info) {\n            DataMgr.equip.eqCombine.deleteByPos(info.pos)\n        } else {\n            if (!DataMgr.equip.eqCombine.add(item)) return\n        }\n        EventMgr.emit(PlaneUIEvent.UpdateBagGrids)\n        this.refreshDisplay()\n    }\n\n    private onMatGridClick(event: EventTouch) {\n        const nd = event.target as Node\n        const pos = parseInt(nd.name)\n        if (!DataMgr.equip.eqCombine.getByPos(pos)) return\n        DataMgr.equip.eqCombine.deleteByPos(pos)\n        this.refreshDisplay()\n        EventMgr.emit(PlaneUIEvent.UpdateBagGrids)\n    }\n\n    private onCombineResultMsg(msg: csproto.cs.IS2CMsg) {\n        DataMgr.bag.refreshItems();\n        DataMgr.equip.eqCombine.reset();\n        this.refreshDisplay()\n        UIMgr.hideUI(TopBlockInputUI)\n        UIMgr.openUI(PlaneCombineResultUI, msg.body?.equip_combine?.results)\n    }\n\n    private onCombineOnceClick() {\n        if (!DataMgr.equip.eqCombine.isFull()) return\n        UIMgr.openUI(TopBlockInputUI)\n        DataMgr.equip.eqCombine.combine();\n    }\n\n    private onCombineAllClick() {\n        if (DataMgr.equip.eqCombine.isFull()) return\n        if (DataMgr.equip.eqCombine.combineAll()) {\n            UIMgr.openUI(TopBlockInputUI)\n        }\n    }\n}\n\n"]}