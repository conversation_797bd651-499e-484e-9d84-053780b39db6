import { _decorator, misc, instantiate, input, Input, EventKeyboard, KeyCode, Component, Rich<PERSON><PERSON><PERSON>, director, assetManager } from 'cc';
import { EDITOR } from 'cc/env';
import { Emitter } from '../../scripts/Game/bullet/Emitter';
import { BulletSystem } from '../../scripts/Game/bullet/BulletSystem';
const { ccclass, playOnFocus, executeInEditMode, property, disallowMultiple, menu  } = _decorator;

@ccclass('EmitterEditor')
@menu('子弹系统/发射器编辑器')
@playOnFocus(true)
@executeInEditMode(true)
@disallowMultiple(true)
export class EmitterEditor extends Component {
    @property({visible:false})
    fixedDelta:number = 16.67; // Fixed time step (e.g., 60 FPS), 单位: 毫秒

    @property({displayName: "目标帧率"})
    public get targetFrameRate(): number {
        return 1000 / this.fixedDelta;
    }

    public set targetFrameRate(value: number) {
        this.fixedDelta = 1000 / value;
    }

    @property({type: RichText, override: true, displayName: "信息显示"})
    richText: RichText = null!;

    // @property({displayName: "当前时间(ms)"})
    // public get frameTime(): number {
    //     return EmitterEditor.frameTimeInMilliseconds;
    // }

    // @property({displayName: "当前发射器数量"})
    // public get emitterCount(): number {
    //     return BulletSystem.allEmitters.length;
    // }

    // @property({displayName: "当前子弹数量"})
    // public get bulletCount(): number {
    //     return BulletSystem.allBullets.length;
    // }

    // @property({displayName: "当前事件组数量"})
    // public get eventGroupCount(): number {
    //     return BulletSystem.allEventGroups.length;
    // }

    static frameCount: number = 0;
    static frameTimeInMilliseconds: number = 0;
    private _updateInEditor: boolean = false;

    resetInEditor() {
        this._updateInEditor = true;
        // console.log('resetInEditor');
    }

    onFocusInEditor() {
        this._updateInEditor = true;
        // console.log('onFocusInEditor');
        
        // @ts-ignore
        // Editor.Selection.select('node', BulletSystem.allEmitters.map(emitter => emitter.node.uuid));

        // loop all children to find emitters
        this.node.walk((node) => {
            const emitter = node.getComponent(Emitter);
            if (emitter) {
                emitter.setActive(true);
            }
        });
    }

    onLostFocusInEditor(): void {
        this._updateInEditor = false;
        this.reset();
    }

    onLoad() {
        input.on(Input.EventType.KEY_PRESSING, this.onKeyPressing, this);
    }

    onDestroy() {
        input.off(Input.EventType.KEY_PRESSING, this.onKeyPressing, this);
    }

    onKeyPressing(event: EventKeyboard) {
        if (!this._updateInEditor) return;

        // use wasd to move player sprite(which is not present right now)
    }

    start() {
        this.reset();
    }

    reset() {
        EmitterEditor.frameCount = 0;
        EmitterEditor.frameTimeInMilliseconds = 0;
        BulletSystem.destroyAllBullets(true);                
        this.node.walk((node) => {
            const emitter = node.getComponent(Emitter);
            if (emitter) {
                emitter.setActive(false);
            }
        });
        BulletSystem.allEventGroups = [];
        console.log('reset: ', this._updateInEditor);
    }

    update(dt: number) {
        if (EDITOR && this._updateInEditor) {
            this.updateInfoText();
            const milli_dt = dt * 1000;
            EmitterEditor.frameCount += 1;
            EmitterEditor.frameTimeInMilliseconds += milli_dt;
            BulletSystem.tick(dt);
        }
    }

    updateInfoText() {
        if (this.richText) {
            this.richText.string = `当前时间: ${EmitterEditor.frameTimeInMilliseconds.toFixed(2)}\n当前发射器数量: ${BulletSystem.allEmitters.length}\n当前子弹数量: ${BulletSystem.allBullets.length}\n当前事件组数量: ${BulletSystem.allEventGroups.length}`;
        }
    }

    // 编辑器方法
    public instantiatePrefab(prefabUuid: string) {
        // replace db://assets/resources/Game/prefabs/emitter/ with assets/resources/Game/prefabs/emitter/
        //prefabUrl = prefabUrl.replace('db://', '');
        assetManager.loadAny({uuid: prefabUuid}, (err, prefab) => {
            if (err) {
                console.log('Failed to load prefab:', err);
                return;
            }
            const node = instantiate(prefab!);
            const parent = this.node;
            parent!.addChild(node);
        });
    }

    // public saveToPrefab(nodeUuid: string, prefabUrl: string): Promise<string> {
    //     console.log('saveToPrefab in Component:', nodeUuid, prefabUrl);        
    //     return new Promise<string>((resolve, reject) => {
    //         const scene = director.getScene();
    //         const target = scene!.getChildByUuid(nodeUuid);
    //         if (!target) {
    //             console.error("node not found:", nodeUuid);
    //             reject();
    //             return;
    //         }
    //         const json = cce.Utils.serialize(target);
    //         // 将节点保存为 Prefab
    //         // _utils.applyTargetOverrides(target as Node);
    //         // Editor.Message.request('asset-db', 'save-asset', prefabUrl, json);
    //         resolve(json);
    //     });
    // }
}
