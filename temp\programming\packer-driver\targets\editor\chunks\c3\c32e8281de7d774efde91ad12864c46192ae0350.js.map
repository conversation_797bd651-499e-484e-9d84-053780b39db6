{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossPlane.ts"], "names": ["_decorator", "size", "Sprite", "tween", "TrackComponent", "GameEnum", "Tools", "GameIns", "TrackGroup", "PlaneBase", "EnemyPlaneRole", "FBoxCollider", "ColliderGroupType", "Bullet", "ccclass", "property", "BossPlane", "_datas", "_data", "_trackCom", "_idleName", "_formIndex", "_formNum", "_posX", "_posY", "_moveToX", "_moveToY", "_moveSpeed", "_bArriveDes", "_transFormMove", "_nextWayPointTime", "_nextWayPointX", "_nextWayPointY", "_nextWayPointInterval", "_nextWaySpeed", "_shootAble", "_atkActions", "_bOrde<PERSON><PERSON><PERSON>ck", "_orderIndex", "_orderAtkArr", "_atkPointDatas", "_action", "_bDamageable", "_b<PERSON><PERSON>ckMove", "_bFirstWayPoint", "transformBattle", "_bRemoveable", "_nextAttackInterval", "_nextAttackTime", "_attackID", "tip", "_hpWhiteTween", "bullets", "initBoss", "datas", "init", "length", "_initUI", "_initProperty", "_initTrack", "_initCollide", "setFormIndex", "curHp", "maxHp", "attack", "addScript", "node", "setTrackGroupStartCall", "setTrackGroupOverCall", "BossAction", "Appear", "setTrackAble", "setAction", "Transform", "setTrackOverCall", "setTrackLeaveCall", "setTrackStartCall", "track", "collide<PERSON>omp", "getComponent", "addComponent", "groupType", "ENEMY_NORMAL", "colliderEnabled", "setCollideAble", "isEnabled", "isEnable", "action", "Normal", "_playSkel", "setDamangeable", "_startAppearTrack", "transformEnd", "AttackPrepare", "scheduleOnce", "AttackIng", "AttackOver", "Blast", "damageable", "anim<PERSON><PERSON>", "loop", "callback", "role", "playAnim", "setTip", "battleManager", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bossFightStart", "index", "i", "attackActions", "push", "point", "attackPoints", "data", "bAvailable", "enterNextForm", "startBattle", "_startNormalTrack", "updateGameLogic", "deltaTime", "isDead", "_processNextWayPoint", "_updateMove", "_processNextAttack", "_udpateShoot", "isDamageable", "removeAble", "value", "trackGroup", "loopNum", "trackIDs", "<PERSON><PERSON><PERSON><PERSON>", "speeds", "trackIntervals", "startTrack", "trackGroups", "x", "y", "moveToPos", "speed", "transformMove", "setPos", "update", "setPosition", "getRandomInArray", "wayPointIntervals", "random_int", "wayPointXs", "wayPointYs", "deltaX", "deltaY", "distance", "Math", "sqrt", "moveX", "moveY", "abs", "onCollide", "collider", "entity", "damage", "getAttack", "hurtEffectManager", "createHurtNumByType", "getPosition", "error", "console", "hurt", "_refreshHpBar", "checkHp", "_toDie", "to<PERSON><PERSON>", "stop", "onDie", "_playDieAnim", "plane", "enemyManager", "planes", "die", "EnemyDestroyType", "Die", "removeBullets", "hpRatio", "isDecreasing", "hpSpr", "fill<PERSON><PERSON><PERSON>", "duration", "hpWhite", "to", "call", "start", "addBullet", "bullet", "removeBullet", "indexOf", "splice", "dieRemove"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAA0CC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;AAAeC,MAAAA,K,OAAAA,K;;AACjEC,MAAAA,c;;AACEC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;AAEFC,MAAAA,S;;AACAC,MAAAA,c;;AACAC,MAAAA,Y;;AACaC,MAAAA,iB,kBAAAA,iB;;AACbC,MAAAA,M;;;;;;;;;OAED;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;yBAGTgB,S,WADpBF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ;AAAA;AAAA,2C,UAGRA,QAAQ,CAACb,MAAD,C,UAERa,QAAQ,CAACb,MAAD,C,UAERa,QAAQ,CAACb,MAAD,C,2BAVb,MACqBc,SADrB;AAAA;AAAA,kCACiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAY7CC,MAZ6C,GAYxB,EAZwB;AAAA,eAa7CC,KAb6C,GAapB,IAboB;AAAA,eAc7CC,SAd6C,GAcV,IAdU;AAAA,eAiB7CC,SAjB6C,GAiBzB,OAjByB;AAAA,eAkB7CC,UAlB6C,GAkBxB,CAAC,CAlBuB;AAkBrB;AAlBqB,eAmB7CC,QAnB6C,GAmB1B,CAnB0B;AAmBxB;AAnBwB,eAqB7CC,KArB6C,GAqB7B,CArB6B;AAAA,eAsB7CC,KAtB6C,GAsB7B,CAtB6B;AAAA,eAwB7CC,QAxB6C,GAwB1B,CAxB0B;AAAA,eAyB7CC,QAzB6C,GAyB1B,CAzB0B;AAAA,eA0B7CC,UA1B6C,GA0BxB,CA1BwB;AAAA,eA2B7CC,WA3B6C,GA2BtB,KA3BsB;AA2BhB;AA3BgB,eA4B7CC,cA5B6C,GA4BnB,KA5BmB;AA6B7C;AA7B6C,eA8B7CC,iBA9B6C,GA8BjB,CA9BiB;AAAA,eA+B7CC,cA/B6C,GA+BpB,CA/BoB;AAAA,eAgC7CC,cAhC6C,GAgCpB,CAhCoB;AAAA,eAiC7CC,qBAjC6C,GAiCb,CAjCa;AAAA,eAkC7CC,aAlC6C,GAkCrB,CAlCqB;AAAA,eAmC7CC,UAnC6C,GAmCvB,IAnCuB;AAAA,eAoC7CC,WApC6C,GAoCxB,EApCwB;AAAA,eAsC7CC,aAtC6C,GAsCpB,KAtCoB;AAAA,eAuC7CC,WAvC6C,GAuCvB,CAvCuB;AAAA,eAwC7CC,YAxC6C,GAwCpB,EAxCoB;AAAA,eA0C7CC,cA1C6C,GA0CrB,EA1CqB;AAAA,eA4C7CC,OA5C6C,GA4C3B,CAAC,CA5C0B;AAAA,eA6C7CC,YA7C6C,GA6CrB,KA7CqB;AAAA,eA8C7CC,YA9C6C,GA8CrB,KA9CqB;AAAA,eA+C7CC,eA/C6C,GA+ClB,KA/CkB;AAAA,eAgD7CC,eAhD6C,GAgDlB,IAhDkB;AAAA,eAiD7CC,YAjD6C,GAiDrB,KAjDqB;AAkD7C;AACA;AACA;AApD6C,eAsD7CC,mBAtD6C,GAsDf,CAtDe;AAAA,eAuD7CC,eAvD6C,GAuDnB,CAvDmB;AAAA,eAwD7CC,SAxD6C,GAwDzB,CAxDyB;AAAA,eA0D7CC,GA1D6C,GA0D/B,EA1D+B;AAAA,eA2D7CC,aA3D6C,GA2DxB,IA3DwB;AAAA,eA4D7CC,OA5D6C,GA4D5B,EA5D4B;AAAA;;AA8D7C;AACJ;AACA;AACA;AACIC,QAAAA,QAAQ,CAACC,KAAD,EAAoB;AACxB,gBAAMC,IAAN;AACA,eAAKtC,MAAL,GAAcqC,KAAd;AACA,eAAKhC,QAAL,GAAgB,KAAKL,MAAL,CAAYuC,MAA5B;AACA,eAAKZ,eAAL,GAAuB,IAAvB;;AACA,eAAKa,OAAL;;AACA,eAAKC,aAAL;;AACA,eAAKC,UAAL;;AACA,eAAKC,YAAL;;AACA,eAAKC,YAAL,CAAkB,CAAlB;AACH;AAED;AACJ;AACA;;;AACIJ,QAAAA,OAAO,GAAG,CAET;;AAEDC,QAAAA,aAAa,GAAE;AACX;AACA,eAAKI,KAAL,GAAa,IAAb;AACA,eAAKC,KAAL,GAAa,KAAKD,KAAlB;AACA,eAAKE,MAAL,GAAc,EAAd;AACH;;AAEDL,QAAAA,UAAU,GAAG;AACT,eAAKxC,SAAL,GAAiB;AAAA;AAAA,8BAAM8C,SAAN,CAAgB,KAAKC,IAArB;AAAA;AAAA,+CAAjB;;AACA,eAAK/C,SAAL,CAAgBgD,sBAAhB,CAAuC,MAAM,CAAG,CAAhD;;AACA,eAAKhD,SAAL,CAAgBiD,qBAAhB,CAAsC,MAAM;AACxC,gBAAI,KAAK3B,OAAL,KAAiB;AAAA;AAAA,sCAAS4B,UAAT,CAAoBC,MAAzC,EAAiD;AAC7C,mBAAKnD,SAAL,CAAgBoD,YAAhB,CAA6B,KAA7B;;AACA,mBAAKC,SAAL,CAAe;AAAA;AAAA,wCAASH,UAAT,CAAoBI,SAAnC;AACH;AACJ,WALD;;AAMA,eAAKtD,SAAL,CAAgBuD,gBAAhB,CAAiC,MAAM,CAAG,CAA1C;;AACA,eAAKvD,SAAL,CAAgBwD,iBAAhB,CAAkC,MAAM,CAAG,CAA3C;;AACA,eAAKxD,SAAL,CAAgByD,iBAAhB,CAAmCC,KAAD,IAAgB,CAEjD,CAFD;AAGH;;AAEDjB,QAAAA,YAAY,GAAS;AACjB,eAAKkB,WAAL,GAAmB,KAAKC,YAAL;AAAA;AAAA,+CAAmC,KAAKC,YAAL;AAAA;AAAA,2CAAtD;AACA,eAAKF,WAAL,CAAkBvB,IAAlB,CAAuB,IAAvB,EAA6BtD,IAAI,CAAC,GAAD,EAAM,GAAN,CAAjC,EAFiB,CAE6B;;AAC9C,eAAK6E,WAAL,CAAkBG,SAAlB,GAA8B;AAAA;AAAA,sDAAkBC,YAAhD;AACA,eAAKC,eAAL,GAAuB,KAAvB;AACH;;AAEDC,QAAAA,cAAc,CAACC,SAAD,EAAqB;AAC/B,eAAKP,WAAL,CAAkBQ,QAAlB,GAA6BD,SAA7B;AACH;;AAEDb,QAAAA,SAAS,CAACe,MAAD,EAAiB;AACtB,cAAI,KAAK9C,OAAL,KAAiB8C,MAArB,EAA6B;AACzB,iBAAK9C,OAAL,GAAe8C,MAAf;AAEA,gBAAIlB,UAAU,GAAG;AAAA;AAAA,sCAASA,UAA1B;;AACA,oBAAQ,KAAK5B,OAAb;AACI,mBAAK4B,UAAU,CAACmB,MAAhB;AACI,qBAAKC,SAAL,CAAe,KAAKrE,SAApB,EAA+B,IAA/B,EAAqC,MAAM,CAAG,CAA9C;;AACA,qBAAKsE,cAAL,CAAoB,IAApB;AACA;;AAEJ,mBAAKrB,UAAU,CAACC,MAAhB;AACI,qBAAKmB,SAAL,CAAgB,QAAO,KAAKpE,UAAL,GAAkB,CAAE,EAA3C,EAA8C,IAA9C,EAAoD,MAAM,CAAG,CAA7D;;AACA,qBAAKqE,cAAL,CAAoB,KAApB;;AACA,qBAAKC,iBAAL;;AACA;;AAEJ,mBAAKtB,UAAU,CAACI,SAAhB;AACI,qBAAKgB,SAAL,CAAgB,QAAO,KAAKpE,UAAL,GAAkB,CAAE,EAA3C,EAA8C,KAA9C,EAAqD,MAAM;AACvD,uBAAKwB,eAAL,IAAwB,KAAK+C,YAAL,EAAxB;AACH,iBAFD;;AAGA,qBAAKF,cAAL,CAAoB,KAApB;AACA;;AAEJ,mBAAKrB,UAAU,CAACwB,aAAhB;AACI,qBAAKC,YAAL,CAAkB,MAAM;AACpB,uBAAKtB,SAAL,CAAeH,UAAU,CAAC0B,SAA1B;AACH,iBAFD;AAGA,qBAAKL,cAAL,CAAoB,IAApB;AACA;;AAEJ,mBAAKrB,UAAU,CAAC0B,SAAhB;AACA,mBAAK1B,UAAU,CAAC2B,UAAhB;AACI,qBAAKN,cAAL,CAAoB,IAApB;AACA;;AAEJ,mBAAKrB,UAAU,CAAC4B,KAAhB;AACI,qBAAKP,cAAL,CAAoB,KAApB;AACA;;AAEJ;AACI,qBAAKA,cAAL,CAAoB,IAApB;AApCR;AAsCH;AACJ;AAED;AACJ;AACA;AACA;;;AACIA,QAAAA,cAAc,CAACQ,UAAD,EAAsB;AAChC,eAAKxD,YAAL,GAAoBwD,UAApB;AACH;;AAGDT,QAAAA,SAAS,CAACU,QAAD,EAAmBC,IAAnB,EAAkCC,QAAlC,EAAsD;AAC3D,eAAKC,IAAL,CAAWC,QAAX,CAAoBJ,QAApB,EAA8BC,IAA9B,EAAoCC,QAApC;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,MAAM,CAACtD,GAAD,EAAc;AAChB,eAAKA,GAAL,GAAWA,GAAX;AACH;AACD;AACJ;AACA;;;AACI0C,QAAAA,YAAY,GAAG;AACX,cAAI,KAAK1C,GAAL,KAAa,EAAjB,EAAqB;AACjB;AAAA;AAAA,oCAAQuD,aAAR,CAAsBC,gBAAtB,CAAuC,KAAKxD,GAA5C;AACH,WAFD,MAEO;AACH;AAAA;AAAA,oCAAQuD,aAAR,CAAsBE,cAAtB;AACH;AACJ;AAGD;AACJ;AACA;AACA;;;AACI9C,QAAAA,YAAY,CAAC+C,KAAD,EAAgB;AACxB,cAAI,KAAKvF,UAAL,KAAoBuF,KAAxB,EAA+B;AAC3B,iBAAKvF,UAAL,GAAkBuF,KAAlB;AACA,iBAAKvE,aAAL,GAAqB,IAArB;AACA,iBAAKC,WAAL,GAAmB,CAAnB;AACA,iBAAKpB,KAAL,GAAa,KAAKD,MAAL,CAAY,KAAKI,UAAjB,CAAb;AACA,iBAAKD,SAAL,GAAkB,OAAM,KAAKC,UAAL,GAAkB,CAAE,EAA5C;;AAEA,gBAAIuF,KAAK,KAAK,CAAd,EAAiB;AACb,mBAAKpC,SAAL,CAAe;AAAA;AAAA,wCAASH,UAAT,CAAoBC,MAAnC;AACH;;AAED,iBAAK/B,YAAL,GAAoB,EAApB;;AACA,iBAAK,IAAIsE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK3F,KAAL,CAAW4F,aAAX,CAAyBtD,MAA7C,EAAqDqD,CAAC,EAAtD,EAA0D;AACtD,mBAAKtE,YAAL,CAAkBwE,IAAlB,CAAuBF,CAAvB;AACH;;AAED,iBAAKrE,cAAL,GAAsB,EAAtB;;AACA,iBAAK,MAAMwE,KAAX,IAAoB,KAAK9F,KAAL,CAAW+F,YAA/B,EAA6C;AACzC,oBAAMC,IAAI,GAAG,CAACF,KAAK,CAACG,UAAP,EAAmBH,KAAnB,CAAb;;AACA,mBAAKxE,cAAL,CAAoBuE,IAApB,CAAyBG,IAAzB;AACH;;AAED,iBAAK9E,WAAL,GAAmB,CAAC,GAAG,KAAKlB,KAAL,CAAW4F,aAAf,CAAnB;AACH;AACJ;AAED;AACJ;AACA;;;AACIM,QAAAA,aAAa,GAAG;AACZ,cAAI,KAAK/F,UAAL,GAAkB,KAAKJ,MAAL,CAAYuC,MAAZ,GAAqB,CAA3C,EAA8C;AAC1C,iBAAKnC,UAAL;AACA,iBAAKwC,YAAL,CAAkB,KAAKxC,UAAvB;AACH;AACJ;AAED;AACJ;AACA;;;AACIgG,QAAAA,WAAW,GAAG;AACV,eAAKC,iBAAL;;AACA,eAAK9C,SAAL,CAAe;AAAA;AAAA,oCAASH,UAAT,CAAoBmB,MAAnC;AACA,eAAKL,eAAL,GAAuB,IAAvB;AACH;AAED;AACJ;AACA;AACA;;;AACIoC,QAAAA,eAAe,CAACC,SAAD,EAAoB;AAC/B,cAAI,CAAC,KAAKC,MAAV,EAAkB;AACd,gBAAIpD,UAAU,GAAG;AAAA;AAAA,sCAASA,UAA1B;;AACA,oBAAQ,KAAK5B,OAAb;AACI,mBAAK4B,UAAU,CAACmB,MAAhB;AACI,qBAAKkC,oBAAL,CAA0BF,SAA1B;;AACA,qBAAKG,WAAL,CAAiBH,SAAjB;;AACA,qBAAKI,kBAAL,CAAwBJ,SAAxB;;AACA;;AAEJ,mBAAKnD,UAAU,CAACC,MAAhB;AACI,qBAAKqD,WAAL,CAAiBH,SAAjB;;AACA,oBAAI,KAAK5F,WAAT,EAAsB;AAClB,uBAAK4C,SAAL,CAAeH,UAAU,CAACI,SAA1B;AACH;;AACD;;AAEJ,mBAAKJ,UAAU,CAACI,SAAhB;AACI,oBAAI,KAAK5C,cAAT,EAAyB;AACrB,uBAAK8F,WAAL,CAAiBH,SAAjB;AACH;;AACD;;AAEJ,mBAAKnD,UAAU,CAACwB,aAAhB;AACI,qBAAK6B,oBAAL,CAA0BF,SAA1B;;AACA,oBAAI,KAAK7E,YAAT,EAAuB;AACnB,uBAAKgF,WAAL,CAAiBH,SAAjB;AACH;;AACD;;AAEJ,mBAAKnD,UAAU,CAAC0B,SAAhB;AACI,qBAAK2B,oBAAL,CAA0BF,SAA1B;;AACA,oBAAI,KAAK7E,YAAT,EAAuB;AACnB,uBAAKgF,WAAL,CAAiBH,SAAjB;AACH;;AACD,qBAAKK,YAAL,CAAkBL,SAAlB;;AACA;;AAEJ,mBAAKnD,UAAU,CAAC2B,UAAhB;AACI,qBAAK0B,oBAAL,CAA0BF,SAA1B;;AACA,oBAAI,KAAK7E,YAAT,EAAuB;AACnB,uBAAKgF,WAAL,CAAiBH,SAAjB;AACH;;AACD,qBAAKhD,SAAL,CAAeH,UAAU,CAACmB,MAA1B;AACA;;AAEJ,mBAAKnB,UAAU,CAAC4B,KAAhB;AACI;AA5CR;AA8CH;AACJ;AAED;AACJ;AACA;;;AACI6B,QAAAA,YAAY,GAAY;AACpB,iBAAO,KAAKpF,YAAZ;AACH;AAED;AACJ;AACA;;;AACkB,YAAVqF,UAAU,GAAY;AACtB,iBAAO,KAAKjF,YAAZ;AACH;;AAEa,YAAViF,UAAU,CAACC,KAAD,EAAiB;AAC3B,eAAKlF,YAAL,GAAoBkF,KAApB;AACH;AAGD;AACJ;AACA;;;AACIrC,QAAAA,iBAAiB,GAAG;AAChB,gBAAMsC,UAAU,GAAG;AAAA;AAAA,yCAAnB;AACAA,UAAAA,UAAU,CAACC,OAAX,GAAqB,CAArB;AACAD,UAAAA,UAAU,CAACE,QAAX,GAAsB,CAAC,KAAKjH,KAAL,CAAYkH,WAAZ,CAAwB,CAAxB,CAAD,CAAtB;AACAH,UAAAA,UAAU,CAACI,MAAX,GAAoB,CAAC,KAAKnH,KAAL,CAAYkH,WAAZ,CAAwB,CAAxB,CAAD,CAApB;AACAH,UAAAA,UAAU,CAACK,cAAX,GAA4B,CAAC,CAAD,CAA5B;;AAEA,eAAKnH,SAAL,CAAgBoC,IAAhB,CAAqB,IAArB,EAA2B,CAAC0E,UAAD,CAA3B,EAAyC,EAAzC,EAA6C,KAAK/G,KAAL,CAAYkH,WAAZ,CAAwB,CAAxB,CAA7C,EAAyE,KAAKlH,KAAL,CAAYkH,WAAZ,CAAwB,CAAxB,CAAzE;;AACA,eAAKjH,SAAL,CAAgBoD,YAAhB,CAA6B,IAA7B;;AACA,eAAKpD,SAAL,CAAgBoH,UAAhB;AACH;AAED;AACJ;AACA;;;AACIjB,QAAAA,iBAAiB,GAAG;AAChB,eAAKnG,SAAL,CAAgBoC,IAAhB,CAAqB,IAArB,EAA2B,KAAKrC,KAAL,CAAYsH,WAAvC,EAAoD,EAApD,EAAwD,KAAKtE,IAAL,CAAUuE,CAAlE,EAAqE,KAAKvE,IAAL,CAAUwE,CAA/E;;AACA,eAAKvH,SAAL,CAAgBoD,YAAhB,CAA6B,IAA7B;;AACA,eAAKpD,SAAL,CAAgBoH,UAAhB;;AACA,eAAK/D,SAAL,CAAe;AAAA;AAAA,oCAASH,UAAT,CAAoBmB,MAAnC;AACH;AAID;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACImD,QAAAA,SAAS,CAACF,CAAD,EAAYC,CAAZ,EAAuBE,KAAvB,EAAsCC,aAAsB,GAAG,KAA/D,EAAsE;AAC3E,eAAKpH,QAAL,GAAgBgH,CAAhB;AACA,eAAK/G,QAAL,GAAgBgH,CAAhB;AACA,eAAK/G,UAAL,GAAkBiH,KAAlB;AACA,eAAKhH,WAAL,GAAmB,KAAnB;AACA,eAAKC,cAAL,GAAsBgH,aAAtB;AACH;;AAGDC,QAAAA,MAAM,CAACL,CAAD,EAAYC,CAAZ,EAAuBK,MAAe,GAAG,IAAzC,EAA+C;AACjD,eAAK7E,IAAL,CAAU8E,WAAV,CAAsBP,CAAtB,EAAyBC,CAAzB;AACA,eAAKnH,KAAL,GAAakH,CAAb;AACA,eAAKjH,KAAL,GAAakH,CAAb;AACH;AAED;AACJ;AACA;AACA;;;AACIhB,QAAAA,oBAAoB,CAACF,SAAD,EAAoB;AACpC,cAAI,KAAK5F,WAAL,IAAoB,KAAKV,KAAL,CAAYsH,WAAZ,CAAwBhF,MAAxB,KAAmC,CAA3D,EAA8D;AAC1D,iBAAK1B,iBAAL,IAA0B0F,SAA1B;;AACA,gBAAI,KAAK1F,iBAAL,GAAyB,KAAKG,qBAAlC,EAAyD;AACrD,mBAAKA,qBAAL,GAA6B;AAAA;AAAA,kCAAMgH,gBAAN,CAAuB,KAAK/H,KAAL,CAAYgI,iBAAnC,CAA7B;AACA,mBAAKpH,iBAAL,GAAyB,CAAzB;;AAEA,kBAAI,KAAKc,eAAT,EAA0B;AACtB,qBAAKA,eAAL,GAAuB,KAAvB;AACH,eAFD,MAEO;AACH,sBAAMgE,KAAK,GAAG;AAAA;AAAA,oCAAMuC,UAAN,CAAiB,CAAjB,EAAoB,KAAKjI,KAAL,CAAYkI,UAAZ,CAAuB5F,MAAvB,GAAgC,CAApD,CAAd;AACA,qBAAKzB,cAAL,GAAsB,KAAKb,KAAL,CAAYkI,UAAZ,CAAuBxC,KAAvB,CAAtB;AACA,qBAAK5E,cAAL,GAAsB,KAAKd,KAAL,CAAYmI,UAAZ,CAAuBzC,KAAvB,CAAtB;AACA,qBAAK1E,aAAL,GAAqB;AAAA;AAAA,oCAAM+G,gBAAN,CAAuB,KAAK/H,KAAL,CAAYmH,MAAnC,CAArB;AACA,qBAAKM,SAAL,CAAe,KAAK5G,cAApB,EAAoC,KAAKC,cAAzC,EAAyD,KAAKE,aAA9D;AACH;AACJ;AACJ;AACJ;;AAGDyF,QAAAA,WAAW,CAACH,SAAD,EAAoB;AAC3B,cAAI,KAAK/E,OAAL,KAAiB;AAAA;AAAA,oCAAS4B,UAAT,CAAoBC,MAArC,IAA+C,KAAKpD,KAAL,CAAYsH,WAAZ,CAAwBhF,MAAxB,GAAiC,CAApF,EAAuF;AACnF;AACA,iBAAKrC,SAAL,CAAgBoG,eAAhB,CAAgCC,SAAhC;AACH,WAHD,MAGO,IAAI,CAAC,KAAK5F,WAAV,EAAuB;AAC1B;AACA;AACA;AAEA,kBAAM0H,MAAM,GAAG,KAAK7H,QAAL,GAAgB,KAAKF,KAApC;AACA,kBAAMgI,MAAM,GAAG,KAAK7H,QAAL,GAAgB,KAAKF,KAApC;AACA,kBAAMgI,QAAQ,GAAGC,IAAI,CAACC,IAAL,CAAUJ,MAAM,GAAGA,MAAT,GAAkBC,MAAM,GAAGA,MAArC,CAAjB;AAEA,gBAAII,KAAK,GAAG,CAAZ;AACA,gBAAIC,KAAK,GAAG,CAAZ,CAV0B,CAY1B;;AACA,gBAAIJ,QAAQ,IAAI,KAAK7H,UAArB,EAAiC;AAC7BgI,cAAAA,KAAK,GAAGL,MAAR;AACAM,cAAAA,KAAK,GAAGL,MAAR;AACH,aAHD,CAIA;AAJA,iBAKK;AACDI,cAAAA,KAAK,GAAG,KAAKhI,UAAL,GAAkB2H,MAAlB,GAA2BE,QAAnC;AACAI,cAAAA,KAAK,GAAG,KAAKjI,UAAL,GAAkB4H,MAAlB,GAA2BC,QAAnC;AACH,aArByB,CAuB1B;;;AACA,iBAAKjI,KAAL,IAAcoI,KAAd;AACA,iBAAKnI,KAAL,IAAcoI,KAAd;AACA,iBAAKd,MAAL,CAAY,KAAKvH,KAAjB,EAAwB,KAAKC,KAA7B,EA1B0B,CA4B1B;;AACA,iBAAKI,WAAL,GAAoB6H,IAAI,CAACI,GAAL,CAASF,KAAT,IAAkB,GAAlB,IAAyBF,IAAI,CAACI,GAAL,CAASD,KAAT,IAAkB,GAA/D;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIhC,QAAAA,kBAAkB,CAACJ,SAAD,EAAoB,CAClC;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACH;AAED;AACJ;AACA;AACA;;;AACsB,cAAZK,YAAY,CAACL,SAAD,EAAoB,CAClC;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACH;;AAGDsC,QAAAA,SAAS,CAACC,QAAD,EAA4B;AACjC,cAAI,CAAC,KAAKtC,MAAN,IAAgB,KAAKK,YAAL,EAApB,EAAyC;AACrC,gBAAIiC,QAAQ,CAACC,MAAT;AAAA;AAAA,iCAAJ,EAAuC;AACnC,kBAAIC,MAAM,GAAGF,QAAQ,CAACC,MAAT,CAAgBE,SAAhB,EAAb;;AACA,kBAAI;AACA;AAAA;AAAA,wCAAQC,iBAAR,CAA0BC,mBAA1B,CAA8CL,QAAQ,CAACC,MAAT,CAAgB9F,IAAhB,CAAqBmG,WAArB,EAA9C,EAAkFJ,MAAlF;AACH,eAFD,CAEE,OAAOK,KAAP,EAAc;AACZC,gBAAAA,OAAO,CAACD,KAAR,CAAcA,KAAd;AACH;;AACD,mBAAKE,IAAL,CAAU,CAACP,MAAX;AACH;AACJ;AACJ;;AAEDO,QAAAA,IAAI,CAACP,MAAD,EAA0B;AAC1B,cAAI,KAAKxC,MAAL,IAAe,CAAC,KAAKK,YAAL,EAApB,EAAyC;AACrC,mBAAO,KAAP;AACH;;AAED,eAAKhE,KAAL,IAAcmG,MAAd;;AACA,cAAI,KAAKnG,KAAL,GAAa,CAAjB,EAAoB;AAChB,iBAAKA,KAAL,GAAa,CAAb;AACH,WAFD,MAEO,IAAI,KAAKA,KAAL,GAAa,KAAKC,KAAtB,EAA6B;AAChC,iBAAKD,KAAL,GAAa,KAAKC,KAAlB;AACH;;AACD,eAAK0G,aAAL;;AACA,eAAKC,OAAL;;AACA,cAAI,CAAC,KAAKjD,MAAV,EAAkB,CACd;AACH;;AACD,iBAAO,IAAP;AACH;;AAEDiD,QAAAA,OAAO,GAAG;AACN,cAAI,KAAK5G,KAAL,IAAc,CAAlB,EAAqB;AACjB,iBAAK6G,MAAL;;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAEDA,QAAAA,MAAM,GAAG;AACL,cAAI,CAAC,MAAMC,KAAN,EAAL,EAAoB;AAChB,mBAAO,KAAP;AACH;;AACD,eAAKzF,eAAL,GAAuB,KAAvB;;AAEA,cAAI,KAAKhC,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB0H,IAAnB;;AACA,iBAAK1H,aAAL,GAAqB,IAArB;AACH;;AAED,eAAK2H,KAAL;AAEA,eAAKtG,SAAL,CAAe;AAAA;AAAA,oCAASH,UAAT,CAAoB4B,KAAnC;;AACA,eAAK8E,YAAL;AACH;;AAEDD,QAAAA,KAAK,GAAG;AACJ,eAAK,MAAME,KAAX,IAAoB;AAAA;AAAA,kCAAQC,YAAR,CAAqBC,MAAzC,EAAiD;AAC7CF,YAAAA,KAAK,CAACG,GAAN,CAAU;AAAA;AAAA,sCAASC,gBAAT,CAA0BC,GAApC;AACH;;AACD,eAAKC,aAAL;AACH;;AAEDP,QAAAA,YAAY,GAAS;AACjB;AACA,eAAKjI,YAAL,GAAoB,IAApB;AACH;;AAED2H,QAAAA,aAAa,GAAG;AACZ,gBAAMc,OAAO,GAAG,KAAKzH,KAAL,GAAa,KAAKC,KAAlC;AACA,gBAAMyH,YAAY,GAAGD,OAAO,GAAG,KAAKE,KAAL,CAAYC,SAA3C,CAFY,CAIZ;;AACA,eAAKD,KAAL,CAAYC,SAAZ,GAAwBH,OAAxB,CALY,CAOZ;;AACA,cAAI,KAAKpI,aAAT,EAAwB;AACpB,iBAAKA,aAAL,CAAmB0H,IAAnB;;AACA,iBAAK1H,aAAL,GAAqB,IAArB;AACH,WAXW,CAaZ;;;AACA,cAAIqI,YAAJ,EAAkB;AACd,kBAAMG,QAAQ,GAAGlC,IAAI,CAACI,GAAL,CAAS,KAAK+B,OAAL,CAAcF,SAAd,GAA0B,KAAKD,KAAL,CAAYC,SAA/C,CAAjB;AACA,iBAAKvI,aAAL,GAAqBhD,KAAK,CAAC,KAAKyL,OAAN,CAAL,CAChBC,EADgB,CACbF,QADa,EACH;AAAED,cAAAA,SAAS,EAAE,KAAKD,KAAL,CAAYC;AAAzB,aADG,EAEhBI,IAFgB,CAEX,MAAM;AACR,mBAAK3I,aAAL,GAAqB,IAArB;AACH,aAJgB,EAKhB4I,KALgB,EAArB;AAMH,WARD,MAQO;AACH,iBAAKH,OAAL,CAAcF,SAAd,GAA0BH,OAA1B;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIS,QAAAA,SAAS,CAACC,MAAD,EAAc;AACnB,cAAI,KAAK7I,OAAT,EAAkB;AACd,iBAAKA,OAAL,CAAa2D,IAAb,CAAkBkF,MAAlB;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,YAAY,CAACD,MAAD,EAAc;AACtB,cAAI,KAAK7I,OAAT,EAAkB;AACd,kBAAMwD,KAAK,GAAG,KAAKxD,OAAL,CAAa+I,OAAb,CAAqBF,MAArB,CAAd;;AACA,gBAAIrF,KAAK,IAAI,CAAb,EAAgB;AACZ,mBAAKxD,OAAL,CAAagJ,MAAb,CAAoBxF,KAApB,EAA2B,CAA3B;AACH;AACJ;AACJ;AAED;AACJ;AACA;;;AACI0E,QAAAA,aAAa,GAAG;AACZ,eAAK,MAAMW,MAAX,IAAqB,KAAK7I,OAA1B,EAAmC;AAC/B6I,YAAAA,MAAM,CAACI,SAAP;AACH;;AACD,eAAKjJ,OAAL,CAAagJ,MAAb,CAAoB,CAApB;AACH;;AA3nB4C,O;;;;;iBAGf,I;;;;;;;iBAGR,I;;;;;;;iBAEC,I;;;;;;;iBAEE,I", "sourcesContent": ["import { _decorator, Component, instantiate, Node, size, Sprite, Tween, tween, UITransform, v3, Vec2, Vec3 } from 'cc';\r\nimport TrackComponent from '../../base/TrackComponent';\r\nimport { GameEnum } from '../../../const/GameEnum';\r\nimport { Tools } from '../../../utils/Tools';\r\nimport { GameIns } from '../../../GameIns';\r\nimport { TrackGroup } from '../../../data/EnemyWave';\r\nimport { BossData } from '../../../data/BossData';\r\nimport PlaneBase from '../PlaneBase';\r\nimport EnemyPlaneRole from '../enemy/EnemyPlaneRole';\r\nimport FBoxCollider from '../../../collider-system/FBoxCollider';\r\nimport FCollider, { ColliderGroupType } from '../../../collider-system/FCollider';\r\nimport Bullet from '../../bullet/Bullet';\r\nimport { AttributeData } from 'db://assets/bundles/common/script/data/base/AttributeData';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass(\"BossPlane\")\r\nexport default class BossPlane extends PlaneBase {\r\n\r\n    @property(EnemyPlaneRole)\r\n    role: EnemyPlaneRole | null = null;\r\n\r\n    @property(Sprite)\r\n    hpBg: Sprite | null = null;\r\n    @property(Sprite)\r\n    hpSpr: Sprite | null = null;\r\n    @property(Sprite)\r\n    hpWhite: Sprite | null = null;\r\n\r\n    _datas: BossData[] = [];\r\n    _data: BossData | null = null;\r\n    _trackCom: TrackComponent | null = null;\r\n\r\n\r\n    _idleName: string = \"idle1\";\r\n    _formIndex: number = -1;//形态索引\r\n    _formNum: number = 0;//形态数量\r\n\r\n    _posX: number = 0;\r\n    _posY: number = 0;\r\n\r\n    _moveToX: number = 0;\r\n    _moveToY: number = 0;\r\n    _moveSpeed: number = 0;\r\n    _bArriveDes: boolean = false;//是否达到目标点\r\n    _transFormMove: boolean = false;\r\n    //下一个航点\r\n    _nextWayPointTime: number = 0;\r\n    _nextWayPointX: number = 0;\r\n    _nextWayPointY: number = 0;\r\n    _nextWayPointInterval: number = 0;\r\n    _nextWaySpeed: number = 0;\r\n    _shootAble: boolean = true;\r\n    _atkActions: any[] = [];\r\n\r\n    _bOrderAttack: boolean = false;\r\n    _orderIndex: number = 0;\r\n    _orderAtkArr: number[] = [];\r\n\r\n    _atkPointDatas: any[] = [];\r\n\r\n    _action: number = -1;\r\n    _bDamageable: boolean = false;\r\n    _bAttackMove: boolean = false;\r\n    _bFirstWayPoint: boolean = false;\r\n    transformBattle: boolean = true;\r\n    _bRemoveable: boolean = false;\r\n    // _shadow: any = null;\r\n    // wingmanPlanes: any[] = [];\r\n    // _cloakeAnim: PfFrameAnim | null = null;\r\n\r\n    _nextAttackInterval: number = 0;\r\n    _nextAttackTime: number = 0;\r\n    _attackID: number = 0;\r\n\r\n    tip: string = \"\";\r\n    _hpWhiteTween: any = null;\r\n    bullets: any[] = [];\r\n\r\n    /**\r\n     * 初始化 Boss 数据\r\n     * @param datas Boss 数据数组\r\n     */\r\n    initBoss(datas: BossData[]) {\r\n        super.init();\r\n        this._datas = datas;\r\n        this._formNum = this._datas.length;\r\n        this._bFirstWayPoint = true;\r\n        this._initUI();\r\n        this._initProperty();\r\n        this._initTrack();\r\n        this._initCollide();\r\n        this.setFormIndex(0);\r\n    }\r\n\r\n    /**\r\n * 初始化 UI\r\n */\r\n    _initUI() {\r\n\r\n    }\r\n\r\n    _initProperty(){\r\n        //暂时写死，后续读取新配表\r\n        this.curHp = 4500;\r\n        this.maxHp = this.curHp;\r\n        this.attack = 60;\r\n    }\r\n\r\n    _initTrack() {\r\n        this._trackCom = Tools.addScript(this.node, TrackComponent);\r\n        this._trackCom!.setTrackGroupStartCall(() => { });\r\n        this._trackCom!.setTrackGroupOverCall(() => {\r\n            if (this._action === GameEnum.BossAction.Appear) {\r\n                this._trackCom!.setTrackAble(false);\r\n                this.setAction(GameEnum.BossAction.Transform);\r\n            }\r\n        });\r\n        this._trackCom!.setTrackOverCall(() => { });\r\n        this._trackCom!.setTrackLeaveCall(() => { });\r\n        this._trackCom!.setTrackStartCall((track: any) => {\r\n\r\n        });\r\n    }\r\n\r\n    _initCollide(): void {\r\n        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);\r\n        this.collideComp!.init(this, size(100, 100)); // 初始化碰撞组件\r\n        this.collideComp!.groupType = ColliderGroupType.ENEMY_NORMAL;\r\n        this.colliderEnabled = false;\r\n    }\r\n\r\n    setCollideAble(isEnabled: boolean) {\r\n        this.collideComp!.isEnable = isEnabled;\r\n    }\r\n\r\n    setAction(action: number) {\r\n        if (this._action !== action) {\r\n            this._action = action;\r\n\r\n            let BossAction = GameEnum.BossAction;\r\n            switch (this._action) {\r\n                case BossAction.Normal:\r\n                    this._playSkel(this._idleName, true, () => { });\r\n                    this.setDamangeable(true);\r\n                    break;\r\n\r\n                case BossAction.Appear:\r\n                    this._playSkel(`enter${this._formIndex + 1}`, true, () => { });\r\n                    this.setDamangeable(false);\r\n                    this._startAppearTrack();\r\n                    break;\r\n\r\n                case BossAction.Transform:\r\n                    this._playSkel(`ready${this._formIndex + 1}`, false, () => {\r\n                        this.transformBattle && this.transformEnd();\r\n                    });\r\n                    this.setDamangeable(false);\r\n                    break;\r\n\r\n                case BossAction.AttackPrepare:\r\n                    this.scheduleOnce(() => {\r\n                        this.setAction(BossAction.AttackIng);\r\n                    });\r\n                    this.setDamangeable(true);\r\n                    break;\r\n\r\n                case BossAction.AttackIng:\r\n                case BossAction.AttackOver:\r\n                    this.setDamangeable(true);\r\n                    break;\r\n\r\n                case BossAction.Blast:\r\n                    this.setDamangeable(false);\r\n                    break;\r\n\r\n                default:\r\n                    this.setDamangeable(true);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n * 设置是否可被攻击\r\n * @param damageable 是否可被攻击\r\n */\r\n    setDamangeable(damageable: boolean) {\r\n        this._bDamageable = damageable;\r\n    }\r\n\r\n\r\n    _playSkel(animName: string, loop: boolean, callback: Function) {\r\n        this.role!.playAnim(animName, loop, callback)\r\n    }\r\n\r\n    /**\r\n     * 设置提示信息\r\n     * @param tip 提示信息\r\n     */\r\n    setTip(tip: string) {\r\n        this.tip = tip;\r\n    }\r\n    /**\r\n* 变形结束\r\n*/\r\n    transformEnd() {\r\n        if (this.tip !== \"\") {\r\n            GameIns.battleManager.bossChangeFinish(this.tip);\r\n        } else {\r\n            GameIns.battleManager.bossFightStart();\r\n        }\r\n    }\r\n\r\n\r\n    /**\r\n     * 设置形态索引\r\n     * @param index 形态索引\r\n     */\r\n    setFormIndex(index: number) {\r\n        if (this._formIndex !== index) {\r\n            this._formIndex = index;\r\n            this._bOrderAttack = true;\r\n            this._orderIndex = 0;\r\n            this._data = this._datas[this._formIndex];\r\n            this._idleName = `idle${this._formIndex + 1}`;\r\n\r\n            if (index === 0) {\r\n                this.setAction(GameEnum.BossAction.Appear);\r\n            }\r\n\r\n            this._orderAtkArr = [];\r\n            for (let i = 0; i < this._data.attackActions.length; i++) {\r\n                this._orderAtkArr.push(i);\r\n            }\r\n\r\n            this._atkPointDatas = [];\r\n            for (const point of this._data.attackPoints) {\r\n                const data = [point.bAvailable, point];\r\n                this._atkPointDatas.push(data);\r\n            }\r\n\r\n            this._atkActions = [...this._data.attackActions];\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 进入下一形态\r\n     */\r\n    enterNextForm() {\r\n        if (this._formIndex < this._datas.length - 1) {\r\n            this._formIndex++;\r\n            this.setFormIndex(this._formIndex);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 开始战斗\r\n     */\r\n    startBattle() {\r\n        this._startNormalTrack();\r\n        this.setAction(GameEnum.BossAction.Normal);\r\n        this.colliderEnabled = true;\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    updateGameLogic(deltaTime: number) {\r\n        if (!this.isDead) {\r\n            let BossAction = GameEnum.BossAction;\r\n            switch (this._action) {\r\n                case BossAction.Normal:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    this._updateMove(deltaTime);\r\n                    this._processNextAttack(deltaTime);\r\n                    break;\r\n\r\n                case BossAction.Appear:\r\n                    this._updateMove(deltaTime);\r\n                    if (this._bArriveDes) {\r\n                        this.setAction(BossAction.Transform);\r\n                    }\r\n                    break;\r\n\r\n                case BossAction.Transform:\r\n                    if (this._transFormMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    break;\r\n\r\n                case BossAction.AttackPrepare:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    if (this._bAttackMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    break;\r\n\r\n                case BossAction.AttackIng:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    if (this._bAttackMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    this._udpateShoot(deltaTime);\r\n                    break;\r\n\r\n                case BossAction.AttackOver:\r\n                    this._processNextWayPoint(deltaTime);\r\n                    if (this._bAttackMove) {\r\n                        this._updateMove(deltaTime);\r\n                    }\r\n                    this.setAction(BossAction.Normal);\r\n                    break;\r\n\r\n                case BossAction.Blast:\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 是否可被攻击\r\n     */\r\n    isDamageable(): boolean {\r\n        return this._bDamageable;\r\n    }\r\n\r\n    /**\r\n     * 是否可移除\r\n     */\r\n    get removeAble(): boolean {\r\n        return this._bRemoveable;\r\n    }\r\n\r\n    set removeAble(value: boolean) {\r\n        this._bRemoveable = value;\r\n    }\r\n\r\n\r\n    /**\r\n     * 开始出现轨迹\r\n     */\r\n    _startAppearTrack() {\r\n        const trackGroup = new TrackGroup();\r\n        trackGroup.loopNum = 1;\r\n        trackGroup.trackIDs = [this._data!.appearParam[2]];\r\n        trackGroup.speeds = [this._data!.appearParam[3]];\r\n        trackGroup.trackIntervals = [0];\r\n\r\n        this._trackCom!.init(this, [trackGroup], [], this._data!.appearParam[0], this._data!.appearParam[1]);\r\n        this._trackCom!.setTrackAble(true);\r\n        this._trackCom!.startTrack();\r\n    }\r\n\r\n    /**\r\n     * 开始正常轨迹\r\n     */\r\n    _startNormalTrack() {\r\n        this._trackCom!.init(this, this._data!.trackGroups, [], this.node.x, this.node.y);\r\n        this._trackCom!.setTrackAble(true);\r\n        this._trackCom!.startTrack();\r\n        this.setAction(GameEnum.BossAction.Normal);\r\n    }\r\n\r\n\r\n\r\n    /**\r\n     * 移动到指定位置\r\n     * @param x X 坐标\r\n     * @param y Y 坐标\r\n     * @param speed 移动速度\r\n     * @param transformMove 是否为变形移动\r\n     */\r\n    moveToPos(x: number, y: number, speed: number, transformMove: boolean = false) {\r\n        this._moveToX = x;\r\n        this._moveToY = y;\r\n        this._moveSpeed = speed;\r\n        this._bArriveDes = false;\r\n        this._transFormMove = transformMove;\r\n    }\r\n\r\n\r\n    setPos(x: number, y: number, update: boolean = true) {\r\n        this.node.setPosition(x, y);\r\n        this._posX = x;\r\n        this._posY = y;\r\n    }\r\n\r\n    /**\r\n     * 处理下一个路径点\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    _processNextWayPoint(deltaTime: number) {\r\n        if (this._bArriveDes && this._data!.trackGroups.length === 0) {\r\n            this._nextWayPointTime += deltaTime;\r\n            if (this._nextWayPointTime > this._nextWayPointInterval) {\r\n                this._nextWayPointInterval = Tools.getRandomInArray(this._data!.wayPointIntervals)!;\r\n                this._nextWayPointTime = 0;\r\n\r\n                if (this._bFirstWayPoint) {\r\n                    this._bFirstWayPoint = false;\r\n                } else {\r\n                    const index = Tools.random_int(0, this._data!.wayPointXs.length - 1);\r\n                    this._nextWayPointX = this._data!.wayPointXs[index];\r\n                    this._nextWayPointY = this._data!.wayPointYs[index];\r\n                    this._nextWaySpeed = Tools.getRandomInArray(this._data!.speeds)!;\r\n                    this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    _updateMove(deltaTime: number) {\r\n        if (this._action === GameEnum.BossAction.Appear || this._data!.trackGroups.length > 0) {\r\n            // 如果 Boss 在出现阶段或有轨迹组，则更新轨迹逻辑\r\n            this._trackCom!.updateGameLogic(deltaTime);\r\n        } else if (!this._bArriveDes) {\r\n            // 如果未到达目标位置，则更新移动逻辑\r\n            // this._prePosX = this._posX;\r\n            // this._prePosY = this._posY;\r\n\r\n            const deltaX = this._moveToX - this._posX;\r\n            const deltaY = this._moveToY - this._posY;\r\n            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);\r\n\r\n            let moveX = 0;\r\n            let moveY = 0;\r\n\r\n            // 如果距离小于等于移动速度，则直接到达目标点\r\n            if (distance <= this._moveSpeed) {\r\n                moveX = deltaX;\r\n                moveY = deltaY;\r\n            }\r\n            // 否则按比例移动\r\n            else {\r\n                moveX = this._moveSpeed * deltaX / distance;\r\n                moveY = this._moveSpeed * deltaY / distance;\r\n            }\r\n\r\n            // 更新位置\r\n            this._posX += moveX;\r\n            this._posY += moveY;\r\n            this.setPos(this._posX, this._posY);\r\n\r\n            // 检查是否到达目的地（当移动量很小时认为已到达）\r\n            this._bArriveDes = (Math.abs(moveX) < 0.5 && Math.abs(moveY) < 0.5);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 处理下一次攻击\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    _processNextAttack(deltaTime: number) {\r\n        // if (this._shootAble && this._action === GameEnum.BossAction.Normal) {\r\n        //     this._nextAttackTime += deltaTime;\r\n        //     if (this._nextAttackTime > this._nextAttackInterval) {\r\n        //         this._nextAttackInterval = Tools.getRandomInArray(this._data!.attackIntervals)!;\r\n        //         this._nextAttackTime = 0;\r\n\r\n        //         let attackAction = null;\r\n        //         if (this._bOrderAttack) {\r\n        //             const randomIndex = Tools.getRandomInArray(this._orderAtkArr)!;\r\n        //             Tools.arrRemove(this._orderAtkArr, randomIndex);\r\n        //             attackAction = this._atkActions[randomIndex];\r\n        //             this._orderIndex++;\r\n        //             if (this._orderIndex > this._atkActions.length - 1) {\r\n        //                 this._bOrderAttack = false;\r\n        //             }\r\n        //         } else {\r\n        //             attackAction = Tools.getRandomInArray(this._atkActions);\r\n        //         }\r\n\r\n        //         if (attackAction) {\r\n        //             this._bAttackMove = attackAction.bAtkMove;\r\n        //             this._attackID = attackAction.atkActId;\r\n        //             this._attackPoints.splice(0);\r\n\r\n        //             for (const pointId of attackAction.atkPointId) {\r\n        //                 const pointData = this._atkPointDatas[pointId];\r\n        //                 if (pointData[0]) {\r\n        //                     let attackPoint = this._atkPointsPool[pointId]\r\n        //                     if (!attackPoint) {\r\n        //                         const pointNode = new Node();\r\n        //                         this.node.addChild(pointNode);\r\n        //                         attackPoint = pointNode.addComponent(AttackPoint);\r\n        //                         this._atkPointsPool.push(attackPoint);\r\n        //                     }\r\n        //                     attackPoint.initForBoss(pointData[1], this);\r\n        //                     this._attackPoints.push(attackPoint);\r\n        //                 }\r\n        //             }\r\n\r\n        //             if (this._attackPoints.length > 0) {\r\n        //                 this.setAction(GameEnum.BossAction.AttackPrepare);\r\n        //             }\r\n        //         }\r\n        //     }\r\n        // }\r\n    }\r\n\r\n    /**\r\n     * 更新射击逻辑\r\n     * @param deltaTime 每帧时间\r\n     */\r\n    async _udpateShoot(deltaTime: number) {\r\n        // if (this._shootAble) {\r\n        //     let allAttacksOver = true;\r\n\r\n        //     for (const attackPoint of this._attackPoints) {\r\n        //         await attackPoint.updateGameLogic(deltaTime);\r\n        //         if (!attackPoint.isAttackOver()) {\r\n        //             allAttacksOver = false;\r\n        //         }\r\n        //     }\r\n\r\n        //     if (allAttacksOver) {\r\n        //         this.setAction(GameEnum.BossAction.AttackOver);\r\n        //     }\r\n        // }\r\n    }\r\n\r\n\r\n    onCollide(collider: FCollider): void {\r\n        if (!this.isDead && this.isDamageable()) {\r\n            if (collider.entity instanceof Bullet) {\r\n                let damage = collider.entity.getAttack();\r\n                try {\r\n                    GameIns.hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), damage);\r\n                } catch (error) {\r\n                    console.error(error);\r\n                }\r\n                this.hurt(-damage);\r\n            }\r\n        }\r\n    }\r\n\r\n    hurt(damage: number): boolean {\r\n        if (this.isDead || !this.isDamageable()) {\r\n            return false;\r\n        }\r\n\r\n        this.curHp += damage;\r\n        if (this.curHp < 0) {\r\n            this.curHp = 0;\r\n        } else if (this.curHp > this.maxHp) {\r\n            this.curHp = this.maxHp;\r\n        }\r\n        this._refreshHpBar();\r\n        this.checkHp();\r\n        if (!this.isDead) {\r\n            // this.role.winkWhite();\r\n        }\r\n        return true;\r\n    }\r\n\r\n    checkHp() {\r\n        if (this.curHp <= 0) {\r\n            this._toDie();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    _toDie() {\r\n        if (!super.toDie()) {\r\n            return false;\r\n        }\r\n        this.colliderEnabled = false;\r\n\r\n        if (this._hpWhiteTween) {\r\n            this._hpWhiteTween.stop();\r\n            this._hpWhiteTween = null;\r\n        }\r\n\r\n        this.onDie();\r\n\r\n        this.setAction(GameEnum.BossAction.Blast);\r\n        this._playDieAnim();\r\n    }\r\n\r\n    onDie() {\r\n        for (const plane of GameIns.enemyManager.planes) {\r\n            plane.die(GameEnum.EnemyDestroyType.Die);\r\n        }\r\n        this.removeBullets();\r\n    }\r\n\r\n    _playDieAnim(): void {\r\n        // this._skel.setAnimation(0, `shake${this._bossPlane.formIndex + 1}`, false);\r\n        this._bRemoveable = true;\r\n    }\r\n\r\n    _refreshHpBar() {\r\n        const hpRatio = this.curHp / this.maxHp;\r\n        const isDecreasing = hpRatio < this.hpSpr!.fillRange;\r\n\r\n        // 更新血条显示\r\n        this.hpSpr!.fillRange = hpRatio;\r\n\r\n        // 停止之前的血条动画\r\n        if (this._hpWhiteTween) {\r\n            this._hpWhiteTween.stop();\r\n            this._hpWhiteTween = null;\r\n        }\r\n\r\n        // 如果血量减少，播放白色血条的动画\r\n        if (isDecreasing) {\r\n            const duration = Math.abs(this.hpWhite!.fillRange - this.hpSpr!.fillRange);\r\n            this._hpWhiteTween = tween(this.hpWhite!)\r\n                .to(duration, { fillRange: this.hpSpr!.fillRange })\r\n                .call(() => {\r\n                    this._hpWhiteTween = null;\r\n                })\r\n                .start();\r\n        } else {\r\n            this.hpWhite!.fillRange = hpRatio;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 添加子弹\r\n     * @param bullet 子弹对象\r\n     */\r\n    addBullet(bullet: any) {\r\n        if (this.bullets) {\r\n            this.bullets.push(bullet);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除子弹\r\n     * @param bullet 子弹对象\r\n     */\r\n    removeBullet(bullet: any) {\r\n        if (this.bullets) {\r\n            const index = this.bullets.indexOf(bullet);\r\n            if (index >= 0) {\r\n                this.bullets.splice(index, 1);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 移除子弹\r\n     */\r\n    removeBullets() {\r\n        for (const bullet of this.bullets) {\r\n            bullet.dieRemove();\r\n        }\r\n        this.bullets.splice(0);\r\n    }\r\n}"]}