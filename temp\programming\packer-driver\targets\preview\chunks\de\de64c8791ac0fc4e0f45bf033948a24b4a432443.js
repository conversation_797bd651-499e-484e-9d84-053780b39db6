System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Sprite, SpriteFrame, EventMgr, PlaneUIEvent, ButtonPlus, TabStatus, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, Tabs;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "db://assets/bundles/common/script/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUIEvent(extras) {
    _reporterNs.report("PlaneUIEvent", "../../../../event/PlaneUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabStatus(extras) {
    _reporterNs.report("TabStatus", "../../PlaneTypes", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Sprite = _cc.Sprite;
      SpriteFrame = _cc.SpriteFrame;
    }, function (_unresolved_2) {
      EventMgr = _unresolved_2.EventMgr;
    }, function (_unresolved_3) {
      PlaneUIEvent = _unresolved_3.PlaneUIEvent;
    }, function (_unresolved_4) {
      ButtonPlus = _unresolved_4.ButtonPlus;
    }, function (_unresolved_5) {
      TabStatus = _unresolved_5.TabStatus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7fd2eQ9BBNEJ55GoNe4x7kE", "Tabs", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'Node', 'Sprite', 'SpriteFrame']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("Tabs", Tabs = (_dec = ccclass('Tabs'), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec4 = property(SpriteFrame), _dec5 = property(SpriteFrame), _dec(_class = (_class2 = class Tabs extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "tabBagBtn", _descriptor, this);

          _initializerDefineProperty(this, "tabMergeBtn", _descriptor2, this);

          _initializerDefineProperty(this, "pressSpriteFrame", _descriptor3, this);

          _initializerDefineProperty(this, "releaseSpriteFrame", _descriptor4, this);

          this._tabStatus = (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).None;
        }

        onLoad() {
          this.tabBagBtn.addClick(this.onClick, this);
          this.tabMergeBtn.addClick(this.onClick, this);
        }

        init() {
          this.setTabStatus(this.tabBagBtn.node);
        }

        onClick(event) {
          var btnNode = event.target;
          this.setTabStatus(btnNode);
        }

        setTabStatus(btnNode) {
          switch (btnNode) {
            case this.tabBagBtn.node:
              this._tabStatus = (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
                error: Error()
              }), TabStatus) : TabStatus).Bag;
              this.tabBagBtn.getComponent(Sprite).spriteFrame = this.pressSpriteFrame;
              this.tabMergeBtn.getComponent(Sprite).spriteFrame = this.releaseSpriteFrame;
              break;

            case this.tabMergeBtn.node:
              this._tabStatus = (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
                error: Error()
              }), TabStatus) : TabStatus).Merge;
              this.tabMergeBtn.getComponent(Sprite).spriteFrame = this.pressSpriteFrame;
              this.tabBagBtn.getComponent(Sprite).spriteFrame = this.releaseSpriteFrame;
              break;

            default:
              //logError("PlaneUI", `Tabs setTabStatus error ${btnNode.name}`)
              return;
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).TabChange, this._tabStatus);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "tabBagBtn", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "tabMergeBtn", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "pressSpriteFrame", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "releaseSpriteFrame", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=de64c8791ac0fc4e0f45bf033948a24b4a432443.js.map