let GameResourceList = {

    MainPlane: "prefabs/mainPlane/MainPlane",
    PrefabBoss: "prefabs/boss/BossPlane",
    FrameAnim: "prefabs/FrameAnim",
    Bullet: "prefabs/Bullet",
    EnemyPlane: "prefabs/enemy/EnemyPlane",
    HurtEffect: "prefabs/HurtEffect",
    HurtNum: "prefabs/HurtNum",
    Hurt0: "prefabs/effect/Hurt",
    EmitterPrefabPath: "prefabs/emitter/",

    font_hurtNum: "font/hurtNum",

    atlas_mainPlane: "texture/mainPlane/package_mainPlane_trans_",

    atlas_enemyBullet: "texture/enemy/enemyBullet",
    atlas_mainBullet: "texture/mainPlane/mainBullet",
    atlas_hurtEffects:"texture/hurtEffect/hurtEffects",
    atlas_enemyBullet1: "texture/enemy/1/enemyBullet1",
    atlas_package_enemy1: "texture/enemy/1/package_enemy1",
    atlas_package_turret1: "texture/enemy/1/package_turret1",
    atlas_boss_unit:"texture/boss/boss_unit",

    texture_map_mask:"texture/mask/mask1/spriteFrame",

    spine_boss_smoke:"spine/skel_boss_smoke",
    spine_mainfire:"spine/mainPlane/firePoint/skel_mainfire",

    GameMap_1:"normal/chapter_1/GameMap_1"
};

// Add "Game/" prefix to all values
(() => {
        for (const key in GameResourceList) {
        GameResourceList[key as keyof typeof GameResourceList] = `Game/${GameResourceList[key as keyof typeof GameResourceList]}`;
    }
})();

export default GameResourceList;