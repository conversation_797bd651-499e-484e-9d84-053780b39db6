{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "Label", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "BundleName", "ccclass", "property", "PlaneShowUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "onShow", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AACZC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;AACRC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;6BAGjBO,W,WADZF,OAAO,CAAC,aAAD,C,UAGHC,QAAQ,CAACL,KAAD,C,UAERK,QAAQ,CAACL,KAAD,C,UAERK,QAAQ,CAACL,KAAD,C,2BAPb,MACaM,WADb;AAAA;AAAA,4BACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAShB,eAANC,MAAM,GAAW;AAAE,iBAAO,uBAAP;AAAiC;;AAC5C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS,CAExB;;AAEKC,QAAAA,MAAM,GAAkB;AAAA;AAE7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AA1BmC,O;;;;;iBAGV,I;;;;;;;iBAEC,I;;;;;;;iBAED,I", "sourcesContent": ["import { _decorator, Label } from 'cc';\r\nimport { BaseUI, UILayer, UIOpt } from '../../../../../scripts/ui/UIMgr';\r\nimport { BundleName } from '../../../../Bundle';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PlaneShowUI')\r\nexport class PlaneShowUI extends BaseUI {\r\n\r\n    @property(Label)\r\n    planeName: Label | null = null;\r\n    @property(Label)\r\n    planePower: Label | null = null;\r\n    @property(Label)\r\n    planeType: Label | null = null;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/PlaneShowUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    public static getUIOption(): UIOpt {\r\n        return { isClickBgCloseUI: false }\r\n    }\r\n\r\n    protected onLoad(): void {\r\n\r\n    }\r\n\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n\r\n}\r\n"]}