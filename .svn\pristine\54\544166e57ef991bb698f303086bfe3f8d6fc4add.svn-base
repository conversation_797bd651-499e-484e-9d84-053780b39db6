import { __private, _decorator, CCFloat, CCInteger, Component, Enum, Node, Prefab } from "cc";
import { LayerType } from "db://assets/scripts/leveldata/leveldata";

const { ccclass, property } = _decorator;

@ccclass('LevelEditorScrollLayerUI')
export class LevelScrollLayerUI { 
    @property({type: Prefab, displayName: '滚动体'})
    public scrollPrefab: Prefab | null = null;

    @property({type: CCInteger, displayName: "权重"})
    public weight: number = 100;
}

@ccclass('LevelEditorRandTerrainUI')
export class LevelRandTerrainUI {
    @property({type: CCInteger, displayName: "权重"})
    public weight: number = 100;

    @property({type: [Prefab], displayName: "地形组预制体"})
    public terrainElements: Prefab | null = null; // 可以是TerrainElem、DynamicTerrains的预制体
}

@ccclass('LevelEditorRandTerrainsLayerUI')
export class LevelRandTerrainsLayerUI {
    @property({type: CCInteger, displayName: "权重"})
    public weight: number = 100;

    @property({type: [LevelRandTerrainUI], displayName: "地形策略"})
    public dynamicTerrains: LevelRandTerrainUI[] = []; 
}

@ccclass('LevelEditorRandTerrainsLayersUI')
export class LevelRandTerrainsLayersUI {
    @property({type: [LevelRandTerrainsLayerUI], displayName: "地形策略组"})
    public dynamicTerrains: LevelRandTerrainsLayerUI[] = []; 
}

@ccclass('LevelEditorLayer')
export class LevelLayer {
    @property(Node)
    public node: Node | null = null;
    @property({type:CCFloat, displayName:"速度"})
    public speed: number = 10;
    @property({type: Enum(LayerType), displayName:"地形类型"})
    public type: LayerType = LayerType.Background;
    @property({ 
        type: [LevelScrollLayerUI], 
        displayName: "滚动组",
        visible: function(this: LevelLayer) {
            return this.type === LayerType.Scroll;
        }
    })
    public scrollLayers: LevelScrollLayerUI[] = [];

    @property({type: [LevelRandTerrainsLayersUI], 
        displayName:"随机组",
        visible: function(this: LevelLayer) {
            return this.type === LayerType.Random;
        }
    })
    public randomLayers: LevelRandTerrainsLayersUI[] = [];
}

@ccclass('LevelEditorBackgroundLayer')
export class LevelBackgroundLayer extends LevelLayer {
    @property({type: [Prefab], displayName: '背景组'})
    public backgrounds: Prefab[] = [];

    public backgroundsNode: Node|null = null;
}

export class LevelEditorUtils {
    public static getOrAddNode(node_parent: Node, name: string): Node {
        var node = node_parent.getChildByName(name);
        if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
        }
        return node;
    }
    public static getOrAddComp<T extends Component>(node: Node, classConstructor: __private.__types_globals__Constructor<T>): T {
        var comp = node.getComponent(classConstructor);
        if (comp == null) {
            comp = node.addComponent(classConstructor);
        }
        return comp;
    }
}

