import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

/**
 * Base interface for all move-able objects
 */
export interface IMovable {
    speed : number;                 // 速度
    speedAngle : number;            // 速度方向 (用角度表示)
    acceleration : number;          // 加速度
    accelerationAngle : number;     // 加速度方向 (用角度表示)
}

export enum eMoveModifier {
    Speed, SpeedAngle, Acceleration, AccelerationAngle
}

export enum eEasing {
    Linear,
    InSine, OutSine, InOutSine,
    InQuad, OutQuad, InOutQuad
}
