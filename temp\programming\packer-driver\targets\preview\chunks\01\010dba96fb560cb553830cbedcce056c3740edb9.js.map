{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts"], "names": ["PK", "csproto", "MyApp", "DataEvent", "EventMgr", "self_info", "other_info", "get_list", "award_info", "init", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GAME_PVP_MATCH", "onGamePvpMatch", "CS_CMD_GAME_PVP_START", "onGamePvpStart", "CS_CMD_GAME_PVP_CANCEL", "onGamePvpCancel", "CS_CMD_GAME_PVP_END", "onGamePvpEnd", "CS_CMD_GAME_PVP_GET_AWARD", "onGamePvpGetAward", "CS_CMD_GAME_PVP_GET_INFO", "onGamePvpGetInfo", "CS_CMD_GAME_PVP_GET_LIST", "onGamePvpGetList", "msg", "body", "game_pvp_match", "ret_code", "comm", "RET_CODE", "RET_CODE_NO_ERROR", "emit", "GamePvpMatchSuc", "game_pvp_start", "info", "game_pvp_cancel", "reason", "MATCH_CANCEL_REASON", "MATCH_CANCEL_REASON_USER_CANCEL", "MATCH_CANCEL_REASON_MATCH_TIMEOUT", "MATCH_CANCEL_REASON_MATCH_RETRY", "MATCH_CANCEL_REASON_MATCH_STOP", "game_pvp_end", "result", "pvp_result", "res", "result_code", "GAME_PVP_RESULT", "GAME_PVP_RESULT_WIN", "GAME_PVP_RESULT_LOSE", "GAME_PVP_RESULT_DRAW", "GAME_PVP_RESULT_CANCEL", "game_pvp_get_award", "GamePvpGetAward", "game_pvp_get_info", "status", "GAME_PVP_STATUS", "GAME_PVP_STATUS_IN_MATCH", "game_pvp_get_list", "list", "GamePvpGetList", "update"], "mappings": ";;;mEAMaA,E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALNC,MAAAA,O;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;oBAEIJ,E,GAAN,MAAMA,EAAN,CAA0B;AAAA;AAAA,eAC7BK,SAD6B;AAAA,eAE7BC,UAF6B;AAAA,eAG7BC,QAH6B;AAAA,eAI7BC,UAJ6B;AAAA;;AAKtBC,QAAAA,IAAI,GAAS;AAChB;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,qBAA/C,EAAsE,KAAKC,cAA3E,EAA2F,IAA3F;AACA;AAAA;AAAA,8BAAML,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBG,qBAA/C,EAAsE,KAAKC,cAA3E,EAA2F,IAA3F;AACA;AAAA;AAAA,8BAAMP,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBK,sBAA/C,EAAuE,KAAKC,eAA5E,EAA6F,IAA7F;AACA;AAAA;AAAA,8BAAMT,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBO,mBAA/C,EAAoE,KAAKC,YAAzE,EAAuF,IAAvF;AACA;AAAA;AAAA,8BAAMX,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBS,yBAA/C,EAA0E,KAAKC,iBAA/E,EAAkG,IAAlG;AACA;AAAA;AAAA,8BAAMb,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBW,wBAA/C,EAAyE,KAAKC,gBAA9E,EAAgG,IAAhG;AACA;AAAA;AAAA,8BAAMf,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBa,wBAA/C,EAAyE,KAAKC,gBAA9E,EAAgG,IAAhG;AACH;;AACDZ,QAAAA,cAAc,CAACa,GAAD,EAAgC;AAC1C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASC,cAA3B,EAA2C;AACvC;AACH;;AACD,cAAIF,GAAG,CAACG,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D,CAC5D,CADD,MACO;AACH;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,wCAAUC,eAAxB;AACH;AACJ;;AAEDnB,QAAAA,cAAc,CAACW,GAAD,EAAgC;AAAA;;AAC1C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASQ,cAA3B,EAA2C;AACvC;AACH;;AACD,eAAKhC,SAAL,4BAAiBuB,GAAG,CAACC,IAAJ,CAASQ,cAAT,CAAwBC,IAAzC,qBAAiB,sBAA8BjC,SAA/C;AACA,eAAKC,UAAL,6BAAkBsB,GAAG,CAACC,IAAJ,CAASQ,cAAT,CAAwBC,IAA1C,qBAAkB,uBAA8BhC,UAAhD;AACH;;AAEDa,QAAAA,eAAe,CAACS,GAAD,EAAgC;AAC3C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASU,eAA3B,EAA4C;AACxC;AACH;;AACD,cAAIC,MAAM,GAAGZ,GAAG,CAACC,IAAJ,CAASU,eAAT,CAAyBC,MAAtC;;AACA,kBAAQA,MAAR;AACI,iBAAK;AAAA;AAAA,oCAAQ5B,EAAR,CAAW6B,mBAAX,CAA+BC,+BAApC;AACI;;AACJ,iBAAK;AAAA;AAAA,oCAAQ9B,EAAR,CAAW6B,mBAAX,CAA+BE,iCAApC;AACI;;AACJ,iBAAK;AAAA;AAAA,oCAAQ/B,EAAR,CAAW6B,mBAAX,CAA+BG,+BAApC;AACI;;AACJ,iBAAK;AAAA;AAAA,oCAAQhC,EAAR,CAAW6B,mBAAX,CAA+BI,8BAApC;AACI;AARR;AAUH;;AAEDxB,QAAAA,YAAY,CAACO,GAAD,EAAgC;AACxC,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASiB,YAA3B,EAAyC;AACrC;AACH;;AACD,cAAIC,MAAM,GAAGnB,GAAG,CAACC,IAAJ,CAASiB,YAAT,CAAsBE,UAAnC;;AACA,cAAID,MAAJ,EAAY;AAAA;;AACR,gBAAIE,GAAG,4BAAGrB,GAAG,CAACC,IAAJ,CAASiB,YAAT,CAAsBE,UAAzB,qBAAG,sBAAkCE,WAA5C;;AACA,oBAAQD,GAAR;AACI,mBAAK;AAAA;AAAA,sCAAQjB,IAAR,CAAamB,eAAb,CAA6BC,mBAAlC;AACI;;AACJ,mBAAK;AAAA;AAAA,sCAAQpB,IAAR,CAAamB,eAAb,CAA6BE,oBAAlC;AACI;;AACJ,mBAAK;AAAA;AAAA,sCAAQrB,IAAR,CAAamB,eAAb,CAA6BG,oBAAlC;AACI;;AACJ,mBAAK;AAAA;AAAA,sCAAQtB,IAAR,CAAamB,eAAb,CAA6BI,sBAAlC;AACI;AARR;AAUH;AACJ;;AAEDhC,QAAAA,iBAAiB,CAACK,GAAD,EAAgC;AAC7C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAAS2B,kBAA3B,EAA+C;AAC3C;AACH;;AACD,cAAI5B,GAAG,CAACG,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D,CAC5D;;AACD,eAAK1B,UAAL,GAAkBoB,GAAG,CAACC,IAAJ,CAAS2B,kBAAT,CAA4BhD,UAA9C;AACA;AAAA;AAAA,oCAAS2B,IAAT,CAAc;AAAA;AAAA,sCAAUsB,eAAxB;AACH;;AAEDhC,QAAAA,gBAAgB,CAACG,GAAD,EAAgC;AAC5C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAAS6B,iBAA3B,EAA8C;AAC1C;AACH;;AACD,cAAMpB,IAAI,GAAGV,GAAG,CAACC,IAAJ,CAAS6B,iBAAT,CAA2BpB,IAAxC;AACA,cAAMqB,MAAM,GAAG/B,GAAG,CAACC,IAAJ,CAAS6B,iBAAT,CAA2BC,MAA1C;;AACA,cAAIA,MAAM,IAAI;AAAA;AAAA,kCAAQ3B,IAAR,CAAa4B,eAAb,CAA6BC,wBAA3C,EAAqE,CAEpE;;AACD,cAAIjC,GAAG,CAACG,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D,CAC5D;AACJ,SA1F4B,CA4F7B;;;AACAP,QAAAA,gBAAgB,CAACC,GAAD,EAAgC;AAC5C,cAAI,CAACA,GAAG,CAACC,IAAL,IAAa,CAACD,GAAG,CAACC,IAAJ,CAASiC,iBAA3B,EAA8C;AAC1C;AACH;;AAED,cAAIlC,GAAG,CAACG,QAAJ,IAAgB;AAAA;AAAA,kCAAQC,IAAR,CAAaC,QAAb,CAAsBC,iBAA1C,EAA6D;AACzD;AACH;;AACD,eAAK3B,QAAL,GAAgBqB,GAAG,CAACC,IAAJ,CAASiC,iBAAT,CAA2BC,IAA3C;AACA;AAAA;AAAA,oCAAS5B,IAAT,CAAc;AAAA;AAAA,sCAAU6B,cAAxB;AACH;;AAEMC,QAAAA,MAAM,GAAS,CACrB;;AA1G4B,O", "sourcesContent": ["\nimport csproto, { comm } from '../../../../../scripts/AutoGen/PB/cs_proto.js';\nimport { MyApp } from '../../../../../scripts/MyApp';\nimport { DataEvent } from '../../event/DataEvent';\nimport { EventMgr } from '../../event/EventManager';\nimport { IData } from \"../DataManager\";\nexport class PK implements IData {\n    self_info?: (comm.IGamePvpInfoSide | null);\n    other_info?: (comm.IGamePvpInfoSide | null);\n    get_list?: (comm.IGamePvpHistory[] | null);\n    award_info?: (comm.IGameAwardInfo | null);\n    public init(): void {\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_MATCH, this.onGamePvpMatch, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_START, this.onGamePvpStart, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_CANCEL, this.onGamePvpCancel, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_END, this.onGamePvpEnd, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_AWARD, this.onGamePvpGetAward, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_INFO, this.onGamePvpGetInfo, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_LIST, this.onGamePvpGetList, this);\n    }\n    onGamePvpMatch(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_match) {\n            return;\n        }\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n        } else {\n            EventMgr.emit(DataEvent.GamePvpMatchSuc);\n        }\n    }\n\n    onGamePvpStart(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_start) {\n            return;\n        }\n        this.self_info = msg.body.game_pvp_start.info?.self_info;\n        this.other_info = msg.body.game_pvp_start.info?.other_info;\n    }\n\n    onGamePvpCancel(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_cancel) {\n            return;\n        }\n        let reason = msg.body.game_pvp_cancel.reason;\n        switch (reason) {\n            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_USER_CANCEL:\n                break;\n            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_TIMEOUT:\n                break;\n            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_RETRY:\n                break;\n            case csproto.cs.MATCH_CANCEL_REASON.MATCH_CANCEL_REASON_MATCH_STOP:\n                break;\n        }\n    }\n\n    onGamePvpEnd(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_end) {\n            return;\n        }\n        let result = msg.body.game_pvp_end.pvp_result;\n        if (result) {\n            let res = msg.body.game_pvp_end.pvp_result?.result_code;\n            switch (res) {\n                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_WIN:\n                    break;\n                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_LOSE:\n                    break;\n                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_DRAW:\n                    break;\n                case csproto.comm.GAME_PVP_RESULT.GAME_PVP_RESULT_CANCEL:\n                    break;\n            }\n        }\n    }\n\n    onGamePvpGetAward(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_get_award) {\n            return;\n        }\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n        }\n        this.award_info = msg.body.game_pvp_get_award.award_info;\n        EventMgr.emit(DataEvent.GamePvpGetAward)\n    }\n\n    onGamePvpGetInfo(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_get_info) {\n            return;\n        }\n        const info = msg.body.game_pvp_get_info.info!\n        const status = msg.body.game_pvp_get_info.status!\n        if (status == csproto.comm.GAME_PVP_STATUS.GAME_PVP_STATUS_IN_MATCH) {\n\n        }\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n        }\n    }\n\n    //登录成功后，拉取PVP列表\n    onGamePvpGetList(msg: csproto.cs.IS2CMsg): void {\n        if (!msg.body || !msg.body.game_pvp_get_list) {\n            return;\n        }\n\n        if (msg.ret_code != csproto.comm.RET_CODE.RET_CODE_NO_ERROR) {\n            return;\n        }\n        this.get_list = msg.body.game_pvp_get_list.list!\n        EventMgr.emit(DataEvent.GamePvpGetList)\n    }\n\n    public update(): void {\n    }\n}\n"]}