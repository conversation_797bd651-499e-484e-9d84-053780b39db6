System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, BottomTab;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "bd511B0jetL+bY2eM7KTaHc", "BottomTab", undefined);

      _export("BottomTab", BottomTab = /*#__PURE__*/function (BottomTab) {
        BottomTab[BottomTab["Shop"] = 0] = "Shop";
        BottomTab[BottomTab["Talent"] = 1] = "Talent";
        BottomTab[BottomTab["Home"] = 2] = "Home";
        BottomTab[BottomTab["Plane"] = 3] = "Plane";
        BottomTab[BottomTab["SkyIsLand"] = 4] = "SkyIsLand";
        return BottomTab;
      }({}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=cb238d9203430c378d7532a262d665f2f844cb27.js.map