{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneEquipInfoUI.ts"], "names": ["_decorator", "Label", "logDebug", "MyApp", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "DataMgr", "EventMgr", "ButtonPlus", "OpenEquipInfoUISource", "ccclass", "property", "PlaneEquipInfoUI", "_planeEquipInfo", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "PopUp", "getBundleName", "HomePlane", "getUIOption", "isClickBgCloseUI", "onLoad", "levelUpEquipBtn", "addClick", "onClickLevelUpEquip", "multiLevelUpEquipBtn", "onClickMultiLevelUpEquip", "unEquipBtn", "onClickUnEquip", "replaceEquipBtn", "onClickReplaceEquip", "onShow", "planeEquipInfo", "source", "tbEquip", "lubanMgr", "table", "TbEquip", "DisPlay", "node", "active", "parent", "slot", "equip", "eqSlots", "getEmptySlotByClass", "get", "item_id", "equipClass", "getComponentInChildren", "string", "lubanTables", "name", "unequip", "guid", "hideUI", "onHide", "onClose", "targetOff", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AAGZC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,qB,iBAAAA,qB;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBb,U;;kCAGjBc,gB,WADZF,OAAO,CAAC,kBAAD,C,UAUHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,2BAhBb,MACaC,gBADb;AAAA;AAAA,4BAC6C;AAAA;AAAA;AAAA,eAOjCC,eAPiC,GAOY,IAPZ;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AACrB,eAANC,MAAM,GAAW;AAAE,iBAAO,4BAAP;AAAsC;;AACjD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,KAAf;AAAsB;;AAC/B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,SAAlB;AAA8B;;AAC7C,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAYSC,QAAAA,MAAM,GAAS;AACrB,eAAKC,eAAL,CAAsBC,QAAtB,CAA+B,KAAKC,mBAApC,EAAyD,IAAzD;AACA,eAAKC,oBAAL,CAA2BF,QAA3B,CAAoC,KAAKG,wBAAzC,EAAmE,IAAnE;AACA,eAAKC,UAAL,CAAiBJ,QAAjB,CAA0B,KAAKK,cAA/B,EAA+C,IAA/C;AACA,eAAKC,eAAL,CAAsBN,QAAtB,CAA+B,KAAKO,mBAApC,EAAyD,IAAzD;AACH;;AAEKC,QAAAA,MAAM,CAACC,cAAD,EAAqCC,MAArC,EAAmF;AAAA;;AAAA;AAAA;;AAC3F;AAAA;AAAA,sCAAS,SAAT,6BAA6CD,cAA7C,gBAAsEC,MAAtE;AACA,gBAAMC,OAAO,GAAG;AAAA;AAAA,gCAAMC,QAAN,CAAeC,KAAf,CAAqBC,OAArC;;AACA,gBAAIJ,MAAM,IAAI;AAAA;AAAA,gEAAsBK,OAApC,EAA6C;AACzC,cAAA,KAAI,CAACT,eAAL,CAAsBU,IAAtB,CAA2BC,MAA3B,GAAoC,KAApC;AACA,cAAA,KAAI,CAACb,UAAL,CAAiBY,IAAjB,CAAsBE,MAAtB,CAA8BD,MAA9B,GAAuC,IAAvC;AACH,aAHD,MAGO;AAAA;;AACH,cAAA,KAAI,CAACX,eAAL,CAAsBU,IAAtB,CAA2BC,MAA3B,GAAoC,IAApC;AACA,cAAA,KAAI,CAACb,UAAL,CAAiBY,IAAjB,CAAuBE,MAAvB,CAA+BD,MAA/B,GAAwC,KAAxC;AACA,kBAAME,IAAI,GAAG;AAAA;AAAA,sCAAQC,KAAR,CAAcC,OAAd,CAAsBC,mBAAtB,0CAA0CX,OAAO,CAACY,GAAR,CAAYd,cAAc,CAACe,OAA3B,CAA1C,qBAA0C,aAAsCC,UAAhF,oCAA8F,CAA9F,CAAb;;AACA,kBAAI,CAACN,IAAL,EAAW;AACP,gBAAA,KAAI,CAACb,eAAL,CAAsBoB,sBAAtB,CAA6ClD,KAA7C,EAAqDmD,MAArD,GAA8D,IAA9D;AACH,eAFD,MAEO;AACH,gBAAA,KAAI,CAACrB,eAAL,CAAsBoB,sBAAtB,CAA6ClD,KAA7C,EAAqDmD,MAArD,GAA8D,IAA9D;AACH;AACJ;;AACD,YAAA,KAAI,CAACD,sBAAL,CAA4BlD,KAA5B,EAAoCmD,MAApC,GAA6C;AAAA;AAAA,gCAAMC,WAAN,CAAkBd,OAAlB,CAA0BS,GAA1B,CAA8Bd,cAAc,CAACe,OAA7C,4CAAwDK,IAAxD,KAAgE,EAA7G;AACA,YAAA,KAAI,CAACvC,eAAL,GAAuBmB,cAAvB;AAjB2F;AAkB9F;;AAEOF,QAAAA,mBAAmB,GAAG;AAC1B;AAAA;AAAA,kCAAQa,KAAR,CAAcC,OAAd,CAAsBD,KAAtB,CAA4B,KAAK9B,eAAjC;AACH;;AAEOe,QAAAA,cAAc,GAAG;AACrB;AAAA;AAAA,kCAAQe,KAAR,CAAcC,OAAd,CAAsBS,OAAtB,CAA8B,KAAKxC,eAAL,CAAsByC,IAApD;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAa3C,gBAAb;AACH;;AAEOY,QAAAA,mBAAmB,GAAG,CAC1B;AACH;;AACOE,QAAAA,wBAAwB,GAAG,CAC/B;AACH;;AAEK8B,QAAAA,MAAM,GAAgC;AAAA;AAAG;;AACzCC,QAAAA,OAAO,GAAgC;AAAA;;AAAA;AACzC;AAAA;AAAA,sCAASC,SAAT,CAAmB,MAAnB;AADyC;AAE5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAlEwC,O;;;;;iBAUJ,I;;;;;;;iBAEL,I;;;;;;;iBAEK,I;;;;;;;iBAEK,I", "sourcesContent": ["\nimport { _decorator, Label } from 'cc';\n\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { logDebug } from 'db://assets/scripts/Utils/Logger';\nimport { MyApp } from '../../../../../scripts/MyApp';\nimport { BaseUI, UILayer, UIMgr, UIOpt } from '../../../../../scripts/ui/UIMgr';\nimport { BundleName } from '../../../../Bundle';\nimport { DataMgr } from '../../data/DataManager';\nimport { EventMgr } from '../../event/EventManager';\nimport { ButtonPlus } from '../common/components/button/ButtonPlus';\nimport { OpenEquipInfoUISource } from './PlaneTypes';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('PlaneEquipInfoUI')\nexport class PlaneEquipInfoUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/PlaneEquipInfoUI\"; }\n    public static getLayer(): UILayer { return UILayer.PopUp }\n    public static getBundleName(): string { return BundleName.HomePlane; }\n    public static getUIOption(): UIOpt {\n        return { isClickBgCloseUI: true }\n    }\n    private _planeEquipInfo: csproto.cs.ICSItem | null = null;\n\n    @property(ButtonPlus)\n    replaceEquipBtn: ButtonPlus | null = null;\n    @property(ButtonPlus)\n    unEquipBtn: ButtonPlus | null = null;\n    @property(ButtonPlus)\n    levelUpEquipBtn: ButtonPlus | null = null;\n    @property(ButtonPlus)\n    multiLevelUpEquipBtn: ButtonPlus | null = null;\n\n    protected onLoad(): void {\n        this.levelUpEquipBtn!.addClick(this.onClickLevelUpEquip, this)\n        this.multiLevelUpEquipBtn!.addClick(this.onClickMultiLevelUpEquip, this)\n        this.unEquipBtn!.addClick(this.onClickUnEquip, this)\n        this.replaceEquipBtn!.addClick(this.onClickReplaceEquip, this)\n    }\n\n    async onShow(planeEquipInfo: csproto.cs.ICSItem, source: OpenEquipInfoUISource): Promise<void> {\n        logDebug(\"PlaneUI\", `onShow planeEquipInfo:${planeEquipInfo} source:${source}`)\n        const tbEquip = MyApp.lubanMgr.table.TbEquip\n        if (source == OpenEquipInfoUISource.DisPlay) {\n            this.replaceEquipBtn!.node.active = false;\n            this.unEquipBtn!.node.parent!.active = true\n        } else {\n            this.replaceEquipBtn!.node.active = true\n            this.unEquipBtn!.node!.parent!.active = false\n            const slot = DataMgr.equip.eqSlots.getEmptySlotByClass(tbEquip.get(planeEquipInfo.item_id!)?.equipClass ?? 0)\n            if (!slot) {\n                this.replaceEquipBtn!.getComponentInChildren(Label)!.string = \"替换\"\n            } else {\n                this.replaceEquipBtn!.getComponentInChildren(Label)!.string = \"装备\"\n            }\n        }\n        this.getComponentInChildren(Label)!.string = MyApp.lubanTables.TbEquip.get(planeEquipInfo.item_id!)?.name || \"\"\n        this._planeEquipInfo = planeEquipInfo;\n    }\n\n    private onClickReplaceEquip() {\n        DataMgr.equip.eqSlots.equip(this._planeEquipInfo!)\n    }\n\n    private onClickUnEquip() {\n        DataMgr.equip.eqSlots.unequip(this._planeEquipInfo!.guid!)\n        UIMgr.hideUI(PlaneEquipInfoUI)\n    }\n\n    private onClickLevelUpEquip() {\n        //EventMgr.emit(PlaneUIEvent.LevelUpEquip)\n    }\n    private onClickMultiLevelUpEquip() {\n        //EventMgr.emit(PlaneUIEvent.MultiLevelUpEquip)\n    }\n\n    async onHide(...args: any[]): Promise<void> { }\n    async onClose(...args: any[]): Promise<void> {\n        EventMgr.targetOff(this)\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}