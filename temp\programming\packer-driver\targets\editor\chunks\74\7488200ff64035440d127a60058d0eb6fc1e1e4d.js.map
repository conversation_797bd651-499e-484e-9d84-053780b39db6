{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/data/MainPlaneFightData.ts"], "names": ["MainPlaneData", "hp", "maxhp", "screenLv", "die", "relifeNum", "lifeNum", "atkAddRatio", "intensifyAtk", "revive"], "mappings": ";;;iBAAaA,a;;;;;;;;;;;;;+BAAAA,a,GAAN,MAAMA,aAAN,CAAmB;AAAA;AAAA,eAEtBC,EAFsB,GAET,CAFS;AAAA,eAGtBC,KAHsB,GAGN,CAHM;AAAA,eAItBC,QAJsB,GAIH,CAJG;AAAA,eAKtBC,GALsB,GAKP,KALO;AAAA,eAMtBC,SANsB,GAMF,CANE;AAAA,eAOtBC,OAPsB,GAOJ,CAPI;AAAA,eAQtBC,WARsB,GAQA,CARA;AAAA,eAStBC,YATsB,GASG,EATH;AAAA,eAUtBC,MAVsB,GAUJ,KAVI;AAAA;;AAAA,O", "sourcesContent": ["export class MainPlaneData{\r\n\r\n    hp: number = 0;\r\n    maxhp: number = 0;\r\n    screenLv: number = 0;\r\n    die: boolean = false;\r\n    relifeNum: number = 0;\r\n    lifeNum: number = 0;\r\n    atkAddRatio: number = 0;\r\n    intensifyAtk: number[] = [];\r\n    revive: boolean = false;\r\n}"]}