{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/BulletSystem.ts"], "names": ["BulletSystem", "_decorator", "find", "path", "Rect", "EventGroup", "ccclass", "join", "tick", "dt", "dtInMiliseconds", "tickEmitters", "tickBullets", "tickEventGroups", "emitter", "allEmitters", "bullet", "allBullets", "i", "allEventGroups", "length", "group", "onCreateEmitter", "push", "bulletParent", "<PERSON><PERSON><PERSON><PERSON>", "bulletParentPath", "foundNode", "console", "warn", "node", "onDestroyEmitter", "index", "indexOf", "splice", "onCreateBullet", "onCreate", "bulletID", "setParent", "onDestroyBullet", "<PERSON><PERSON><PERSON><PERSON>", "destroyAllBullets", "isEditor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createEmitterEventGroup", "ctx", "data", "eventGroups", "createBulletEventGroup", "eventGroup", "start", "onCreateEventGroup", "onDestroyEventGroup", "emitterEventGroupPath", "bulletEventGroupPath", "worldBounds"], "mappings": ";;;yHAYaA,Y;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAZJC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAwCC,MAAAA,I,OAAAA,I;;AAG1DC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA;AAAF,O,GAAcL,U;OACd;AAAEM,QAAAA;AAAF,O,GAAWJ,I;AAEjB;AACA;AACA;AACA;;8BACaH,Y,GAAN,MAAMA,YAAN,CAAmB;AA8BtB;AACJ;AACA;AACsB,eAAJQ,IAAI,CAACC,EAAD,EAAa;AAC3B,gBAAMC,eAAe,GAAGD,EAAE,GAAG,IAA7B;AACA,eAAKE,YAAL,CAAkBD,eAAlB;AACA,eAAKE,WAAL,CAAiBF,eAAjB;AACA,eAAKG,eAAL,CAAqBH,eAArB;AACH;;AAEyB,eAAZC,YAAY,CAACF,EAAD,EAAY;AAClC,eAAK,MAAMK,OAAX,IAAsB,KAAKC,WAA3B,EAAwC;AACpCD,YAAAA,OAAO,CAACN,IAAR,CAAaC,EAAb;AACH;AACJ;;AAEwB,eAAXG,WAAW,CAACH,EAAD,EAAY;AACjC,eAAK,MAAMO,MAAX,IAAqB,KAAKC,UAA1B,EAAsC;AAClCD,YAAAA,MAAM,CAACR,IAAP,CAAYC,EAAZ;AACH;AACJ;;AAE4B,eAAfI,eAAe,CAACJ,EAAD,EAAa;AACtC,eAAK,IAAIS,CAAC,GAAG,KAAKC,cAAL,CAAoBC,MAApB,GAA6B,CAA1C,EAA6CF,CAAC,IAAI,CAAlD,EAAqDA,CAAC,EAAtD,EAA0D;AACtD,kBAAMG,KAAK,GAAG,KAAKF,cAAL,CAAoBD,CAApB,CAAd;AACAG,YAAAA,KAAK,CAACb,IAAN,CAAWC,EAAX,EAFsD,CAGtD;AACA;AACA;AACA;AACH;AACJ;;AAE4B,eAAfa,eAAe,CAACR,OAAD,EAAkB;AAC3C,eAAK,IAAII,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKH,WAAL,CAAiBK,MAArC,EAA6CF,CAAC,EAA9C,EAAkD;AAC9C,gBAAI,KAAKH,WAAL,CAAiBG,CAAjB,MAAwBJ,OAA5B,EAAqC;AACjC;AACH;AACJ;;AAED,eAAKC,WAAL,CAAiBQ,IAAjB,CAAsBT,OAAtB;;AAEA,cAAI,CAAC,KAAKU,YAAN,IAAsB,CAAC,KAAKA,YAAL,CAAkBC,OAA7C,EAAsD;AAClD,gBAAI,KAAKC,gBAAL,CAAsBN,MAAtB,GAA+B,CAAnC,EAAsC;AAClC,oBAAMO,SAAS,GAAGzB,IAAI,CAAC,KAAKwB,gBAAN,CAAtB;;AACA,kBAAIC,SAAJ,EAAe;AACX,qBAAKH,YAAL,GAAoBG,SAApB;AACH,eAFD,MAEO;AACHC,gBAAAA,OAAO,CAACC,IAAR,CAAa,oBAAoB,KAAKH,gBAAtC;AACA,qBAAKF,YAAL,GAAoBV,OAAO,CAACgB,IAA5B;AACH;AACJ;AACJ;AACJ;;AAE6B,eAAhBC,gBAAgB,CAACjB,OAAD,EAAkB;AAC5C,gBAAMkB,KAAa,GAAG,KAAKjB,WAAL,CAAiBkB,OAAjB,CAAyBnB,OAAzB,EAAkC,CAAlC,CAAtB;;AACA,cAAIkB,KAAK,GAAG,CAAC,CAAb,EAAgB;AACZ,iBAAKjB,WAAL,CAAiBmB,MAAjB,CAAwBF,KAAxB,EAA+B,CAA/B;AACH;AACJ;;AAE2B,eAAdG,cAAc,CAACrB,OAAD,EAAmBE,MAAnB,EAAmC;AAC3D;AACA;AACA;AACA;AACA;AACA;AAEAA,UAAAA,MAAM,CAACoB,QAAP,CAAgBtB,OAAhB,EAAyBA,OAAO,CAACuB,QAAjC;AACA,eAAKpB,UAAL,CAAgBM,IAAhB,CAAqBP,MAArB;AACAA,UAAAA,MAAM,CAACc,IAAP,CAAYQ,SAAZ,CAAsB,KAAKd,YAA3B,EAAyC,IAAzC;AACH;;AAE4B,eAAfe,eAAe,CAACvB,MAAD,EAAiB;AAC1CA,UAAAA,MAAM,CAACwB,WAAP;AACA,gBAAMR,KAAa,GAAG,KAAKf,UAAL,CAAgBgB,OAAhB,CAAwBjB,MAAxB,EAAgC,CAAhC,CAAtB;;AACA,cAAIgB,KAAK,GAAG,CAAC,CAAb,EAAgB;AACZ,iBAAKf,UAAL,CAAgBiB,MAAhB,CAAuBF,KAAvB,EAA8B,CAA9B;AACH;AACJ;;AAE8B,eAAjBS,iBAAiB,CAACC,QAAiB,GAAG,KAArB,EAA4B;AACvD,eAAK,MAAM1B,MAAX,IAAqB,KAAKC,UAA1B,EAAsC;AAClCD,YAAAA,MAAM,CAACwB,WAAP;AACH;;AACD,eAAKvB,UAAL,GAAkB,EAAlB;;AAEA,cAAIyB,QAAQ,IAAI,KAAKlB,YAArB,EAAmC;AAC/B,iBAAKA,YAAL,CAAkBmB,iBAAlB;AACH;AACJ;;AAEoC,eAAvBC,uBAAuB,CAACC,GAAD,EAAyBC,IAAzB,EAAqD;AAAA;;AACtF,0BAAAD,GAAG,CAAC/B,OAAJ,0BAAaiC,WAAb,CAAyBxB,IAAzB,CAA8B;AAAA;AAAA,wCAAesB,GAAf,EAAoBC,IAApB,CAA9B;AACH;;AAEmC,eAAtBE,sBAAsB,CAACH,GAAD,EAAyBC,IAAzB,EAAqD;AAAA;;AACrF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAMG,UAAU,GAAG;AAAA;AAAA,wCAAeJ,GAAf,EAAoBC,IAApB,CAAnB;AACAG,UAAAA,UAAU,CAACC,KAAX;AACA,yBAAAL,GAAG,CAAC7B,MAAJ,yBAAY+B,WAAZ,CAAwBxB,IAAxB,CAA6B0B,UAA7B;AACH;AAED;AACJ;AACA;;;AACoC,eAAlBE,kBAAkB,CAACF,UAAD,EAAyB;AACrD,eAAK9B,cAAL,CAAoBI,IAApB,CAAyB0B,UAAzB;AACH;;AAEgC,eAAnBG,mBAAmB,CAACH,UAAD,EAAyB;AACtD,gBAAMjB,KAAa,GAAG,KAAKb,cAAL,CAAoBc,OAApB,CAA4BgB,UAA5B,EAAwC,CAAxC,CAAtB;;AACA,cAAIjB,KAAK,GAAG,CAAC,CAAb,EAAgB;AACZ,iBAAKb,cAAL,CAAoBe,MAApB,CAA2BF,KAA3B,EAAkC,CAAlC;AACH,WAJqD,CAKtD;;AACH;;AAhKqB,O;;AAAbhC,MAAAA,Y,CACc0B,gB,GAA2B,oB;AADzC1B,MAAAA,Y,CAEcqD,qB,GAAgC,6B;AAF9CrD,MAAAA,Y,CAGcsD,oB,GAA+B,4B;;AAEtD;AACJ;AACA;AAPatD,MAAAA,Y,CAQKiB,U,GAAuB,E;;AAErC;AACJ;AACA;AAZajB,MAAAA,Y,CAaKe,W,GAAyB,E;;AAEvC;AACJ;AACA;AAjBaf,MAAAA,Y,CAkBKmB,c,GAA+B,E;AAE7C;AACA;AArBSnB,MAAAA,Y,CAsBKwB,Y;;AAEd;AACJ;AACA;AACA;AA3BaxB,MAAAA,Y,CA4BKuD,W,GAAc,IAAInD,IAAJ,CAAS,CAAC,GAAV,EAAe,CAAC,GAAhB,EAAqB,GAArB,EAA0B,IAA1B,C", "sourcesContent": ["import { _decorator, find, path, Vec2, Node, resources, JsonAsset, Rect } from \"cc\";\nimport { Bullet } from \"./Bullet\";\nimport { Emitter } from \"./Emitter\";\nimport { EventGroup, EventGroupContext, eEventGroupStatus } from \"./EventGroup\";\nimport { EventGroupData } from \"../data/bullet/EventGroupData\";\nconst { ccclass } = _decorator;\nconst { join } = path;\n\n/**\n * BulletSystem - manages all bullets in the game world\n * Handles bullet creation, movement, collision, and cleanup\n */\nexport class BulletSystem {\n    public static readonly bulletParentPath: string = 'Canvas/bullet_root';\n    public static readonly emitterEventGroupPath: string = 'Game/emitter/events/Emitter';\n    public static readonly bulletEventGroupPath: string = 'Game/emitter/events/Bullet';\n\n    /**\n     * All active bullets\n     */\n    public static allBullets: Bullet[] = [];\n\n    /**\n     * All active emitters\n     */\n    public static allEmitters: Emitter[] = [];\n\n    /**\n     * All active action groups\n     */\n    public static allEventGroups: EventGroup[] = [];\n\n    // public static isEmitterEnabled: boolean = true;\n    // public static isBulletEnabled: boolean = true;\n    public static bulletParent: Node;\n\n    /**\n     * Bounds of the game world for bullet cleanup\n     * 这个值需要在合适的地方适当调整\n     */\n    public static worldBounds = new Rect(-375, -667, 750, 1334);\n\n    /**\n     * Main update loop\n     */\n    public static tick(dt: number) {\n        const dtInMiliseconds = dt * 1000;\n        this.tickEmitters(dtInMiliseconds);\n        this.tickBullets(dtInMiliseconds);\n        this.tickEventGroups(dtInMiliseconds);\n    }\n\n    public static tickEmitters(dt:number) {\n        for (const emitter of this.allEmitters) {\n            emitter.tick(dt);\n        }\n    }\n\n    public static tickBullets(dt:number) {\n        for (const bullet of this.allBullets) {\n            bullet.tick(dt);\n        }\n    }\n\n    public static tickEventGroups(dt: number) {\n        for (let i = this.allEventGroups.length - 1; i >= 0; i--) {\n            const group = this.allEventGroups[i];\n            group.tick(dt);\n            // group will remove itself when stopped\n            // if (group.status === eEventGroupStatus.Stopped) {\n            //     this.allEventGroups.splice(i, 1);\n            // }\n        }\n    }\n\n    public static onCreateEmitter(emitter:Emitter) {\n        for (let i = 0; i < this.allEmitters.length; i++) {\n            if (this.allEmitters[i] === emitter) {\n                return;\n            }\n        }\n\n        this.allEmitters.push(emitter);\n\n        if (!this.bulletParent || !this.bulletParent.isValid) {\n            if (this.bulletParentPath.length > 0) {\n                const foundNode = find(this.bulletParentPath);\n                if (foundNode) {\n                    this.bulletParent = foundNode;\n                } else {\n                    console.warn('没有找到子弹父节点请检查路径:' + this.bulletParentPath);\n                    this.bulletParent = emitter.node;\n                }\n            }\n        }\n    }\n\n    public static onDestroyEmitter(emitter:Emitter) {\n        const index: number = this.allEmitters.indexOf(emitter, 0);\n        if (index > -1) {\n            this.allEmitters.splice(index, 1);\n        }\n    }\n\n    public static onCreateBullet(emitter: Emitter, bullet: Bullet) {\n        // 这个检查是否会比较冗余\n        // for (let i = 0; i < this.allBullets.length; i++) {\n        //     if (this.allBullets[i] === bullet) {\n        //         return;\n        //     }\n        // }\n\n        bullet.onCreate(emitter, emitter.bulletID);\n        this.allBullets.push(bullet);\n        bullet.node.setParent(this.bulletParent, true);\n    }\n\n    public static onDestroyBullet(bullet: Bullet) {\n        bullet.willDestroy();\n        const index: number = this.allBullets.indexOf(bullet, 0);\n        if (index > -1) {\n            this.allBullets.splice(index, 1);\n        }\n    }\n\n    public static destroyAllBullets(isEditor: boolean = false) {\n        for (const bullet of this.allBullets) {\n            bullet.willDestroy();\n        }\n        this.allBullets = [];\n\n        if (isEditor && this.bulletParent) {\n            this.bulletParent.removeAllChildren();\n        }\n    }\n\n    public static createEmitterEventGroup(ctx: EventGroupContext, data: EventGroupData): void {\n        ctx.emitter?.eventGroups.push(new EventGroup(ctx, data));\n    }\n\n    public static createBulletEventGroup(ctx: EventGroupContext, data: EventGroupData): void {\n        // the name is the json file name\n        // let finalPath = join(this.bulletEventGroupPath, name);\n        // resources.load(finalPath, JsonAsset, (err, data) => {\n        //     if (err) {\n        //         console.error(\"Failed to load bullet event group:\", err);\n        //         return null;\n        //     }\n        //     const eventData = EventGroupData.fromJSON(data.json);\n        //     const eventGroup = new EventGroup(ctx, eventData);\n        //     eventGroup.start();\n        //     ctx.bullet?.eventGroups.push(eventGroup);\n        // });\n        // console.log('createBulletEventGroup: ', data.name);\n        const eventGroup = new EventGroup(ctx, data);\n        eventGroup.start();\n        ctx.bullet?.eventGroups.push(eventGroup);\n    }\n\n    /**\n     * Called when a new event group is created or turn active\n     */\n    public static onCreateEventGroup(eventGroup: EventGroup) {\n        this.allEventGroups.push(eventGroup);\n    }\n\n    public static onDestroyEventGroup(eventGroup: EventGroup) {\n        const index: number = this.allEventGroups.indexOf(eventGroup, 0);\n        if (index > -1) {\n            this.allEventGroups.splice(index, 1);\n        }\n        // console.log('onDestroyEventGroup: ', eventGroup.data.name);\n    }\n}"]}