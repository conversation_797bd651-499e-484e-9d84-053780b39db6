
import { SingletonBase } from "../../core/base/SingletonBase";
import { GameConst } from "../const/GameConst";
import {GameEnum} from "../const/GameEnum";
import { GameIns } from "../GameIns";
import GameMapRun from "../ui/map/GameMapRun";
import { UIMgr } from "../../ui/UIMgr";
import { LoadingUI } from "../../ui/LoadingUI";
import { PlaneData } from "db://assets/bundles/common/script/data/plane/PlaneData";
import { BulletSystem } from "../bullet/BulletSystem";

export class BattleManager extends SingletonBase<BattleManager> {

    _percent = 0;
    gameType = GameEnum.GameType.Common;
    initBattleEnd = false;
    gameStart = false;
    animSpeed = 1;
    _gameTime = 0;

    mainStage = 0;
    subStage = 0;

    _loadFinish = false;
    _loadTotal = 0;
    _loadCount = 0;

    setBattleInfo(mainStage: number, subStage: number,planeData:PlaneData) {
        this.mainStage = mainStage;
        this.subStage = subStage;
        GameIns.mainPlaneManager.setPlaneData(planeData);
    }

    mainReset() {
        GameIns.enemyManager.mainReset();
        GameIns.bossManager.mainReset();
        GameIns.waveManager.reset();
        GameIns.mainPlaneManager.mainReset();
        GameMapRun.instance!.reset();
        GameMapRun.instance!.clear();
        GameIns.bulletManager.clear();
        GameIns.hurtEffectManager.clear();
        GameIns.gameRuleManager.reset();
    }

    subReset() {
        GameIns.gameRuleManager.reset();
        GameIns.waveManager.reset();
        GameIns.enemyManager.subReset();
        GameIns.bossManager.subReset();
    }

    /**
     * 检查所有资源是否加载完成
     */
    checkLoadFinish() {
        this._loadCount++;
        let loadingUI = UIMgr.get(LoadingUI)
        loadingUI.updateProgress(this._loadCount / this._loadTotal)
        if (this._loadCount >= this._loadTotal) {
            this.initBattle();
            UIMgr.closeUI(LoadingUI)
        }

    }

    addLoadCount(count :number) {
        this._loadTotal += count;
    }

    startLoading() {
        GameIns.gameRuleManager.gameSortie();
        GameIns.gameResManager.preload();
        GameIns.mainPlaneManager.preload();
        GameIns.bulletManager.preLoad(this.mainStage);//子弹资源
        GameIns.hurtEffectManager.preLoad();//伤害特效资源
        GameMapRun.instance!.initData(this.mainStage);//地图背景初始化
        GameIns.enemyManager.preLoad();//敌人资源
        GameIns.bossManager.preLoad();//boss资源
    }



    initBattle() {
        GameIns.stageManager.initBattle(this.mainStage, this.subStage);
        GameIns.mainPlaneManager.mainPlane!.initBattle();
        GameIns.mainPlaneManager.mainPlane!.planeIn();
    }

    onPlaneIn() {
        this.initBattleEnd = true;
        this.beginBattle();
    }

    beginBattle() {
        if (this.initBattleEnd && !this.gameStart) {
            this.gameStart = true;

            GameIns.stageManager.gameStart();
            GameIns.waveManager.gameStart();
            GameIns.gameRuleManager.gameStart();

            GameIns.mainPlaneManager.mainPlane!.begine(true);
        }
    }

    /**
     * 更新游戏逻辑
     * @param {number} dt 每帧的时间间隔
     */
    update(dt:number) {
        if (GameIns.gameRuleManager.isGameOver()) {
            if (GameIns.gamePlaneManager) {
                GameIns.gamePlaneManager.enemyTarget = null;
            }
            return;
        }

        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {
            GameIns.gamePlaneManager.update(dt);
            GameIns.waveManager.updateGameLogic(dt);
            GameIns.enemyManager.updateGameLogic(dt);
            GameIns.bossManager.updateGameLogic(dt);

            GameIns.gameRuleManager.updateGameLogic(dt);

            //子弹发射器系统
            BulletSystem.tick(dt);

            this._gameTime += dt;
        } else if (GameIns.gamePlaneManager) {
            GameIns.gamePlaneManager.enemyTarget = null;
        }
    }

    /**
     * 战斗失败逻辑
     */
    async battleDie() {
        // GameFunc.addDialog(ReplayUI.default);
        GameIns.gameRuleManager.gamePause();
    }

    //     /**
    //      * 战斗复活逻辑
    //      */
    //     relifeBattle() {
    //         GameIns.eventManager.emit(GameEvent.MainRelife);
    //         GameIns.gameRuleManager.gameResume();
    //     }

    /**
     * 战斗失败结算
     */
    battleFail() {
        GameIns.gameMainUI!.showGameResult(false);
        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;
        this.endBattle();
    }

    /**
     * 战斗胜利逻辑
     */
    battleSucc() {
        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;
        this.endBattle();

        if (GameIns.stageManager.checkStage(this.mainStage,this.subStage + 1)){
            this.startNextBattle();
        }else{
            GameIns.gameMainUI!.showGameResult(true);
        }
    }
        /**
     * 继续下一场战斗
     */
    startNextBattle() {
        this.subReset();
        this.subStage += 1;
        this.initBattle();
    }

    /**
     * 结束战斗
     */
    endBattle() {
        GameIns.gameRuleManager.gameOver();
        GameIns.bulletManager.removeAll(false, true);

        this.gameStart = false;
        this.initBattleEnd = false;
    }


    /**
     * Boss切换完成
     * @param {string} bossName Boss名称
     */
    bossChangeFinish(bossName: string) {
        // const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
        // if (bossEnterDialog) {
        //     bossEnterDialog.node.active = true;
        //     GameIns.mainPlaneManager.moveAble = false;
        //     bossEnterDialog.showTips(bossName);
        // }
    }

    bossWillEnter() {
        //        GameIns.mainPlaneManager.fireEnable = false;
        //        GameIns.mainPlaneManager.moveAble = false;
        //         WinePlaneManager.default.me.pauseBattle();

        //         const inGameUI = GameIns.uiManager.getDialog(InGameUI.default);
        //         if (inGameUI) {
        //             inGameUI.hideUI();
        //         }

        //         const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);
        //         if (bossEnterDialog) {
        //             if (!bossEnterDialog.node.parent) {
        //                 GameIns.uiManager.addDialog(BossEnterDialog.default, bossEnterDialog);
        //             }
        //             bossEnterDialog.node.active = true;
        //             bossEnterDialog.play();
        //         }

        //         GameIns.audioManager.playbg("bg_3");
    }
    /**
     * 开始Boss战斗
     */
    bossFightStart() {
        GameIns.mainPlaneManager.mainPlane!.setFireEnable(true);
        GameIns.mainPlaneManager.moveAble = true;

        GameIns.bossManager.bossFightStart();
    }

    /**
     * 获取屏幕比例
     * @returns {number} 屏幕比例
     */
    getRatio() {
        return 0.666667; // 固定比例值
    }

    isGameType(gameType : number) {
        return this.gameType == gameType;
    }
}