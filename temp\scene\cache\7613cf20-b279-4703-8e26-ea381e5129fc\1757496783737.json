"[\n  {\n    \"__type__\": \"cc.SceneAsset\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_native\": \"\",\n    \"scene\": {\n      \"__id__\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.Scene\",\n    \"_name\": \"BulletEditor\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": null,\n    \"_children\": [\n      {\n        \"__id__\": 2\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": {\n      \"__id__\": 45\n    },\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"autoReleaseAssets\": false,\n    \"_globals\": {\n      \"__id__\": 46\n    },\n    \"_id\": \"7613cf20-b279-4703-8e26-ea381e5129fc\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Canvas\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 1\n    },\n    \"_children\": [\n      {\n        \"__id__\": 3\n      },\n      {\n        \"__id__\": 5\n      },\n      {\n        \"__id__\": 9\n      },\n      {\n        \"__id__\": 14\n      },\n      {\n        \"__id__\": 33\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 41\n      },\n      {\n        \"__id__\": 42\n      },\n      {\n        \"__id__\": 43\n      },\n      {\n        \"__id__\": 44\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 375,\n      \"y\": 667,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"beI88Z2HpFELqR4T5EMHpg\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"Camera\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 4\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 1000\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"ebFwiq8gBFaYpqYbdoDODe\"\n  },\n  {\n    \"__type__\": \"cc.Camera\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 3\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_projection\": 0,\n    \"_priority\": 0,\n    \"_fov\": 45,\n    \"_fovAxis\": 0,\n    \"_orthoHeight\": 667,\n    \"_near\": 0,\n    \"_far\": 2000,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 0,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_depth\": 1,\n    \"_stencil\": 0,\n    \"_clearFlags\": 7,\n    \"_rect\": {\n      \"__type__\": \"cc.Rect\",\n      \"x\": 0,\n      \"y\": 0,\n      \"width\": 1,\n      \"height\": 1\n    },\n    \"_aperture\": 19,\n    \"_shutter\": 7,\n    \"_iso\": 0,\n    \"_screenScale\": 1,\n    \"_visibility\": 1108344832,\n    \"_targetTexture\": null,\n    \"_postProcess\": null,\n    \"_usePostProcess\": false,\n    \"_cameraType\": -1,\n    \"_trackingType\": 0,\n    \"_id\": \"63WIch3o5BEYRlXzTT0oWc\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"InfoText\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 6\n      },\n      {\n        \"__id__\": 7\n      },\n      {\n        \"__id__\": 8\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -375,\n      \"y\": 667,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 33554432,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d2HKu6I5NMBo0v1gFAigLY\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 294.134765625,\n      \"height\": 170.4000000000001\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0,\n      \"y\": 1\n    },\n    \"_id\": \"30orea5UxK06KIlRNBPujK\"\n  },\n  {\n    \"__type__\": \"cc.RichText\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_lineHeight\": 40,\n    \"_string\": \"当前时间: 3193.00\\n当前发射器数量: 1\\n当前子弹数量: 3\\n当前事件组数量: 0\",\n    \"_horizontalAlign\": 0,\n    \"_verticalAlign\": 0,\n    \"_fontSize\": 36,\n    \"_fontColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_maxWidth\": 0,\n    \"_fontFamily\": \"Arial\",\n    \"_font\": null,\n    \"_isSystemFontUsed\": true,\n    \"_userDefinedFont\": null,\n    \"_cacheMode\": 0,\n    \"_imageAtlas\": null,\n    \"_handleTouchEvent\": true,\n    \"_id\": \"00q4ZFuttM4p34Bp0x6t/e\"\n  },\n  {\n    \"__type__\": \"cc.Widget\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 5\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_alignFlags\": 45,\n    \"_target\": null,\n    \"_left\": 0,\n    \"_right\": 455.865234375,\n    \"_top\": 0,\n    \"_bottom\": 1163.6,\n    \"_horizontalCenter\": 0,\n    \"_verticalCenter\": 0,\n    \"_isAbsLeft\": true,\n    \"_isAbsRight\": true,\n    \"_isAbsTop\": true,\n    \"_isAbsBottom\": true,\n    \"_isAbsHorizontalCenter\": true,\n    \"_isAbsVerticalCenter\": true,\n    \"_originalWidth\": 171.11328125,\n    \"_originalHeight\": 90.39999999999999,\n    \"_alignMode\": 2,\n    \"_lockFlags\": 0,\n    \"_id\": \"86fa+Yb5lBA5qe/2oriN1W\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"GizmoDrawer\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [],\n    \"_active\": true,\n    \"_components\": [\n      {\n        \"__id__\": 10\n      },\n      {\n        \"__id__\": 11\n      },\n      {\n        \"__id__\": 12\n      },\n      {\n        \"__id__\": 13\n      }\n    ],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"83aCzsRKhL5ZUgHIq76xKv\"\n  },\n  {\n    \"__type__\": \"cc.Widget\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 9\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_alignFlags\": 45,\n    \"_target\": {\n      \"__id__\": 2\n    },\n    \"_left\": 0,\n    \"_right\": 0,\n    \"_top\": 0,\n    \"_bottom\": 0,\n    \"_horizontalCenter\": 0,\n    \"_verticalCenter\": 0,\n    \"_isAbsLeft\": true,\n    \"_isAbsRight\": true,\n    \"_isAbsTop\": true,\n    \"_isAbsBottom\": true,\n    \"_isAbsHorizontalCenter\": true,\n    \"_isAbsVerticalCenter\": true,\n    \"_originalWidth\": 100,\n    \"_originalHeight\": 100,\n    \"_alignMode\": 2,\n    \"_lockFlags\": 0,\n    \"_id\": \"74yBO9nshJFZKbpIgzHmCx\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 9\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 750,\n      \"height\": 1334\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"c9qFCCyyBPGIRxQ2QfobqA\"\n  },\n  {\n    \"__type__\": \"cc.Graphics\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 9\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_customMaterial\": null,\n    \"_srcBlendFactor\": 2,\n    \"_dstBlendFactor\": 4,\n    \"_color\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_lineWidth\": 5.165268628057984,\n    \"_strokeColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 0,\n      \"b\": 0,\n      \"a\": 255\n    },\n    \"_lineJoin\": 2,\n    \"_lineCap\": 0,\n    \"_fillColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 255,\n      \"g\": 255,\n      \"b\": 255,\n      \"a\": 255\n    },\n    \"_miterLimit\": 10,\n    \"_id\": \"34lwRCJf9C5ZyvA5fpKG98\"\n  },\n  {\n    \"__type__\": \"35b7e0iBnFHtqqvAd1SurM7\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 9\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"gizmosEnabled\": true,\n    \"drawInPlayMode\": false,\n    \"refreshRate\": 60,\n    \"maxDrawDistance\": 2000,\n    \"_id\": \"74QXLdv9xKjbN9eec5SI/a\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_name\": \"bullet_root\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_children\": [\n      {\n        \"__id__\": 15\n      },\n      {\n        \"__id__\": 21\n      },\n      {\n        \"__id__\": 27\n      }\n    ],\n    \"_active\": true,\n    \"_components\": [],\n    \"_prefab\": null,\n    \"_lpos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_lrot\": {\n      \"__type__\": \"cc.Quat\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 1\n    },\n    \"_lscale\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1,\n      \"y\": 1,\n      \"z\": 1\n    },\n    \"_mobility\": 0,\n    \"_layer\": 1073741824,\n    \"_euler\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0\n    },\n    \"_id\": \"d6QdlOoLNLorEups0PY+Ft\"\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 14\n    },\n    \"_prefab\": {\n      \"__id__\": 16\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 15\n    },\n    \"asset\": {\n      \"__uuid__\": \"68ac1a9d-3829-40ab-9efb-62a7794c31ed\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"cceer9/u5JSKedIniAH4fm\",\n    \"instance\": {\n      \"__id__\": 17\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"f2jftV68VN2LM7yiXZtG5x\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 18\n      },\n      {\n        \"__id__\": 20\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 19\n    },\n    \"propertyPath\": [\n      \"_name\"\n    ],\n    \"value\": \"_bullet_\"\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"cceer9/u5JSKedIniAH4fm\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 19\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 113.59396543627737,\n      \"y\": -256.8790000059659,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 14\n    },\n    \"_prefab\": {\n      \"__id__\": 22\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 21\n    },\n    \"asset\": {\n      \"__uuid__\": \"68ac1a9d-3829-40ab-9efb-62a7794c31ed\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"cceer9/u5JSKedIniAH4fm\",\n    \"instance\": {\n      \"__id__\": 23\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"11c2ToiCxHzLapirOj/fNJ\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 24\n      },\n      {\n        \"__id__\": 26\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 25\n    },\n    \"propertyPath\": [\n      \"_name\"\n    ],\n    \"value\": \"_bullet_\"\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"cceer9/u5JSKedIniAH4fm\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 25\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 163.59396543627741,\n      \"y\": -256.8790000059659,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 14\n    },\n    \"_prefab\": {\n      \"__id__\": 28\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 27\n    },\n    \"asset\": {\n      \"__uuid__\": \"68ac1a9d-3829-40ab-9efb-62a7794c31ed\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"cceer9/u5JSKedIniAH4fm\",\n    \"instance\": {\n      \"__id__\": 29\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"eeHhzaYM1FM6ZCmIvJDVUh\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 30\n      },\n      {\n        \"__id__\": 32\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 31\n    },\n    \"propertyPath\": [\n      \"_name\"\n    ],\n    \"value\": \"_bullet_\"\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"cceer9/u5JSKedIniAH4fm\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 31\n    },\n    \"propertyPath\": [\n      \"_lpos\"\n    ],\n    \"value\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 63.59396543627738,\n      \"y\": -256.8790000059659,\n      \"z\": 0\n    }\n  },\n  {\n    \"__type__\": \"cc.Node\",\n    \"_objFlags\": 0,\n    \"_parent\": {\n      \"__id__\": 2\n    },\n    \"_prefab\": {\n      \"__id__\": 34\n    },\n    \"__editorExtras__\": {}\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": {\n      \"__id__\": 33\n    },\n    \"asset\": {\n      \"__uuid__\": \"3f214341-4ed4-4f54-82d6-eeb4a6ae356d\",\n      \"__expectedType__\": \"cc.Prefab\"\n    },\n    \"fileId\": \"7a5JhlAcVKZLHMF0PFwOI9\",\n    \"instance\": {\n      \"__id__\": 35\n    },\n    \"targetOverrides\": null,\n    \"nestedPrefabInstanceRoots\": null\n  },\n  {\n    \"__type__\": \"cc.PrefabInstance\",\n    \"fileId\": \"39eYKthJtPerDPe2E9x1WB\",\n    \"prefabRootNode\": null,\n    \"mountedChildren\": [],\n    \"mountedComponents\": [],\n    \"propertyOverrides\": [\n      {\n        \"__id__\": 36\n      },\n      {\n        \"__id__\": 38\n      },\n      {\n        \"__id__\": 39\n      },\n      {\n        \"__id__\": 40\n      }\n    ],\n    \"removedComponents\": []\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 37\n    },\n    \"propertyPath\": [\n      \"bulletData\",\n      \"speed\",\n      \"value\"\n    ],\n    \"value\": 50\n  },\n  {\n    \"__type__\": \"cc.TargetInfo\",\n    \"localID\": [\n      \"31FxgqmNJKxJexG1cGb/qX\"\n    ]\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 37\n    },\n    \"propertyPath\": [\n      \"bulletData\",\n      \"speed\",\n      \"expression\"\n    ],\n    \"value\": \"50\"\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 37\n    },\n    \"propertyPath\": [\n      \"emitterData\",\n      \"emitInterval\",\n      \"value\"\n    ],\n    \"value\": 5000\n  },\n  {\n    \"__type__\": \"CCPropertyOverrideInfo\",\n    \"targetInfo\": {\n      \"__id__\": 37\n    },\n    \"propertyPath\": [\n      \"emitterData\",\n      \"emitInterval\",\n      \"expression\"\n    ],\n    \"value\": \"5000\"\n  },\n  {\n    \"__type__\": \"cc.UITransform\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_contentSize\": {\n      \"__type__\": \"cc.Size\",\n      \"width\": 750,\n      \"height\": 1334\n    },\n    \"_anchorPoint\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 0.5,\n      \"y\": 0.5\n    },\n    \"_id\": \"d6rUX5yfhMlKoWX2bSbawx\"\n  },\n  {\n    \"__type__\": \"cc.Canvas\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_cameraComponent\": {\n      \"__id__\": 4\n    },\n    \"_alignCanvasWithScreen\": true,\n    \"_id\": \"12O/ljcVlEqLmVm3U2gEOQ\"\n  },\n  {\n    \"__type__\": \"cc.Widget\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"_alignFlags\": 45,\n    \"_target\": null,\n    \"_left\": 0,\n    \"_right\": 0,\n    \"_top\": 0,\n    \"_bottom\": 0,\n    \"_horizontalCenter\": 0,\n    \"_verticalCenter\": 0,\n    \"_isAbsLeft\": true,\n    \"_isAbsRight\": true,\n    \"_isAbsTop\": true,\n    \"_isAbsBottom\": true,\n    \"_isAbsHorizontalCenter\": true,\n    \"_isAbsVerticalCenter\": true,\n    \"_originalWidth\": 0,\n    \"_originalHeight\": 0,\n    \"_alignMode\": 1,\n    \"_lockFlags\": 0,\n    \"_id\": \"c5V1EV8IpMtrIvY1OE9t2u\"\n  },\n  {\n    \"__type__\": \"6c1a6Gwu4lMHr2sSELvATS6\",\n    \"_name\": \"\",\n    \"_objFlags\": 0,\n    \"__editorExtras__\": {},\n    \"node\": {\n      \"__id__\": 2\n    },\n    \"_enabled\": true,\n    \"__prefab\": null,\n    \"fixedDelta\": 16.666666666666668,\n    \"richText\": {\n      \"__id__\": 7\n    },\n    \"_id\": \"f1b3kzWuVIXb4TagTkjCJG\"\n  },\n  {\n    \"__type__\": \"cc.PrefabInfo\",\n    \"root\": null,\n    \"asset\": null,\n    \"fileId\": \"7613cf20-b279-4703-8e26-ea381e5129fc\",\n    \"instance\": null,\n    \"targetOverrides\": [],\n    \"nestedPrefabInstanceRoots\": [\n      {\n        \"__id__\": 15\n      },\n      {\n        \"__id__\": 21\n      },\n      {\n        \"__id__\": 27\n      },\n      {\n        \"__id__\": 33\n      }\n    ]\n  },\n  {\n    \"__type__\": \"cc.SceneGlobals\",\n    \"ambient\": {\n      \"__id__\": 47\n    },\n    \"shadows\": {\n      \"__id__\": 48\n    },\n    \"_skybox\": {\n      \"__id__\": 49\n    },\n    \"fog\": {\n      \"__id__\": 50\n    },\n    \"octree\": {\n      \"__id__\": 51\n    },\n    \"skin\": {\n      \"__id__\": 52\n    },\n    \"lightProbeInfo\": {\n      \"__id__\": 53\n    },\n    \"postSettings\": {\n      \"__id__\": 54\n    },\n    \"bakedWithStationaryMainLight\": false,\n    \"bakedWithHighpLightmap\": false\n  },\n  {\n    \"__type__\": \"cc.AmbientInfo\",\n    \"_skyColorHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0.520833125\n    },\n    \"_skyColor\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0.520833125\n    },\n    \"_skyIllumHDR\": 20000,\n    \"_skyIllum\": 20000,\n    \"_groundAlbedoHDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0\n    },\n    \"_groundAlbedo\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0,\n      \"y\": 0,\n      \"z\": 0,\n      \"w\": 0\n    },\n    \"_skyColorLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.5,\n      \"z\": 0.8,\n      \"w\": 1\n    },\n    \"_skyIllumLDR\": 20000,\n    \"_groundAlbedoLDR\": {\n      \"__type__\": \"cc.Vec4\",\n      \"x\": 0.2,\n      \"y\": 0.2,\n      \"z\": 0.2,\n      \"w\": 1\n    }\n  },\n  {\n    \"__type__\": \"cc.ShadowsInfo\",\n    \"_enabled\": false,\n    \"_type\": 0,\n    \"_normal\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 0,\n      \"y\": 1,\n      \"z\": 0\n    },\n    \"_distance\": 0,\n    \"_planeBias\": 1,\n    \"_shadowColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 76,\n      \"g\": 76,\n      \"b\": 76,\n      \"a\": 255\n    },\n    \"_maxReceived\": 4,\n    \"_size\": {\n      \"__type__\": \"cc.Vec2\",\n      \"x\": 512,\n      \"y\": 512\n    }\n  },\n  {\n    \"__type__\": \"cc.SkyboxInfo\",\n    \"_envLightingType\": 0,\n    \"_envmapHDR\": null,\n    \"_envmap\": null,\n    \"_envmapLDR\": null,\n    \"_diffuseMapHDR\": null,\n    \"_diffuseMapLDR\": null,\n    \"_enabled\": false,\n    \"_useHDR\": true,\n    \"_editableMaterial\": null,\n    \"_reflectionHDR\": null,\n    \"_reflectionLDR\": null,\n    \"_rotationAngle\": 0\n  },\n  {\n    \"__type__\": \"cc.FogInfo\",\n    \"_type\": 0,\n    \"_fogColor\": {\n      \"__type__\": \"cc.Color\",\n      \"r\": 200,\n      \"g\": 200,\n      \"b\": 200,\n      \"a\": 255\n    },\n    \"_enabled\": false,\n    \"_fogDensity\": 0.3,\n    \"_fogStart\": 0.5,\n    \"_fogEnd\": 300,\n    \"_fogAtten\": 5,\n    \"_fogTop\": 1.5,\n    \"_fogRange\": 1.2,\n    \"_accurate\": false\n  },\n  {\n    \"__type__\": \"cc.OctreeInfo\",\n    \"_enabled\": false,\n    \"_minPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": -1024,\n      \"y\": -1024,\n      \"z\": -1024\n    },\n    \"_maxPos\": {\n      \"__type__\": \"cc.Vec3\",\n      \"x\": 1024,\n      \"y\": 1024,\n      \"z\": 1024\n    },\n    \"_depth\": 8\n  },\n  {\n    \"__type__\": \"cc.SkinInfo\",\n    \"_enabled\": false,\n    \"_blurRadius\": 0.01,\n    \"_sssIntensity\": 3\n  },\n  {\n    \"__type__\": \"cc.LightProbeInfo\",\n    \"_giScale\": 1,\n    \"_giSamples\": 1024,\n    \"_bounces\": 2,\n    \"_reduceRinging\": 0,\n    \"_showProbe\": true,\n    \"_showWireframe\": true,\n    \"_showConvex\": false,\n    \"_data\": null,\n    \"_lightProbeSphereVolume\": 1\n  },\n  {\n    \"__type__\": \"cc.PostSettingsInfo\",\n    \"_toneMappingType\": 0\n  }\n]"