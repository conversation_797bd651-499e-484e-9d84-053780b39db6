System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Node, UITransform, DataMgr, EventMgr, MyApp, logDebug, UIMgr, PlaneUIEvent, List, PlaneEquipInfoUI, OpenEquipInfoUISource, TabStatus, BagItem, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, BagGrid;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "db://assets/bundles/common/script/data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "db://assets/bundles/common/script/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "../../../../../../../scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUIEvent(extras) {
    _reporterNs.report("PlaneUIEvent", "../../../../event/PlaneUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../../../common/components/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneEquipInfoUI(extras) {
    _reporterNs.report("PlaneEquipInfoUI", "../../PlaneEquipInfoUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfOpenEquipInfoUISource(extras) {
    _reporterNs.report("OpenEquipInfoUISource", "../../PlaneTypes", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabStatus(extras) {
    _reporterNs.report("TabStatus", "../../PlaneTypes", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBagItem(extras) {
    _reporterNs.report("BagItem", "./BagItem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Node = _cc.Node;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      DataMgr = _unresolved_2.DataMgr;
    }, function (_unresolved_3) {
      EventMgr = _unresolved_3.EventMgr;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      logDebug = _unresolved_5.logDebug;
    }, function (_unresolved_6) {
      UIMgr = _unresolved_6.UIMgr;
    }, function (_unresolved_7) {
      PlaneUIEvent = _unresolved_7.PlaneUIEvent;
    }, function (_unresolved_8) {
      List = _unresolved_8.default;
    }, function (_unresolved_9) {
      PlaneEquipInfoUI = _unresolved_9.PlaneEquipInfoUI;
    }, function (_unresolved_10) {
      OpenEquipInfoUISource = _unresolved_10.OpenEquipInfoUISource;
      TabStatus = _unresolved_10.TabStatus;
    }, function (_unresolved_11) {
      BagItem = _unresolved_11.BagItem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1ce0bH/NfdBLL19SPqwUpSI", "BagGrid", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'UITransform']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("BagGrid", BagGrid = (_dec = ccclass('BagGrid'), _dec2 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec3 = property(Node), _dec4 = property(Node), _dec(_class = (_class2 = class BagGrid extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "bagList", _descriptor, this);

          _initializerDefineProperty(this, "separator", _descriptor2, this);

          _initializerDefineProperty(this, "mergeSelectMaskBg", _descriptor3, this);

          this._sortedItems = [];
          this._sortedEquips = [];
          this._lineGridNum = 5;
          this._separatorRow = 0;
          this._tabStatus = (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).None;
        }

        onLoad() {
          this.separator.removeFromParent();
          this.mergeSelectMaskBg.active = false;
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).SortTypeChange, this.onSortTypeChange, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).BagItemClick, this.onBagItemClick, this);
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).on((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).UpdateMergeEquipStatus, this.onUpdateMergeEquipStatus, this);
        }

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

        onUpdateMergeEquipStatus() {
          this.bagList.updateAll();
        }
        /*暂时只有装备点击*/


        onBagItemClick(item) {
          switch (this._tabStatus) {
            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Bag:
              this.mergeSelectMaskBg.active = false;
              (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                error: Error()
              }), logDebug) : logDebug)("PlaneUI", "onBagItemClick item:" + item);
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(_crd && PlaneEquipInfoUI === void 0 ? (_reportPossibleCrUseOfPlaneEquipInfoUI({
                error: Error()
              }), PlaneEquipInfoUI) : PlaneEquipInfoUI, item, (_crd && OpenEquipInfoUISource === void 0 ? (_reportPossibleCrUseOfOpenEquipInfoUISource({
                error: Error()
              }), OpenEquipInfoUISource) : OpenEquipInfoUISource).BagGrid);
              break;

            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Merge:
              if ((_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
                error: Error()
              }), DataMgr) : DataMgr).equip.eqCombine.isFull() || !(_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
                error: Error()
              }), DataMgr) : DataMgr).equip.eqCombine.isCanCombine(item)) {
                return;
              }

              this.onUpdateMergeEquipStatus();
              break;
          }
        }

        onSortTypeChange(tabStatus, items) {
          this._tabStatus = tabStatus;
          this.mergeSelectMaskBg.active = false;
          this.separator.active = false;
          this.separator.removeFromParent();
          this.bagList._customSize = {};

          this.bagList._resizeContent();

          var listNum = 0;

          switch (tabStatus) {
            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Bag:
              this._sortedItems = items.filter(v => (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).lubanTables.TbItem.get(v.item_id) != null);
              this._sortedEquips = items.filter(v => (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
                error: Error()
              }), MyApp) : MyApp).lubanTables.TbEquip.get(v.item_id) != null);
              this._separatorRow = Math.ceil(this._sortedEquips.length / this._lineGridNum);
              var itemRowNum = Math.ceil(this._sortedItems.length / this._lineGridNum);
              listNum = this._separatorRow + itemRowNum + 1;
              break;

            case (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
              error: Error()
            }), TabStatus) : TabStatus).Merge:
              this._separatorRow = -1;
              this._sortedItems = items;
              listNum = Math.ceil(this._sortedItems.length / this._lineGridNum);
              break;
          }

          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("PlaneUI", "onSortTypeChange list num:" + this.bagList.numItems + " maxPlanePartRowNum:" + this._separatorRow);
          this.bagList.numItems = listNum;
          this.bagList.scrollTo(0, 1);
        }

        onListRenderInBagStatus(listItem, row) {
          listItem.name = "" + row;

          if (row == this._separatorRow) {
            var normalSize = this.bagList.tmpNode.getComponent(UITransform).contentSize;
            listItem.children.forEach(v => v.active = false);
            this.separator.removeFromParent();
            this.separator.active = true;
            listItem.addChild(this.separator);
            listItem.getComponent(UITransform).setContentSize(normalSize.width, normalSize.height / 2);
            return;
          }

          if (listItem.children.length > 5) {
            this.separator.removeFromParent();
            this.separator.active = false;
          }

          var bagItems = listItem.getComponentsInChildren(_crd && BagItem === void 0 ? (_reportPossibleCrUseOfBagItem({
            error: Error()
          }), BagItem) : BagItem);

          if (row < this._separatorRow) {
            for (var index = 0; index < bagItems.length; index++) {
              var item = bagItems[index];
              var dataIndex = row * this._lineGridNum + index;

              if (dataIndex >= this._sortedEquips.length) {
                item.node.active = false;
                (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
                  error: Error()
                }), logDebug) : logDebug)("PlaneUI", "onListRender bagItem index:" + index + " dataIndex:" + dataIndex + " row:" + row + " sortedLen:" + this._sortedEquips.length);
                continue;
              }

              item.node.active = true;
              item.onBagTabStatusRender(this._sortedEquips[dataIndex]);
            }
          } else {
            for (var _index = 0; _index < bagItems.length; _index++) {
              var _item = bagItems[_index];

              var _dataIndex = (row - this._separatorRow - 1) * this._lineGridNum + _index;

              if (_dataIndex >= this._sortedItems.length) {
                _item.node.active = false;
                continue;
              }

              _item.node.active = true;

              _item.onBagTabStatusRender(this._sortedItems[_dataIndex]);
            }
          }
        }

        onListRenderInCombineStatus(listItem, row) {
          var bagItems = listItem.getComponentsInChildren(_crd && BagItem === void 0 ? (_reportPossibleCrUseOfBagItem({
            error: Error()
          }), BagItem) : BagItem);

          for (var index = 0; index < bagItems.length; index++) {
            var item = bagItems[index];
            var dataIndex = row * this._lineGridNum + index;

            if (dataIndex >= this._sortedItems.length) {
              item.node.active = false;
              continue;
            }

            item.node.active = true;
            item.onCombineTabStatusRender(this._sortedItems[dataIndex]);
          }
        }

        onListRender(listItem, row) {
          listItem.name = "listItem" + row;

          if (this._tabStatus == (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).Bag) {
            this.onListRenderInBagStatus(listItem, row);
          } else {
            this.onListRenderInCombineStatus(listItem, row);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bagList", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "separator", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "mergeSelectMaskBg", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=5e81cf07ecf39ceec91a161814d8daf069764468.js.map