System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, assetManager, BlockInputEvents, Canvas, Component, director, instantiate, Node, Prefab, tween, UIOpacity, UITransform, v3, Widget, ButtonPlus, BaseUI, UIManager, _class2, _crd, UILayer, UIMgr;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "db://assets/bundles/common/script/ui/common/components/button/ButtonPlus", _context.meta, extras);
  }

  _export({
    BaseUI: void 0,
    UIManager: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      assetManager = _cc.assetManager;
      BlockInputEvents = _cc.BlockInputEvents;
      Canvas = _cc.Canvas;
      Component = _cc.Component;
      director = _cc.director;
      instantiate = _cc.instantiate;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
      UITransform = _cc.UITransform;
      v3 = _cc.v3;
      Widget = _cc.Widget;
    }, function (_unresolved_2) {
      ButtonPlus = _unresolved_2.ButtonPlus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "5b94ckAhMJDm5t9Dur/Z6Ix", "UIMgr", undefined); // ui.ts


      __checkObsolete__(['assetManager', 'BlockInputEvents', 'Canvas', 'Component', 'director', 'EventTouch', 'instantiate', 'Node', 'Prefab', 'tween', 'UIOpacity', 'UITransform', 'v3', 'Widget']);

      // UI 层级枚举，定义不同的 UI 显示层级
      _export("UILayer", UILayer = /*#__PURE__*/function (UILayer) {
        UILayer[UILayer["Background"] = 0] = "Background";
        UILayer[UILayer["Default"] = 1] = "Default";
        UILayer[UILayer["PopUp"] = 2] = "PopUp";
        UILayer[UILayer["Top"] = 3] = "Top";
        return UILayer;
      }({}));
      /**
       * UI 类的构造函数接口，定义了 UI 类需要实现的静态方法
       * @template T - 继承自 BaseUI 的类
       */


      // 抽象的基础 UI 类，所有具体的 UI 类都应继承自此类
      _export("BaseUI", BaseUI = class BaseUI extends Component {
        /**
         * 获取该 UI 类对应的资源路径，默认返回 "BaseUI"
         * @returns {string} 资源路径
         */
        static getUrl() {
          return "BaseUI";
        }
        /**
         * 获取该 UI 类对应的显示层级，默认返回背景层
         * @returns {UILayer} 显示层级
         */


        static getLayer() {
          return UILayer.Background;
        }
        /**
         * 获取该UI所在bundle名称
         * @returns {string} bundle名称
         */


        static getBundleName() {
          return "resources";
        }
        /**
         * 对应的UI类
         */


        /**
         * 获取该 UI 类对应的选项
         * @returns {UIOpt} 选项
         */
        static getUIOption() {
          return {
            isClickBgCloseUI: false,
            isClickBgHideUI: false
          };
        } // 当前 UI 实例所在的显示层级

        /**
         * 显示 UI 的方法，需要子类实现
         * @param {...any[]} args - 传递给显示方法的参数
         * @returns {Promise<void>} 显示完成的 Promise
         */

        /**
         * 隐藏 UI 的方法，需要子类实现
         * @param {...any[]} args - 传递给隐藏方法的参数
         * @returns {Promise<void>} 隐藏完成的 Promise
         */

        /**
         * 关闭 UI 的方法，需要子类实现
         * @param {...any[]} args - 传递给关闭方法的参数
         * @returns {Promise<void>} 关闭完成的 Promise
         */


        /**
         * 构造函数，调用父类的构造函数
         */
        constructor() {
          super();
          this.uiClass = null;
          this.uiLayer = UILayer.Background;
        }
        /**
         * 显示时的动画效果，仅对弹出层生效
         * @returns {Promise<void>} 动画完成的 Promise
         */


        fadeIn() {
          var _this = this;

          return _asyncToGenerator(function* () {
            // 非弹出层不执行动画
            if (_this.uiLayer !== UILayer.PopUp) return; // 获取 UI 透明度组件

            var opacityComp = _this.getComponent(UIOpacity);

            if (opacityComp) {
              // 设置初始透明度为 0
              opacityComp.opacity = 0;
              return new Promise(resolve => {
                // 使用 tween 动画将透明度在 0.5 秒内渐变为 255
                tween(opacityComp).to(0.5, {
                  opacity: 255
                }).call(() => resolve()).start();
              });
            }
          })();
        }
        /**
         * 隐藏时的动画效果，仅对弹出层且激活状态的节点生效
         * @returns {Promise<void>} 动画完成的 Promise
         */


        fadeOut() {
          var _this2 = this;

          return _asyncToGenerator(function* () {
            // 节点未激活或非弹出层不执行动画
            if (!_this2.node.active || _this2.uiLayer !== UILayer.PopUp) {
              return;
            } // 获取 UI 透明度组件


            var opacityComp = _this2.getComponent(UIOpacity);

            if (opacityComp) {
              return new Promise(resolve => {
                // 使用 tween 动画将透明度在 0.5 秒内渐变为 0
                tween(opacityComp).to(0.5, {
                  opacity: 0
                }).call(() => resolve()).start();
              });
            }
          })();
        }

      }); // UI 管理器类，负责 UI 的加载、显示、隐藏和关闭等操作


      _export("UIManager", UIManager = class UIManager {
        constructor() {
          // 存储已打开的 UI 实例，键为 UI 类对应的资源路径
          this.uiMap = new Map();
          // UI 层级节点，键为 UI 层级，值为对应层级的节点
          this.layers = new Map();
          // UI 根节点
          this._uiRoot = null;
        }

        /**
         * 初始化 UI 根节点
         */
        initUIRoot() {
          var _director$getScene;

          // 获取场景中的 Canvas 节点
          var canvas = director.getScene().getChildByName("Canvas"); // 创建 UI 根节点

          this._uiRoot = new Node('UI_Root'); // 为 UI 根节点添加 Canvas 组件

          this._uiRoot.addComponent(Canvas); // 为节点添加 Widget 组件


          var widget = this._uiRoot.addComponent(Widget); // 宽度拉伸


          widget.isAlignLeft = true;
          widget.isAlignRight = true;
          widget.left = 0; // 左边距 0

          widget.right = 0; // 右边距 0
          // 高度拉伸

          widget.isAlignTop = true;
          widget.isAlignBottom = true;
          widget.top = 0; // 上边距 0

          widget.bottom = 0; // 下边距 0
          // 统一使用像素单位

          widget.isAbsoluteLeft = true;
          widget.isAbsoluteRight = true;
          widget.isAbsoluteTop = true;
          widget.isAbsoluteBottom = true;
          widget.updateAlignment(); // 节点将铺满父容器
          // 设置 UI 根节点的位置与 Canvas 节点相同

          this._uiRoot.setPosition(canvas.position); // 获取 UI 根节点的 UI 变换组件


          var uiTransform = this._uiRoot.getComponent(UITransform); // 设置 UI 根节点的内容大小与 Canvas 节点相同


          uiTransform.contentSize = canvas.getComponent(UITransform).contentSize; // 将 UI 根节点设置为持久化节点

          director.addPersistRootNode(this._uiRoot); // 将 UI 根节点添加到场景中

          (_director$getScene = director.getScene()) == null || _director$getScene.addChild(this._uiRoot);
        }
        /**
         * 初始化各个 UI 层级节点
         */


        initializeLayers() {
          if (this._uiRoot) return; // 初始化 UI 根节点

          this.initUIRoot(); // 遍历所有 UI 层级

          for (var layer = UILayer.Background; layer <= UILayer.Top; layer++) {
            // 创建对应层级的节点
            var nd = new Node(UILayer[layer]);
            nd.addComponent(UITransform).contentSize = this._uiRoot.getComponent(UITransform).contentSize; // 为节点添加 Widget 组件

            var widget = nd.addComponent(Widget); // 宽度拉伸

            widget.isAlignLeft = true;
            widget.isAlignRight = true;
            widget.left = 0; // 左边距 0

            widget.right = 0; // 右边距 0
            // 高度拉伸

            widget.isAlignTop = true;
            widget.isAlignBottom = true;
            widget.top = 0; // 上边距 0

            widget.bottom = 0; // 下边距 0
            // 统一使用像素单位

            widget.isAbsoluteLeft = true;
            widget.isAbsoluteRight = true;
            widget.isAbsoluteTop = true;
            widget.isAbsoluteBottom = true;
            widget.updateAlignment(); // 节点将铺满父容器
            // 将节点存储到层级映射中

            this.layers.set(layer, nd); // 将节点添加到 UI 根节点下

            this._uiRoot.addChild(nd);
          }
        }
        /**
         * 加载指定 UI 类的实例
         * @template T - 继承自 BaseUI 的类
         * @param {UIClass<T>} uiClass - UI 类
         * @returns {Promise<T>} 加载完成的 UI 实例
         */


        loadUI(uiClass) {
          var _this3 = this;

          return _asyncToGenerator(function* () {
            var ui; // 获取 UI 类对应的资源路径

            var url = uiClass.getUrl();

            if (_this3.uiMap.has(url)) {
              // 如果 UI 实例已存在，从缓存中获取并激活节点
              ui = _this3.uiMap.get(url);
            } else {
              var startTime = Date.now();

              try {
                // 使用 Cocos 内置的 resources.load 加载预制体
                var prefab = yield new Promise((resolve, reject) => {
                  var bundle = assetManager.getBundle(uiClass.getBundleName());

                  if (!bundle) {
                    throw new Error("Bundle not found: " + uiClass.getBundleName());
                  }

                  bundle.load(url, Prefab, (err, asset) => {
                    if (err) {
                      reject(err);
                    } else {
                      resolve(asset);
                    }
                  });
                }); // 实例化预制体

                var node = instantiate(prefab); // 获取 UI 组件

                ui = node.getComponent(uiClass);

                if (!ui) {
                  throw new Error("UI component not found on prefab: " + url);
                } // 添加到对应的层级


                var layer = _this3.layers.get(uiClass.getLayer());

                if (layer) {
                  layer.addChild(node);
                } else {
                  throw new Error("UI layer not found: " + uiClass.getLayer());
                } // 设置节点位置


                node.setPosition(v3()); // 设置 UI 实例的显示层级

                ui.uiLayer = uiClass.getLayer();
                ui.uiClass = uiClass; // 将 UI 实例存储到 uiMap 中

                _this3.uiMap.set(url, ui);

                node.active = false; // 自动创建背景节点

                _this3.addClickBgHandler(ui, uiClass);

                var cost = Date.now() - startTime;
                console.debug("[UIManager] loadUI bundle:" + uiClass.getBundleName() + " " + uiClass.getUrl() + " load success cost " + cost + "ms");
              } catch (error) {
                console.error("[UIManager] loadUI bundle:" + uiClass.getBundleName() + " " + uiClass.getUrl() + " load failed: " + error.message);
                throw error;
              }
            }

            return ui;
          })();
        }
        /**
         * 获取指定 UI 类的实例
         * @template T - 继承自 BaseUI 的类
         * @param {UIClass<T>} uiClass - UI 类
         * @returns {T} UI 实例
         */


        get(uiClass) {
          return this.uiMap.get(uiClass.getUrl());
        }
        /**
         * 打开指定 UI 类的实例，支持传递参数
         * @template T - 继承自 BaseUI 的类
         * @param {UIClass<T>} uiClass - UI 类
         * @param {...any[]} args - 传递给显示方法的参数
         * @returns {Promise<void>} 打开完成的 Promise
         */


        openUI(uiClass) {
          var _arguments = arguments,
              _this4 = this;

          return _asyncToGenerator(function* () {
            var startTime = Date.now(); // 加载 UI 实例

            var ui = yield _this4.loadUI(uiClass);
            ui.node.active = true; // 执行显示动画

            yield ui.fadeIn(); // 调用子类的显示方法

            for (var _len = _arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
              args[_key - 1] = _arguments[_key];
            }

            yield ui.onShow(...args);
            var cost = Date.now() - startTime;
            console.debug("[UIManager] openUI " + uiClass.getUrl() + " cost " + cost + "ms");
          })();
        }
        /**
         * 隐藏指定 UI 类的实例，支持传递参数，但不关闭
         * @template T - 继承自 BaseUI 的类
         * @param {UIClass<T>} uiClass - UI 类
         * @param {...any[]} args - 传递给隐藏方法的参数
         * @returns {Promise<void>} 隐藏完成的 Promise
         */


        hideUI(uiClass) {
          var _arguments2 = arguments,
              _this5 = this;

          return _asyncToGenerator(function* () {
            // 从 uiMap 中获取 UI 实例
            var ui = _this5.uiMap.get(uiClass.getUrl());

            if (!ui) {
              console.warn("[hideUI] uiMap not found", uiClass.getUrl());
              return;
            }

            var startTime = Date.now(); // 执行隐藏动画

            yield ui.fadeOut(); // 调用子类的隐藏方法

            for (var _len2 = _arguments2.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {
              args[_key2 - 1] = _arguments2[_key2];
            }

            yield ui.onHide(...args); // 隐藏节点

            ui.node.active = false;
            var cost = Date.now() - startTime;
            console.debug("[UIManager] hideUI " + uiClass.getUrl() + " cost " + cost + "ms");
          })();
        }
        /**
         * 关闭指定 UI 类的实例，支持传递参数
         * @template T - 继承自 BaseUI 的类
         * @param {UIClass<T>} uiClass - UI 类
         * @param {...any[]} args - 传递给关闭方法的参数
         * @returns {Promise<void>} 关闭完成的 Promise
         */


        closeUI(uiClass) {
          var _arguments3 = arguments,
              _this6 = this;

          return _asyncToGenerator(function* () {
            // 从 uiMap 中获取 UI 实例
            var ui = _this6.uiMap.get(uiClass.getUrl());

            if (!ui) {
              console.warn("[UIManager] closeUI uiMap not found", uiClass.getUrl());
              return;
            }

            var startTime = Date.now(); // 执行隐藏动画

            yield ui.fadeOut(); // 调用子类的隐藏方法

            for (var _len3 = _arguments3.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {
              args[_key3 - 1] = _arguments3[_key3];
            }

            yield ui.onHide(...args); // 调用子类的关闭方法

            yield ui.onClose(...args); // 隐藏节点

            ui.node.active = false; // 销毁节点

            ui.node.destroy(); // 从 uiMap 中删除该 UI 实例

            _this6.uiMap.delete(uiClass.getUrl());

            var cost = Date.now() - startTime;
            console.debug("[UIManager] closeUI " + uiClass.getUrl() + " cost " + cost + "ms");
          })();
        }

        addClickBgHandler(ui, uiClass) {
          var _blockInputEvents$get;

          if (!uiClass.getUIOption().isClickBgHideUI && !uiClass.getUIOption().isClickBgCloseUI) {
            return;
          }

          var blockInputEvents = ui.getComponent(BlockInputEvents);

          if (!blockInputEvents) {
            blockInputEvents = ui.node.addComponent(BlockInputEvents);
          }

          (_blockInputEvents$get = blockInputEvents.getComponent(UITransform)) == null || _blockInputEvents$get.setContentSize(750 * 2, 1334 * 2);
          var btn = ui.node.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus);

          if (!btn) {
            btn = ui.node.addComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
              error: Error()
            }), ButtonPlus) : ButtonPlus);
          }

          btn.clickDefZoomScale = false;
          btn.addClick(event => {
            if (uiClass.getUIOption().isClickBgHideUI) {
              this.hideUI(uiClass);
            } else if (uiClass.getUIOption().isClickBgCloseUI) {
              this.closeUI(uiClass);
            }
          }, ui);
        }

      }); // 导出 UIManager 实例


      _class2 = UIManager;
      // 单例实例
      UIManager.Instance = new _class2();

      _export("UIMgr", UIMgr = UIManager.Instance); // 将 UIMgr 挂载到 window 对象上，方便全局访问
      //window["UIMgr"] = UIMgr;


      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=5e1cf596f6b1c93eea960bb66596523aeb25389e.js.map