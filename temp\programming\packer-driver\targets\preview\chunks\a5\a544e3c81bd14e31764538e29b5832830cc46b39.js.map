{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/dropdown/DropDown.ts"], "names": ["_decorator", "Component", "instantiate", "Layout", "ScrollView", "UITransform", "Vec3", "ButtonPlus", "List", "ccclass", "property", "DropDown", "_currentOptKeyList", "_originOptKeyList", "_renderFunc", "_onClick", "_selected<PERSON><PERSON>", "_expandBtn", "<PERSON><PERSON><PERSON>", "onLoad", "statusMarkNode", "addClick", "onClick", "init", "optKeyList", "renderFunc", "filter", "v", "optionList", "numItems", "node", "angle", "unExpand", "isUpdate", "length", "active", "expand", "onList<PERSON>ender", "item", "idx", "name", "startsWith", "btn", "getComponent", "optKey", "listPos", "getPosition", "parent", "setWorldPosition", "getWorldPosition", "itemSize", "contentSize", "layout", "content", "newPos", "x", "y", "height", "paddingTop", "z", "setPosition", "event", "click<PERSON>ey", "target", "Number", "split", "isSameKey", "filtered"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAuBC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAcC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;;AACvFC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,I;;;;;;;;;OACD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBV,U;;0BAIjBW,Q,WADZF,OAAO,CAAC,UAAD,C,UAEHC,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ;AAAA;AAAA,mC,2BAJb,MACaC,QADb,SAC8BV,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAM5BW,kBAN4B,GAMA,EANA;AAAA,eAO5BC,iBAP4B,GAOD,EAPC;AAAA,eAQ5BC,WAR4B,GAQiC,IARjC;AAAA,eAS5BC,QAT4B,GASkB,IATlB;AAAA,eAU5BC,YAV4B,GAUR,IAVQ;AAAA,eAW5BC,UAX4B,GAWI,IAXJ;AAAA;;AAYrB,YAAXC,WAAW,GAAG;AACd,iBAAO,KAAKF,YAAZ;AACH;;AAESG,QAAAA,MAAM,GAAS;AACrB,eAAKC,cAAL,CAAqBC,QAArB,CAA8B,KAAKC,OAAnC,EAA4C,IAA5C,EADqB,CAErB;AACH;;AAEDC,QAAAA,IAAI,CAACC,UAAD,EAAoBC,UAApB,EAAsEH,OAAtE,EAAyG;AACzG,eAAKT,iBAAL,GAAyB,CAAC,GAAGW,UAAJ,CAAzB;AACA,eAAKV,WAAL,GAAmBW,UAAnB;AACA,eAAKV,QAAL,GAAgBO,OAAhB;AACA,eAAKN,YAAL,GAAoB,KAAKH,iBAAL,CAAuB,CAAvB,CAApB;AACA,eAAKD,kBAAL,GAA0B,CAAC,GAAGY,UAAU,CAACE,MAAX,CAAkBC,CAAC,IAAIA,CAAC,IAAI,KAAKX,YAAjC,CAAJ,CAA1B;AACA,eAAKY,UAAL,CAAiBC,QAAjB,GAA4B,CAA5B;AACA,eAAKT,cAAL,CAAqBU,IAArB,CAA0BC,KAA1B,GAAkC,CAAlC;AACH;;AAEDC,QAAAA,QAAQ,CAACC,QAAD,EAAqB;AACzB,cAAI,KAAKrB,kBAAL,CAAwBsB,MAAxB,IAAkC,CAAtC,EAAyC;AACrC;AACH;;AACD,eAAKN,UAAL,CAAiBE,IAAjB,CAAsBK,MAAtB,GAA+B,KAA/B;AACA,eAAKf,cAAL,CAAqBU,IAArB,CAA0BC,KAA1B,GAAkC,CAAlC;;AACA,cAAIE,QAAJ,EAAc;AACV,iBAAKnB,WAAL,CAAkB,KAAKG,UAAL,CAAiBa,IAAnC,EAAyC,KAAKd,YAA9C;AACH;AACJ;;AAEDoB,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKxB,kBAAL,CAAwBsB,MAAxB,IAAkC,CAAtC,EAAyC;AACrC;AACH;;AACD,eAAKN,UAAL,CAAiBE,IAAjB,CAAsBK,MAAtB,GAA+B,IAA/B;AACA,eAAKf,cAAL,CAAqBU,IAArB,CAA0BC,KAA1B,GAAkC,EAAlC;;AACA,eAAKjB,WAAL,CAAkB,KAAKG,UAAL,CAAiBa,IAAnC,EAAyC,KAAKd,YAA9C;;AACA,eAAKY,UAAL,CAAiBC,QAAjB,GAA4B,KAAKjB,kBAAL,CAAwBsB,MAApD;AACH;;AAEOG,QAAAA,YAAY,CAACC,IAAD,EAAaC,GAAb,EAA0B;AAC1C,cAAI,CAACD,IAAI,CAACE,IAAL,CAAUC,UAAV,CAAqB,QAArB,CAAL,EAAqC;AACjC,gBAAMC,GAAG,GAAGJ,IAAI,CAACK,YAAL;AAAA;AAAA,yCAAZ;AACAD,YAAAA,GAAG,CAAErB,QAAL,CAAc,KAAKC,OAAnB,EAA4B,IAA5B;AACH;;AACD,cAAIsB,MAAM,GAAG,KAAK1B,WAAlB;;AACA,cAAI,KAAKN,kBAAL,CAAwBsB,MAAxB,GAAiC,CAArC,EAAwC;AACpCU,YAAAA,MAAM,GAAG,KAAKhC,kBAAL,CAAwB2B,GAAxB,CAAT;AACH;;AACD,cAAMM,OAAO,GAAG,KAAKjB,UAAL,CAAiBE,IAAjB,CAAsBgB,WAAtB,EAAhB;;AACA,eAAKhC,WAAL,CAAkBwB,IAAlB,EAAwBM,MAAxB;;AACA,cAAI,CAAC,KAAK3B,UAAV,EAAsB;AAClB,iBAAKA,UAAL,GAAkBf,WAAW,CAACoC,IAAD,CAAX,CAAkBK,YAAlB;AAAA;AAAA,yCAAlB;;AACA,iBAAK1B,UAAL,CAAiBI,QAAjB,CAA0B,KAAKC,OAA/B,EAAwC,IAAxC;;AACA,iBAAKL,UAAL,CAAiBa,IAAjB,CAAsBiB,MAAtB,GAA+B,KAAKnB,UAAL,CAAiBE,IAAjB,CAAsBiB,MAArD;;AACA,iBAAK9B,UAAL,CAAiBa,IAAjB,CAAsBkB,gBAAtB,CAAuCV,IAAI,CAACW,gBAAL,EAAvC;;AACA,iBAAKnC,WAAL,CAAkB,KAAKG,UAAL,CAAiBa,IAAnC,EAAyC,KAAKd,YAA9C;;AAEA,gBAAMkC,QAAQ,GAAGZ,IAAI,CAACK,YAAL,CAAkBtC,WAAlB,EAAgC8C,WAAjD;AACA,gBAAMC,MAAM,GAAG,KAAKxB,UAAL,CAAiBe,YAAjB,CAA8BvC,UAA9B,EAA2CiD,OAA3C,CAAoDV,YAApD,CAAiExC,MAAjE,CAAf;AACA,gBAAMmD,MAAM,GAAG,IAAIhD,IAAJ,CAASuC,OAAO,CAACU,CAAjB,EAAoBV,OAAO,CAACW,CAAR,GAAYN,QAAQ,CAACO,MAArB,GAA8BL,MAAM,CAAEM,UAA1D,EAAsEb,OAAO,CAACc,CAA9E,CAAf;AACA,iBAAK/B,UAAL,CAAiBE,IAAjB,CAAsBK,MAAtB,GAA+B,KAA/B;AACA,iBAAKP,UAAL,CAAiBE,IAAjB,CAAsB8B,WAAtB,CAAkCN,MAAlC;AACH;;AACDhB,UAAAA,IAAI,CAACE,IAAL,GAAY,YAAYD,GAAxB;AACH;;AAEOjB,QAAAA,OAAO,CAACuC,KAAD,EAAoB;AAC/B,cAAIC,QAAgB,GAAG,EAAvB;AACA,cAAMpB,GAAG,GAAGmB,KAAK,CAACE,MAAN,CAAapB,YAAb;AAAA;AAAA,uCAAZ;;AACA,cAAID,GAAG,CAACZ,IAAJ,CAASU,IAAT,CAAcC,UAAd,CAAyB,QAAzB,KAAsC,KAAK7B,kBAAL,CAAwBsB,MAAxB,GAAiC,CAA3E,EAA8E;AAC1E,gBAAMK,GAAG,GAAGyB,MAAM,CAACtB,GAAG,CAACZ,IAAJ,CAASU,IAAT,CAAcyB,KAAd,CAAoB,GAApB,EAAyB,CAAzB,CAAD,CAAlB;AACAH,YAAAA,QAAQ,GAAG,KAAKlD,kBAAL,CAAwB2B,GAAxB,CAAX;AACH,WAHD,MAGO;AACHuB,YAAAA,QAAQ,GAAG,KAAK5C,WAAhB;AACH;;AACD,cAAMgD,SAAS,GAAGJ,QAAQ,IAAI,KAAK9C,YAAnC;AACA,eAAKA,YAAL,GAAoB8C,QAApB;;AACA,cAAMK,QAAQ,GAAG,KAAKtD,iBAAL,CAAuBa,MAAvB,CAA8BY,IAAI,IAAIA,IAAI,KAAK,KAAKtB,YAApD,CAAjB;;AACA,eAAKJ,kBAAL,GAA0B,CAAC,GAAGuD,QAAJ,CAA1B;;AAEA,cAAI,KAAKvC,UAAL,CAAiBE,IAAjB,CAAsBK,MAA1B,EAAkC;AAC9B,iBAAKH,QAAL,CAAc,CAACkC,SAAf;AACH,WAFD,MAEO;AACH,iBAAK9B,MAAL;AACH;;AACD,cAAI,CAAC8B,SAAL,EAAgB;AACZ,iBAAKnD,QAAL,CAAe,KAAKC,YAApB;AACH;AACJ;;AArGmC,O;;;;;iBAEV,I;;;;;;;iBAEU,I", "sourcesContent": ["import { _decorator, Component, EventTouch, instantiate, Layout, Node, ScrollView, UITransform, Vec3 } from 'cc';\nimport { ButtonPlus } from '../button/ButtonPlus';\nimport List from '../list/List';\nconst { ccclass, property } = _decorator;\n\n\n@ccclass('DropDown')\nexport class DropDown extends Component {\n    @property(List)\n    optionList: List | null = null\n    @property(ButtonPlus)\n    statusMarkNode: ButtonPlus | null = null\n\n    private _currentOptKeyList: any[] = [];\n    private _originOptKeyList: any[] = [];\n    private _renderFunc: ((item: Node, optKey: string) => void) | null = null;\n    private _onClick: ((optKey: string) => void) | null = null;\n    private _selectedKey: any = null;\n    private _expandBtn: ButtonPlus | null = null;\n    get selectedKey() {\n        return this._selectedKey\n    }\n\n    protected onLoad(): void {\n        this.statusMarkNode!.addClick(this.onClick, this)\n        //this.expandBtn.addClick(this.unExpand, this)\n    }\n\n    init(optKeyList: any[], renderFunc: (item: Node, optKey: string) => void, onClick: (optKey: string) => void) {\n        this._originOptKeyList = [...optKeyList];\n        this._renderFunc = renderFunc;\n        this._onClick = onClick;\n        this._selectedKey = this._originOptKeyList[0];\n        this._currentOptKeyList = [...optKeyList.filter(v => v != this._selectedKey)];\n        this.optionList!.numItems = 1;\n        this.statusMarkNode!.node.angle = 0\n    }\n\n    unExpand(isUpdate?: boolean) {\n        if (this._currentOptKeyList.length == 0) {\n            return\n        }\n        this.optionList!.node.active = false;\n        this.statusMarkNode!.node.angle = 0\n        if (isUpdate) {\n            this._renderFunc!(this._expandBtn!.node, this._selectedKey)\n        }\n    }\n\n    expand() {\n        if (this._currentOptKeyList.length == 0) {\n            return\n        }\n        this.optionList!.node.active = true;\n        this.statusMarkNode!.node.angle = 90\n        this._renderFunc!(this._expandBtn!.node, this._selectedKey)\n        this.optionList!.numItems = this._currentOptKeyList.length;\n    }\n\n    private onListRender(item: Node, idx: number) {\n        if (!item.name.startsWith(\"dwItem\")) {\n            const btn = item.getComponent(ButtonPlus);\n            btn!.addClick(this.onClick, this)\n        }\n        let optKey = this.selectedKey\n        if (this._currentOptKeyList.length > 0) {\n            optKey = this._currentOptKeyList[idx]\n        }\n        const listPos = this.optionList!.node.getPosition();\n        this._renderFunc!(item, optKey);\n        if (!this._expandBtn) {\n            this._expandBtn = instantiate(item).getComponent(ButtonPlus);\n            this._expandBtn!.addClick(this.onClick, this)\n            this._expandBtn!.node.parent = this.optionList!.node.parent\n            this._expandBtn!.node.setWorldPosition(item.getWorldPosition())\n            this._renderFunc!(this._expandBtn!.node, this._selectedKey)\n\n            const itemSize = item.getComponent(UITransform)!.contentSize\n            const layout = this.optionList!.getComponent(ScrollView)!.content!.getComponent(Layout)\n            const newPos = new Vec3(listPos.x, listPos.y - itemSize.height - layout!.paddingTop, listPos.z)\n            this.optionList!.node.active = false;\n            this.optionList!.node.setPosition(newPos)\n        }\n        item.name = \"dwItem_\" + idx\n    }\n\n    private onClick(event: EventTouch) {\n        let clickKey: string = \"\"\n        const btn = event.target.getComponent(ButtonPlus);\n        if (btn.node.name.startsWith(\"dwItem\") && this._currentOptKeyList.length > 0) {\n            const idx = Number(btn.node.name.split(\"_\")[1])\n            clickKey = this._currentOptKeyList[idx]\n        } else {\n            clickKey = this.selectedKey;\n        }\n        const isSameKey = clickKey == this._selectedKey;\n        this._selectedKey = clickKey;\n        const filtered = this._originOptKeyList.filter(item => item !== this._selectedKey);\n        this._currentOptKeyList = [...filtered];\n\n        if (this.optionList!.node.active) {\n            this.unExpand(!isSameKey);\n        } else {\n            this.expand();\n        }\n        if (!isSameKey) {\n            this._onClick!(this._selectedKey);\n        }\n    }\n}"]}