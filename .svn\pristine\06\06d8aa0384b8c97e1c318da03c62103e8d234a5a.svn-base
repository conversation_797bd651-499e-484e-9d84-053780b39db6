import { _decorator, Component } from 'cc';
import BaseComp from './BaseComp';
import FCollider from '../../collider-system/FCollider';

const { ccclass } = _decorator;

export enum eEntityTag {
    None = 0,
    Player = 1,
    Enemy = 2,
    PlayerBullet = 4,
    EnemyBullet = 8,
    Boss = 16,
}

@ccclass('Entity')
export default class Entity extends Component {

    new_uuid = 0;
    m_comps = new Map<string, BaseComp>(); // 存储组件的 Map
    m_tags: eEntityTag = eEntityTag.None;

    init() {
        this.initComps()
    }

    initComps() {
        this.m_comps.forEach((comp) => {
            comp.init(this);
        });
    }

    addTag(tag: eEntityTag) {
        this.m_tags |= tag;
    }

    removeTag(tag: eEntityTag) {
        this.m_tags &= ~tag;
    }

    hasTag(tag: eEntityTag): boolean {
        return (this.m_tags & tag) !== 0;
    }

    clearTags() {
        this.m_tags = eEntityTag.None;
    }

    /**
     * 获取指定类型的组件
     * @param {string} type 组件类型
     * @returns {any} 组件实例
     */
    getComp(type:string) {
        return this.m_comps.get(type);
    }

    /**
     * 获取指定类型的所有组件
     * @param {string} type 组件类型
     * @returns {any} 组件实例
     */
    getComps(type: string) {
        return this.m_comps.get(type);
    }

    /**
     * 添加组件
     * @param {string} type 组件类型
     * @param {any} comp 组件实例
     * @returns {any} 添加的组件实例
     */
    addComp(type: string, comp: BaseComp) {
        this.m_comps.set(type, comp);
        return comp;
    }

    /**
     * 移除指定类型的组件
     * @param {string} type 组件类型
     */
    removeComp(type: string) {
        const comp = this.getComp(type);
        if (comp) {
            comp.remove();
        }
        this.m_comps.delete(type);
    }

    /**
     * 移除所有组件
     */
    removeAllComp() {
        if (this.m_comps != null) {
            Array.from(this.m_comps.values()).forEach((comp) => {
                comp.remove();
            });
            this.m_comps.clear();
        }
    }

    /**
     * 移除除指定组件外的其他组件
     * @param {BaseComp} keepComp 要保留的组件
     */
    removeOtherComps(keepComp: BaseComp) {
        if (this.m_comps != null) {
            this.m_comps.forEach((comp, type) => {
                if (comp !== keepComp) {
                    this.m_comps.delete(type);
                }
            });
        }
    }

    onCollide(collision: FCollider) {
        
    }

    onOutScreen() {
        
    }
}