{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts"], "names": ["_decorator", "BundleName", "ButtonPlus", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "TalentUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getBundleName", "HomeTalent", "onLoad", "onShow", "onHide", "onClose", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AAEAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OACX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBN,U;;0BAGjBO,Q,WADZF,OAAO,CAAC,UAAD,C,UAKHC,QAAQ;AAAA;AAAA,mC,2BALb,MACaC,QADb;AAAA;AAAA,4BACqC;AAAA;AAAA;;AAAA;AAAA;;AACb,eAANC,MAAM,GAAW;AAAE,iBAAO,oBAAP;AAA8B;;AACzC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AACpC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,UAAlB;AAA8B;;AAI5DC,QAAAA,MAAM,GAAS,CAExB;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAlBgC,O;;;;;iBAKF,I", "sourcesContent": ["import { _decorator } from 'cc';\n\nimport { BundleName } from 'db://assets/bundles/Bundle';\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\nimport { BaseUI, UILayer } from 'db://assets/scripts/ui/UIMgr';\nconst { ccclass, property } = _decorator;\n\n@ccclass('TalentUI')\nexport class TalentUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/TalentUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    public static getBundleName(): string { return BundleName.HomeTalent }\n    @property(ButtonPlus)\n    btnBattle: ButtonPlus | null = null;\n\n    protected onLoad(): void {\n\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}