System.register(["__unresolved_0", "cc"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, Node, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, uiSelectItem;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfuiSelect(extras) {
    _reporterNs.report("uiSelect", "./uiSelect", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      Node = _cc.Node;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "758be6Gva5HD64SecpouLyN", "uiSelectItem", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("uiSelectItem", uiSelectItem = (_dec = ccclass('uiSelectItem'), _dec2 = property(Label), _dec(_class = (_class2 = class uiSelectItem extends Component {
        constructor() {
          super(...arguments);

          // 绑定的文本组件
          _initializerDefineProperty(this, "labelValue", _descriptor, this);

          // uiSelect组件
          this._select = void 0;
          // 项目数据
          this.itemData = void 0;
        }

        /**
         * 更新项目数据
         * @param itemData 项目数据
         */
        updateValue(_select, itemData) {
          this._select = _select;
          this.itemData = itemData;
          this.labelValue.string = this.itemData;
        }

        onLoad() {
          // 注册点击事件
          this.node.on(Node.EventType.TOUCH_END, this.onChoose, this);
        }

        onDestroy() {
          // 注销点击事件
          this.node.off(Node.EventType.TOUCH_END, this.onChoose, this);
        } // 选择当前项目


        onChoose() {
          this._select.onChooseItem(this.itemData);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "labelValue", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: null
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=30ed714289e1a7c4347955fc87830b6ec0dcc4f2.js.map