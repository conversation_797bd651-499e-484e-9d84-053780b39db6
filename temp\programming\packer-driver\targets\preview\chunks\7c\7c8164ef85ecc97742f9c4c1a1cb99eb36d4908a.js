System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, CCFloat, CCInteger, Enum, Node, Prefab, LayerType, LevelEditorUtils, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _dec4, _dec5, _dec6, _class4, _class5, _descriptor3, _descriptor4, _dec7, _dec8, _dec9, _class7, _class8, _descriptor5, _descriptor6, _dec10, _dec11, _class10, _class11, _descriptor7, _dec12, _dec13, _dec14, _dec15, _dec16, _dec17, _class13, _class14, _descriptor8, _descriptor9, _descriptor10, _descriptor11, _descriptor12, _dec18, _dec19, _class16, _class17, _descriptor13, _crd, ccclass, property, LevelScrollLayerUI, LevelRandTerrainUI, LevelRandTerrainsLayerUI, LevelRandTerrainsLayersUI, LevelLayer, LevelBackgroundLayer;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLayerType(extras) {
    _reporterNs.report("LayerType", "db://assets/scripts/leveldata/leveldata", _context.meta, extras);
  }

  _export("LevelEditorUtils", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      CCFloat = _cc.CCFloat;
      CCInteger = _cc.CCInteger;
      Enum = _cc.Enum;
      Node = _cc.Node;
      Prefab = _cc.Prefab;
    }, function (_unresolved_2) {
      LayerType = _unresolved_2.LayerType;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "afa21Rw2yVKupu5NJaG6p2/", "utils", undefined);

      __checkObsolete__(['__private', '_decorator', 'CCFloat', 'CCInteger', 'Component', 'Enum', 'Node', 'Prefab']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("LevelScrollLayerUI", LevelScrollLayerUI = (_dec = ccclass('LevelEditorScrollLayerUI'), _dec2 = property({
        type: Prefab,
        displayName: '滚动体'
      }), _dec3 = property({
        type: CCInteger,
        displayName: "权重"
      }), _dec(_class = (_class2 = class LevelScrollLayerUI {
        constructor() {
          _initializerDefineProperty(this, "scrollPrefab", _descriptor, this);

          _initializerDefineProperty(this, "weight", _descriptor2, this);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "scrollPrefab", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "weight", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 100;
        }
      })), _class2)) || _class));

      _export("LevelRandTerrainUI", LevelRandTerrainUI = (_dec4 = ccclass('LevelEditorRandTerrainUI'), _dec5 = property({
        type: CCInteger,
        displayName: "权重"
      }), _dec6 = property({
        type: [Prefab],
        displayName: "地形组预制体"
      }), _dec4(_class4 = (_class5 = class LevelRandTerrainUI {
        constructor() {
          _initializerDefineProperty(this, "weight", _descriptor3, this);

          _initializerDefineProperty(this, "terrainElements", _descriptor4, this);
        } // 可以是TerrainElem、DynamicTerrains的预制体


      }, (_descriptor3 = _applyDecoratedDescriptor(_class5.prototype, "weight", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 100;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class5.prototype, "terrainElements", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class5)) || _class4));

      _export("LevelRandTerrainsLayerUI", LevelRandTerrainsLayerUI = (_dec7 = ccclass('LevelEditorRandTerrainsLayerUI'), _dec8 = property({
        type: CCInteger,
        displayName: "权重"
      }), _dec9 = property({
        type: [LevelRandTerrainUI],
        displayName: "地形策略"
      }), _dec7(_class7 = (_class8 = class LevelRandTerrainsLayerUI {
        constructor() {
          _initializerDefineProperty(this, "weight", _descriptor5, this);

          _initializerDefineProperty(this, "dynamicTerrains", _descriptor6, this);
        }

      }, (_descriptor5 = _applyDecoratedDescriptor(_class8.prototype, "weight", [_dec8], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 100;
        }
      }), _descriptor6 = _applyDecoratedDescriptor(_class8.prototype, "dynamicTerrains", [_dec9], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class8)) || _class7));

      _export("LevelRandTerrainsLayersUI", LevelRandTerrainsLayersUI = (_dec10 = ccclass('LevelEditorRandTerrainsLayersUI'), _dec11 = property({
        type: [LevelRandTerrainsLayerUI],
        displayName: "地形策略组"
      }), _dec10(_class10 = (_class11 = class LevelRandTerrainsLayersUI {
        constructor() {
          _initializerDefineProperty(this, "dynamicTerrains", _descriptor7, this);
        }

      }, (_descriptor7 = _applyDecoratedDescriptor(_class11.prototype, "dynamicTerrains", [_dec11], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class11)) || _class10));

      _export("LevelLayer", LevelLayer = (_dec12 = ccclass('LevelEditorLayer'), _dec13 = property(Node), _dec14 = property({
        type: CCFloat,
        displayName: "速度"
      }), _dec15 = property({
        type: Enum(_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
          error: Error()
        }), LayerType) : LayerType),
        displayName: "地形类型"
      }), _dec16 = property({
        type: [LevelScrollLayerUI],
        displayName: "滚动组",
        visible: function visible() {
          return this.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Scroll;
        }
      }), _dec17 = property({
        type: [LevelRandTerrainsLayersUI],
        displayName: "随机组",
        visible: function visible() {
          return this.type === (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Random;
        }
      }), _dec12(_class13 = (_class14 = class LevelLayer {
        constructor() {
          _initializerDefineProperty(this, "node", _descriptor8, this);

          _initializerDefineProperty(this, "speed", _descriptor9, this);

          _initializerDefineProperty(this, "type", _descriptor10, this);

          _initializerDefineProperty(this, "scrollLayers", _descriptor11, this);

          _initializerDefineProperty(this, "randomLayers", _descriptor12, this);
        }

      }, (_descriptor8 = _applyDecoratedDescriptor(_class14.prototype, "node", [_dec13], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor9 = _applyDecoratedDescriptor(_class14.prototype, "speed", [_dec14], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 10;
        }
      }), _descriptor10 = _applyDecoratedDescriptor(_class14.prototype, "type", [_dec15], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return (_crd && LayerType === void 0 ? (_reportPossibleCrUseOfLayerType({
            error: Error()
          }), LayerType) : LayerType).Background;
        }
      }), _descriptor11 = _applyDecoratedDescriptor(_class14.prototype, "scrollLayers", [_dec16], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      }), _descriptor12 = _applyDecoratedDescriptor(_class14.prototype, "randomLayers", [_dec17], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class14)) || _class13));

      _export("LevelBackgroundLayer", LevelBackgroundLayer = (_dec18 = ccclass('LevelEditorBackgroundLayer'), _dec19 = property({
        type: [Prefab],
        displayName: '背景组'
      }), _dec18(_class16 = (_class17 = class LevelBackgroundLayer extends LevelLayer {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "backgrounds", _descriptor13, this);

          this.backgroundsNode = null;
        }

      }, (_descriptor13 = _applyDecoratedDescriptor(_class17.prototype, "backgrounds", [_dec19], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return [];
        }
      })), _class17)) || _class16));

      _export("LevelEditorUtils", LevelEditorUtils = class LevelEditorUtils {
        static getOrAddNode(node_parent, name) {
          var node = node_parent.getChildByName(name);

          if (node == null) {
            node = new Node(name);
            node_parent.addChild(node);
          }

          return node;
        }

        static getOrAddComp(node, classConstructor) {
          var comp = node.getComponent(classConstructor);

          if (comp == null) {
            comp = node.addComponent(classConstructor);
          }

          return comp;
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7c8164ef85ecc97742f9c4c1a1cb99eb36d4908a.js.map