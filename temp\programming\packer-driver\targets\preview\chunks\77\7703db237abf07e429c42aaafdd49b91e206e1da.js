System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, sp, Button, LevelEditorUtils, _dec, _dec2, _dec3, _class, _class2, _descriptor, _crd, ccclass, property, executeInEditMode, PlaneView;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfLevelEditorUtils(extras) {
    _reporterNs.report("LevelEditorUtils", "../level/utils", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      sp = _cc.sp;
      Button = _cc.Button;
    }, function (_unresolved_2) {
      LevelEditorUtils = _unresolved_2.LevelEditorUtils;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "41ecdGSSbZMUL0dPPZc+eh0", "PlaneView", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'sp', 'CCString', 'CCFloat', 'CCBoolean', 'Button']);

      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("PlaneView", PlaneView = (_dec = ccclass('PlaneView'), _dec2 = executeInEditMode(true), _dec3 = property({
        type: sp.SkeletonData,
        displayName: "Spine骨骼数据",
        tooltip: "拖拽Spine骨骼数据到这里"
      }), _dec(_class = _dec2(_class = (_class2 = class PlaneView extends Component {
        constructor() {
          super(...arguments);

          // ========== Spine相关属性 ==========
          _initializerDefineProperty(this, "skeletonData", _descriptor, this);

          this._animSpeed = 1.0;
          // ========== 私有属性 ==========
          this.spine = null;
          this.currentAnimIndex = 0;
          this.cycle = true;
        }

        set animSpeed(value) {
          this._animSpeed = value;

          if (this.spine) {
            this.spine.timeScale = value;
          }
        }

        onLoad() {
          this.initSpine();
        }

        start() {
          var playNode = this.node.getChildByPath("ui/play");
          playNode.getComponentsInChildren(Button).forEach(button => {
            button.node.on(Button.EventType.CLICK, () => {
              this.playAnimation(button.node.name);
            });
          });
        }
        /**
         * 初始化Spine组件
         */


        initSpine() {
          // 获取或创建Spine组件
          this.spine = (_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddComp((_crd && LevelEditorUtils === void 0 ? (_reportPossibleCrUseOfLevelEditorUtils({
            error: Error()
          }), LevelEditorUtils) : LevelEditorUtils).getOrAddNode(this.node, "plane"), sp.Skeleton); // 设置骨骼数据

          if (this.skeletonData) {
            this.spine.skeletonData = this.skeletonData;
          } // 设置播放速度


          this.spine.timeScale = this._animSpeed; // 设置动画完成监听

          this.spine.setCompleteListener(trackEntry => {
            var _trackEntry$animation;

            console.log("\u52A8\u753B\u64AD\u653E\u5B8C\u6210: " + ((_trackEntry$animation = trackEntry.animation) == null ? void 0 : _trackEntry$animation.name));
          });
        }
        /**
         * 播放指定动画
         * @param animName 动画名称
         * @param loop 是否循环
         */


        playAnimation(animName) {
          if (!this.spine) {
            console.warn("Spine组件未初始化");
            return;
          }

          if (!animName) {
            console.warn("动画名称为空");
            return;
          }

          if (animName == 'Enter') {
            animName = 'animation';
          } else if (animName == 'Moveleft') {
            animName = 'animation2';
          }

          this._animSpeed = 1.0;

          try {
            this.spine.setAnimation(0, animName, this.cycle);
            console.log("\u64AD\u653E\u52A8\u753B: " + animName + ", \u5FAA\u73AF: " + this.cycle);
          } catch (error) {
            console.error("\u64AD\u653E\u52A8\u753B\u5931\u8D25: " + animName, error);
          }
        }
        /**
         * 停止当前动画
         */


        stopAnimation() {
          if (this.spine) {
            this.spine.clearTracks();
            console.log("停止动画");
          }
        }
        /**
         * 暂停/恢复动画
         */


        pauseResumeAnimation() {
          if (this.spine) {
            this.spine.paused = !this.spine.paused;
            console.log(this.spine.paused ? "暂停动画" : "恢复动画");
          }
        }

        setAnimationCycle() {
          this.cycle = !this.cycle;

          if (this.spine) {
            var _this$spine$getCurren;

            this.spine.setAnimation(0, ((_this$spine$getCurren = this.spine.getCurrent(0)) == null || (_this$spine$getCurren = _this$spine$getCurren.animation) == null ? void 0 : _this$spine$getCurren.name) || "", this.cycle);
          }
        }

        downAnimationSpeed() {
          this.animSpeed = Math.max(this._animSpeed / 2, 0.125);
        }

        upAnimationSpeed() {
          this.animSpeed = Math.min(this._animSpeed * 2, 8);
        }
        /**
         * 获取所有可用的动画名称
         */


        getAvailableAnimations() {
          if (!this.spine || !this.spine.skeletonData) {
            return [];
          }

          var animations = [];
          var skeletonData = this.spine.skeletonData.getRuntimeData();

          if (skeletonData && skeletonData.animations) {
            for (var i = 0; i < skeletonData.animations.length; i++) {
              animations.push(skeletonData.animations[i].name);
            }
          }

          return animations;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "skeletonData", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7703db237abf07e429c42aaafdd49b91e206e1da.js.map