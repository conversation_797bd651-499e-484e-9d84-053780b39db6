System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, misc, Component, Enum, Vec2, Vec3, UITransform, BulletSystem, _dec, _dec2, _class, _class2, _descriptor, _crd, degreesToRadians, radiansToDegrees, ccclass, property, executeInEditMode, eSpriteDefaultFacing, Movable;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfIMovable(extras) {
    _reporterNs.report("IMovable", "./IMovable", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBulletSystem(extras) {
    _reporterNs.report("BulletSystem", "../bullet/BulletSystem", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      misc = _cc.misc;
      Component = _cc.Component;
      Enum = _cc.Enum;
      Vec2 = _cc.Vec2;
      Vec3 = _cc.Vec3;
      UITransform = _cc.UITransform;
    }, function (_unresolved_2) {
      BulletSystem = _unresolved_2.BulletSystem;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "69d1c91vGlPmascxGbqaXaG", "Movable", undefined);

      __checkObsolete__(['_decorator', 'misc', 'size', 'Component', 'Enum', 'Vec2', 'Vec3', 'Node', 'UITransform']);

      ({
        degreesToRadians,
        radiansToDegrees
      } = misc);
      ({
        ccclass,
        property,
        executeInEditMode
      } = _decorator);

      _export("eSpriteDefaultFacing", eSpriteDefaultFacing = /*#__PURE__*/function (eSpriteDefaultFacing) {
        eSpriteDefaultFacing[eSpriteDefaultFacing["Right"] = 0] = "Right";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Up"] = -90] = "Up";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Down"] = 90] = "Down";
        eSpriteDefaultFacing[eSpriteDefaultFacing["Left"] = 180] = "Left";
        return eSpriteDefaultFacing;
      }({}));

      _export("Movable", Movable = (_dec = ccclass('Movable'), _dec2 = property({
        type: Enum(eSpriteDefaultFacing),
        displayName: '图片默认朝向'
      }), _dec(_class = executeInEditMode(_class = (_class2 = class Movable extends Component {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "defaultFacing", _descriptor, this);

          this.isFacingMoveDir = false;
          // 是否朝向行进方向
          this.isTrackingTarget = false;
          // 是否正在追踪目标
          this.speed = 1;
          // 速度
          this.speedAngle = 0;
          // 速度方向 (用角度表示)
          this.turnSpeed = 60;
          // 转向速度（仅用在追踪目标时）
          this.acceleration = 0;
          // 加速度
          this.accelerationAngle = 0;
          // 加速度方向 (用角度表示)
          // TODO: 
          this.tiltSpeed = 0;
          // 偏移速度
          this.tiltOffset = 0;
          // 偏移距离
          this.target = null;
          // 追踪的目标节点
          this.arrivalDistance = 10;
          // 到达目标的距离
          this._selfSize = new Vec2();
          this._position = new Vec3();
          this._isVisible = true;
          // Callbacks:
          this.onBecomeVisibleCallback = null;
          this.onBecomeInvisibleCallback = null;
        }

        // 是否可见
        get isVisible() {
          return this._isVisible;
        }

        // public onCollideCallback: Function | null = null;
        onLoad() {
          const uiTransform = this.node.getComponent(UITransform);
          const self_size = uiTransform ? uiTransform.contentSize : {
            width: 0,
            height: 0
          };

          this._selfSize.set(self_size.width / 2, self_size.height / 2);
        }

        tick(dt) {
          // 根据移动属性更新位置
          this.node.getPosition(this._position); // Convert speed and angle to velocity vector

          let velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));
          let velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));

          if (this.isTrackingTarget && this.target) {
            const targetPos = this.target.getPosition();
            const currentPos = this.node.getPosition(); // Calculate direction to target

            const directionX = targetPos.x - currentPos.x;
            const directionY = targetPos.y - currentPos.y;
            const distance = Math.sqrt(directionX * directionX + directionY * directionY);

            if (distance > 0) {
              // Calculate desired angle to target
              const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX)); // Smoothly adjust speedAngle toward target

              const angleDiff = desiredAngle - this.speedAngle; // Normalize angle difference to [-180, 180] range

              const normalizedAngleDiff = (angleDiff + 180) % 360 - 180; // Apply tracking adjustment (you can add a trackingStrength property to control this)

              const trackingStrength = 1.0; // Can be made configurable

              const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable

              const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);
              this.speedAngle += turnAmount * trackingStrength; // Recalculate velocity with new angle

              velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));
              velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));
            }
          } // Convert acceleration and angle to acceleration vector


          const accelerationX = this.acceleration * Math.cos(degreesToRadians(this.accelerationAngle));
          const accelerationY = this.acceleration * Math.sin(degreesToRadians(this.accelerationAngle)); // Update velocity vector: v = v + a * dt

          const newVelocityX = velocityX + accelerationX * dt;
          const newVelocityY = velocityY + accelerationY * dt; // Convert back to speed and angle

          this.speed = Math.sqrt(newVelocityX * newVelocityX + newVelocityY * newVelocityY);
          this.speedAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX)); // Update position: p = p + v * dt

          if (newVelocityX !== 0 || newVelocityY !== 0) {
            this._position.x += newVelocityX * dt;
            this._position.y += newVelocityY * dt;
            this.node.setPosition(this._position); // this.checkVisibility();
          }

          if (this.isFacingMoveDir && this.speed > 0) {
            const movementAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));
            const finalAngle = movementAngle + this.defaultFacing;
            this.node.setRotationFromEuler(0, 0, finalAngle);
          }
        }

        checkVisibility() {
          // 这里目前的检查逻辑没有考虑旋转和缩放
          // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的
          const visibleSize = (_crd && BulletSystem === void 0 ? (_reportPossibleCrUseOfBulletSystem({
            error: Error()
          }), BulletSystem) : BulletSystem).worldBounds;
          const isVisible = this._position.x + this._selfSize.x >= visibleSize.xMin && this._position.x - this._selfSize.x <= visibleSize.xMax && this._position.y - this._selfSize.y >= visibleSize.yMin && this._position.y + this._selfSize.y <= visibleSize.yMax;
          this.setVisible(isVisible);
        }

        setVisible(visible) {
          if (this._isVisible === visible) return;
          this._isVisible = visible;

          if (visible && this.onBecomeVisibleCallback) {
            this.onBecomeVisibleCallback();
          } else if (!visible && this.onBecomeInvisibleCallback) {
            this.onBecomeInvisibleCallback();
          }
        }
        /**
         * Set the target to track
         */


        setTarget(target) {
          this.target = target;
          this.isTrackingTarget = target !== null;
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "defaultFacing", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return eSpriteDefaultFacing.Up;
        }
      })), _class2)) || _class) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=74d99d57d6cae72651016f5964274bf61390b81d.js.map