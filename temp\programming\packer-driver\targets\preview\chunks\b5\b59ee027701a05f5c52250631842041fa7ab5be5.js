System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, Label, math, Node, Sprite, DataMgr, EventMgr, MyApp, logDebug, PlaneUIEvent, ButtonPlus, TabStatus, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _crd, ccclass, property, BagItem;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfDataMgr(extras) {
    _reporterNs.report("DataMgr", "db://assets/bundles/common/script/data/DataManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "db://assets/bundles/common/script/event/EventManager", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResEquip(extras) {
    _reporterNs.report("ResEquip", "db://assets/scripts/AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfResItem(extras) {
    _reporterNs.report("ResItem", "db://assets/scripts/AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "db://assets/scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogDebug(extras) {
    _reporterNs.report("logDebug", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneUIEvent(extras) {
    _reporterNs.report("PlaneUIEvent", "../../../../event/PlaneUIEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../../../common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabStatus(extras) {
    _reporterNs.report("TabStatus", "../../PlaneTypes", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      Label = _cc.Label;
      math = _cc.math;
      Node = _cc.Node;
      Sprite = _cc.Sprite;
    }, function (_unresolved_2) {
      DataMgr = _unresolved_2.DataMgr;
    }, function (_unresolved_3) {
      EventMgr = _unresolved_3.EventMgr;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      logDebug = _unresolved_5.logDebug;
    }, function (_unresolved_6) {
      PlaneUIEvent = _unresolved_6.PlaneUIEvent;
    }, function (_unresolved_7) {
      ButtonPlus = _unresolved_7.ButtonPlus;
    }, function (_unresolved_8) {
      TabStatus = _unresolved_8.TabStatus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "ba4ccxmS+JKWLAx1Q7EdsGk", "BagItem", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Label', 'math', 'Node', 'Sprite']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("BagItem", BagItem = (_dec = ccclass('BagItem'), _dec2 = property(Node), _dec3 = property(Node), _dec4 = property(Label), _dec(_class = (_class2 = class BagItem extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "selectedIcon", _descriptor, this);

          _initializerDefineProperty(this, "mask", _descriptor2, this);

          _initializerDefineProperty(this, "itemNum", _descriptor3, this);

          this._item = null;
          this._tabStatus = (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).None;
          this._fakeColors = ["#A0A0A0", "#1EFF00", "#0070FF", "#A335EE", "#FF8000", "#80e6e6ff", "#E6CC80"];
        }

        onLoad() {
          this.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus).addClick(this.onClick, this);
        }

        onDestroy() {
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).targetOff(this);
        }

        onClick() {
          (_crd && logDebug === void 0 ? (_reportPossibleCrUseOflogDebug({
            error: Error()
          }), logDebug) : logDebug)("PlaneUI", "onClick");
          if (!this._item) return;

          if (this._tabStatus == (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).Merge) {
            if (this.mask.active && !this.selectedIcon) {
              return;
            }

            if (!this.mask.active && (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).equip.eqCombine.isFull()) {
              return;
            }
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && PlaneUIEvent === void 0 ? (_reportPossibleCrUseOfPlaneUIEvent({
            error: Error()
          }), PlaneUIEvent) : PlaneUIEvent).BagItemClick, this._item);
        }

        onBagTabStatusRender(item) {
          this._tabStatus = (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).Bag;
          this._item = item;
          this.selectedIcon.active = false;
          this.mask.active = false;
          this.onMetaDataRender();
        }

        onCombineTabStatusRender(item) {
          this._tabStatus = (_crd && TabStatus === void 0 ? (_reportPossibleCrUseOfTabStatus({
            error: Error()
          }), TabStatus) : TabStatus).Merge;
          this._item = item;

          if ((_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
            error: Error()
          }), DataMgr) : DataMgr).equip.eqCombine.isCanCombine(this._item)) {
            var info = (_crd && DataMgr === void 0 ? (_reportPossibleCrUseOfDataMgr({
              error: Error()
            }), DataMgr) : DataMgr).equip.eqCombine.getByGuid(this._item.guid);

            if (info) {
              this.selectedIcon.active = true;
              this.mask.active = true;
            } else {
              this.selectedIcon.active = false;
              this.mask.active = false;
            }
          } else {
            this.selectedIcon.active = false;
            this.mask.active = true;
          }

          this.onMetaDataRender();
        }

        onMetaDataRender() {
          var _item_id;

          var EquipCfg = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbEquip.get((_item_id = this._item.item_id) != null ? _item_id : 0);

          if (EquipCfg) {
            this.onEquipDataRender(EquipCfg);
          } else {
            var _item_id2;

            var itemCfg = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
              error: Error()
            }), MyApp) : MyApp).lubanTables.TbItem.get((_item_id2 = this._item.item_id) != null ? _item_id2 : 0);

            if (itemCfg) {
              this.onItemDataRender(itemCfg);
            } else {
              this.getComponentInChildren(Label).string = "未知";
            }
          }
        }

        onEquipDataRender(equipCfg) {
          this.itemNum.node.active = false;
          this.getComponentInChildren(Label).string = (equipCfg == null ? void 0 : equipCfg.name) + ("(\u54C1\u8D28:" + (equipCfg == null ? void 0 : equipCfg.quality) + ")");
          this.node.getComponentInChildren(Sprite).color = math.color(this._fakeColors[equipCfg.quality]);
        }

        onItemDataRender(itemCfg) {
          var _toString, _this$_item;

          this.itemNum.node.active = true;
          this.itemNum.string = (_toString = (_this$_item = this._item) == null ? void 0 : _this$_item.count.toString()) != null ? _toString : "0";
          this.getComponentInChildren(Label).string = (itemCfg == null ? void 0 : itemCfg.name) + ("(\u54C1\u8D28:" + (itemCfg == null ? void 0 : itemCfg.quality) + ")");
          this.node.getComponentInChildren(Sprite).color = math.color(this._fakeColors[itemCfg.quality]);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "selectedIcon", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "mask", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "itemNum", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b59ee027701a05f5c52250631842041fa7ab5be5.js.map