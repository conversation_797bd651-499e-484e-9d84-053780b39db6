{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts"], "names": ["_decorator", "CCFloat", "Component", "Node", "CCString", "assetManager", "instantiate", "UITransform", "view", "Graphics", "Color", "Rect", "Vec2", "LayerType", "LevelDataBackgroundLayer", "LevelDataLayer", "LevelDataRandTerrains", "LevelDataScroll", "LevelEditorLayerUI", "LevelBackgroundLayer", "LevelEditorUtils", "<PERSON><PERSON><PERSON><PERSON>", "LevelScrollLayerUI", "ccclass", "property", "executeInEditMode", "BackgroundsNodeName", "LevelEditorBaseUI", "type", "displayName", "_totalHeight", "backgroundLayerNode", "floorLayersNode", "skyLayersNode", "drawNode", "graphics", "onLoad", "console", "log", "getOrAddNode", "node", "getComponent", "addComponent", "uuid", "update", "dt", "checkLayerNode", "floorLayers", "skyLayers", "for<PERSON>ach", "layer", "_checkScrollNode", "_checkRandTerrainNode", "setBackgroundNodePosition", "yOff", "height", "contentSize", "setPosition", "getVisibleSize", "tick", "progress", "i", "background<PERSON>ayer", "backgroundsNode", "children", "length", "bg", "backgrounds", "bgIndex", "prefab", "<PERSON><PERSON><PERSON><PERSON>", "position", "y", "removeFromParent", "totalTime", "speed", "add<PERSON><PERSON>er", "parentNode", "name", "layerNode", "layerCom", "layers", "removeLayerNodes", "push", "find", "element", "<PERSON><PERSON><PERSON><PERSON>", "data", "scrollsNode", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isCountMatch", "scrollLayers", "isUUIDMatch", "scrollNode", "scrollPrefabUUID", "<PERSON><PERSON><PERSON><PERSON>", "_prefab", "asset", "_uuid", "scrollUUID", "scrollPrefab", "scroll", "index", "loadAny", "err", "error", "totalHeight", "childCount", "posOffsetY", "child", "offY", "dynamicNode", "Random", "randomLayers", "dynaNode", "randTerrains", "dynamicTerrains", "terrains", "terrain", "terrainElements", "initByLevelData", "levelname", "background", "setSiblingIndex", "scrolls", "scrollData", "scroll<PERSON>ayer", "initLayers", "dataLayers", "<PERSON><PERSON>ayer", "levelEditorLayerUI", "initScorllsByLevelData", "fillLevelLayerData", "dataLayer", "weight", "ZERO", "scale", "ONE", "rotation", "random<PERSON>ayer", "terrainElement", "terrainData", "dynamics", "fillLevelData", "fillLevelLayersData", "drawViewport", "drawTransform", "viewport", "getPosition", "x", "width", "strokeColor", "RED", "lineWidth", "rect", "stroke", "<PERSON><PERSON><PERSON><PERSON>", "maskHeight", "fillColor", "BLACK", "fillRect", "drawClear", "clear"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAkBC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;;AAE7HC,MAAAA,S,iBAAAA,S;AAAsBC,MAAAA,wB,iBAAAA,wB;AAA0BC,MAAAA,c,iBAAAA,c;AAAsCC,MAAAA,qB,iBAAAA,qB;AAAuBC,MAAAA,e,iBAAAA,e;;AAC7GC,MAAAA,kB,iBAAAA,kB;;AACAC,MAAAA,oB,iBAAAA,oB;AAAsBC,MAAAA,gB,iBAAAA,gB;AAAkBC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OAHvD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CzB,U;AAK3C0B,MAAAA,mB,GAAsB,a;;mCAIfC,iB,WAFZJ,OAAO,CAAC,mBAAD,C,UACPE,iBAAiB,E,UAEbD,QAAQ,CAACpB,QAAD,C,UAERoB,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAC3B,OAAN;AAAe4B,QAAAA,WAAW,EAAC;AAA3B,OAAD,C,UAIRL,QAAQ;AAAA;AAAA,uD,UAERA,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAC;AAAA;AAAA,qCAAN;AAAoBC,QAAAA,WAAW,EAAC;AAAhC,OAAD,C,UAERL,QAAQ,CAAC;AAACI,QAAAA,IAAI,EAAC;AAAA;AAAA,qCAAN;AAAoBC,QAAAA,WAAW,EAAC;AAAhC,OAAD,C,0CAbb,MAEaF,iBAFb,SAEuCzB,SAFvC,CAEiD;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAKrC4B,YALqC,GAKd,CALc;;AAAA;;AAAA;;AAAA;;AAAA,eAcrCC,mBAdqC,GAcL,IAdK;AAAA,eAerCC,eAfqC,GAeT,IAfS;AAAA,eAgBrCC,aAhBqC,GAgBX,IAhBW;AAAA,eAkBrCC,QAlBqC,GAkBb,IAlBa;AAAA,eAmBrCC,QAnBqC,GAmBT,IAnBS;AAAA;;AAqB7CC,QAAAA,MAAM,GAAQ;AAAA;;AACVC,UAAAA,OAAO,CAACC,GAAR;AACA,eAAKP,mBAAL,GAA2B;AAAA;AAAA,oDAAiBQ,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,iBAAzC,CAA3B;AACA,eAAKR,eAAL,GAAuB;AAAA;AAAA,oDAAiBO,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,aAAzC,CAAvB;AACA,eAAKP,aAAL,GAAqB;AAAA;AAAA,oDAAiBM,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,WAAzC,CAArB;AAEA,eAAKN,QAAL,GAAgB;AAAA;AAAA,oDAAiBK,YAAjB,CAA8B,KAAKC,IAAnC,EAAyC,UAAzC,CAAhB;;AACA,cAAI,CAAC,KAAKL,QAAV,EAAoB;AAChB,iBAAKA,QAAL,GAAgB,KAAKD,QAAL,CAAcO,YAAd,CAA2BhC,QAA3B,KAAwC,KAAKyB,QAAL,CAAcQ,YAAd,CAA2BjC,QAA3B,CAAxD;AACH;;AAED4B,UAAAA,OAAO,CAACC,GAAR,wDAAuC,KAAKN,eAA5C,qBAAuC,sBAAsBW,IAA7D;AACH;;AACDC,QAAAA,MAAM,CAACC,EAAD,EAAiB;AACnB,eAAKC,cAAL,CAAoB,KAAKd,eAAzB,EAA2C,KAAKe,WAAhD;AACA,eAAKD,cAAL,CAAoB,KAAKb,aAAzB,EAAyC,KAAKe,SAA9C;AACA,eAAKD,WAAL,CAAiBE,OAAjB,CAA0BC,KAAD,IAAW;AAChC,iBAAKC,gBAAL,CAAsBD,KAAtB,EAA4BA,KAAK,CAACV,IAAlC;;AACA,iBAAKY,qBAAL,CAA2BF,KAA3B,EAAiCA,KAAK,CAACV,IAAvC;AACH,WAHD;AAIA,eAAKQ,SAAL,CAAeC,OAAf,CAAwBC,KAAD,IAAW;AAC9B,iBAAKC,gBAAL,CAAsBD,KAAtB,EAA4BA,KAAK,CAACV,IAAlC;;AACA,iBAAKY,qBAAL,CAA2BF,KAA3B,EAAiCA,KAAK,CAACV,IAAvC;AACH,WAHD;AAIH;;AACOa,QAAAA,yBAAyB,CAACb,IAAD,EAAYc,IAAZ,EAAgC;AAC7D,cAAMC,MAAM,GAAGf,IAAI,CAACC,YAAL,CAAkBlC,WAAlB,EAAgCiD,WAAhC,CAA4CD,MAA3D;AACAf,UAAAA,IAAI,CAACiB,WAAL,CAAiB,CAAjB,EAAoBH,IAAI,GAAC9C,IAAI,CAACkD,cAAL,GAAsBH,MAAtB,GAA6B,CAAlC,GAAoCA,MAAM,GAAC,CAA/D;AACA,iBAAOA,MAAP;AAEH;;AACMI,QAAAA,IAAI,CAACC,QAAD,EAAwB;AAC/B,cAAIN,IAAI,GAAG,CAAX;;AACA,eAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CC,MAAnE,EAA2EJ,CAAC,EAA5E,EAAgF;AAC5E,gBAAIK,EAAE,GAAG,KAAKJ,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CH,CAA/C,CAAT;AACAP,YAAAA,IAAI,IAAI,KAAKD,yBAAL,CAA+Ba,EAA/B,EAAmCZ,IAAnC,CAAR;AACH;;AACD,iBAAM,KAAKQ,eAAL,CAAqBK,WAArB,CAAiCF,MAAjC,GAA0C,CAA1C,IAA+CX,IAAI,GAAG,KAAKxB,YAAjE,EAA+E;AAC3E,gBAAIoC,GAAY,GAAG,IAAnB;AACA,gBAAIE,OAAO,GAAG,KAAKN,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CC,MAA/C,GAAwD,KAAKH,eAAL,CAAqBK,WAArB,CAAiCF,MAAvG;AACA,gBAAMI,MAAM,GAAG,KAAKP,eAAL,CAAqBK,WAArB,CAAiCC,OAAjC,CAAf;;AACA,gBAAIC,MAAM,IAAI,IAAd,EAAoB;AAChBH,cAAAA,GAAE,GAAG5D,WAAW,CAAC+D,MAAD,CAAhB;AACH;;AACD,gBAAIH,GAAE,IAAI,IAAV,EAAgB;AACZA,cAAAA,GAAE,GAAG,IAAI/D,IAAJ,CAAS,OAAT,CAAL;AACA+D,cAAAA,GAAE,CAACxB,YAAH,CAAgBnC,WAAhB,EAA6BgD,MAA7B,GAAsC,IAAtC;AACH;;AACD,iBAAKO,eAAL,CAAqBC,eAArB,CAAsCO,QAAtC,CAA+CJ,GAA/C;AACAZ,YAAAA,IAAI,IAAI,KAAKD,yBAAL,CAA+Ba,GAA/B,EAAmCZ,IAAnC,CAAR;AACH;;AACD,eAAK,IAAIO,EAAC,GAAG,KAAKC,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CC,MAA/C,GAAwD,CAArE,EAAwEJ,EAAC,IAAI,CAA7E,EAAgFA,EAAC,EAAjF,EAAqF;AACjF,gBAAMK,IAAE,GAAG,KAAKJ,eAAL,CAAqBC,eAArB,CAAsCC,QAAtC,CAA+CH,EAA/C,CAAX;;AACA,gBAAIK,IAAE,CAACK,QAAH,CAAYC,CAAZ,GAAgBN,IAAE,CAACzB,YAAH,CAAgBlC,WAAhB,EAA8BgD,MAA9B,GAAqC,CAArD,GAAyD,KAAKzB,YAAlE,EAAgF;AAC5EoC,cAAAA,IAAE,CAACO,gBAAH;AACH,aAFD,MAEO;AACH;AACH;AACJ;;AAED,eAAKX,eAAL,CAAsBtB,IAAtB,CAA4BC,YAA5B;AAAA;AAAA,wDAAkFkB,IAAlF,CACIC,QADJ,EACc,KAAKc,SADnB,EAC8B,KAAKZ,eAAL,CAAqBa,KADnD;AAEA,eAAK5B,WAAL,CAAiBE,OAAjB,CAA0BC,KAAD,IAAW;AAChCA,YAAAA,KAAK,CAACV,IAAN,CAAYC,YAAZ;AAAA;AAAA,0DAAkEkB,IAAlE,CAAuEC,QAAvE,EAAiF,KAAKc,SAAtF,EAAiGxB,KAAK,CAACyB,KAAvG;AACH,WAFD;AAGA,eAAK3B,SAAL,CAAeC,OAAf,CAAwBC,KAAD,IAAW;AAC9BA,YAAAA,KAAK,CAACV,IAAN,CAAYC,YAAZ;AAAA;AAAA,0DAAkEkB,IAAlE,CAAuEC,QAAvE,EAAiF,KAAKc,SAAtF,EAAiGxB,KAAK,CAACyB,KAAvG;AACH,WAFD;AAGH;;AAEsB,eAARC,QAAQ,CAACC,UAAD,EAAmBC,IAAnB,EAAqD;AACxE,cAAIC,SAAS,GAAG,IAAI5E,IAAJ,CAAS2E,IAAT,CAAhB;AACA,cAAIE,QAAQ,GAAGD,SAAS,CAACrC,YAAV;AAAA;AAAA,uDAAf;AACAmC,UAAAA,UAAU,CAACP,QAAX,CAAoBS,SAApB;AACA,iBAAOC,QAAP;AACH;;AAEOlC,QAAAA,cAAc,CAAC+B,UAAD,EAAmBI,MAAnB,EAA8C;AAChE,cAAIC,gBAAwB,GAAG,EAA/B;AACAL,UAAAA,UAAU,CAACb,QAAX,CAAoBf,OAApB,CAA4BT,IAAI,IAAI;AAChC,gBAAIwC,QAAQ,GAAGxC,IAAI,CAACC,YAAL;AAAA;AAAA,yDAAf;;AACA,gBAAIuC,QAAQ,IAAI,IAAhB,EAAsB;AAClB3C,cAAAA,OAAO,CAACC,GAAR,kCAA2CE,IAAI,CAACsC,IAAhD;AACAI,cAAAA,gBAAgB,CAACC,IAAjB,CAAsB3C,IAAtB;AACA;AACH;;AACD,gBAAIyC,MAAM,CAACG,IAAP,CAAalC,KAAD,IAAWA,KAAK,CAACV,IAAN,IAAcA,IAArC,KAA8C,IAAlD,EAAwD;AACpDH,cAAAA,OAAO,CAACC,GAAR,kCAA2CE,IAAI,CAACsC,IAAhD;AACAI,cAAAA,gBAAgB,CAACC,IAAjB,CAAsB3C,IAAtB;AACA;AACH;AACJ,WAZD;AAaA0C,UAAAA,gBAAgB,CAACjC,OAAjB,CAAyBoC,OAAO,IAAI;AAChCA,YAAAA,OAAO,CAACZ,gBAAR;AACH,WAFD;AAGAQ,UAAAA,MAAM,CAAChC,OAAP,CAAe,CAACC,KAAD,EAAQW,CAAR,KAAc;AACzB,gBAAIX,KAAK,CAACV,IAAN,IAAc,IAAd,IAAsBU,KAAK,CAACV,IAAN,CAAW8C,OAAX,IAAsB,KAAhD,EAAuD;AACnDjD,cAAAA,OAAO,CAACC,GAAR;AACAY,cAAAA,KAAK,CAACV,IAAN,GAAab,iBAAiB,CAACiD,QAAlB,CAA2BC,UAA3B,aAAgDhB,CAAhD,EAAqDrB,IAAlE;AACH;AACJ,WALD;AAMH;;AAEOW,QAAAA,gBAAgB,CAACoC,IAAD,EAAmBV,UAAnB,EAA0C;AAC9D,cAAMW,WAAW,GAAG;AAAA;AAAA,oDAAiBjD,YAAjB,CAA8BsC,UAA9B,EAA0C,SAA1C,CAApB;;AACA,cAAIU,IAAI,CAAC3D,IAAL,IAAa;AAAA;AAAA,sCAAU6D,MAA3B,EAAmC;AAC/BD,YAAAA,WAAW,CAACE,iBAAZ;AACA;AACH;;AAED,cAAMC,YAAY,GAAGH,WAAW,CAACxB,QAAZ,CAAqBC,MAArB,KAAgCsB,IAAI,CAACK,YAAL,CAAkB3B,MAAvE;AACA,cAAI4B,WAAW,GAAG,IAAlB;;AACA,eAAK,IAAIhC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2B,WAAW,CAACxB,QAAZ,CAAqBC,MAAzC,EAAiDJ,CAAC,EAAlD,EAAsD;AAAA;;AAClD,gBAAMiC,UAAU,GAAGN,WAAW,CAACxB,QAAZ,CAAqBH,CAArB,CAAnB;AACA,gBAAIkC,gBAAgB,GAAG,EAAvB;AACA,gBAAIC,UAAJ;;AACA,gBAAIF,UAAU,IAAI,IAAlB,EAAwB;AACpBE,cAAAA,UAAU,GAAGF,UAAU,CAAC9B,QAAX,CAAoB,CAApB,CAAb;;AACA,kBAAIgC,UAAU,IAAI,IAAlB,EAAwB;AAAA;;AACpB;AACAD,gBAAAA,gBAAgB,0BAAGC,UAAU,CAACC,OAAd,oCAAG,oBAAoBC,KAAvB,qBAAG,oBAA2BC,KAA9C;AACH;AACJ,aAViD,CAWlD;;;AACA,gBAAMC,UAAU,2BAAGb,IAAI,CAACK,YAAL,CAAkB/B,CAAlB,CAAH,qCAAG,qBAAsBwC,YAAzB,qBAAG,qBAAoCF,KAAvD;;AAEA,gBAAIJ,gBAAgB,IAAIK,UAAxB,EAAoC;AAChC/D,cAAAA,OAAO,CAACC,GAAR,CAAY,mEAAZ,EAAiFyD,gBAAjF,EAAmGK,UAAnG;AACAP,cAAAA,WAAW,GAAG,KAAd;AACA;AACH;AACJ;;AAED,cAAI,CAACF,YAAD,IAAiB,CAACE,WAAtB,EAAmC;AAC/BL,YAAAA,WAAW,CAACE,iBAAZ;AACAH,YAAAA,IAAI,CAACK,YAAL,CAAkB3C,OAAlB,CAA0B,CAACqD,MAAD,EAASC,KAAT,KAAmB;AACzC,kBAAID,MAAM,CAACD,YAAP,IAAuB,IAA3B,EAAiC;AAAA;;AACzBhG,gBAAAA,YAAY,CAACmG,OAAb,CAAqB;AAAC7D,kBAAAA,IAAI,0BAAC2D,MAAM,CAACD,YAAR,qBAAC,qBAAqB1D;AAA3B,iBAArB,EAAuD,CAAC8D,GAAD,EAAMpC,MAAN,KAAwB;AAC/E,sBAAIoC,GAAJ,EAAS;AACLpE,oBAAAA,OAAO,CAACqE,KAAR,CAAc,2DAAd,EAA2ED,GAA3E;AACA;AACH;;AACD,sBAAMX,UAAU,GAAG;AAAA;AAAA,4DAAiBvD,YAAjB,CAA8BiD,WAA9B,cAAqDe,KAArD,CAAnB;AAEA,sBAAII,WAAW,GAAGpB,IAAI,CAACZ,KAAL,GAAa,KAAKD,SAApC;AACArC,kBAAAA,OAAO,CAACC,GAAR,CAAY,+BAAZ,EAA6CqE,WAA7C;AACA,sBAAIC,UAAU,GAAGD,WAAW,GAAGpB,IAAI,CAACZ,KAApC;AACA,sBAAIkC,UAAU,GAAG,CAAjB;;AACA,uBAAK,IAAIhD,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAG+C,UAApB,EAAgC/C,GAAC,EAAjC,EAAqC;AACjC,wBAAMiD,KAAK,GAAGxG,WAAW,CAAC+D,MAAD,CAAzB;AACAyC,oBAAAA,KAAK,CAACrD,WAAN,CAAkB,CAAlB,EAAqBoD,UAArB,EAAiC,CAAjC;AACA,wBAAIE,IAAI,GAAGD,KAAK,CAACrE,YAAN,CAAmBlC,WAAnB,EAAiCiD,WAAjC,CAA6CD,MAAxD;AACAuC,oBAAAA,UAAU,CAAExB,QAAZ,CAAqBwC,KAArB;AACAD,oBAAAA,UAAU,IAAIE,IAAd;AACH;AACJ,iBAlBG;AAmBP;AACJ,aAtBD;AAuBH;AACJ;;AAEO3D,QAAAA,qBAAqB,CAACmC,IAAD,EAAmBV,UAAnB,EAA0C;AACnE,cAAMmC,WAAW,GAAG;AAAA;AAAA,oDAAiBzE,YAAjB,CAA8BsC,UAA9B,EAA0C,SAA1C,CAApB;;AACA,cAAIU,IAAI,CAAC3D,IAAL,IAAa;AAAA;AAAA,sCAAUqF,MAA3B,EAAmC;AAC/BD,YAAAA,WAAW,CAACtB,iBAAZ;AACA;AACH;;AAED,cAAIC,YAAY,GAAGqB,WAAW,CAAChD,QAAZ,CAAqBC,MAArB,KAAgCsB,IAAI,CAAC2B,YAAL,CAAkBjD,MAArE;AAEA,cAAIkD,QAAJ;AACA5B,UAAAA,IAAI,CAAC2B,YAAL,CAAkBjE,OAAlB,CAA0B,CAACmE,YAAD,EAAeb,KAAf,KAAyB;AAC/CY,YAAAA,QAAQ,GAAG;AAAA;AAAA,sDAAiB5E,YAAjB,CAA8ByE,WAA9B,YAAmDT,KAAnD,CAAX;;AACA,gBAAIY,QAAQ,KAAK,IAAb,IAAqBA,QAAQ,CAACnD,QAAT,CAAkBC,MAAlB,IAA4BmD,YAAY,CAACC,eAAb,CAA6BpD,MAAlF,EAA0F;AACtF0B,cAAAA,YAAY,GAAG,KAAf;AACH;AACJ,WALD;;AAOA,cAAI,CAACA,YAAL,EAAmB;AACfqB,YAAAA,WAAW,CAACtB,iBAAZ;AACAH,YAAAA,IAAI,CAAC2B,YAAL,CAAkBjE,OAAlB,CAA2BmE,YAAD,IAAkB;AACxC,kBAAIA,YAAY,CAACC,eAAb,CAA6BpD,MAA7B,GAAsC,CAA1C,EAA6C;AACzCmD,gBAAAA,YAAY,CAACC,eAAb,CAA6BpE,OAA7B,CAAsCqE,QAAD,IAAc;AAC/CA,kBAAAA,QAAQ,CAACD,eAAT,CAAyBpE,OAAzB,CAAkCsE,OAAD,IAAa;AAAA;;AAC1ClH,oBAAAA,YAAY,CAACmG,OAAb,CAAqB;AAAC7D,sBAAAA,IAAI,EAAE4E,OAAF,qCAAEA,OAAO,CAAEC,eAAX,qBAAE,sBAA0B7E;AAAjC,qBAArB,EAA6D,CAAC8D,GAAD,EAAMpC,MAAN,KAAwB;AACjF,0BAAIoC,GAAJ,EAAS;AACL;AACH;;AAED,0BAAIjE,IAAI,GAAGlC,WAAW,CAAC+D,MAAD,CAAtB;AACA8C,sBAAAA,QAAQ,CAAE7C,QAAV,CAAmB9B,IAAnB;AACH,qBAPD;AAQH,mBATD;AAUH,iBAXD;AAYH;AACJ,aAfD;AAgBH;AACJ;;AAEMiF,QAAAA,eAAe,CAAClC,IAAD,EAAuB;AAAA;;AACzC,eAAKmC,SAAL,GAAiBnC,IAAI,CAACT,IAAtB;AACA,eAAKJ,SAAL,GAAiBa,IAAI,CAACb,SAAtB;AAEA,eAAK3C,mBAAL,CAA0B2D,iBAA1B;AACA,eAAK5B,eAAL,GAAuB;AAAA;AAAA,6DAAvB;AACA,eAAKA,eAAL,CAAqBK,WAArB,GAAmC,EAAnC;AACA,mCAAAoB,IAAI,CAACzB,eAAL,4DAAsBK,WAAtB,mCAAmClB,OAAnC,CAA4C0E,UAAD,IAAgB;AACvDtH,YAAAA,YAAY,CAACmG,OAAb,CAAqB;AAAC7D,cAAAA,IAAI,EAACgF;AAAN,aAArB,EAAwC,CAAClB,GAAD,EAAapC,MAAb,KAA+B;AACnE,kBAAIoC,GAAJ,EAAS;AACLpE,gBAAAA,OAAO,CAACqE,KAAR,CAAc,8DAAd,EAA8ED,GAA9E;AACA;AACH;;AACD,mBAAK3C,eAAL,CAAqBK,WAArB,CAAiCgB,IAAjC,CAAsCd,MAAtC;AACH,aAND;AAOH,WARD;AASA,eAAKP,eAAL,CAAqBa,KAArB,6BAA6BY,IAAI,CAACzB,eAAlC,qBAA6B,uBAAsBa,KAAnD;AACA,eAAK7C,YAAL,GAAoB,KAAKgC,eAAL,CAAqBa,KAArB,GAA6B,KAAKD,SAAtD;AACA,eAAKZ,eAAL,CAAqBtB,IAArB,GAA4Bb,iBAAiB,CAACiD,QAAlB,CAA2B,KAAK7C,mBAAhC,EAAsD,OAAtD,EAA+DS,IAA3F;AACA,eAAKsB,eAAL,CAAqBC,eAArB,GAAuC;AAAA;AAAA,oDAAiBxB,YAAjB,CAA8B,KAAKuB,eAAL,CAAqBtB,IAAnD,EAAyDd,mBAAzD,CAAvC;AACA,eAAKoC,eAAL,CAAqBC,eAArB,CAAqC6D,eAArC,CAAqD,CAArD;AACA,eAAK9D,eAAL,CAAqBtB,IAArB,CAA0BC,YAA1B;AAAA;AAAA,wDAAgFgF,eAAhF,CAAgGlC,IAAI,CAACzB,eAArG;;AACA,cAAIyB,IAAI,CAACzB,eAAL,IAAwByB,IAAI,CAACzB,eAAL,CAAqB+D,OAAjD,EAA0D;AACtDtC,YAAAA,IAAI,CAACzB,eAAL,CAAqB+D,OAArB,CAA6B5E,OAA7B,CAAsC6E,UAAD,IAAgB;AACjD,kBAAMC,WAAW,GAAG;AAAA;AAAA,6DAApB;AACA1H,cAAAA,YAAY,CAACmG,OAAb,CAAqB;AAAC7D,gBAAAA,IAAI,EAAEmF,UAAU,CAACnF;AAAlB,eAArB,EAA8C,CAAC8D,GAAD,EAAapC,MAAb,KAA+B;AACzE,oBAAIoC,GAAJ,EAAS;AACLpE,kBAAAA,OAAO,CAACqE,KAAR,CAAc,2EAAd,EAA2FD,GAA3F;AACA;AACH;;AACDsB,gBAAAA,WAAW,CAAC1B,YAAZ,GAA2BhC,MAA3B;AACA,qBAAKP,eAAL,CAAqB8B,YAArB,CAAkCT,IAAlC,CAAuC4C,WAAvC;AACH,eAPD;AAQH,aAVD;AAWH;;AAED,eAAKhF,WAAL,GAAmB,EAAnB;AACA,eAAKC,SAAL,GAAiB,EAAjB;AACArB,UAAAA,iBAAiB,CAACqG,UAAlB,CAA6B,KAAKhG,eAAlC,EAAoD,KAAKe,WAAzD,EAAsEwC,IAAI,CAACxC,WAA3E;AACApB,UAAAA,iBAAiB,CAACqG,UAAlB,CAA6B,KAAK/F,aAAlC,EAAkD,KAAKe,SAAvD,EAAkEuC,IAAI,CAACvC,SAAvE;AACH;;AAEwB,eAAVgF,UAAU,CAACnD,UAAD,EAAmBI,MAAnB,EAAyCgD,UAAzC,EAA4E;AACjGpD,UAAAA,UAAU,CAACa,iBAAX;AACAuC,UAAAA,UAAU,CAAChF,OAAX,CAAmB,CAACC,KAAD,EAAQW,CAAR,KAAc;AAC7B,gBAAIqE,UAAU,GAAG;AAAA;AAAA,2CAAjB;AACAA,YAAAA,UAAU,CAACvD,KAAX,GAAmBzB,KAAK,CAACyB,KAAzB;AACAuD,YAAAA,UAAU,CAACtG,IAAX,GAAkBsB,KAAK,CAACtB,IAAxB;AACAsG,YAAAA,UAAU,CAAC1F,IAAX,GAAkBb,iBAAiB,CAACiD,QAAlB,CAA2BC,UAA3B,aAAgDhB,CAAhD,EAAqDrB,IAAvE;AACA,gBAAM2F,kBAAkB,GAAGD,UAAU,CAAC1F,IAAX,CAAgBC,YAAhB;AAAA;AAAA,yDAA3B;AACA0F,YAAAA,kBAAkB,CAACV,eAAnB,CAAmCvE,KAAnC;AACAiF,YAAAA,kBAAkB,CAACC,sBAAnB,CAA0CF,UAA1C,EAAsDhF,KAAtD;AACA+B,YAAAA,MAAM,CAACE,IAAP,CAAY+C,UAAZ;AACH,WATD;AAUH;;AAEgC,eAAlBG,kBAAkB,CAACnF,KAAD,EAAoBoF,SAApB,EAAoD;AACjFA,UAAAA,SAAS,CAAC3D,KAAV,GAAkBzB,KAAK,CAACyB,KAAxB;AACA2D,UAAAA,SAAS,CAAC1G,IAAV,GAAiBsB,KAAK,CAACtB,IAAvB;;AACA,cAAIsB,KAAK,CAACtB,IAAN,KAAe;AAAA;AAAA,sCAAU6D,MAA7B,EAAqC;AACjCvC,YAAAA,KAAK,CAAC0C,YAAN,CAAmB3C,OAAnB,CAA4B8E,WAAD,IAAiB;AAAA;;AACxC,kBAAIxC,IAAI,GAAG;AAAA;AAAA,uDAAX;AACAA,cAAAA,IAAI,CAAC5C,IAAL,4BAAYoF,WAAW,CAAC1B,YAAxB,qBAAY,sBAA0B1D,IAAtC;AACA4C,cAAAA,IAAI,CAACgD,MAAL,GAAcR,WAAW,CAACQ,MAA1B;AACAhD,cAAAA,IAAI,CAAChB,QAAL,GAAgB3D,IAAI,CAAC4H,IAArB;AACAjD,cAAAA,IAAI,CAACkD,KAAL,GAAa7H,IAAI,CAAC8H,GAAlB;AACAnD,cAAAA,IAAI,CAACoD,QAAL,GAAgB,CAAhB;AACAL,cAAAA,SAAS,CAACT,OAAV,CAAkB1C,IAAlB,CAAuBI,IAAvB;AACAlD,cAAAA,OAAO,CAACC,GAAR,CAAY,yCAAZ,EAAuDgG,SAAvD;AACH,aATD;AAUH,WAXD,MAWO,IAAIpF,KAAK,CAACtB,IAAN,KAAe;AAAA;AAAA,sCAAUqF,MAA7B,EAAqC;AACxC/D,YAAAA,KAAK,CAACgE,YAAN,CAAmBjE,OAAnB,CAA4B2F,WAAD,IAAiB;AACxCA,cAAAA,WAAW,CAACvB,eAAZ,CAA4BpE,OAA5B,CAAqCqE,QAAD,IAAc;AAC9C,oBAAI/B,IAAI,GAAG;AAAA;AAAA,qEAAX;AACAA,gBAAAA,IAAI,CAAC+B,QAAL,GAAgB,EAAhB;AACA/B,gBAAAA,IAAI,CAACgD,MAAL,GAAcjB,QAAQ,CAACiB,MAAvB;AACAjB,gBAAAA,QAAQ,CAACD,eAAT,CAAyBpE,OAAzB,CAAkC4F,cAAD,IAAoB;AAAA;;AACjD,sBAAIC,WAAiC,GAAG;AACpCP,oBAAAA,MAAM,EAAEM,cAAc,CAACN,MADa;AAEpC5F,oBAAAA,IAAI,2BAAEkG,cAAc,CAACrB,eAAjB,qBAAE,sBAAgC7E;AAFF,mBAAxC;AAIA4C,kBAAAA,IAAI,CAAC+B,QAAL,CAAcnC,IAAd,CAAmB2D,WAAnB;AACH,iBAND;AAOAR,gBAAAA,SAAS,CAACS,QAAV,CAAmB5D,IAAnB,CAAwBI,IAAxB;AACH,eAZD;AAaH,aAdD;AAeH;;AACDrC,UAAAA,KAAK,CAACV,IAAN,CAAYC,YAAZ;AAAA;AAAA,wDAAkEuG,aAAlE,CAAgFV,SAAhF;AACH;;AACiC,eAAnBW,mBAAmB,CAAChE,MAAD,EAAuBgD,UAAvB,EAA0D;AACxFhD,UAAAA,MAAM,CAAChC,OAAP,CAAgBC,KAAD,IAAW;AACtB,gBAAIgF,UAAU,GAAG;AAAA;AAAA,mDAAjB;AACAvG,YAAAA,iBAAiB,CAAC0G,kBAAlB,CAAqCnF,KAArC,EAA4CgF,UAA5C;AACAD,YAAAA,UAAU,CAAC9C,IAAX,CAAgB+C,UAAhB;AACH,WAJD;AAKH;;AACMc,QAAAA,aAAa,CAACzD,IAAD,EAAuB;AACvCA,UAAAA,IAAI,CAACT,IAAL,GAAY,KAAK4C,SAAjB;AACAnC,UAAAA,IAAI,CAACb,SAAL,GAAiB,KAAKA,SAAtB;AAEAa,UAAAA,IAAI,CAACzB,eAAL,GAAuB;AAAA;AAAA,qEAAvB;;AACA,eAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKC,eAAL,CAAqBK,WAArB,CAAiCF,MAArD,EAA6DJ,CAAC,EAA9D,EAAkE;AAC9D,gBAAMQ,MAAM,GAAG,KAAKP,eAAL,CAAqBK,WAArB,CAAiCN,CAAjC,CAAf;;AACA,gBAAIQ,MAAM,IAAI,IAAd,EAAoB;AAChB;AACH;;AACDkB,YAAAA,IAAI,CAACzB,eAAL,CAAqBK,WAArB,CAAiCgB,IAAjC,CAAsCd,MAAM,CAAC1B,IAA7C;AACH;;AACDhB,UAAAA,iBAAiB,CAAC0G,kBAAlB,CAAqC,KAAKvE,eAA1C,EAA2DyB,IAAI,CAACzB,eAAhE;AAEAyB,UAAAA,IAAI,CAACxC,WAAL,GAAmB,EAAnB;AACAwC,UAAAA,IAAI,CAACvC,SAAL,GAAiB,EAAjB;AACArB,UAAAA,iBAAiB,CAACsH,mBAAlB,CAAsC,KAAKlG,WAA3C,EAAwDwC,IAAI,CAACxC,WAA7D;AACApB,UAAAA,iBAAiB,CAACsH,mBAAlB,CAAsC,KAAKjG,SAA3C,EAAsDuC,IAAI,CAACvC,SAA3D;AACH;;AAEMkG,QAAAA,YAAY,GAAS;AACxB,cAAI,CAAC,KAAK/G,QAAV,EAAoB;AAEhB,cAAMgH,aAAa,GAAG,KAAKjH,QAAL,CAAeO,YAAf,CAA4BlC,WAA5B,CAAtB,CAHoB,CAKpB;;AACA,cAAM6I,QAAQ,GAAG,IAAIzI,IAAJ,CACb,KAAKuB,QAAL,CAAemH,WAAf,GAA6BC,CAA7B,GAAiCH,aAAa,CAAC3F,WAAd,CAA0B+F,KAA1B,GAAkC,CADtD,EAEb,KAAKrH,QAAL,CAAemH,WAAf,GAA6B7E,CAA7B,GAAiC2E,aAAa,CAAC3F,WAAd,CAA0BD,MAA1B,GAAmC,CAFvD,EAGb4F,aAAa,CAAC3F,WAAd,CAA0B+F,KAHb,EAIbJ,aAAa,CAAC3F,WAAd,CAA0BD,MAJb,CAAjB,CANoB,CAapB;;AACA,eAAKpB,QAAL,CAAcqH,WAAd,GAA4B9I,KAAK,CAAC+I,GAAlC;AACA,eAAKtH,QAAL,CAAcuH,SAAd,GAA0B,EAA1B;AACA,eAAKvH,QAAL,CAAcwH,IAAd,CAAmBP,QAAQ,CAACE,CAA5B,EAA+BF,QAAQ,CAAC5E,CAAxC,EAA2C4E,QAAQ,CAACG,KAApD,EAA2DH,QAAQ,CAAC7F,MAApE;AACA,eAAKpB,QAAL,CAAcyH,MAAd,GAjBoB,CAmBpB;;AACA,cAAMC,SAAS,GAAG,KAAlB;AACA,cAAMC,UAAU,GAAGX,aAAa,CAAC3F,WAAd,CAA0BD,MAA7C;AACA,eAAKpB,QAAL,CAAc4H,SAAd,GAA0BrJ,KAAK,CAACsJ,KAAhC,CAtBoB,CAwBpB;;AACA,eAAK7H,QAAL,CAAc8H,QAAd,CACI,CAACJ,SAAD,GAAa,CADjB,EAEI,KAAK3H,QAAL,CAAemH,WAAf,GAA6B7E,CAA7B,GAAiC2E,aAAa,CAAC3F,WAAd,CAA0BD,MAA3D,GAAoEuG,UAAU,GAAG,CAFrF,EAGID,SAHJ,EAIIC,UAJJ,EAzBoB,CAgCpB;;AACA,eAAK3H,QAAL,CAAc8H,QAAd,CACI,CAACJ,SAAD,GAAa,CADjB,EAEI,KAAK3H,QAAL,CAAemH,WAAf,GAA6B7E,CAA7B,GAAiC2E,aAAa,CAAC3F,WAAd,CAA0BD,MAA3D,GAAoEuG,UAAU,GAAG,CAFrF,EAGID,SAHJ,EAIIC,UAJJ,EAjCoB,CAwCpB;;AACA,eAAK3H,QAAL,CAAc8H,QAAd,CACI,CAACJ,SAAD,GAAaV,aAAa,CAAC3F,WAAd,CAA0B+F,KAA1B,GAAkC,CADnD,EAEI,KAAKrH,QAAL,CAAemH,WAAf,GAA6B7E,CAA7B,GAAiC2E,aAAa,CAAC3F,WAAd,CAA0BD,MAA1B,GAAmC,CAFxE,EAGIsG,SAHJ,EAIIC,UAJJ,EAzCoB,CAgDpB;;AACA,eAAK3H,QAAL,CAAc8H,QAAd,CACId,aAAa,CAAC3F,WAAd,CAA0B+F,KAA1B,GAAkC,CADtC,EAEI,KAAKrH,QAAL,CAAemH,WAAf,GAA6B7E,CAA7B,GAAiC2E,aAAa,CAAC3F,WAAd,CAA0BD,MAA1B,GAAmC,CAFxE,EAGIsG,SAHJ,EAIIC,UAJJ;AAMP;;AAEMI,QAAAA,SAAS,GAAS;AACrB,cAAI,CAAC,KAAK/H,QAAV,EAAoB;AACpB,eAAKA,QAAL,CAAcgI,KAAd;AACH;;AA5Y4C,O;;;;;iBAElB,E;;;;;;;iBAEA,E;;;;;;;iBAIoB;AAAA;AAAA,6D;;;;;;;iBAEZ,E;;;;;;;iBAEF,E", "sourcesContent": ["import { _decorator, CCFloat, Component, Node, CCString, Prefab, assetManager, instantiate, UITransform, view, Graphics, Color, Rect, Vec2 } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\nimport { LayerType, LevelData, LevelDataBackgroundLayer, LevelDataLayer, LevelDataRandTerrain, LevelDataRandTerrains, LevelDataScroll, LevelDataTerrain } from '../../scripts/leveldata/leveldata';\r\nimport { LevelEditorLayerUI } from './LevelEditorLayerUI';\r\nimport { LevelBackgroundLayer, LevelEditorUtils, LevelLayer, LevelScrollLayerUI } from './utils';\r\n\r\nconst BackgroundsNodeName = \"backgrounds\";\r\n\r\n@ccclass('LevelEditorBaseUI')\r\n@executeInEditMode()\r\nexport class LevelEditorBaseUI extends Component {\r\n    @property(CCString)\r\n    public levelname: string = \"\";\r\n    @property({type:CCFloat, displayName:\"关卡时长\"})\r\n    public totalTime: number = 10;\r\n    private _totalHeight: number = 0;\r\n\r\n    @property(LevelBackgroundLayer)\r\n    public backgroundLayer: LevelBackgroundLayer = new LevelBackgroundLayer();\r\n    @property({type:[LevelLayer], displayName:\"地面层\"})\r\n    public floorLayers: LevelLayer[] = [];\r\n    @property({type:[LevelLayer], displayName:\"天空层\"})\r\n    public skyLayers: LevelLayer[] = [];\r\n\r\n    private backgroundLayerNode:Node|null = null;\r\n    private floorLayersNode:Node|null = null;\r\n    private skyLayersNode:Node|null = null;\r\n\r\n    private drawNode: Node | null = null;\r\n    private graphics: Graphics | null = null;\r\n\r\n    onLoad():void {\r\n        console.log(`LevelEditorBaseUI start.`);\r\n        this.backgroundLayerNode = LevelEditorUtils.getOrAddNode(this.node, \"BackgroundLayer\");\r\n        this.floorLayersNode = LevelEditorUtils.getOrAddNode(this.node, \"FloorLayers\");\r\n        this.skyLayersNode = LevelEditorUtils.getOrAddNode(this.node, \"SkyLayers\");\r\n\r\n        this.drawNode = LevelEditorUtils.getOrAddNode(this.node, \"DrawNode\");\r\n        if (!this.graphics) {\r\n            this.graphics = this.drawNode.getComponent(Graphics) || this.drawNode.addComponent(Graphics);\r\n        }\r\n        \r\n        console.log(`LevelEditorBaseUI start ${this.floorLayersNode?.uuid}`);\r\n    }\r\n    update(dt:number):void {\r\n        this.checkLayerNode(this.floorLayersNode!, this.floorLayers);\r\n        this.checkLayerNode(this.skyLayersNode!, this.skyLayers);\r\n        this.floorLayers.forEach((layer) => {\r\n            this._checkScrollNode(layer,layer.node!);\r\n            this._checkRandTerrainNode(layer,layer.node!);\r\n        });\r\n        this.skyLayers.forEach((layer) => {\r\n            this._checkScrollNode(layer,layer.node!);\r\n            this._checkRandTerrainNode(layer,layer.node!);\r\n        });\r\n    }\r\n    private setBackgroundNodePosition(node:Node, yOff:number):number {\r\n        const height = node.getComponent(UITransform)!.contentSize.height;\r\n        node.setPosition(0, yOff-view.getVisibleSize().height/2+height/2);\r\n        return height\r\n\r\n    }\r\n    public tick(progress: number):void {\r\n        let yOff = 0\r\n        for (let i = 0; i < this.backgroundLayer.backgroundsNode!.children.length; i++) {\r\n            var bg = this.backgroundLayer.backgroundsNode!.children[i]\r\n            yOff += this.setBackgroundNodePosition(bg, yOff)\r\n        }\r\n        while(this.backgroundLayer.backgrounds.length > 0 && yOff < this._totalHeight) {\r\n            let bg:Node|null = null;\r\n            let bgIndex = this.backgroundLayer.backgroundsNode!.children.length % this.backgroundLayer.backgrounds.length;\r\n            const prefab = this.backgroundLayer.backgrounds[bgIndex]\r\n            if (prefab != null) {\r\n                bg = instantiate(prefab)\r\n            } \r\n            if (bg == null) {\r\n                bg = new Node(\"empty\");\r\n                bg.addComponent(UITransform).height = 1024;\r\n            }\r\n            this.backgroundLayer.backgroundsNode!.addChild(bg);\r\n            yOff += this.setBackgroundNodePosition(bg, yOff)\r\n        }\r\n        for (let i = this.backgroundLayer.backgroundsNode!.children.length - 1; i >= 0; i--) {\r\n            const bg = this.backgroundLayer.backgroundsNode!.children[i]\r\n            if (bg.position.y - bg.getComponent(UITransform)!.height/2 > this._totalHeight) {\r\n                bg.removeFromParent()\r\n            } else {\r\n                break;\r\n            }\r\n        }\r\n\r\n        this.backgroundLayer!.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(\r\n            progress, this.totalTime, this.backgroundLayer.speed);\r\n        this.floorLayers.forEach((layer) => {\r\n            layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(progress, this.totalTime, layer.speed);\r\n        });\r\n        this.skyLayers.forEach((layer) => {\r\n            layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.tick(progress, this.totalTime, layer.speed);\r\n        });\r\n    }\r\n\r\n    private static addLayer(parentNode: Node, name: string): LevelEditorLayerUI {\r\n        var layerNode = new Node(name);\r\n        var layerCom = layerNode.addComponent<LevelEditorLayerUI>(LevelEditorLayerUI);\r\n        parentNode.addChild(layerNode);\r\n        return layerCom;\r\n    }\r\n\r\n    private checkLayerNode(parentNode: Node, layers: LevelLayer[]):void {\r\n        var removeLayerNodes: Node[] = []\r\n        parentNode.children.forEach(node => {\r\n            var layerCom = node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI);\r\n            if (layerCom == null) {\r\n                console.log(`Level checkLayerNode remove ${node.name} because layerCom == null\"`);\r\n                removeLayerNodes.push(node)\r\n                return;\r\n            }\r\n            if (layers.find((layer) => layer.node == node) == null) {\r\n                console.log(`Level checkLayerNode remove ${node.name} because not in layers\"`);\r\n                removeLayerNodes.push(node)\r\n                return;\r\n            }\r\n        });\r\n        removeLayerNodes.forEach(element => {\r\n            element.removeFromParent();    \r\n        });\r\n        layers.forEach((layer, i) => {\r\n            if (layer.node == null || layer.node.isValid == false) {\r\n                console.log(`Level checkLayerNode add because layer == null`);\r\n                layer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node; \r\n            }\r\n        });\r\n    }\r\n\r\n    private _checkScrollNode(data: LevelLayer, parentNode: Node):void {\r\n        const scrollsNode = LevelEditorUtils.getOrAddNode(parentNode, \"scrolls\");\r\n        if (data.type != LayerType.Scroll) {\r\n            scrollsNode.removeAllChildren();\r\n            return;\r\n        }\r\n\r\n        const isCountMatch = scrollsNode.children.length === data.scrollLayers.length;\r\n        let isUUIDMatch = true;\r\n        for (let i = 0; i < scrollsNode.children.length; i++) {\r\n            const scrollNode = scrollsNode.children[i];\r\n            var scrollPrefabUUID = \"\";\r\n            var firstChild;\r\n            if (scrollNode != null) {\r\n                firstChild = scrollNode.children[0];  \r\n                if (firstChild != null) {\r\n                    // @ts-ignore\r\n                    scrollPrefabUUID = firstChild._prefab?.asset?._uuid;\r\n                } \r\n            }\r\n            // @ts-ignore\r\n            const scrollUUID = data.scrollLayers[i]?.scrollPrefab?._uuid;\r\n\r\n            if (scrollPrefabUUID != scrollUUID) {\r\n                console.log(\"LevelEditorBaseUI _checkScrollNode scrollPrefabUUID != scrollUUID\", scrollPrefabUUID, scrollUUID);\r\n                isUUIDMatch = false;\r\n                break;\r\n            }\r\n        }\r\n\r\n        if (!isCountMatch || !isUUIDMatch) {\r\n            scrollsNode.removeAllChildren();\r\n            data.scrollLayers.forEach((scroll, index) => { \r\n                if (scroll.scrollPrefab != null) {\r\n                        assetManager.loadAny({uuid:scroll.scrollPrefab?.uuid}, (err, prefab:Prefab) => { \r\n                        if (err) {\r\n                            console.error(\"LevelEditorBaseUI _checkScrollNode load scroll prefab err\", err);\r\n                            return;\r\n                        }\r\n                        const scrollNode = LevelEditorUtils.getOrAddNode(scrollsNode, `scroll_${index}`);\r\n                        \r\n                        var totalHeight = data.speed * this.totalTime;\r\n                        console.log(\"LevelEditorBaseUI totalHeight\", totalHeight);\r\n                        var childCount = totalHeight / data.speed;\r\n                        var posOffsetY = 0;\r\n                        for (let i = 0; i < childCount; i++) {\r\n                            const child = instantiate(prefab);\r\n                            child.setPosition(0, posOffsetY, 0);\r\n                            var offY = child.getComponent(UITransform)!.contentSize.height;\r\n                            scrollNode!.addChild(child);\r\n                            posOffsetY += offY;\r\n                        }\r\n                    });\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    private _checkRandTerrainNode(data: LevelLayer, parentNode: Node):void {\r\n        const dynamicNode = LevelEditorUtils.getOrAddNode(parentNode, \"dynamic\");\r\n        if (data.type != LayerType.Random) {\r\n            dynamicNode.removeAllChildren();\r\n            return;\r\n        }\r\n\r\n        var isCountMatch = dynamicNode.children.length === data.randomLayers.length;\r\n\r\n        var dynaNode;\r\n        data.randomLayers.forEach((randTerrains, index) => {\r\n            dynaNode = LevelEditorUtils.getOrAddNode(dynamicNode, `dyna_${index}`);\r\n            if (dynaNode === null || dynaNode.children.length != randTerrains.dynamicTerrains.length) {\r\n                isCountMatch = false;\r\n            }\r\n        });\r\n\r\n        if (!isCountMatch) {\r\n            dynamicNode.removeAllChildren();\r\n            data.randomLayers.forEach((randTerrains) => { \r\n                if (randTerrains.dynamicTerrains.length > 0) {\r\n                    randTerrains.dynamicTerrains.forEach((terrains) => {\r\n                        terrains.dynamicTerrains.forEach((terrain) => {\r\n                            assetManager.loadAny({uuid: terrain?.terrainElements?.uuid}, (err, prefab:Prefab) => { \r\n                                if (err) {\r\n                                    return;\r\n                                }\r\n\r\n                                var node = instantiate(prefab);\r\n                                dynaNode!.addChild(node);\r\n                            });\r\n                        });\r\n                    });\r\n                }\r\n            });\r\n        }\r\n    }\r\n\r\n    public initByLevelData(data: LevelData):void {\r\n        this.levelname = data.name;\r\n        this.totalTime = data.totalTime\r\n\r\n        this.backgroundLayerNode!.removeAllChildren()\r\n        this.backgroundLayer = new LevelBackgroundLayer();\r\n        this.backgroundLayer.backgrounds = [];\r\n        data.backgroundLayer?.backgrounds?.forEach((background) => {\r\n            assetManager.loadAny({uuid:background}, (err: Error, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"LevelEditorBaseUI initByLevelData load background prefab err\", err);\r\n                    return\r\n                } \r\n                this.backgroundLayer.backgrounds.push(prefab);\r\n            });\r\n        });\r\n        this.backgroundLayer.speed = data.backgroundLayer?.speed\r\n        this._totalHeight = this.backgroundLayer.speed * this.totalTime;\r\n        this.backgroundLayer.node = LevelEditorBaseUI.addLayer(this.backgroundLayerNode!, \"layer\").node;\r\n        this.backgroundLayer.backgroundsNode = LevelEditorUtils.getOrAddNode(this.backgroundLayer.node, BackgroundsNodeName);\r\n        this.backgroundLayer.backgroundsNode.setSiblingIndex(0); \r\n        this.backgroundLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.initByLevelData(data.backgroundLayer);\r\n        if (data.backgroundLayer && data.backgroundLayer.scrolls) {\r\n            data.backgroundLayer.scrolls.forEach((scrollData) => {\r\n                const scrollLayer = new LevelScrollLayerUI();\r\n                assetManager.loadAny({uuid: scrollData.uuid}, (err: Error, prefab:Prefab) => {\r\n                    if (err) {\r\n                        console.error(\"LevelEditorBaseUI initByLevelData load background scroll layer prefab err\", err);\r\n                        return;\r\n                    }\r\n                    scrollLayer.scrollPrefab = prefab;\r\n                    this.backgroundLayer.scrollLayers.push(scrollLayer);\r\n                });\r\n            });\r\n        }\r\n    \r\n        this.floorLayers = []\r\n        this.skyLayers = []\r\n        LevelEditorBaseUI.initLayers(this.floorLayersNode!, this.floorLayers, data.floorLayers);\r\n        LevelEditorBaseUI.initLayers(this.skyLayersNode!, this.skyLayers, data.skyLayers);\r\n    }\r\n\r\n    private static initLayers(parentNode: Node, layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {\r\n        parentNode.removeAllChildren()\r\n        dataLayers.forEach((layer, i) => {\r\n            var levelLayer = new LevelLayer();\r\n            levelLayer.speed = layer.speed;\r\n            levelLayer.type = layer.type;\r\n            levelLayer.node = LevelEditorBaseUI.addLayer(parentNode, `layer_${i}`).node;\r\n            const levelEditorLayerUI = levelLayer.node.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!;\r\n            levelEditorLayerUI.initByLevelData(layer);\r\n            levelEditorLayerUI.initScorllsByLevelData(levelLayer, layer);\r\n            layers.push(levelLayer);\r\n        });\r\n    }\r\n\r\n    private static fillLevelLayerData(layer: LevelLayer, dataLayer: LevelDataLayer):void {\r\n        dataLayer.speed = layer.speed;\r\n        dataLayer.type = layer.type;\r\n        if (layer.type === LayerType.Scroll) {\r\n            layer.scrollLayers.forEach((scrollLayer) => {       \r\n                var data = new LevelDataScroll();\r\n                data.uuid = scrollLayer.scrollPrefab?.uuid!;\r\n                data.weight = scrollLayer.weight;\r\n                data.position = Vec2.ZERO;\r\n                data.scale = Vec2.ONE;\r\n                data.rotation = 0;\r\n                dataLayer.scrolls.push(data);\r\n                console.log(\"LevelEditorBaseUI fill scrollLayersData\", dataLayer);\r\n            });\r\n        } else if (layer.type === LayerType.Random) {\r\n            layer.randomLayers.forEach((randomLayer) => {    \r\n                randomLayer.dynamicTerrains.forEach((terrains) => {\r\n                    var data = new LevelDataRandTerrains();\r\n                    data.terrains = [];\r\n                    data.weight = terrains.weight;\r\n                    terrains.dynamicTerrains.forEach((terrainElement) => {\r\n                        var terrainData: LevelDataRandTerrain = {\r\n                            weight: terrainElement.weight,\r\n                            uuid: terrainElement.terrainElements?.uuid!\r\n                        };\r\n                        data.terrains.push(terrainData);\r\n                    });\r\n                    dataLayer.dynamics.push(data);\r\n                }); \r\n            });\r\n        }\r\n        layer.node!.getComponent<LevelEditorLayerUI>(LevelEditorLayerUI)!.fillLevelData(dataLayer);\r\n    }\r\n    private static fillLevelLayersData(layers: LevelLayer[], dataLayers: LevelDataLayer[]):void {\r\n        layers.forEach((layer) => {\r\n            var levelLayer = new LevelDataLayer();\r\n            LevelEditorBaseUI.fillLevelLayerData(layer, levelLayer);\r\n            dataLayers.push(levelLayer);\r\n        });\r\n    }\r\n    public fillLevelData(data: LevelData):void {\r\n        data.name = this.levelname;\r\n        data.totalTime = this.totalTime;\r\n\r\n        data.backgroundLayer = new LevelDataBackgroundLayer();\r\n        for (let i = 0; i < this.backgroundLayer.backgrounds.length; i++) {\r\n            const prefab = this.backgroundLayer.backgrounds[i];\r\n            if (prefab == null) {\r\n                continue;\r\n            }\r\n            data.backgroundLayer.backgrounds.push(prefab.uuid);\r\n        }\r\n        LevelEditorBaseUI.fillLevelLayerData(this.backgroundLayer, data.backgroundLayer);\r\n\r\n        data.floorLayers = []\r\n        data.skyLayers = []\r\n        LevelEditorBaseUI.fillLevelLayersData(this.floorLayers, data.floorLayers);\r\n        LevelEditorBaseUI.fillLevelLayersData(this.skyLayers, data.skyLayers);\r\n    }\r\n\r\n    public drawViewport(): void {\r\n        if (!this.graphics) return;\r\n            \r\n            const drawTransform = this.drawNode!.getComponent(UITransform)!;\r\n\r\n            // 计算视口矩形\r\n            const viewport = new Rect(\r\n                this.drawNode!.getPosition().x - drawTransform.contentSize.width / 2,\r\n                this.drawNode!.getPosition().y - drawTransform.contentSize.height / 2,\r\n                drawTransform.contentSize.width,\r\n                drawTransform.contentSize.height\r\n            );\r\n            \r\n            // Draw viewport rectangle\r\n            this.graphics.strokeColor = Color.RED;\r\n            this.graphics.lineWidth = 10;\r\n            this.graphics.rect(viewport.x, viewport.y, viewport.width, viewport.height);\r\n            this.graphics.stroke();\r\n\r\n            // 绘制4个填充矩形表示视口边界\r\n            const maskWidth = 10000;\r\n            const maskHeight = drawTransform.contentSize.height;\r\n            this.graphics.fillColor = Color.BLACK;\r\n            \r\n            // 顶部矩形\r\n            this.graphics.fillRect(\r\n                -maskWidth / 2,\r\n                this.drawNode!.getPosition().y + drawTransform.contentSize.height - maskHeight / 2, \r\n                maskWidth, \r\n                maskHeight\r\n            );\r\n            \r\n            // 底部矩形\r\n            this.graphics.fillRect(\r\n                -maskWidth / 2,\r\n                this.drawNode!.getPosition().y - drawTransform.contentSize.height - maskHeight / 2,  \r\n                maskWidth, \r\n                maskHeight\r\n            );\r\n            \r\n            // 左侧矩形\r\n            this.graphics.fillRect(\r\n                -maskWidth - drawTransform.contentSize.width / 2, \r\n                this.drawNode!.getPosition().y - drawTransform.contentSize.height / 2, \r\n                maskWidth, \r\n                maskHeight\r\n            );\r\n            \r\n            // 右侧矩形\r\n            this.graphics.fillRect(\r\n                drawTransform.contentSize.width / 2, \r\n                this.drawNode!.getPosition().y - drawTransform.contentSize.height / 2, \r\n                maskWidth, \r\n                maskHeight\r\n            );\r\n    }\r\n\r\n    public drawClear(): void {\r\n        if (!this.graphics) return;\r\n        this.graphics.clear();\r\n    }\r\n}"]}