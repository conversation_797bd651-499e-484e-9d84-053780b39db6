{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts"], "names": ["EquipSlots", "<PERSON>", "csproto", "MyApp", "logError", "DataEvent", "EventMgr", "slots", "init", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GET_EQUIP_SLOT_INFO", "onGetEquipSlotInfoMsg", "CS_CMD_EQUIP_SLOT_INSTALL", "onEquipSlotInstallMsg", "CS_CMD_EQUIP_SLOT_UNINSTALL", "onEquipSlotUnInstallMsg", "sendMessage", "get_equip_slot_info", "msg", "body", "emit", "EquipSlotRefresh", "m", "equip_slot_install", "slot", "find", "slot_id", "equip_id", "guid", "equip_slot_uninstall", "ZERO", "getEmptySlots", "filter", "getEquippedSlots", "getEmptySlotByClass", "equipClass", "equip_class", "eq", "getEquipSlotInfo", "slotID", "equip", "item", "equipCfg", "lubanMgr", "table", "TbEquip", "get", "item_id", "length", "equippedSlot", "emptySlot", "unequip"], "mappings": ";;;mFAMaA,U;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AANNC,MAAAA,I;;AACAC,MAAAA,O;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;4BACIN,U,GAAN,MAAMA,UAAN,CAAiB;AAAA;AAAA,eACpBO,KADoB,GACmB,EADnB;AAAA;;AAEpBC,QAAAA,IAAI,GAAG;AACH;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,0BAA/C,EAA2E,KAAKC,qBAAhF,EAAuG,IAAvG;AACA;AAAA;AAAA,8BAAML,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBG,yBAA/C,EAA0E,KAAKC,qBAA/E,EAAsG,IAAtG;AACA;AAAA;AAAA,8BAAMP,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBK,2BAA/C,EAA4E,KAAKC,uBAAjF,EAA0G,IAA1G;AACA;AAAA;AAAA,8BAAMT,MAAN,CAAaU,WAAb,CAAyB;AAAA;AAAA,kCAAQR,EAAR,CAAWC,MAAX,CAAkBC,0BAA3C,EAAuE;AAAEO,YAAAA,mBAAmB,EAAE;AAAvB,WAAvE;AACH;;AAEON,QAAAA,qBAAqB,CAACO,GAAD,EAAgC;AACzD,eAAKd,KAAL,GAAac,GAAG,CAACC,IAAJ,CAAUF,mBAAV,CAA+Bb,KAA/B,IAAwC,EAArD;AACA;AAAA;AAAA,oCAASgB,IAAT,CAAc;AAAA;AAAA,sCAAUC,gBAAxB;AACH;;AAEOR,QAAAA,qBAAqB,CAACK,GAAD,EAAgC;AACzD,cAAMI,CAAC,GAAGJ,GAAG,CAAEC,IAAL,CAAWI,kBAArB;AACA,cAAMC,IAAI,GAAG,KAAKpB,KAAL,CAAWqB,IAAX,CAAgBD,IAAI,IAAIA,IAAI,CAACE,OAAL,IAAgBJ,CAAC,CAAEI,OAA3C,CAAb;;AACA,cAAIF,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACG,QAAL,GAAgBL,CAAC,CAAEK,QAAnB;AACAH,YAAAA,IAAI,CAACI,IAAL,GAAYN,CAAC,CAAEM,IAAf;AACH;;AACD;AAAA;AAAA,oCAASR,IAAT,CAAc;AAAA;AAAA,sCAAUC,gBAAxB;AACH;;AAEON,QAAAA,uBAAuB,CAACG,GAAD,EAAgC;AAC3D,cAAMI,CAAC,GAAGJ,GAAG,CAAEC,IAAL,CAAWU,oBAArB;AACA,cAAML,IAAI,GAAG,KAAKpB,KAAL,CAAWqB,IAAX,CAAgBD,IAAI,IAAIA,IAAI,CAACE,OAAL,IAAgBJ,CAAC,CAAEI,OAA3C,CAAb;;AACA,cAAIF,IAAJ,EAAU;AACNA,YAAAA,IAAI,CAACG,QAAL,GAAgB,CAAhB;AACAH,YAAAA,IAAI,CAACI,IAAL,GAAY;AAAA;AAAA,8BAAKE,IAAjB;AACH;;AACD;AAAA;AAAA,oCAASV,IAAT,CAAc;AAAA;AAAA,sCAAUC,gBAAxB;AACH;;AAEDU,QAAAA,aAAa,GAAkC;AAC3C,iBAAO,KAAK3B,KAAL,CAAW4B,MAAX,CAAkBR,IAAI,IAAIA,IAAI,CAACG,QAAL,IAAiB,CAA3C,CAAP;AACH;;AAEDM,QAAAA,gBAAgB,GAAkC;AAC9C,iBAAO,KAAK7B,KAAL,CAAW4B,MAAX,CAAkBR,IAAI,IAAIA,IAAI,CAACG,QAAL,IAAiB,CAA3C,CAAP;AACH;;AAEDO,QAAAA,mBAAmB,CAACC,UAAD,EAAyD;AACxE,iBAAO,KAAK/B,KAAL,CAAWqB,IAAX,CAAgBD,IAAI,IAAIA,IAAI,CAACY,WAAL,IAAoBD,UAApB,IAAkCX,IAAI,CAACI,IAAvC,IAA+CJ,IAAI,CAACI,IAAL,CAAUS,EAAV,CAAa,CAAb,CAAvE,KAA2F,IAAlG;AACH;;AAEDC,QAAAA,gBAAgB,CAACC,MAAD,EAAqD;AACjE,iBAAO,KAAKnC,KAAL,CAAWqB,IAAX,CAAgBD,IAAI,IAAIA,IAAI,CAACE,OAAL,IAAgBa,MAAxC,KAAmD,IAA1D;AACH;;AAEDC,QAAAA,KAAK,CAACC,IAAD,EAA2B;AAC5B,cAAMC,QAAQ,GAAG;AAAA;AAAA,8BAAMC,QAAN,CAAeC,KAAf,CAAqBC,OAArB,CAA6BC,GAA7B,CAAiCL,IAAI,CAACM,OAAtC,CAAjB;AACA,cAAM3C,KAAK,GAAG,KAAKA,KAAL,CAAW4B,MAAX,CAAkBR,IAAI,IAAIA,IAAI,CAACY,WAAL,KAAoBM,QAApB,oBAAoBA,QAAQ,CAAEP,UAA9B,CAA1B,CAAd;;AACA,cAAI/B,KAAK,CAAC4C,MAAN,IAAgB,CAApB,EAAuB;AACnB;AAAA;AAAA,sCAAS,YAAT,4BAA+CP,IAAI,CAACM,OAApD,SAA+DN,IAAI,CAACb,IAApE,qBAAuFc,QAAvF,oBAAuFA,QAAQ,CAAEP,UAAjG;AACA;AACH;;AACD,cAAIc,YAAY,GAAG7C,KAAK,CAAC,CAAD,CAAxB;;AACA,cAAIA,KAAK,CAAC4C,MAAN,GAAe,CAAnB,EAAsB;AAClB,gBAAME,SAAS,GAAG9C,KAAK,CAACqB,IAAN,CAAWD,IAAI,IAAIA,IAAI,CAACI,IAAL,CAAWS,EAAX,CAAc,CAAd,CAAnB,CAAlB;;AACA,gBAAIa,SAAJ,EAAe;AACXD,cAAAA,YAAY,GAAGC,SAAf;AACH,aAFD,MAEO;AACHD,cAAAA,YAAY,GAAG7C,KAAK,CAAC,CAAD,CAApB;AACH;AACJ;;AACD;AAAA;AAAA,8BAAME,MAAN,CAAaU,WAAb,CAAyB;AAAA;AAAA,kCAAQR,EAAR,CAAWC,MAAX,CAAkBG,yBAA3C,EAAsE;AAClEW,YAAAA,kBAAkB,EAAE;AAChBG,cAAAA,OAAO,EAAEuB,YAAY,CAACvB,OADN;AAEhBE,cAAAA,IAAI,EAAEa,IAAI,CAACb;AAFK;AAD8C,WAAtE;AAMH;;AAEDuB,QAAAA,OAAO,CAACvB,IAAD,EAAa;AAChB,cAAMJ,IAAI,GAAG,KAAKpB,KAAL,CAAWqB,IAAX,CAAgBD,IAAI,IAAIA,IAAI,CAACI,IAAL,CAAWS,EAAX,CAAcT,IAAd,CAAxB,CAAb;;AACA,cAAI,CAACJ,IAAL,EAAW;AACP;AAAA;AAAA,sCAAS,SAAT,yBAAyCI,IAAzC;AACA;AACH;;AACD;AAAA;AAAA,8BAAMtB,MAAN,CAAaU,WAAb,CAAyB;AAAA;AAAA,kCAAQR,EAAR,CAAWC,MAAX,CAAkBK,2BAA3C,EAAwE;AACpEe,YAAAA,oBAAoB,EAAE;AAClBH,cAAAA,OAAO,EAAEF,IAAI,CAACE;AADI;AAD8C,WAAxE;AAKH;;AArFmB,O", "sourcesContent": ["import Long from 'long';\nimport csproto from '../../../../../scripts/AutoGen/PB/cs_proto.js';\nimport { MyApp } from '../../../../../scripts/MyApp';\nimport { logError } from '../../../../../scripts/Utils/Logger';\nimport { DataEvent } from '../../event/DataEvent';\nimport { EventMgr } from '../../event/EventManager';\nexport class EquipSlots {\n    slots: csproto.cs.ICSEquipSlotInfo[] = []\n    init() {\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_EQUIP_SLOT_INFO, this.onGetEquipSlotInfoMsg, this)\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_SLOT_INSTALL, this.onEquipSlotInstallMsg, this)\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_EQUIP_SLOT_UNINSTALL, this.onEquipSlotUnInstallMsg, this)\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GET_EQUIP_SLOT_INFO, { get_equip_slot_info: {} })\n    }\n\n    private onGetEquipSlotInfoMsg(msg: csproto.cs.IS2CMsg): void {\n        this.slots = msg.body!.get_equip_slot_info!.slots || []\n        EventMgr.emit(DataEvent.EquipSlotRefresh)\n    }\n\n    private onEquipSlotInstallMsg(msg: csproto.cs.IS2CMsg): void {\n        const m = msg!.body!.equip_slot_install\n        const slot = this.slots.find(slot => slot.slot_id == m!.slot_id)\n        if (slot) {\n            slot.equip_id = m!.equip_id\n            slot.guid = m!.guid\n        }\n        EventMgr.emit(DataEvent.EquipSlotRefresh)\n    }\n\n    private onEquipSlotUnInstallMsg(msg: csproto.cs.IS2CMsg): void {\n        const m = msg!.body!.equip_slot_uninstall\n        const slot = this.slots.find(slot => slot.slot_id == m!.slot_id)\n        if (slot) {\n            slot.equip_id = 0\n            slot.guid = Long.ZERO\n        }\n        EventMgr.emit(DataEvent.EquipSlotRefresh)\n    }\n\n    getEmptySlots(): csproto.cs.ICSEquipSlotInfo[] {\n        return this.slots.filter(slot => slot.equip_id == 0)\n    }\n\n    getEquippedSlots(): csproto.cs.ICSEquipSlotInfo[] {\n        return this.slots.filter(slot => slot.equip_id != 0)\n    }\n\n    getEmptySlotByClass(equipClass: number): csproto.cs.ICSEquipSlotInfo | null {\n        return this.slots.find(slot => slot.equip_class == equipClass && slot.guid && slot.guid.eq(0)) || null\n    }\n\n    getEquipSlotInfo(slotID: number): csproto.cs.ICSEquipSlotInfo | null {\n        return this.slots.find(slot => slot.slot_id == slotID) || null\n    }\n\n    equip(item: csproto.cs.ICSItem) {\n        const equipCfg = MyApp.lubanMgr.table.TbEquip.get(item.item_id!)\n        const slots = this.slots.filter(slot => slot.equip_class == equipCfg?.equipClass)\n        if (slots.length == 0) {\n            logError(\"EquipSlots\", ` not found slot item:${item.item_id} ${item.guid} equipClass:${equipCfg?.equipClass}`)\n            return\n        }\n        let equippedSlot = slots[0]\n        if (slots.length > 1) {\n            const emptySlot = slots.find(slot => slot.guid!.eq(0))\n            if (emptySlot) {\n                equippedSlot = emptySlot\n            } else {\n                equippedSlot = slots[0]\n            }\n        }\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_EQUIP_SLOT_INSTALL, {\n            equip_slot_install: {\n                slot_id: equippedSlot.slot_id,\n                guid: item.guid,\n            }\n        })\n    }\n\n    unequip(guid: Long) {\n        const slot = this.slots.find(slot => slot.guid!.eq(guid))\n        if (!slot) {\n            logError(\"PlaneUI\", `unequip fail guid:${guid}`)\n            return\n        }\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_EQUIP_SLOT_UNINSTALL, {\n            equip_slot_uninstall: {\n                slot_id: slot.slot_id,\n            }\n        })\n    }\n}"]}