{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/List.ts"], "names": ["_decorator", "CCBoolean", "CCFloat", "CCInteger", "Component", "Enum", "EventHandler", "instantiate", "<PERSON><PERSON><PERSON><PERSON>", "Layout", "Node", "NodePool", "Prefab", "ScrollView", "Size", "tween", "UITransform", "Vec3", "Widget", "DEV", "ListItem", "ccclass", "property", "disallowMultiple", "menu", "executionOrder", "requireComponent", "TemplateType", "SlideType", "SelectedType", "List", "type", "tooltip", "visible", "templateType", "NODE", "PREFAB", "range", "slide", "_slideMode", "PAGE", "val", "slideMode", "NORMAL", "cyclic", "virtual", "lackCenter", "lackSlide", "selectedMode", "NONE", "SINGLE", "serializable", "_selectedId", "_lastSelectedId", "multSelected", "_forceUpdate", "_align", "_horizontalDir", "_verticalDir", "_startAxis", "_alignCalcType", "content", "_contentUt", "firstListId", "displayItemNum", "_updateDone", "_updateCounter", "_actualNumItems", "_cyclicNum", "_cyclicPos1", "_cyclicPos2", "_inited", "_scrollView", "_layout", "_resizeMode", "_topGap", "_rightGap", "_bottomGap", "_leftGap", "_columnGap", "_lineGap", "_colLineNum", "_lastDisplayData", "displayData", "_pool", "_itemTmp", "_itemTmpUt", "_needUpdateWidget", "_itemSize", "_sizeType", "_customSize", "frameCount", "_aniDelRuning", "_aniDelCB", "_aniDelItem", "_aniDelBeforePos", "_aniDelBeforeScale", "viewTop", "viewRight", "viewBottom", "viewLeft", "_doneAfterUpdate", "elasticTop", "elasticRight", "elasticBottom", "elasticLeft", "scrollToListId", "adhering", "_ad<PERSON><PERSON><PERSON><PERSON>", "nearestListId", "curPage<PERSON>um", "_beganPos", "_scrollPos", "_curScrollIsTouch", "_scrollToListId", "_scrollToEndTime", "_scrollToSo", "_lack", "_allItemSize", "_allItemSizeNoEdge", "_scrollItem", "_thisNodeUt", "_virtual", "_numItems", "_onScrolling", "updateRate", "_updateRate", "selectedId", "t", "item", "repeatEventSingle", "getItemByListId", "listItem", "getComponent", "selected", "lastItem", "selectedEvent", "emitEvents", "MULT", "bool", "sub", "indexOf", "push", "splice", "numItems", "checkInited", "console", "error", "_resizeContent", "frameByFrameRenderNum", "layout", "enabled", "_delRedundantItem", "len", "n", "_createOrUpdateItem2", "scrollView", "onLoad", "_init", "onDestroy", "destroy", "tmpNode", "clear", "onEnable", "_registerEvent", "position", "scale", "onDisable", "_unregisterEvent", "node", "on", "EventType", "TOUCH_START", "_onTouchStart", "_onTouchUp", "TOUCH_CANCEL", "_onTouchCancelled", "_onScrollBegan", "_onScrollEnded", "SIZE_CHANGED", "_onSizeChanged", "off", "name", "resizeMode", "startAxis", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "spacingX", "spacingY", "verticalDirection", "horizontalDirection", "setTemplateItem", "tmpPrefab", "ADHERING", "inertia", "_onMouseWheel", "_processAutoScrolling", "bind", "_startBounceBackIfNeeded", "Type", "HORIZONTAL", "HorizontalDirection", "LEFT_TO_RIGHT", "RIGHT_TO_LEFT", "VERTICAL", "VerticalDirection", "TOP_TO_BOTTOM", "BOTTOM_TO_TOP", "GRID", "AxisDirection", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dt", "OUT_OF_BOUNDARY_BREAKING_FACTOR", "EPSILON", "ZERO", "quintEaseOut", "time", "sv", "isAutoScrollBrake", "brakingFactor", "percentage", "Math", "min", "clonedAutoScrollTargetDelta", "clone", "multiplyScalar", "clonedAutoScrollStartPosition", "add", "reachedEnd", "abs", "fireEvent", "SCROLL_ENG_WITH_THRESHOLD", "brakeOffsetPosition", "subtract", "set", "move<PERSON><PERSON><PERSON>", "outOfBoundary", "equals", "deltaMove", "SCROLLING", "SCROLL_ENDED", "ResizeMode", "CHILDREN", "cellSize", "itemUt", "width", "height", "com", "remove", "trimW", "floor", "trimH", "printLog", "result", "fixed", "_getFixedSize", "undefined", "count", "lineNum", "ceil", "colNum", "totalSize", "spacing", "_cyclicAllItemSize", "_cycilcAllItemSizeNoEdge", "slideOffset", "targetWH", "ev", "scrollPos", "getPosition", "y", "x", "addVal", "contentPos", "z", "setPosition", "isAutoScrolling", "_calcViewPos", "vTop", "vRight", "vBottom", "vLeft", "itemPos", "curId", "endId", "breakFor", "_calcItemPos", "right", "left", "length", "bottom", "top", "ww", "hh", "id", "haveDataChange", "sort", "a", "b", "c", "_createOrUpdateItem", "_calcNearestItem", "itemX", "itemY", "cs", "offset", "anchorX", "anchorY", "colLine", "_calcExistItemPos", "ut", "pos", "data", "getItemPos", "listId", "parseInt", "to", "start", "adhere", "_page<PERSON><PERSON><PERSON>", "captureListeners", "isMe", "target", "itemNode", "_listId", "parent", "simulate", "_onItemAdaptive", "updateAll", "unschedule", "scrollTo", "max", "Date", "getTime", "curPos", "dis", "pageDistance", "canSkip", "timeInSecond", "prePage", "nextPage", "update", "canGet", "size", "get", "setContentSize", "_resetItemSize", "<PERSON><PERSON><PERSON><PERSON>", "widget", "updateAlignment", "setSiblingIndex", "children", "list", "renderEvent", "_updateListItem", "_updateItemPos", "listIdOrItem", "isNaN", "setMultSelected", "args", "Array", "isArray", "getMultSelected", "hasMultSelected", "updateItem", "_getOutsideItem", "find", "d", "arr", "<PERSON><PERSON><PERSON>d", "put", "m", "_delSingleItem", "removeFromParent", "aniDelItem", "callFunc", "aniType", "warn", "curLastId", "resetSelectedId", "showAni", "newId", "newData", "newCustomSize", "idNumber", "sec", "twe", "haveCB", "posData", "call", "overStress", "updateLayout", "targetX", "targetY", "viewPos", "comparePos", "runScroll", "scrollToOffset", "scheduleOnce", "center", "skipPage", "pageNum", "pageChangeEvent", "calcCustomSize", "temp", "Object", "keys"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAOSA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,O,OAAAA,O;AAASC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,Q,OAAAA,Q;AAAUC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,K,OAAAA,K;AAAcC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACzLC,MAAAA,G,UAAAA,G;;AACFC,MAAAA,Q;;;;;;;AATP;AACA;AACA;AACA;AACA;AACA;OACM;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA,gBAArB;AAAuCC,QAAAA,IAAvC;AAA6CC,QAAAA,cAA7C;AAA6DC,QAAAA;AAA7D,O,GAAkF1B,U;;;;AAKnF2B,MAAAA,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;QAAAA,Y;;AAKAC,MAAAA,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;QAAAA,S;;AAMAC,MAAAA,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;QAAAA,Y;;yBAYgBC,I,WALpBP,gBAAgB,E,UAChBC,IAAI,CAAC,MAAD,C,UACJE,gBAAgB,CAACb,UAAD,C,UAEhBY,cAAc,CAAC,CAAC,IAAF,C,UAGVH,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAE1B,IAAI,CAACsB,YAAD,CAAZ;AAA4BK,QAAAA,OAAO,EAAEb,GAAG,GAAG,MAAH,GAAY;AAApD,OAAD,C,UAGRG,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAErB,IADA;AAENsB,QAAAA,OAAO,EAAEb,GAAG,GAAG,QAAH,GAAc,EAFpB;;AAGN;AACAc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKC,YAAL,IAAqBP,YAAY,CAACQ,IAAzC;AAAgD;;AAJtD,OAAD,C,UAQRb,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAEnB,MADA;AAENoB,QAAAA,OAAO,EAAEb,GAAG,GAAG,QAAH,GAAc,EAFpB;;AAGN;AACAc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKC,YAAL,IAAqBP,YAAY,CAACS,MAAzC;AAAkD;;AAJxD,OAAD,C,UAQRd,QAAQ,CAAC,EAAD,C,UAERA,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE1B,IAAI,CAACuB,SAAD,CADJ;AAENI,QAAAA,OAAO,EAAEb,GAAG,GAAG,MAAH,GAAY;AAFlB,OAAD,C,WAWRG,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE7B,OADA;AAENmC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,EAAP,CAFD;AAGNL,QAAAA,OAAO,EAAEb,GAAG,GAAG,QAAH,GAAc,EAHpB;AAINmB,QAAAA,KAAK,EAAE,IAJD;;AAKN;AACAL,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKM,UAAL,IAAmBX,SAAS,CAACY,IAApC;AAA2C;;AANjD,OAAD,C,WAURlB,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAEzB,YADA;AAEN0B,QAAAA,OAAO,EAAEb,GAAG,GAAG,QAAH,GAAc,EAFpB;;AAGN;AACAc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKM,UAAL,IAAmBX,SAAS,CAACY,IAApC;AAA2C;;AAJjD,OAAD,C,WAQRlB,QAAQ,CAAC,EAAD,C,WAERA,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE9B,SADA;AAEN+B,QAAAA,OAAO,EAAEb,GAAG,GAAG,eAAH,GAAqB;AAF3B,OAAD,C,WAeRG,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAEb,GAAG,GAAG,SAAH,GAAe,EADrB;;AAENc,QAAAA,OAAO,GAAG;AACN;AACA,cAAIQ,GAAY;AAAG;AAAoB,eAAKC,SAAL,IAAkBd,SAAS,CAACe,MAAnE;;AACA,cAAI,CAACF,GAAL,EAAU;AACN;AACA,iBAAKG,MAAL,GAAc,KAAd;AACH;;AACD,iBAAOH,GAAP;AACH;;AAVK,OAAD,C,WAcRnB,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAEb,GAAG,GAAG,2CAAH,GAAiD,EADvD;;AAEN;AACAc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKY,OAAZ;AAAsB;;AAH5B,OAAD,C,WAORvB,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAEb,GAAG,GAAG,2BAAH,GAAiC,EADvC;;AAENc,QAAAA,OAAO,GAAG;AACN;AACA,cAAIQ,GAAY,GAAG,KAAKI,OAAL,IAAgB,CAAC,KAAKC,UAAzC;;AACA,cAAI,CAACL,GAAL,EAAU;AACN;AACA,iBAAKM,SAAL,GAAiB,KAAjB;AACH;;AACD,iBAAON,GAAP;AACH;;AAVK,OAAD,C,WAcRnB,QAAQ,CAAC;AAAES,QAAAA,IAAI,EAAE5B;AAAR,OAAD,C,WAERmB,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE5B,SADA;AAENkC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,CAFD;AAGNL,QAAAA,OAAO,EAAEb,GAAG,GAAG,sBAAH,GAA4B,EAHlC;AAINmB,QAAAA,KAAK,EAAE;AAJD,OAAD,C,WAeRhB,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE5B,SADA;AAENkC,QAAAA,KAAK,EAAE,CAAC,CAAD,EAAI,EAAJ,EAAQ,CAAR,CAFD;AAGNL,QAAAA,OAAO,EAAEb,GAAG,GAAG,+BAAH,GAAqC,EAH3C;AAINmB,QAAAA,KAAK,EAAE;AAJD,OAAD,C,WAQRhB,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAEzB,YADA;AAEN0B,QAAAA,OAAO,EAAEb,GAAG,GAAG,WAAH,GAAiB;AAFvB,OAAD,C,WAMRG,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAE1B,IAAI,CAACwB,YAAD,CADJ;AAENG,QAAAA,OAAO,EAAEb,GAAG,GAAG,MAAH,GAAY;AAFlB,OAAD,C,WAMRG,QAAQ,CAAC;AACNS,QAAAA,IAAI,EAAEzB,YADA;AAEN0B,QAAAA,OAAO,EAAEb,GAAG,GAAG,QAAH,GAAc,EAFpB;;AAGN;AACAc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKe,YAAL,GAAoBnB,YAAY,CAACoB,IAAxC;AAA+C;;AAJrD,OAAD,C,WAOR3B,QAAQ,CAAC;AACNU,QAAAA,OAAO,EAAEb,GAAG,GAAG,YAAH,GAAkB,EADxB;;AAEN;AACAc,QAAAA,OAAO,GAAG;AAAE,iBAAO,KAAKe,YAAL,IAAqBnB,YAAY,CAACqB,MAAzC;AAAkD;;AAHxD,OAAD,C,WAqFR5B,QAAQ,CAAC;AACN6B,QAAAA,YAAY,EAAE;AADR,OAAD,C,EA/OZ9B,O,gFAAD,MAMqBS,IANrB,SAMkC1B,SANlC,CAM4C;AAAA;AAAA;;AACxC;AADwC;;AAIxC;AAJwC;;AAYxC;AAZwC;;AAoBxC;AApBwC;;AAiCxC;AAjCwC;;AA2CxC;AA3CwC;;AAmDxC;AAnDwC;;AAoExC;AApEwC;;AAkFxC;AAlFwC;;AAyFxC;AAzFwC;;AAuGxC;AAvGwC;;AAwHxC;AAxHwC;;AAgIxC;AAhIwC;;AAsIxC;AAtIwC;;AA4IxC;AA5IwC;;AAAA;;AA2JxC;AA3JwC,eA4JhCgD,WA5JgC,GA4JV,CAAC,CA5JS;AAAA,eA6JhCC,eA7JgC,GA6JN,CAAC,CA7JK;AAAA,eA8JhCC,YA9JgC,GA8JP,EA9JO;AAAA,eAwNhCC,YAxNgC,GAwNR,KAxNQ;AAAA,eAyNhCC,MAzNgC,GAyNf,CAzNe;AAAA,eA0NhCC,cA1NgC,GA0NP,CA1NO;AAAA,eA2NhCC,YA3NgC,GA2NT,CA3NS;AAAA,eA4NhCC,UA5NgC,GA4NX,CA5NW;AAAA,eA6NhCC,cA7NgC,GA6NP,CA7NO;AAAA,eA8NjCC,OA9NiC,GA8NV,IA9NU;AAAA,eA+NhCC,UA/NgC,GA+NC,IA/ND;AAAA,eAgOhCC,WAhOgC,GAgOV,CAhOU;AAAA,eAiOjCC,cAjOiC,GAiOR,CAjOQ;AAAA,eAkOhCC,WAlOgC,GAkOT,IAlOS;AAAA,eAmOhCC,cAnOgC,GAmOP,CAnOO;AAAA,eAoOjCC,eApOiC,GAoOP,CApOO;AAAA,eAqOhCC,UArOgC,GAqOX,CArOW;AAAA,eAsOhCC,WAtOgC,GAsOV,CAtOU;AAAA,eAuOhCC,WAvOgC,GAuOV,CAvOU;;AAwOxC;AAxOwC;;AAAA,eAkShCC,OAlSgC,GAkSb,KAlSa;AAAA,eAmShCC,WAnSgC,GAmSC,IAnSD;AAAA,eAuShCC,OAvSgC,GAuSP,IAvSO;AAAA,eAwShCC,WAxSgC,GAwSV,CAxSU;AAAA,eAyShCC,OAzSgC,GAySd,CAzSc;AAAA,eA0ShCC,SA1SgC,GA0SZ,CA1SY;AAAA,eA2ShCC,UA3SgC,GA2SX,CA3SW;AAAA,eA4ShCC,QA5SgC,GA4Sb,CA5Sa;AAAA,eA8ShCC,UA9SgC,GA8SX,CA9SW;AAAA,eA+ShCC,QA/SgC,GA+Sb,CA/Sa;AAAA,eAgThCC,WAhTgC,GAgTV,CAhTU;AAAA,eAkThCC,gBAlTgC,GAkTH,EAlTG;AAAA,eAmTjCC,WAnTiC,GAmTZ,EAnTY;AAAA,eAoThCC,KApTgC,GAoTP,IApTO;AAAA,eAsThCC,QAtTgC;AAAA,eAuThCC,UAvTgC,GAuTC,IAvTD;AAAA,eAwThCC,iBAxTgC,GAwTH,KAxTG;AAAA,eAyThCC,SAzTgC,GAyTP,IAzTO;AAAA,eA0ThCC,SA1TgC,GA0TX,KA1TW;AAAA,eA4TjCC,WA5TiC;AAAA,eA8ThCC,UA9TgC,GA8TX,CA9TW;AAAA,eA+ThCC,aA/TgC,GA+TP,KA/TO;AAAA,eAgUhCC,SAhUgC,GAgUH,IAhUG;AAAA,eAiUhCC,WAjUgC;AAAA,eAkUhCC,gBAlUgC,GAkUA,IAlUA;AAAA,eAmUhCC,kBAnUgC,GAmUH,CAnUG;AAAA,eAoUhCC,OApUgC,GAoUd,CApUc;AAAA,eAqUhCC,SArUgC,GAqUZ,CArUY;AAAA,eAsUhCC,UAtUgC,GAsUX,CAtUW;AAAA,eAuUhCC,QAvUgC,GAuUb,CAvUa;AAAA,eAyUhCC,gBAzUgC,GAyUJ,KAzUI;AAAA,eA2UhCC,UA3UgC,GA2UX,CA3UW;AAAA,eA4UhCC,YA5UgC,GA4UT,CA5US;AAAA,eA6UhCC,aA7UgC,GA6UR,CA7UQ;AAAA,eA8UhCC,WA9UgC,GA8UV,CA9UU;AAAA,eAgVhCC,cAhVgC,GAgVP,CAhVO;AAAA,eAkVhCC,QAlVgC,GAkVZ,KAlVY;AAAA,eAoVhCC,gBApVgC,GAoVJ,KApVI;AAAA,eAqVhCC,aArVgC,GAqVR,CArVQ;AAAA,eAuVjCC,UAvViC,GAuVZ,CAvVY;AAAA,eAwVhCC,SAxVgC;AAAA,eAyVhCC,UAzVgC;AAAA,eA0VhCC,iBA1VgC,GA0VH,KA1VG;AA0VG;AA1VH,eA4VhCC,eA5VgC;AAAA,eA6VhCC,gBA7VgC;AAAA,eA8VhCC,WA9VgC;AAAA,eAgWhCC,KAhWgC,GAgWf,KAhWe;AAAA,eAiWhCC,YAjWgC,GAiWT,CAjWS;AAAA,eAkWhCC,kBAlWgC,GAkWH,CAlWG;AAAA,eAoWhCC,WApWgC;AAoWf;AApWe,eAsWhCC,WAtWgC,GAsWE,IAtWF;AAAA;;AA2B3B,YAAT/E,SAAS,CAACD,GAAD,EAAiB;AAC1B,eAAKF,UAAL,GAAkBE,GAAlB;AACH;;AACY,YAATC,SAAS,GAAG;AACZ,iBAAO,KAAKH,UAAZ;AACH;;AA0BU,YAAPM,OAAO,CAACJ,GAAD,EAAe;AACtB,cAAIA,GAAG,IAAI,IAAX,EACI,KAAKiF,QAAL,GAAgBjF,GAAhB;;AACJ,cAAI,CAACtB,GAAD,IAAQ,KAAKwG,SAAL,IAAkB,CAA9B,EAAiC;AAC7B,iBAAKC,YAAL;AACH;AACJ;;AACU,YAAP/E,OAAO,GAAG;AACV,iBAAO,KAAK6E,QAAZ;AACH;;AA6Ca,YAAVG,UAAU,CAACpF,GAAD,EAAc;AACxB,cAAIA,GAAG,IAAI,CAAP,IAAYA,GAAG,IAAI,CAAvB,EAA0B;AACtB,iBAAKqF,WAAL,GAAmBrF,GAAnB;AACH;AACJ;;AACa,YAAVoF,UAAU,GAAG;AACb,iBAAO,KAAKC,WAAZ;AACH;;AAwCa,YAAVC,UAAU,CAACtF,GAAD,EAAc;AACxB,cAAIuF,CAAM,GAAG,IAAb;AACA,cAAIC,IAAJ;;AACA,kBAAQD,CAAC,CAAChF,YAAV;AACI,iBAAKnB,YAAY,CAACqB,MAAlB;AAA0B;AACtB,oBAAI,CAAC8E,CAAC,CAACE,iBAAH,IAAwBzF,GAAG,IAAIuF,CAAC,CAAC5E,WAArC,EACI;AACJ6E,gBAAAA,IAAI,GAAGD,CAAC,CAACG,eAAF,CAAkB1F,GAAlB,CAAP,CAHsB,CAItB;AACA;;AACA,oBAAI2F,QAAJ;AACA,oBAAIJ,CAAC,CAAC5E,WAAF,IAAiB,CAArB,EACI4E,CAAC,CAAC3E,eAAF,GAAoB2E,CAAC,CAAC5E,WAAtB,CADJ,KAEK;AACD4E,kBAAAA,CAAC,CAAC3E,eAAF,GAAoB,IAApB;AACJ2E,gBAAAA,CAAC,CAAC5E,WAAF,GAAgBX,GAAhB;;AACA,oBAAIwF,IAAJ,EAAU;AACNG,kBAAAA,QAAQ,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,2CAAX;AACAD,kBAAAA,QAAQ,CAACE,QAAT,GAAoB,IAApB;AACH;;AACD,oBAAIN,CAAC,CAAC3E,eAAF,IAAqB,CAArB,IAA0B2E,CAAC,CAAC3E,eAAF,IAAqB2E,CAAC,CAAC5E,WAArD,EAAkE;AAC9D,sBAAImF,QAAa,GAAGP,CAAC,CAACG,eAAF,CAAkBH,CAAC,CAAC3E,eAApB,CAApB;;AACA,sBAAIkF,QAAJ,EAAc;AACVA,oBAAAA,QAAQ,CAACF,YAAT;AAAA;AAAA,8CAAgCC,QAAhC,GAA2C,KAA3C;AACH;AACJ;;AACD,oBAAIN,CAAC,CAACQ,aAAN,EAAqB;AACjBlI,kBAAAA,YAAY,CAACmI,UAAb,CAAwB,CAACT,CAAC,CAACQ,aAAH,CAAxB,EAA2CP,IAA3C,EAAiDxF,GAAG,GAAG,KAAK0B,eAA5D,EAA6E6D,CAAC,CAAC3E,eAAF,IAAqB,IAArB,GAA4B,IAA5B,GAAoC2E,CAAC,CAAC3E,eAAF,GAAoB,KAAKc,eAA1I;AACH;;AACD;AACH;;AACD,iBAAKtC,YAAY,CAAC6G,IAAlB;AAAwB;AACpBT,gBAAAA,IAAI,GAAGD,CAAC,CAACG,eAAF,CAAkB1F,GAAlB,CAAP;AACA,oBAAI,CAACwF,IAAL,EACI;;AACJ,oBAAIG,SAAQ,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,yCAAf;;AACA,oBAAIL,CAAC,CAAC5E,WAAF,IAAiB,CAArB,EACI4E,CAAC,CAAC3E,eAAF,GAAoB2E,CAAC,CAAC5E,WAAtB;AACJ4E,gBAAAA,CAAC,CAAC5E,WAAF,GAAgBX,GAAhB;AACA,oBAAIkG,IAAa,GAAG,CAACP,SAAQ,CAACE,QAA9B;AACAF,gBAAAA,SAAQ,CAACE,QAAT,GAAoBK,IAApB;AACA,oBAAIC,GAAW,GAAGZ,CAAC,CAAC1E,YAAF,CAAeuF,OAAf,CAAuBpG,GAAvB,CAAlB;;AACA,oBAAIkG,IAAI,IAAIC,GAAG,GAAG,CAAlB,EAAqB;AACjBZ,kBAAAA,CAAC,CAAC1E,YAAF,CAAewF,IAAf,CAAoBrG,GAApB;AACH,iBAFD,MAEO,IAAI,CAACkG,IAAD,IAASC,GAAG,IAAI,CAApB,EAAuB;AAC1BZ,kBAAAA,CAAC,CAAC1E,YAAF,CAAeyF,MAAf,CAAsBH,GAAtB,EAA2B,CAA3B;AACH;;AACD,oBAAIZ,CAAC,CAACQ,aAAN,EAAqB;AACjBlI,kBAAAA,YAAY,CAACmI,UAAb,CAAwB,CAACT,CAAC,CAACQ,aAAH,CAAxB,EAA2CP,IAA3C,EAAiDxF,GAAG,GAAG,KAAK0B,eAA5D,EAA6E6D,CAAC,CAAC3E,eAAF,IAAqB,IAArB,GAA4B,IAA5B,GAAoC2E,CAAC,CAAC3E,eAAF,GAAoB,KAAKc,eAA1I,EAA4JwE,IAA5J;AACH;;AACD;AACH;AAhDL;AAkDH;;AACa,YAAVZ,UAAU,GAAG;AACb,iBAAO,KAAK3E,WAAZ;AACH;;AAsBW,YAAR4F,QAAQ,CAACvG,GAAD,EAAc;AACtB,cAAIuF,CAAC,GAAG,IAAR;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,CAAc,KAAd,CAAL,EACI;;AACJ,cAAIxG,GAAG,IAAI,IAAP,IAAeA,GAAG,GAAG,CAAzB,EAA4B;AACxByG,YAAAA,OAAO,CAACC,KAAR,CAAc,0BAAd,EAA0C1G,GAA1C;AACA;AACH;;AACDuF,UAAAA,CAAC,CAAC7D,eAAF,GAAoB6D,CAAC,CAACL,SAAF,GAAclF,GAAlC;AACAuF,UAAAA,CAAC,CAACzE,YAAF,GAAiB,IAAjB;;AAEA,cAAIyE,CAAC,CAACN,QAAN,EAAgB;AACZM,YAAAA,CAAC,CAACoB,cAAF;;AACA,gBAAIpB,CAAC,CAACpF,MAAN,EAAc;AACVoF,cAAAA,CAAC,CAACL,SAAF,GAAcK,CAAC,CAAC5D,UAAF,GAAe4D,CAAC,CAACL,SAA/B;AACH;;AACDK,YAAAA,CAAC,CAACJ,YAAF;;AACA,gBAAI,CAACI,CAAC,CAACqB,qBAAH,IAA4BrB,CAAC,CAACtF,SAAF,IAAed,SAAS,CAACY,IAAzD,EACIwF,CAAC,CAAClB,UAAF,GAAekB,CAAC,CAACnB,aAAjB;AACP,WARD,MAQO;AAAA;;AACH,gBAAImB,CAAC,CAACpF,MAAN,EAAc;AACVoF,cAAAA,CAAC,CAACoB,cAAF;;AACApB,cAAAA,CAAC,CAACL,SAAF,GAAcK,CAAC,CAAC5D,UAAF,GAAe4D,CAAC,CAACL,SAA/B;AACH;;AACD,gBAAI2B,MAAqB,iBAAGtB,CAAC,CAACnE,OAAL,qBAAG,WAAWwE,YAAX,CAAwB5H,MAAxB,CAA5B;;AACA,gBAAI6I,MAAJ,EAAY;AACRA,cAAAA,MAAM,CAACC,OAAP,GAAiB,IAAjB;AACH;;AACDvB,YAAAA,CAAC,CAACwB,iBAAF;;AAEAxB,YAAAA,CAAC,CAACjE,WAAF,GAAgB,CAAhB;;AACA,gBAAIiE,CAAC,CAACqB,qBAAF,GAA0B,CAA9B,EAAiC;AAC7B;AACA,kBAAII,GAAW,GAAGzB,CAAC,CAACqB,qBAAF,GAA0BrB,CAAC,CAACL,SAA5B,GAAwCK,CAAC,CAACL,SAA1C,GAAsDK,CAAC,CAACqB,qBAA1E;;AACA,mBAAK,IAAIK,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGD,GAA5B,EAAiCC,CAAC,EAAlC,EAAsC;AAClC1B,gBAAAA,CAAC,CAAC2B,oBAAF,CAAuBD,CAAvB;AACH;;AACD,kBAAI1B,CAAC,CAACqB,qBAAF,GAA0BrB,CAAC,CAACL,SAAhC,EAA2C;AACvCK,gBAAAA,CAAC,CAAC9D,cAAF,GAAmB8D,CAAC,CAACqB,qBAArB;AACArB,gBAAAA,CAAC,CAAC/D,WAAF,GAAgB,KAAhB;AACH;AACJ,aAVD,MAUO;AACH,mBAAK,IAAIyF,EAAS,GAAG,CAArB,EAAwBA,EAAC,GAAG1B,CAAC,CAACL,SAA9B,EAAyC+B,EAAC,EAA1C,EAA8C;AAC1C1B,gBAAAA,CAAC,CAAC2B,oBAAF,CAAuBD,EAAvB;AACH;;AACD1B,cAAAA,CAAC,CAAChE,cAAF,GAAmBgE,CAAC,CAACL,SAArB;AACH;AACJ;AACJ;;AACW,YAARqB,QAAQ,GAAG;AACX,iBAAO,KAAK7E,eAAZ;AACH;;AAIa,YAAVyF,UAAU,GAAG;AACb,iBAAO,KAAKpF,WAAZ;AACH;;AAkED;AAEAqF,QAAAA,MAAM,GAAG;AACL,eAAKC,KAAL;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,cAAI/B,CAAM,GAAG,IAAb;AACA,cAAIxH,OAAO,CAACwH,CAAC,CAAC3C,QAAH,CAAX,EACI2C,CAAC,CAAC3C,QAAF,CAAW2E,OAAX;AACJ,cAAIxJ,OAAO,CAACwH,CAAC,CAACiC,OAAH,CAAX,EACIjC,CAAC,CAACiC,OAAF,CAAUD,OAAV;AACJhC,UAAAA,CAAC,CAAC5C,KAAF,IAAW4C,CAAC,CAAC5C,KAAF,CAAQ8E,KAAR,EAAX;AACH;;AAEDC,QAAAA,QAAQ,GAAG;AACP;AACA,eAAKC,cAAL;;AACA,eAAKN,KAAL,GAHO,CAIP;;;AACA,cAAI,KAAKlE,aAAT,EAAwB;AACpB,iBAAKA,aAAL,GAAqB,KAArB;;AACA,gBAAI,KAAKE,WAAT,EAAsB;AAClB,kBAAI,KAAKC,gBAAT,EAA2B;AACvB,qBAAKD,WAAL,CAAiBuE,QAAjB,GAA4B,KAAKtE,gBAAjC,CADuB,CAEvB;;AACA,qBAAKA,gBAAL,GAAwB,IAAxB;AACH;;AACD,kBAAI,KAAKC,kBAAT,EAA6B;AACzB,qBAAKF,WAAL,CAAiBwE,KAAjB,GAAyB,KAAKtE,kBAA9B,CADyB,CAEzB;;AACA,qBAAKA,kBAAL,GAA0B,CAA1B;AACH;;AACD,qBAAO,KAAKF,WAAZ;AACH;;AACD,gBAAI,KAAKD,SAAT,EAAoB;AAChB,mBAAKA,SAAL,GADgB,CAEhB;;;AACA,mBAAKA,SAAL,GAAiB,IAAjB;AACH;AACJ;AACJ;;AAED0E,QAAAA,SAAS,GAAG;AACR;AACA,eAAKC,gBAAL;AACH,SAtZuC,CAuZxC;;;AACAJ,QAAAA,cAAc,GAAG;AACb,cAAIpC,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAUhK,IAAI,CAACiK,SAAL,CAAeC,WAAzB,EAAsC5C,CAAC,CAAC6C,aAAxC,EAAuD7C,CAAvD;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAU,UAAV,EAAsB1C,CAAC,CAAC8C,UAAxB,EAAoC9C,CAApC;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAUhK,IAAI,CAACiK,SAAL,CAAeI,YAAzB,EAAuC/C,CAAC,CAACgD,iBAAzC,EAA4DhD,CAA5D;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAU,cAAV,EAA0B1C,CAAC,CAACiD,cAA5B,EAA4CjD,CAA5C;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAU,cAAV,EAA0B1C,CAAC,CAACkD,cAA5B,EAA4ClD,CAA5C;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAU,WAAV,EAAuB1C,CAAC,CAACJ,YAAzB,EAAuCI,CAAvC;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOC,EAAP,CAAUhK,IAAI,CAACiK,SAAL,CAAeQ,YAAzB,EAAuCnD,CAAC,CAACoD,cAAzC,EAAyDpD,CAAzD;AACH,SAjauC,CAkaxC;;;AACAwC,QAAAA,gBAAgB,GAAG;AACf,cAAIxC,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW3K,IAAI,CAACiK,SAAL,CAAeC,WAA1B,EAAuC5C,CAAC,CAAC6C,aAAzC,EAAwD7C,CAAxD;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW,UAAX,EAAuBrD,CAAC,CAAC8C,UAAzB,EAAqC9C,CAArC;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW3K,IAAI,CAACiK,SAAL,CAAeI,YAA1B,EAAwC/C,CAAC,CAACgD,iBAA1C,EAA6DhD,CAA7D;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW,cAAX,EAA2BrD,CAAC,CAACiD,cAA7B,EAA6CjD,CAA7C;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW,cAAX,EAA2BrD,CAAC,CAACkD,cAA7B,EAA6ClD,CAA7C;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW,WAAX,EAAwBrD,CAAC,CAACJ,YAA1B,EAAwCI,CAAxC;AACAA,UAAAA,CAAC,CAACyC,IAAF,CAAOY,GAAP,CAAW3K,IAAI,CAACiK,SAAL,CAAeQ,YAA1B,EAAwCnD,CAAC,CAACoD,cAA1C,EAA0DpD,CAA1D;AACH,SA5auC,CA6axC;;;AACA8B,QAAAA,KAAK,GAAG;AACJ,cAAI9B,CAAM,GAAG,IAAb;AACA,cAAIA,CAAC,CAACzD,OAAN,EACI;AAEJyD,UAAAA,CAAC,CAACP,WAAF,GAAgBO,CAAC,CAACyC,IAAF,CAAOpC,YAAP,CAAoBrH,WAApB,CAAhB;AACAgH,UAAAA,CAAC,CAACxD,WAAF,GAAgBwD,CAAC,CAACyC,IAAF,CAAOpC,YAAP,CAAoBxH,UAApB,CAAhB;AAEAmH,UAAAA,CAAC,CAACnE,OAAF,GAAYmE,CAAC,CAACxD,WAAF,CAAcX,OAA1B;AACAmE,UAAAA,CAAC,CAAClE,UAAF,GAAekE,CAAC,CAACnE,OAAF,CAAWwE,YAAX,CAAwBrH,WAAxB,CAAf;;AACA,cAAI,CAACgH,CAAC,CAACnE,OAAP,EAAgB;AACZqF,YAAAA,OAAO,CAACC,KAAR,CAAcnB,CAAC,CAACyC,IAAF,CAAOa,IAAP,GAAc,8BAA5B;AACA;AACH;;AAEDtD,UAAAA,CAAC,CAACvD,OAAF,GAAYuD,CAAC,CAACnE,OAAF,CAAWwE,YAAX,CAAwB5H,MAAxB,CAAZ;AAEAuH,UAAAA,CAAC,CAACxE,MAAF,GAAWwE,CAAC,CAACvD,OAAF,CAAU1C,IAArB,CAjBI,CAiBuB;;AAC3BiG,UAAAA,CAAC,CAACtD,WAAF,GAAgBsD,CAAC,CAACvD,OAAF,CAAU8G,UAA1B,CAlBI,CAkBkC;;AACtCvD,UAAAA,CAAC,CAACrE,UAAF,GAAeqE,CAAC,CAACvD,OAAF,CAAU+G,SAAzB;AAEAxD,UAAAA,CAAC,CAACrD,OAAF,GAAYqD,CAAC,CAACvD,OAAF,CAAUgH,UAAtB,CArBI,CAqB8B;;AAClCzD,UAAAA,CAAC,CAACpD,SAAF,GAAcoD,CAAC,CAACvD,OAAF,CAAUiH,YAAxB,CAtBI,CAsBkC;;AACtC1D,UAAAA,CAAC,CAACnD,UAAF,GAAemD,CAAC,CAACvD,OAAF,CAAUkH,aAAzB,CAvBI,CAuBoC;;AACxC3D,UAAAA,CAAC,CAAClD,QAAF,GAAakD,CAAC,CAACvD,OAAF,CAAUmH,WAAvB,CAxBI,CAwBgC;;AAEpC5D,UAAAA,CAAC,CAACjD,UAAF,GAAeiD,CAAC,CAACvD,OAAF,CAAUoH,QAAzB,CA1BI,CA0B+B;;AACnC7D,UAAAA,CAAC,CAAChD,QAAF,GAAagD,CAAC,CAACvD,OAAF,CAAUqH,QAAvB,CA3BI,CA2B6B;;AAEjC9D,UAAAA,CAAC,CAAC/C,WAAF,CA7BI,CA6BW;;AAEf+C,UAAAA,CAAC,CAACtE,YAAF,GAAiBsE,CAAC,CAACvD,OAAF,CAAUsH,iBAA3B,CA/BI,CA+B0C;;AAC9C/D,UAAAA,CAAC,CAACvE,cAAF,GAAmBuE,CAAC,CAACvD,OAAF,CAAUuH,mBAA7B,CAhCI,CAgC8C;;AAElDhE,UAAAA,CAAC,CAACiE,eAAF,CAAkB1L,WAAW,CAACyH,CAAC,CAAC9F,YAAF,IAAkBP,YAAY,CAACS,MAA/B,GAAwC4F,CAAC,CAACkE,SAA1C,GAAsDlE,CAAC,CAACiC,OAAzD,CAA7B,EAlCI,CAoCJ;;AACA,cAAIjC,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACuK,QAA1B,IAAsCnE,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACY,IAApE,EAA0E;AACtEwF,YAAAA,CAAC,CAACxD,WAAF,CAAc4H,OAAd,GAAwB,KAAxB;;AACApE,YAAAA,CAAC,CAACxD,WAAF,CAAc6H,aAAd,GAA8B,YAAY;AACtC;AACH,aAFD;AAGH;;AACD,cAAI,CAACrE,CAAC,CAACnF,OAAP,EAAwB;AACpBmF,YAAAA,CAAC,CAAClF,UAAF,GAAe,KAAf;AAEJkF,UAAAA,CAAC,CAAC9C,gBAAF,GAAqB,EAArB,CA9CI,CA8CqB;;AACzB8C,UAAAA,CAAC,CAAC7C,WAAF,GAAgB,EAAhB,CA/CI,CA+CgB;;AACpB6C,UAAAA,CAAC,CAAC5C,KAAF,GAAU,IAAIzE,QAAJ,EAAV,CAhDI,CAgDyB;;AAC7BqH,UAAAA,CAAC,CAACzE,YAAF,GAAiB,KAAjB,CAjDI,CAiD4B;;AAChCyE,UAAAA,CAAC,CAAC9D,cAAF,GAAmB,CAAnB,CAlDI,CAkD4B;;AAChC8D,UAAAA,CAAC,CAAC/D,WAAF,GAAgB,IAAhB,CAnDI,CAmD4B;;AAEhC+D,UAAAA,CAAC,CAAClB,UAAF,GAAe,CAAf,CArDI,CAqD4B;;AAEhC,cAAIkB,CAAC,CAACpF,MAAF,IAAY,CAAhB,EAAmB;AACfoF,YAAAA,CAAC,CAACxD,WAAF,CAAc8H,qBAAd,GAAsC,KAAKA,qBAAL,CAA2BC,IAA3B,CAAgCvE,CAAhC,CAAtC;;AACAA,YAAAA,CAAC,CAACxD,WAAF,CAAcgI,wBAAd,GAAyC,YAAY;AACjD,qBAAO,KAAP;AACH,aAFD;AAGH;;AAED,kBAAQxE,CAAC,CAACxE,MAAV;AACI,iBAAK/C,MAAM,CAACgM,IAAP,CAAYC,UAAjB;AAA6B;AACzB,wBAAQ1E,CAAC,CAACvE,cAAV;AACI,uBAAKhD,MAAM,CAACkM,mBAAP,CAA2BC,aAAhC;AACI5E,oBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;;AACJ,uBAAKnD,MAAM,CAACkM,mBAAP,CAA2BE,aAAhC;AACI7E,oBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;AANR;;AAQA;AACH;;AACD,iBAAKnD,MAAM,CAACgM,IAAP,CAAYK,QAAjB;AAA2B;AACvB,wBAAQ9E,CAAC,CAACtE,YAAV;AACI,uBAAKjD,MAAM,CAACsM,iBAAP,CAAyBC,aAA9B;AACIhF,oBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;;AACJ,uBAAKnD,MAAM,CAACsM,iBAAP,CAAyBE,aAA9B;AACIjF,oBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;AANR;;AAQA;AACH;;AACD,iBAAKnD,MAAM,CAACgM,IAAP,CAAYS,IAAjB;AAAuB;AACnB,wBAAQlF,CAAC,CAACrE,UAAV;AACI,uBAAKlD,MAAM,CAAC0M,aAAP,CAAqBT,UAA1B;AACI,4BAAQ1E,CAAC,CAACtE,YAAV;AACI,2BAAKjD,MAAM,CAACsM,iBAAP,CAAyBC,aAA9B;AACIhF,wBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;;AACJ,2BAAKnD,MAAM,CAACsM,iBAAP,CAAyBE,aAA9B;AACIjF,wBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;AANR;;AAQA;;AACJ,uBAAKnD,MAAM,CAAC0M,aAAP,CAAqBL,QAA1B;AACI,4BAAQ9E,CAAC,CAACvE,cAAV;AACI,2BAAKhD,MAAM,CAACkM,mBAAP,CAA2BC,aAAhC;AACI5E,wBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;;AACJ,2BAAKnD,MAAM,CAACkM,mBAAP,CAA2BE,aAAhC;AACI7E,wBAAAA,CAAC,CAACpE,cAAF,GAAmB,CAAnB;AACA;AANR;;AAQA;AApBR;;AAsBA;AACH;AA/CL,WA9DI,CA+GJ;AACA;AACA;AACA;AACA;AACA;;;AACAoE,UAAAA,CAAC,CAACnE,OAAF,CAAWuJ,iBAAX;AACApF,UAAAA,CAAC,CAACzD,OAAF,GAAY,IAAZ;AACH;AACD;AACJ;AACA;AACA;;;AACI+H,QAAAA,qBAAqB,CAACe,EAAD,EAAa;AAE9B;AACA,cAAMC,+BAA+B,GAAG,IAAxC;AACA,cAAMC,OAAO,GAAG,IAAhB;AACA,cAAMC,IAAI,GAAG,IAAIvM,IAAJ,EAAb;;AACA,cAAMwM,YAAY,GAAIC,IAAD,IAAkB;AACnCA,YAAAA,IAAI,IAAI,CAAR;AACA,mBAAQA,IAAI,GAAGA,IAAP,GAAcA,IAAd,GAAqBA,IAArB,GAA4BA,IAA5B,GAAmC,CAA3C;AACH,WAHD,CAN8B,CAU9B;;;AAEA,cAAIC,EAAc,GAAG,KAAKnJ,WAA1B;AAEA,cAAMoJ,iBAAiB,GAAGD,EAAE,CAAC,6BAAD,CAAF,EAA1B;AACA,cAAME,aAAa,GAAGD,iBAAiB,GAAGN,+BAAH,GAAqC,CAA5E;AACAK,UAAAA,EAAE,CAAC,4BAAD,CAAF,IAAoCN,EAAE,IAAI,IAAIQ,aAAR,CAAtC;AAEA,cAAIC,UAAU,GAAGC,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYL,EAAE,CAAC,4BAAD,CAAF,GAAmCA,EAAE,CAAC,sBAAD,CAAjD,CAAjB;;AACA,cAAIA,EAAE,CAAC,sBAAD,CAAN,EAAgC;AAC5BG,YAAAA,UAAU,GAAGL,YAAY,CAACK,UAAD,CAAzB;AACH;;AAED,cAAMG,2BAA2B,GAAGN,EAAE,CAAC,wBAAD,CAAF,CAA6BO,KAA7B,EAApC;AACAD,UAAAA,2BAA2B,CAACE,cAA5B,CAA2CL,UAA3C;AACA,cAAMM,6BAA6B,GAAGT,EAAE,CAAC,0BAAD,CAAF,CAA+BO,KAA/B,EAAtC;AACAE,UAAAA,6BAA6B,CAACC,GAA9B,CAAkCJ,2BAAlC;AACA,cAAIK,UAAU,GAAGP,IAAI,CAACQ,GAAL,CAAST,UAAU,GAAG,CAAtB,KAA4BP,OAA7C;AAEA,cAAMiB,SAAS,GAAGT,IAAI,CAACQ,GAAL,CAAST,UAAU,GAAG,CAAtB,KAA4BH,EAAE,CAAC,2BAAD,CAAF,EAA9C;;AACA,cAAIa,SAAS,IAAI,CAACb,EAAE,CAAC,uCAAD,CAApB,EAA+D;AAC3DA,YAAAA,EAAE,CAAC,gBAAD,CAAF,CAAqB9M,UAAU,CAAC8J,SAAX,CAAqB8D,yBAA1C;AACAd,YAAAA,EAAE,CAAC,uCAAD,CAAF,GAA8C,IAA9C;AACH;;AAED,cAAIA,EAAE,CAAC,SAAD,CAAN,EAAmB;AACf,gBAAMe,mBAAmB,GAAGN,6BAA6B,CAACF,KAA9B,EAA5B;AACAQ,YAAAA,mBAAmB,CAACC,QAApB,CAA6BhB,EAAE,CAAC,iCAAD,CAA/B;;AACA,gBAAIC,iBAAJ,EAAuB;AACnBc,cAAAA,mBAAmB,CAACP,cAApB,CAAmCN,aAAnC;AACH;;AACDO,YAAAA,6BAA6B,CAACQ,GAA9B,CAAkCjB,EAAE,CAAC,iCAAD,CAApC;AACAS,YAAAA,6BAA6B,CAACC,GAA9B,CAAkCK,mBAAlC;AACH,WARD,MAQO;AACH,gBAAMG,SAAS,GAAGT,6BAA6B,CAACF,KAA9B,EAAlB,CADG,CAEH;;AACAW,YAAAA,SAAS,CAACF,QAAV,CAAmBhB,EAAE,CAAC9J,OAAH,CAAYwG,QAA/B;AACA,gBAAMyE,aAAa,GAAGnB,EAAE,CAAC,0BAAD,CAAF,CAA+BkB,SAA/B,CAAtB;;AACA,gBAAI,CAACC,aAAa,CAACC,MAAd,CAAqBvB,IAArB,EAA2BD,OAA3B,CAAL,EAA0C;AACtCa,cAAAA,6BAA6B,CAACC,GAA9B,CAAkCS,aAAlC;AACAR,cAAAA,UAAU,GAAG,IAAb;AACH;AACJ;;AAED,cAAIA,UAAJ,EAAgB;AACZX,YAAAA,EAAE,CAAC,gBAAD,CAAF,GAAuB,KAAvB;AACH;;AAED,cAAMqB,SAAS,GAAG,IAAI/N,IAAJ,CAASmN,6BAAT,CAAlB,CA1D8B,CA2D9B;;AACAY,UAAAA,SAAS,CAACL,QAAV,CAAmBhB,EAAE,CAAC9J,OAAH,CAAYwG,QAA/B;AACAsD,UAAAA,EAAE,CAAC,aAAD,CAAF,CAAkBqB,SAAlB;AACArB,UAAAA,EAAE,CAAC,cAAD,CAAF,CAAmBqB,SAAnB,EAA8BV,UAA9B;AACAX,UAAAA,EAAE,CAAC,gBAAD,CAAF,CAAqB9M,UAAU,CAAC8J,SAAX,CAAqBsE,SAA1C;;AAEA,cAAI,CAACtB,EAAE,CAAC,gBAAD,CAAP,EAA2B;AACvBA,YAAAA,EAAE,CAAC,aAAD,CAAF,GAAoB,KAApB;AACAA,YAAAA,EAAE,CAAC,YAAD,CAAF,GAAmB,KAAnB;AACAA,YAAAA,EAAE,CAAC,gBAAD,CAAF,CAAqB9M,UAAU,CAAC8J,SAAX,CAAqBuE,YAA1C;AACH;AACJ,SAhnBuC,CAinBxC;;;AACAjD,QAAAA,eAAe,CAAChE,IAAD,EAAY;AACvB,cAAI,CAACA,IAAL,EACI;AACJ,cAAID,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAAC3C,QAAF,GAAa4C,IAAb;AACAD,UAAAA,CAAC,CAAC1C,UAAF,GAAe2C,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAAf;AAEA,cAAIgH,CAAC,CAACtD,WAAF,IAAiBjE,MAAM,CAAC0O,UAAP,CAAkBC,QAAvC,EACIpH,CAAC,CAACxC,SAAF,GAAcwC,CAAC,CAACvD,OAAF,CAAU4K,QAAxB,CADJ,KAEK;AACD,gBAAIC,MAAmB,GAAGrH,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAA1B;AACAgH,YAAAA,CAAC,CAACxC,SAAF,GAAc,IAAI1E,IAAJ,CAASwO,MAAM,CAACC,KAAhB,EAAuBD,MAAM,CAACE,MAA9B,CAAd;AACH,WAZsB,CAcvB;;AACA,cAAIC,GAAQ,GAAGxH,IAAI,CAACI,YAAL;AAAA;AAAA,mCAAf;AACA,cAAIqH,MAAM,GAAG,KAAb;AACA,cAAI,CAACD,GAAL,EACIC,MAAM,GAAG,IAAT,CAlBmB,CAmBvB;AACA;AACA;AACA;AACA;;AACA,cAAIA,MAAJ,EAAY;AACR1H,YAAAA,CAAC,CAAChF,YAAF,GAAiBnB,YAAY,CAACoB,IAA9B;AACH;;AACDwM,UAAAA,GAAG,GAAGxH,IAAI,CAACI,YAAL,CAAkBnH,MAAlB,CAAN;;AACA,cAAIuO,GAAG,IAAIA,GAAG,CAAClG,OAAf,EAAwB;AACpBvB,YAAAA,CAAC,CAACzC,iBAAF,GAAsB,IAAtB;AACH;;AACD,cAAIyC,CAAC,CAAChF,YAAF,IAAkBnB,YAAY,CAAC6G,IAAnC,EACIV,CAAC,CAAC1E,YAAF,GAAiB,EAAjB;;AAEJ,kBAAQ0E,CAAC,CAACxE,MAAV;AACI,iBAAK/C,MAAM,CAACgM,IAAP,CAAYC,UAAjB;AACI1E,cAAAA,CAAC,CAAC/C,WAAF,GAAgB,CAAhB;AACA+C,cAAAA,CAAC,CAACvC,SAAF,GAAc,KAAd;AACA;;AACJ,iBAAKhF,MAAM,CAACgM,IAAP,CAAYK,QAAjB;AACI9E,cAAAA,CAAC,CAAC/C,WAAF,GAAgB,CAAhB;AACA+C,cAAAA,CAAC,CAACvC,SAAF,GAAc,IAAd;AACA;;AACJ,iBAAKhF,MAAM,CAACgM,IAAP,CAAYS,IAAjB;AACI,sBAAQlF,CAAC,CAACrE,UAAV;AACI,qBAAKlD,MAAM,CAAC0M,aAAP,CAAqBT,UAA1B;AACI;AACA,sBAAIiD,KAAa,GAAG3H,CAAC,CAAClE,UAAF,CAAcyL,KAAd,GAAsBvH,CAAC,CAAClD,QAAxB,GAAmCkD,CAAC,CAACpD,SAAzD;AACAoD,kBAAAA,CAAC,CAAC/C,WAAF,GAAgB8I,IAAI,CAAC6B,KAAL,CAAW,CAACD,KAAK,GAAG3H,CAAC,CAACjD,UAAX,KAA0BiD,CAAC,CAACxC,SAAF,CAAY+J,KAAZ,GAAoBvH,CAAC,CAACjD,UAAhD,CAAX,CAAhB;AACAiD,kBAAAA,CAAC,CAACvC,SAAF,GAAc,IAAd;AACA;;AACJ,qBAAKhF,MAAM,CAAC0M,aAAP,CAAqBL,QAA1B;AACI;AACA,sBAAI+C,KAAa,GAAG7H,CAAC,CAAClE,UAAF,CAAc0L,MAAd,GAAuBxH,CAAC,CAACrD,OAAzB,GAAmCqD,CAAC,CAACnD,UAAzD;AACAmD,kBAAAA,CAAC,CAAC/C,WAAF,GAAgB8I,IAAI,CAAC6B,KAAL,CAAW,CAACC,KAAK,GAAG7H,CAAC,CAAChD,QAAX,KAAwBgD,CAAC,CAACxC,SAAF,CAAYgK,MAAZ,GAAqBxH,CAAC,CAAChD,QAA/C,CAAX,CAAhB;AACAgD,kBAAAA,CAAC,CAACvC,SAAF,GAAc,KAAd;AACA;AAZR;;AAcA;AAxBR;AA0BH;AACD;AACJ;AACA;AACA;AACA;;;AACIwD,QAAAA,WAAW,CAAC6G,QAAD,EAA2B;AAAA,cAA1BA,QAA0B;AAA1BA,YAAAA,QAA0B,GAAN,IAAM;AAAA;;AAClC,cAAI,CAAC,KAAKvL,OAAV,EAAmB;AACf,gBAAIuL,QAAJ,EACI5G,OAAO,CAACC,KAAR,CAAc,oCAAd;AACJ,mBAAO,KAAP;AACH;;AACD,iBAAO,IAAP;AACH,SA3rBuC,CA4rBxC;;;AACAC,QAAAA,cAAc,GAAG;AACb,cAAIpB,CAAM,GAAG,IAAb;AACA,cAAI+H,MAAc,GAAG,CAArB;;AAEA,kBAAQ/H,CAAC,CAACxE,MAAV;AACI,iBAAK/C,MAAM,CAACgM,IAAP,CAAYC,UAAjB;AAA6B;AACzB,oBAAI1E,CAAC,CAACtC,WAAN,EAAmB;AACf,sBAAIsK,KAAU,GAAGhI,CAAC,CAACiI,aAAF,CAAgBC,SAAhB,CAAjB;;AACAH,kBAAAA,MAAM,GAAG/H,CAAC,CAAClD,QAAF,GAAakL,KAAK,CAACvN,GAAnB,GAA0BuF,CAAC,CAACxC,SAAF,CAAa+J,KAAb,IAAsBvH,CAAC,CAACL,SAAF,GAAcqI,KAAK,CAACG,KAA1C,CAA1B,GAA+EnI,CAAC,CAACjD,UAAF,IAAgBiD,CAAC,CAACL,SAAF,GAAc,CAA9B,CAA/E,GAAmHK,CAAC,CAACpD,SAA9H;AACH,iBAHD,MAGO;AACHmL,kBAAAA,MAAM,GAAG/H,CAAC,CAAClD,QAAF,GAAckD,CAAC,CAACxC,SAAF,CAAa+J,KAAb,GAAqBvH,CAAC,CAACL,SAArC,GAAmDK,CAAC,CAACjD,UAAF,IAAgBiD,CAAC,CAACL,SAAF,GAAc,CAA9B,CAAnD,GAAuFK,CAAC,CAACpD,SAAlG;AACH;;AACD;AACH;;AACD,iBAAKnE,MAAM,CAACgM,IAAP,CAAYK,QAAjB;AAA2B;AACvB,oBAAI9E,CAAC,CAACtC,WAAN,EAAmB;AACf,sBAAIsK,MAAU,GAAGhI,CAAC,CAACiI,aAAF,CAAgBC,SAAhB,CAAjB;;AACAH,kBAAAA,MAAM,GAAG/H,CAAC,CAACrD,OAAF,GAAYqL,MAAK,CAACvN,GAAlB,GAAyBuF,CAAC,CAACxC,SAAF,CAAagK,MAAb,IAAuBxH,CAAC,CAACL,SAAF,GAAcqI,MAAK,CAACG,KAA3C,CAAzB,GAA+EnI,CAAC,CAAChD,QAAF,IAAcgD,CAAC,CAACL,SAAF,GAAc,CAA5B,CAA/E,GAAiHK,CAAC,CAACnD,UAA5H;AACH,iBAHD,MAGO;AACHkL,kBAAAA,MAAM,GAAG/H,CAAC,CAACrD,OAAF,GAAaqD,CAAC,CAACxC,SAAF,CAAagK,MAAb,GAAsBxH,CAAC,CAACL,SAArC,GAAmDK,CAAC,CAAChD,QAAF,IAAcgD,CAAC,CAACL,SAAF,GAAc,CAA5B,CAAnD,GAAqFK,CAAC,CAACnD,UAAhG;AACH;;AACD;AACH;;AACD,iBAAKpE,MAAM,CAACgM,IAAP,CAAYS,IAAjB;AAAuB;AACnB;AACA,oBAAIlF,CAAC,CAAClF,UAAN,EACIkF,CAAC,CAAClF,UAAF,GAAe,KAAf;;AACJ,wBAAQkF,CAAC,CAACrE,UAAV;AACI,uBAAKlD,MAAM,CAAC0M,aAAP,CAAqBT,UAA1B;AACI,wBAAI0D,OAAe,GAAGrC,IAAI,CAACsC,IAAL,CAAUrI,CAAC,CAACL,SAAF,GAAcK,CAAC,CAAC/C,WAA1B,CAAtB;AACA8K,oBAAAA,MAAM,GAAG/H,CAAC,CAACrD,OAAF,GAAaqD,CAAC,CAACxC,SAAF,CAAagK,MAAb,GAAsBY,OAAnC,GAA+CpI,CAAC,CAAChD,QAAF,IAAcoL,OAAO,GAAG,CAAxB,CAA/C,GAA6EpI,CAAC,CAACnD,UAAxF;AACA;;AACJ,uBAAKpE,MAAM,CAAC0M,aAAP,CAAqBL,QAA1B;AACI,wBAAIwD,MAAc,GAAGvC,IAAI,CAACsC,IAAL,CAAUrI,CAAC,CAACL,SAAF,GAAcK,CAAC,CAAC/C,WAA1B,CAArB;AACA8K,oBAAAA,MAAM,GAAG/H,CAAC,CAAClD,QAAF,GAAckD,CAAC,CAACxC,SAAF,CAAa+J,KAAb,GAAqBe,MAAnC,GAA8CtI,CAAC,CAACjD,UAAF,IAAgBuL,MAAM,GAAG,CAAzB,CAA9C,GAA6EtI,CAAC,CAACpD,SAAxF;AACA;AARR;;AAUA;AACH;AAlCL;;AAqCA,cAAI0E,MAAc,GAAGtB,CAAC,CAACnE,OAAF,CAAWwE,YAAX,CAAwB5H,MAAxB,CAArB;AACA,cAAI6I,MAAJ,EACIA,MAAM,CAACC,OAAP,GAAiB,KAAjB;AAEJvB,UAAAA,CAAC,CAACV,YAAF,GAAiByI,MAAjB;AACA/H,UAAAA,CAAC,CAACT,kBAAF,GAAuBS,CAAC,CAACV,YAAF,IAAkBU,CAAC,CAACvC,SAAF,GAAeuC,CAAC,CAACrD,OAAF,GAAYqD,CAAC,CAACnD,UAA7B,GAA4CmD,CAAC,CAAClD,QAAF,GAAakD,CAAC,CAACpD,SAA7E,CAAvB;;AAEA,cAAIoD,CAAC,CAACpF,MAAN,EAAc;AACV,gBAAI2N,SAAiB,GAAIvI,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACP,WAAF,CAAe+H,MAA7B,GAAsCxH,CAAC,CAACP,WAAF,CAAe8H,KAA9E;AAEAvH,YAAAA,CAAC,CAAC3D,WAAF,GAAgB,CAAhB;AACAkM,YAAAA,SAAS,IAAIvI,CAAC,CAAC3D,WAAf;AACA2D,YAAAA,CAAC,CAAC5D,UAAF,GAAe2J,IAAI,CAACsC,IAAL,CAAUE,SAAS,GAAGvI,CAAC,CAACT,kBAAxB,IAA8C,CAA7D;AACA,gBAAIiJ,OAAe,GAAGxI,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAAChD,QAAhB,GAA2BgD,CAAC,CAACjD,UAAnD;AACAiD,YAAAA,CAAC,CAAC1D,WAAF,GAAgB0D,CAAC,CAAC3D,WAAF,GAAgB2D,CAAC,CAACT,kBAAlB,GAAuCiJ,OAAvD;AACAxI,YAAAA,CAAC,CAACyI,kBAAF,GAAuBzI,CAAC,CAACV,YAAF,GAAkBU,CAAC,CAACT,kBAAF,IAAwBS,CAAC,CAAC5D,UAAF,GAAe,CAAvC,CAAlB,GAAgEoM,OAAO,IAAIxI,CAAC,CAAC5D,UAAF,GAAe,CAAnB,CAA9F;AACA4D,YAAAA,CAAC,CAAC0I,wBAAF,GAA6B1I,CAAC,CAACT,kBAAF,GAAuBS,CAAC,CAAC5D,UAAtD;AACA4D,YAAAA,CAAC,CAAC0I,wBAAF,IAA8BF,OAAO,IAAIxI,CAAC,CAAC5D,UAAF,GAAe,CAAnB,CAArC,CAVU,CAWV;AACH;;AAED4D,UAAAA,CAAC,CAACX,KAAF,GAAU,CAACW,CAAC,CAACpF,MAAH,IAAaoF,CAAC,CAACV,YAAF,IAAkBU,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACP,WAAF,CAAe+H,MAA7B,GAAsCxH,CAAC,CAACP,WAAF,CAAe8H,KAAvE,CAAvB;AACA,cAAIoB,WAAmB,GAAI,CAAC,CAAC3I,CAAC,CAACX,KAAH,IAAY,CAACW,CAAC,CAAClF,UAAhB,KAA+BkF,CAAC,CAACjF,SAAlC,GAA+C,CAA/C,GAAmD,EAA7E;AAEA,cAAI6N,QAAgB,GAAG5I,CAAC,CAACX,KAAF,GAAW,CAACW,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACP,WAAF,CAAe+H,MAA7B,GAAsCxH,CAAC,CAACP,WAAF,CAAe8H,KAAtD,IAA+DoB,WAA1E,GAA0F3I,CAAC,CAACpF,MAAF,GAAWoF,CAAC,CAACyI,kBAAb,GAAkCzI,CAAC,CAACV,YAArJ;AACA,cAAIsJ,QAAQ,GAAG,CAAf,EACIA,QAAQ,GAAG,CAAX;;AAEJ,cAAI5I,CAAC,CAACvC,SAAN,EAAiB;AACbuC,YAAAA,CAAC,CAAClE,UAAF,CAAc0L,MAAd,GAAuBoB,QAAvB;AACH,WAFD,MAEO;AACH5I,YAAAA,CAAC,CAAClE,UAAF,CAAcyL,KAAd,GAAsBqB,QAAtB;AACH,WAzEY,CA2Eb;;AACH,SAzwBuC,CA2wBxC;;;AACAhJ,QAAAA,YAAY,CAACiJ,EAAD,EAAwB;AAChC,cAAI,KAAKlL,UAAL,IAAmB,IAAvB,EACI,KAAKA,UAAL,GAAkB,KAAKmC,WAAvB;;AACJ,cAAI,CAAC,KAAKvE,YAAN,IAAuBsN,EAAE,IAAIA,EAAE,CAAC9O,IAAH,IAAW,cAAxC,IAA2D,KAAK4D,UAAL,GAAkB,CAAjF,EAAoF;AAChF,iBAAKA,UAAL;AACA;AACH,WAHD,MAII,KAAKA,UAAL,GAAkB,KAAKmC,WAAvB;;AAEJ,cAAI,KAAKlC,aAAT,EACI,OAV4B,CAYhC;;AACA,cAAI,KAAKhD,MAAT,EAAiB;AACb,gBAAIkO,SAAc,GAAG,KAAKjN,OAAL,CAAckN,WAAd,EAArB;AACAD,YAAAA,SAAS,GAAG,KAAKrL,SAAL,GAAiBqL,SAAS,CAACE,CAA3B,GAA+BF,SAAS,CAACG,CAArD;AAEA,gBAAIC,MAAM,GAAG,KAAK3J,kBAAL,IAA2B,KAAK9B,SAAL,GAAiB,KAAKT,QAAtB,GAAiC,KAAKD,UAAjE,CAAb;AACA,gBAAIsJ,GAAQ,GAAG,KAAK5I,SAAL,GAAiB,IAAIxE,IAAJ,CAAS,CAAT,EAAYiQ,MAAZ,EAAoB,CAApB,CAAjB,GAA0C,IAAIjQ,IAAJ,CAASiQ,MAAT,EAAiB,CAAjB,EAAoB,CAApB,CAAzD;AAEA,gBAAIC,UAAU,GAAG,KAAKtN,OAAL,CAAckN,WAAd,EAAjB;;AAEA,oBAAQ,KAAKnN,cAAb;AACI,mBAAK,CAAL;AAAO;AACH,oBAAIkN,SAAS,GAAG,CAAC,KAAKzM,WAAtB,EAAmC;AAC/B8M,kBAAAA,UAAU,CAACvC,GAAX,CAAe,CAAC,KAAKtK,WAArB,EAAkC6M,UAAU,CAACH,CAA7C,EAAgDG,UAAU,CAACC,CAA3D;AACA,uBAAKvN,OAAL,CAAcwN,WAAd,CAA0BF,UAA1B;;AACA,sBAAI,KAAK3M,WAAL,CAAkB8M,eAAlB,EAAJ,EAAyC;AACrC,yBAAK9M,WAAL,CAAkB,0BAAlB,IAAgD,KAAKA,WAAL,CAAkB,0BAAlB,EAA8CmK,QAA9C,CAAuDN,GAAvD,CAAhD;AACH,mBAL8B,CAM/B;AACA;AACA;;AACH,iBATD,MASO,IAAIyC,SAAS,GAAG,CAAC,KAAKxM,WAAtB,EAAmC;AACtC6M,kBAAAA,UAAU,CAACvC,GAAX,CAAe,CAAC,KAAKvK,WAArB,EAAkC8M,UAAU,CAACH,CAA7C,EAAgDG,UAAU,CAACC,CAA3D;AACA,uBAAKvN,OAAL,CAAcwN,WAAd,CAA0BF,UAA1B;;AACA,sBAAI,KAAK3M,WAAL,CAAkB8M,eAAlB,EAAJ,EAAyC;AACrC,yBAAK9M,WAAL,CAAkB,0BAAlB,IAAgD,KAAKA,WAAL,CAAkB,0BAAlB,EAA8C6J,GAA9C,CAAkDA,GAAlD,CAAhD;AACH,mBALqC,CAMtC;AACA;AACA;;AACH;;AACD;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAIyC,SAAS,GAAG,KAAKzM,WAArB,EAAkC;AAC9B8M,kBAAAA,UAAU,CAACvC,GAAX,CAAe,KAAKtK,WAApB,EAAiC6M,UAAU,CAACH,CAA5C,EAA+CG,UAAU,CAACC,CAA1D;AACA,uBAAKvN,OAAL,CAAcwN,WAAd,CAA0BF,UAA1B;;AACA,sBAAI,KAAK3M,WAAL,CAAkB8M,eAAlB,EAAJ,EAAyC;AACrC,yBAAK9M,WAAL,CAAkB,0BAAlB,IAAgD,KAAKA,WAAL,CAAkB,0BAAlB,EAA8C6J,GAA9C,CAAkDA,GAAlD,CAAhD;AACH;AACJ,iBAND,MAMO,IAAIyC,SAAS,GAAG,KAAKxM,WAArB,EAAkC;AACrC6M,kBAAAA,UAAU,CAACvC,GAAX,CAAe,KAAKvK,WAApB,EAAiC8M,UAAU,CAACH,CAA5C,EAA+CG,UAAU,CAACC,CAA1D;AACA,uBAAKvN,OAAL,CAAcwN,WAAd,CAA0BF,UAA1B;;AACA,sBAAI,KAAK3M,WAAL,CAAkB8M,eAAlB,EAAJ,EAAyC;AACrC,yBAAK9M,WAAL,CAAkB,0BAAlB,IAAgD,KAAKA,WAAL,CAAkB,0BAAlB,EAA8CmK,QAA9C,CAAuDN,GAAvD,CAAhD;AACH;AACJ;;AACD;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAIyC,SAAS,GAAG,KAAKzM,WAArB,EAAkC;AAC9B8M,kBAAAA,UAAU,CAACvC,GAAX,CAAeuC,UAAU,CAACF,CAA1B,EAA6B,KAAK3M,WAAlC,EAA+C6M,UAAU,CAACC,CAA1D;AACA,uBAAKvN,OAAL,CAAcwN,WAAd,CAA0BF,UAA1B;;AACA,sBAAI,KAAK3M,WAAL,CAAkB8M,eAAlB,EAAJ,EAAyC;AACrC,yBAAK9M,WAAL,CAAkB,0BAAlB,IAAgD,KAAKA,WAAL,CAAkB,0BAAlB,EAA8C6J,GAA9C,CAAkDA,GAAlD,CAAhD;AACH;AACJ,iBAND,MAMO,IAAIyC,SAAS,GAAG,KAAKxM,WAArB,EAAkC;AACrC6M,kBAAAA,UAAU,CAACvC,GAAX,CAAeuC,UAAU,CAACF,CAA1B,EAA6B,KAAK5M,WAAlC,EAA+C8M,UAAU,CAACC,CAA1D;AACA,uBAAKvN,OAAL,CAAcwN,WAAd,CAA0BF,UAA1B;;AACA,sBAAI,KAAK3M,WAAL,CAAkB8M,eAAlB,EAAJ,EAAyC;AACrC,yBAAK9M,WAAL,CAAkB,0BAAlB,IAAgD,KAAKA,WAAL,CAAkB,0BAAlB,EAA8CmK,QAA9C,CAAuDN,GAAvD,CAAhD;AACH;AACJ;;AACD;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAIyC,SAAS,GAAG,CAAC,KAAKzM,WAAtB,EAAmC;AAC/B8M,kBAAAA,UAAU,CAACvC,GAAX,CAAeuC,UAAU,CAACF,CAA1B,EAA6B,CAAC,KAAK3M,WAAnC,EAAgD6M,UAAU,CAACC,CAA3D;AACA,uBAAKvN,OAAL,CAAcwN,WAAd,CAA0BF,UAA1B;;AACA,sBAAI,KAAK3M,WAAL,CAAkB8M,eAAlB,EAAJ,EAAyC;AACrC,yBAAK9M,WAAL,CAAkB,0BAAlB,IAAgD,KAAKA,WAAL,CAAkB,0BAAlB,EAA8CmK,QAA9C,CAAuDN,GAAvD,CAAhD;AACH;AACJ,iBAND,MAMO,IAAIyC,SAAS,GAAG,CAAC,KAAKxM,WAAtB,EAAmC;AACtC6M,kBAAAA,UAAU,CAACvC,GAAX,CAAeuC,UAAU,CAACF,CAA1B,EAA6B,CAAC,KAAK5M,WAAnC,EAAgD8M,UAAU,CAACC,CAA3D;AACA,uBAAKvN,OAAL,CAAcwN,WAAd,CAA0BF,UAA1B;;AACA,sBAAI,KAAK3M,WAAL,CAAkB8M,eAAlB,EAAJ,EAAyC;AACrC,yBAAK9M,WAAL,CAAkB,0BAAlB,IAAgD,KAAKA,WAAL,CAAkB,0BAAlB,EAA8C6J,GAA9C,CAAkDA,GAAlD,CAAhD;AACH;AACJ;;AACD;AAlER;AAoEH;;AAED,eAAKkD,YAAL;;AAEA,cAAIC,IAAY,GAAG,CAAnB;AAAA,cAAsBC,MAAc,GAAG,CAAvC;AAAA,cAA0CC,OAAe,GAAG,CAA5D;AAAA,cAA+DC,KAAa,GAAG,CAA/E;;AACA,cAAI,KAAKlM,SAAT,EAAoB;AAChB+L,YAAAA,IAAI,GAAG,KAAKvL,OAAZ;AACAyL,YAAAA,OAAO,GAAG,KAAKvL,UAAf;AACH,WAHD,MAGO;AACHsL,YAAAA,MAAM,GAAG,KAAKvL,SAAd;AACAyL,YAAAA,KAAK,GAAG,KAAKvL,QAAb;AACH;;AAED,cAAI,KAAKsB,QAAT,EAAmB;AACf,iBAAKvC,WAAL,GAAmB,EAAnB;AACA,gBAAIyM,OAAJ;AAEA,gBAAIC,KAAa,GAAG,CAApB;AACA,gBAAIC,KAAa,GAAG,KAAKnK,SAAL,GAAiB,CAArC;;AAEA,gBAAI,KAAKjC,WAAT,EAAsB;AAClB,kBAAIqM,QAAiB,GAAG,KAAxB,CADkB,CAElB;;AACA,qBAAOF,KAAK,IAAIC,KAAT,IAAkB,CAACC,QAA1B,EAAoCF,KAAK,EAAzC,EAA6C;AACzCD,gBAAAA,OAAO,GAAG,KAAKI,YAAL,CAAkBH,KAAlB,CAAV;;AACA,wBAAQ,KAAKrO,MAAb;AACI,uBAAK/C,MAAM,CAACgM,IAAP,CAAYC,UAAjB;AACI,wBAAIkF,OAAO,CAACK,KAAR,IAAiBN,KAAjB,IAA0BC,OAAO,CAACM,IAAR,IAAgBT,MAA9C,EAAsD;AAClD,2BAAKtM,WAAL,CAAiB2D,IAAjB,CAAsB8I,OAAtB;AACH,qBAFD,MAEO,IAAIC,KAAK,IAAI,CAAT,IAAc,KAAK1M,WAAL,CAAiBgN,MAAjB,GAA0B,CAA5C,EAA+C;AAClDJ,sBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,uBAAKtR,MAAM,CAACgM,IAAP,CAAYK,QAAjB;AACI,wBAAI8E,OAAO,CAACQ,MAAR,IAAkBZ,IAAlB,IAA0BI,OAAO,CAACS,GAAR,IAAeX,OAA7C,EAAsD;AAClD,2BAAKvM,WAAL,CAAiB2D,IAAjB,CAAsB8I,OAAtB;AACH,qBAFD,MAEO,IAAIC,KAAK,IAAI,CAAT,IAAc,KAAK1M,WAAL,CAAiBgN,MAAjB,GAA0B,CAA5C,EAA+C;AAClDJ,sBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,uBAAKtR,MAAM,CAACgM,IAAP,CAAYS,IAAjB;AACI,4BAAQ,KAAKvJ,UAAb;AACI,2BAAKlD,MAAM,CAAC0M,aAAP,CAAqBT,UAA1B;AACI,4BAAIkF,OAAO,CAACQ,MAAR,IAAkBZ,IAAlB,IAA0BI,OAAO,CAACS,GAAR,IAAeX,OAA7C,EAAsD;AAClD,+BAAKvM,WAAL,CAAiB2D,IAAjB,CAAsB8I,OAAtB;AACH,yBAFD,MAEO,IAAIC,KAAK,IAAI,CAAT,IAAc,KAAK1M,WAAL,CAAiBgN,MAAjB,GAA0B,CAA5C,EAA+C;AAClDJ,0BAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,2BAAKtR,MAAM,CAAC0M,aAAP,CAAqBL,QAA1B;AACI,4BAAI8E,OAAO,CAACK,KAAR,IAAiBN,KAAjB,IAA0BC,OAAO,CAACM,IAAR,IAAgBT,MAA9C,EAAsD;AAClD,+BAAKtM,WAAL,CAAiB2D,IAAjB,CAAsB8I,OAAtB;AACH,yBAFD,MAEO,IAAIC,KAAK,IAAI,CAAT,IAAc,KAAK1M,WAAL,CAAiBgN,MAAjB,GAA0B,CAA5C,EAA+C;AAClDJ,0BAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;AAdR;;AAgBA;AAhCR;AAkCH;AACJ,aAxCD,MAwCO;AACH,kBAAIO,EAAU,GAAG,KAAK9M,SAAL,CAAgB+J,KAAhB,GAAwB,KAAKxK,UAA9C;AACA,kBAAIwN,EAAU,GAAG,KAAK/M,SAAL,CAAgBgK,MAAhB,GAAyB,KAAKxK,QAA/C;;AACA,sBAAQ,KAAKpB,cAAb;AACI,qBAAK,CAAL;AAAO;AACHiO,kBAAAA,KAAK,GAAG,CAACF,KAAK,GAAG,KAAK7M,QAAd,IAA0BwN,EAAlC;AACAR,kBAAAA,KAAK,GAAG,CAACL,MAAM,GAAG,KAAK3M,QAAf,IAA2BwN,EAAnC;AACA;;AACJ,qBAAK,CAAL;AAAO;AACHT,kBAAAA,KAAK,GAAG,CAAC,CAACJ,MAAD,GAAU,KAAK7M,SAAhB,IAA6B0N,EAArC;AACAR,kBAAAA,KAAK,GAAG,CAAC,CAACH,KAAD,GAAS,KAAK/M,SAAf,IAA4B0N,EAApC;AACA;;AACJ,qBAAK,CAAL;AAAO;AACHT,kBAAAA,KAAK,GAAG,CAAC,CAACL,IAAD,GAAQ,KAAK7M,OAAd,IAAyB4N,EAAjC;AACAT,kBAAAA,KAAK,GAAG,CAAC,CAACJ,OAAD,GAAW,KAAK/M,OAAjB,IAA4B4N,EAApC;AACA;;AACJ,qBAAK,CAAL;AAAO;AACHV,kBAAAA,KAAK,GAAG,CAACH,OAAO,GAAG,KAAK7M,UAAhB,IAA8B0N,EAAtC;AACAT,kBAAAA,KAAK,GAAG,CAACN,IAAI,GAAG,KAAK3M,UAAb,IAA2B0N,EAAnC;AACA;AAhBR;;AAkBAV,cAAAA,KAAK,GAAG9D,IAAI,CAAC6B,KAAL,CAAWiC,KAAX,IAAoB,KAAK5M,WAAjC;AACA6M,cAAAA,KAAK,GAAG/D,IAAI,CAACsC,IAAL,CAAUyB,KAAV,IAAmB,KAAK7M,WAAhC;AACA6M,cAAAA,KAAK;AACL,kBAAID,KAAK,GAAG,CAAZ,EACIA,KAAK,GAAG,CAAR;AACJ,kBAAIC,KAAK,IAAI,KAAKnK,SAAlB,EACImK,KAAK,GAAG,KAAKnK,SAAL,GAAiB,CAAzB;;AACJ,qBAAOkK,KAAK,IAAIC,KAAhB,EAAuBD,KAAK,EAA5B,EAAgC;AAC5B,qBAAK1M,WAAL,CAAiB2D,IAAjB,CAAsB,KAAKkJ,YAAL,CAAkBH,KAAlB,CAAtB;AACH;AACJ;;AACD,iBAAKrI,iBAAL;;AACA,gBAAI,KAAKrE,WAAL,CAAiBgN,MAAjB,IAA2B,CAA3B,IAAgC,CAAC,KAAKxK,SAA1C,EAAqD;AAAE;AACnD,mBAAKzC,gBAAL,GAAwB,EAAxB;AACA;AACH;;AACD,iBAAKnB,WAAL,GAAmB,KAAKoB,WAAL,CAAiB,CAAjB,EAAoBqN,EAAvC;AACA,iBAAKxO,cAAL,GAAsB,KAAKmB,WAAL,CAAiBgN,MAAvC;AAEA,gBAAI1I,GAAW,GAAG,KAAKvE,gBAAL,CAAsBiN,MAAxC;AAEA,gBAAIM,cAAuB,GAAG,KAAKzO,cAAL,IAAuByF,GAArD;;AACA,gBAAIgJ,cAAJ,EAAoB;AAChB;AACA,kBAAI,KAAKpJ,qBAAL,GAA6B,CAAjC,EAAoC;AAChC,qBAAKnE,gBAAL,CAAsBwN,IAAtB,CAA2B,CAACC,CAAD,EAAIC,CAAJ,KAAU;AAAE,yBAAOD,CAAC,GAAGC,CAAX;AAAc,iBAArD;AACH,eAJe,CAKhB;;;AACAH,cAAAA,cAAc,GAAG,KAAK1O,WAAL,IAAoB,KAAKmB,gBAAL,CAAsB,CAAtB,CAApB,IAAgD,KAAKC,WAAL,CAAiB,KAAKnB,cAAL,GAAsB,CAAvC,EAA0CwO,EAA1C,IAAgD,KAAKtN,gBAAL,CAAsBuE,GAAG,GAAG,CAA5B,CAAjH;AACH;;AAED,gBAAI,KAAKlG,YAAL,IAAqBkP,cAAzB,EAAyC;AAAK;AAC1C,kBAAI,KAAKpJ,qBAAL,GAA6B,CAAjC,EAAoC;AAChC;AACA;AACA;AACA,oBAAI,KAAK1B,SAAL,GAAiB,CAArB,EAAwB;AACpB,sBAAI,CAAC,KAAK1D,WAAV,EAAuB;AACnB,yBAAKoC,gBAAL,GAAwB,IAAxB;AACH,mBAFD,MAEO;AACH,yBAAKnC,cAAL,GAAsB,CAAtB;AACH;;AACD,uBAAKD,WAAL,GAAmB,KAAnB;AACH,iBAPD,MAOO;AACH,uBAAKC,cAAL,GAAsB,CAAtB;AACA,uBAAKD,WAAL,GAAmB,IAAnB;AACH,iBAd+B,CAehC;;AACH,eAhBD,MAgBO;AACH;AACA,qBAAKiB,gBAAL,GAAwB,EAAxB,CAFG,CAGH;;AACA,qBAAK,IAAI2N,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAK7O,cAAzB,EAAyC6O,CAAC,EAA1C,EAA8C;AAC1C,uBAAKC,mBAAL,CAAyB,KAAK3N,WAAL,CAAiB0N,CAAjB,CAAzB;AACH;;AACD,qBAAKtP,YAAL,GAAoB,KAApB;AACH;AACJ;;AACD,iBAAKwP,gBAAL;AACH;AACJ,SAn/BuC,CAo/BxC;;;AACAxB,QAAAA,YAAY,GAAG;AACX,cAAIT,SAAc,GAAG,KAAKjN,OAAL,CAAckN,WAAd,EAArB;;AACA,kBAAQ,KAAKnN,cAAb;AACI,iBAAK,CAAL;AAAO;AACH,mBAAK6C,WAAL,GAAmBqK,SAAS,CAACG,CAAV,GAAc,CAAd,GAAkBH,SAAS,CAACG,CAA5B,GAAgC,CAAnD;AACA,mBAAK7K,QAAL,GAAgB,CAAC0K,SAAS,CAACG,CAAV,GAAc,CAAd,GAAkB,CAACH,SAAS,CAACG,CAA7B,GAAiC,CAAlC,IAAuC,KAAKxK,WAA5D;AAEA,mBAAKP,SAAL,GAAiB,KAAKE,QAAL,GAAgB,KAAKqB,WAAL,CAAkB8H,KAAnD;AACA,mBAAKhJ,YAAL,GAAoB,KAAKL,SAAL,GAAiB,KAAKpC,UAAL,CAAiByL,KAAlC,GAA0CxB,IAAI,CAACQ,GAAL,CAAS,KAAKrI,SAAL,GAAiB,KAAKpC,UAAL,CAAiByL,KAA3C,CAA1C,GAA8F,CAAlH;AACA,mBAAKrJ,SAAL,IAAkB,KAAKK,YAAvB,CANJ,CAOI;;AACA;;AACJ,iBAAK,CAAL;AAAO;AACH,mBAAKA,YAAL,GAAoBuK,SAAS,CAACG,CAAV,GAAc,CAAd,GAAkB,CAACH,SAAS,CAACG,CAA7B,GAAiC,CAArD;AACA,mBAAK/K,SAAL,GAAiB,CAAC4K,SAAS,CAACG,CAAV,GAAc,CAAd,GAAkB,CAACH,SAAS,CAACG,CAA7B,GAAiC,CAAlC,IAAuC,KAAK1K,YAA7D;AACA,mBAAKH,QAAL,GAAgB,KAAKF,SAAL,GAAiB,KAAKuB,WAAL,CAAkB8H,KAAnD;AACA,mBAAK9I,WAAL,GAAmB,KAAKL,QAAL,GAAgB,CAAC,KAAKtC,UAAL,CAAiByL,KAAlC,GAA0CxB,IAAI,CAACQ,GAAL,CAAS,KAAKnI,QAAL,GAAgB,KAAKtC,UAAL,CAAiByL,KAA1C,CAA1C,GAA6F,CAAhH;AACA,mBAAKnJ,QAAL,IAAiB,KAAKK,WAAtB,CALJ,CAMI;;AACA;;AACJ,iBAAK,CAAL;AAAO;AACH,mBAAKH,UAAL,GAAkBwK,SAAS,CAACE,CAAV,GAAc,CAAd,GAAkBjD,IAAI,CAACQ,GAAL,CAASuC,SAAS,CAACE,CAAnB,CAAlB,GAA0C,CAA5D;AACA,mBAAK/K,OAAL,GAAe,CAAC6K,SAAS,CAACE,CAAV,GAAc,CAAd,GAAkB,CAACF,SAAS,CAACE,CAA7B,GAAiC,CAAlC,IAAuC,KAAK1K,UAA3D;AACA,mBAAKH,UAAL,GAAkB,KAAKF,OAAL,GAAe,KAAKwB,WAAL,CAAkB+H,MAAnD;AACA,mBAAKhJ,aAAL,GAAqB,KAAKL,UAAL,GAAkB,CAAC,KAAKrC,UAAL,CAAiB0L,MAApC,GAA6CzB,IAAI,CAACQ,GAAL,CAAS,KAAKpI,UAAL,GAAkB,KAAKrC,UAAL,CAAiB0L,MAA5C,CAA7C,GAAmG,CAAxH;AACA,mBAAKrJ,UAAL,IAAmB,KAAKK,aAAxB,CALJ,CAMI;;AACA;;AACJ,iBAAK,CAAL;AAAO;AACH,mBAAKA,aAAL,GAAqBsK,SAAS,CAACE,CAAV,GAAc,CAAd,GAAkBjD,IAAI,CAACQ,GAAL,CAASuC,SAAS,CAACE,CAAnB,CAAlB,GAA0C,CAA/D;AACA,mBAAK7K,UAAL,GAAkB,CAAC2K,SAAS,CAACE,CAAV,GAAc,CAAd,GAAkB,CAACF,SAAS,CAACE,CAA7B,GAAiC,CAAlC,IAAuC,KAAKxK,aAA9D;AACA,mBAAKP,OAAL,GAAe,KAAKE,UAAL,GAAkB,KAAKsB,WAAL,CAAkB+H,MAAnD;AACA,mBAAKlJ,UAAL,GAAkB,KAAKL,OAAL,GAAe,KAAKnC,UAAL,CAAiB0L,MAAhC,GAAyCzB,IAAI,CAACQ,GAAL,CAAS,KAAKtI,OAAL,GAAe,KAAKnC,UAAL,CAAiB0L,MAAzC,CAAzC,GAA4F,CAA9G;AACA,mBAAKvJ,OAAL,IAAgB,KAAKK,UAArB,CALJ,CAMI;;AACA;AAjCR;AAmCH,SA1hCuC,CA2hCxC;;;AACA0L,QAAAA,YAAY,CAACQ,EAAD,EAAa;AACrB,cAAIjD,KAAJ,EAAmBC,MAAnB,EAAmC6C,GAAnC,EAAgDD,MAAhD,EAAgEF,IAAhE,EAA8ED,KAA9E,EAA6Fe,KAA7F,EAA4GC,KAA5G;AACA1D,UAAAA,KAAK,GAAGC,MAAM,GAAG6C,GAAG,GAAGD,MAAM,GAAGF,IAAI,GAAGD,KAAK,GAAGe,KAAK,GAAGC,KAAK,GAAG,CAA/D;;AACA,kBAAQ,KAAKzP,MAAb;AACI,iBAAK/C,MAAM,CAACgM,IAAP,CAAYC,UAAjB;AACI,sBAAQ,KAAKjJ,cAAb;AACI,qBAAKhD,MAAM,CAACkM,mBAAP,CAA2BC,aAAhC;AAA+C;AAC3C,wBAAI,KAAKlH,WAAT,EAAsB;AAClB,0BAAIsK,KAAU,GAAG,KAAKC,aAAL,CAAmBuC,EAAnB,CAAjB;;AACAN,sBAAAA,IAAI,GAAG,KAAKpN,QAAL,GAAiB,CAAC,KAAKU,SAAL,CAAgB+J,KAAhB,GAAwB,KAAKxK,UAA9B,KAA6CyN,EAAE,GAAGxC,KAAK,CAACG,KAAxD,CAAjB,IAAoFH,KAAK,CAACvN,GAAN,GAAa,KAAKsC,UAAL,GAAkBiL,KAAK,CAACG,KAAzH,CAAP;AACA,0BAAI+C,EAAU,GAAG,KAAKxN,WAAL,CAAiB8M,EAAjB,CAAjB;AACAjD,sBAAAA,KAAK,GAAI2D,EAAE,GAAG,CAAL,GAASA,EAAT,GAAc,KAAK1N,SAAL,CAAgB+J,KAAvC;AACH,qBALD,MAKO;AACH2C,sBAAAA,IAAI,GAAG,KAAKpN,QAAL,GAAiB,CAAC,KAAKU,SAAL,CAAgB+J,KAAhB,GAAwB,KAAKxK,UAA9B,IAA4CyN,EAApE;AACAjD,sBAAAA,KAAK,GAAG,KAAK/J,SAAL,CAAgB+J,KAAxB;AACH;;AACD,wBAAI,KAAKzM,UAAT,EAAqB;AACjBoP,sBAAAA,IAAI,IAAI,KAAKpN,QAAb;AACA,0BAAIqO,MAAc,GAAI,KAAKrP,UAAL,CAAiByL,KAAjB,GAAyB,CAA1B,GAAgC,KAAKhI,kBAAL,GAA0B,CAA/E;AACA2K,sBAAAA,IAAI,IAAIiB,MAAR;AACH;;AACDlB,oBAAAA,KAAK,GAAGC,IAAI,GAAG3C,KAAf;AACA,2BAAO;AACHiD,sBAAAA,EAAE,EAAEA,EADD;AAEHN,sBAAAA,IAAI,EAAEA,IAFH;AAGHD,sBAAAA,KAAK,EAAEA,KAHJ;AAIHhB,sBAAAA,CAAC,EAAEiB,IAAI,GAAI,KAAK5M,UAAL,CAAiB8N,OAAjB,GAA2B7D,KAJnC;AAKHyB,sBAAAA,CAAC,EAAE,KAAK3L,QAAL,CAAc2L;AALd,qBAAP;AAOH;;AACD,qBAAKvQ,MAAM,CAACkM,mBAAP,CAA2BE,aAAhC;AAA+C;AAC3C,wBAAI,KAAKnH,WAAT,EAAsB;AAClB,0BAAIsK,OAAU,GAAG,KAAKC,aAAL,CAAmBuC,EAAnB,CAAjB;;AACAP,sBAAAA,KAAK,GAAG,CAAC,KAAKrN,SAAN,GAAmB,CAAC,KAAKY,SAAL,CAAgB+J,KAAhB,GAAwB,KAAKxK,UAA9B,KAA6CyN,EAAE,GAAGxC,OAAK,CAACG,KAAxD,CAAnB,IAAsFH,OAAK,CAACvN,GAAN,GAAa,KAAKsC,UAAL,GAAkBiL,OAAK,CAACG,KAA3H,CAAR;AACA,0BAAI+C,GAAU,GAAG,KAAKxN,WAAL,CAAiB8M,EAAjB,CAAjB;AACAjD,sBAAAA,KAAK,GAAI2D,GAAE,GAAG,CAAL,GAASA,GAAT,GAAc,KAAK1N,SAAL,CAAgB+J,KAAvC;AACH,qBALD,MAKO;AACH0C,sBAAAA,KAAK,GAAG,CAAC,KAAKrN,SAAN,GAAmB,CAAC,KAAKY,SAAL,CAAgB+J,KAAhB,GAAwB,KAAKxK,UAA9B,IAA4CyN,EAAvE;AACAjD,sBAAAA,KAAK,GAAG,KAAK/J,SAAL,CAAgB+J,KAAxB;AACH;;AACD,wBAAI,KAAKzM,UAAT,EAAqB;AACjBmP,sBAAAA,KAAK,IAAI,KAAKrN,SAAd;;AACA,0BAAIuO,OAAc,GAAI,KAAKrP,UAAL,CAAiByL,KAAjB,GAAyB,CAA1B,GAAgC,KAAKhI,kBAAL,GAA0B,CAA/E;;AACA0K,sBAAAA,KAAK,IAAIkB,OAAT;AACH;;AACDjB,oBAAAA,IAAI,GAAGD,KAAK,GAAG1C,KAAf;AACA,2BAAO;AACHiD,sBAAAA,EAAE,EAAEA,EADD;AAEHP,sBAAAA,KAAK,EAAEA,KAFJ;AAGHC,sBAAAA,IAAI,EAAEA,IAHH;AAIHjB,sBAAAA,CAAC,EAAEiB,IAAI,GAAI,KAAK5M,UAAL,CAAiB8N,OAAjB,GAA2B7D,KAJnC;AAKHyB,sBAAAA,CAAC,EAAE,KAAK3L,QAAL,CAAc2L;AALd,qBAAP;AAOH;AAhDL;;AAkDA;;AACJ,iBAAKvQ,MAAM,CAACgM,IAAP,CAAYK,QAAjB;AAA2B;AACvB,wBAAQ,KAAKpJ,YAAb;AACI,uBAAKjD,MAAM,CAACsM,iBAAP,CAAyBC,aAA9B;AAA6C;AACzC,0BAAI,KAAKtH,WAAT,EAAsB;AAClB,4BAAIsK,OAAU,GAAG,KAAKC,aAAL,CAAmBuC,EAAnB,CAAjB;;AACAH,wBAAAA,GAAG,GAAG,CAAC,KAAK1N,OAAN,GAAiB,CAAC,KAAKa,SAAL,CAAgBgK,MAAhB,GAAyB,KAAKxK,QAA/B,KAA4CwN,EAAE,GAAGxC,OAAK,CAACG,KAAvD,CAAjB,IAAmFH,OAAK,CAACvN,GAAN,GAAa,KAAKuC,QAAL,GAAgBgL,OAAK,CAACG,KAAtH,CAAN;AACA,4BAAI+C,IAAU,GAAG,KAAKxN,WAAL,CAAiB8M,EAAjB,CAAjB;AACAhD,wBAAAA,MAAM,GAAI0D,IAAE,GAAG,CAAL,GAASA,IAAT,GAAc,KAAK1N,SAAL,CAAgBgK,MAAxC;AACH,uBALD,MAKO;AACH6C,wBAAAA,GAAG,GAAG,CAAC,KAAK1N,OAAN,GAAiB,CAAC,KAAKa,SAAL,CAAgBgK,MAAhB,GAAyB,KAAKxK,QAA/B,IAA2CwN,EAAlE;AACAhD,wBAAAA,MAAM,GAAG,KAAKhK,SAAL,CAAgBgK,MAAzB;AACH;;AACD,0BAAI,KAAK1M,UAAT,EAAqB;AACjBuP,wBAAAA,GAAG,IAAI,KAAK1N,OAAZ;;AACA,4BAAIwO,QAAc,GAAI,KAAKrP,UAAL,CAAiB0L,MAAjB,GAA0B,CAA3B,GAAiC,KAAKjI,kBAAL,GAA0B,CAAhF;;AACA8K,wBAAAA,GAAG,IAAIc,QAAP;AACH;;AACDf,sBAAAA,MAAM,GAAGC,GAAG,GAAG7C,MAAf;AACA,6BAAO;AACHgD,wBAAAA,EAAE,EAAEA,EADD;AAEHH,wBAAAA,GAAG,EAAEA,GAFF;AAGHD,wBAAAA,MAAM,EAAEA,MAHL;AAIHnB,wBAAAA,CAAC,EAAE,KAAK5L,QAAL,CAAc4L,CAJd;AAKHD,wBAAAA,CAAC,EAAEoB,MAAM,GAAI,KAAK9M,UAAL,CAAiB+N,OAAjB,GAA2B7D;AALrC,uBAAP;AAOH;;AACD,uBAAK/O,MAAM,CAACsM,iBAAP,CAAyBE,aAA9B;AAA6C;AACzC,0BAAI,KAAKvH,WAAT,EAAsB;AAClB,4BAAIsK,OAAU,GAAG,KAAKC,aAAL,CAAmBuC,EAAnB,CAAjB;;AACAJ,wBAAAA,MAAM,GAAG,KAAKvN,UAAL,GAAmB,CAAC,KAAKW,SAAL,CAAgBgK,MAAhB,GAAyB,KAAKxK,QAA/B,KAA4CwN,EAAE,GAAGxC,OAAK,CAACG,KAAvD,CAAnB,IAAqFH,OAAK,CAACvN,GAAN,GAAa,KAAKuC,QAAL,GAAgBgL,OAAK,CAACG,KAAxH,CAAT;AACA,4BAAI+C,IAAU,GAAG,KAAKxN,WAAL,CAAiB8M,EAAjB,CAAjB;AACAhD,wBAAAA,MAAM,GAAI0D,IAAE,GAAG,CAAL,GAASA,IAAT,GAAc,KAAK1N,SAAL,CAAgBgK,MAAxC;AACH,uBALD,MAKO;AACH4C,wBAAAA,MAAM,GAAG,KAAKvN,UAAL,GAAmB,CAAC,KAAKW,SAAL,CAAgBgK,MAAhB,GAAyB,KAAKxK,QAA/B,IAA2CwN,EAAvE;AACAhD,wBAAAA,MAAM,GAAG,KAAKhK,SAAL,CAAgBgK,MAAzB;AACH;;AACD,0BAAI,KAAK1M,UAAT,EAAqB;AACjBsP,wBAAAA,MAAM,IAAI,KAAKvN,UAAf;;AACA,4BAAIsO,QAAc,GAAI,KAAKrP,UAAL,CAAiB0L,MAAjB,GAA0B,CAA3B,GAAiC,KAAKjI,kBAAL,GAA0B,CAAhF;;AACA6K,wBAAAA,MAAM,IAAIe,QAAV;AACH;;AACDd,sBAAAA,GAAG,GAAGD,MAAM,GAAG5C,MAAf;AACA,6BAAO;AACHgD,wBAAAA,EAAE,EAAEA,EADD;AAEHH,wBAAAA,GAAG,EAAEA,GAFF;AAGHD,wBAAAA,MAAM,EAAEA,MAHL;AAIHnB,wBAAAA,CAAC,EAAE,KAAK5L,QAAL,CAAc4L,CAJd;AAKHD,wBAAAA,CAAC,EAAEoB,MAAM,GAAI,KAAK9M,UAAL,CAAiB+N,OAAjB,GAA2B7D;AALrC,uBAAP;AAOA;AACH;AAjDL;AAmDH;;AACD,iBAAK/O,MAAM,CAACgM,IAAP,CAAYS,IAAjB;AAAuB;AACnB,oBAAIoG,OAAe,GAAGvF,IAAI,CAAC6B,KAAL,CAAW4C,EAAE,GAAG,KAAKvN,WAArB,CAAtB;;AACA,wBAAQ,KAAKtB,UAAb;AACI,uBAAKlD,MAAM,CAAC0M,aAAP,CAAqBT,UAA1B;AAAsC;AAClC,8BAAQ,KAAKhJ,YAAb;AACI,6BAAKjD,MAAM,CAACsM,iBAAP,CAAyBC,aAA9B;AAA6C;AACzCqF,4BAAAA,GAAG,GAAG,CAAC,KAAK1N,OAAN,GAAiB,CAAC,KAAKa,SAAL,CAAgBgK,MAAhB,GAAyB,KAAKxK,QAA/B,IAA2CsO,OAAlE;AACAlB,4BAAAA,MAAM,GAAGC,GAAG,GAAG,KAAK7M,SAAL,CAAgBgK,MAA/B;AACAyD,4BAAAA,KAAK,GAAGb,MAAM,GAAI,KAAK9M,UAAL,CAAiB+N,OAAjB,GAA2B,KAAK7N,SAAL,CAAgBgK,MAA7D;AACA;AACH;;AACD,6BAAK/O,MAAM,CAACsM,iBAAP,CAAyBE,aAA9B;AAA6C;AACzCmF,4BAAAA,MAAM,GAAG,KAAKvN,UAAL,GAAmB,CAAC,KAAKW,SAAL,CAAgBgK,MAAhB,GAAyB,KAAKxK,QAA/B,IAA2CsO,OAAvE;AACAjB,4BAAAA,GAAG,GAAGD,MAAM,GAAG,KAAK5M,SAAL,CAAgBgK,MAA/B;AACAyD,4BAAAA,KAAK,GAAGb,MAAM,GAAI,KAAK9M,UAAL,CAAiB+N,OAAjB,GAA2B,KAAK7N,SAAL,CAAgBgK,MAA7D;AACA;AACH;AAZL;;AAcAwD,sBAAAA,KAAK,GAAG,KAAKlO,QAAL,GAAkB0N,EAAE,GAAG,KAAKvN,WAAX,IAA2B,KAAKO,SAAL,CAAgB+J,KAAhB,GAAwB,KAAKxK,UAAxD,CAAzB;;AACA,8BAAQ,KAAKtB,cAAb;AACI,6BAAKhD,MAAM,CAACkM,mBAAP,CAA2BC,aAAhC;AAA+C;AAC3CoG,4BAAAA,KAAK,IAAK,KAAK1N,UAAL,CAAiB8N,OAAjB,GAA2B,KAAK5N,SAAL,CAAgB+J,KAArD;AACAyD,4BAAAA,KAAK,IAAK,KAAKlP,UAAL,CAAiBsP,OAAjB,GAA2B,KAAKtP,UAAL,CAAiByL,KAAtD;AACA;AACH;;AACD,6BAAK9O,MAAM,CAACkM,mBAAP,CAA2BE,aAAhC;AAA+C;AAC3CmG,4BAAAA,KAAK,IAAK,CAAC,IAAI,KAAK1N,UAAL,CAAiB8N,OAAtB,IAAiC,KAAK5N,SAAL,CAAgB+J,KAA3D;AACAyD,4BAAAA,KAAK,IAAK,CAAC,IAAI,KAAKlP,UAAL,CAAiBsP,OAAtB,IAAiC,KAAKtP,UAAL,CAAiByL,KAA5D;AACAyD,4BAAAA,KAAK,IAAI,CAAC,CAAV;AACA;AACH;AAXL;;AAaA,6BAAO;AACHR,wBAAAA,EAAE,EAAEA,EADD;AAEHH,wBAAAA,GAAG,EAAEA,GAFF;AAGHD,wBAAAA,MAAM,EAAEA,MAHL;AAIHnB,wBAAAA,CAAC,EAAE+B,KAJA;AAKHhC,wBAAAA,CAAC,EAAEiC;AALA,uBAAP;AAOH;;AACD,uBAAKxS,MAAM,CAAC0M,aAAP,CAAqBL,QAA1B;AAAoC;AAChC,8BAAQ,KAAKrJ,cAAb;AACI,6BAAKhD,MAAM,CAACkM,mBAAP,CAA2BC,aAAhC;AAA+C;AAC3CsF,4BAAAA,IAAI,GAAG,KAAKpN,QAAL,GAAiB,CAAC,KAAKU,SAAL,CAAgB+J,KAAhB,GAAwB,KAAKxK,UAA9B,IAA4CuO,OAApE;AACArB,4BAAAA,KAAK,GAAGC,IAAI,GAAG,KAAK1M,SAAL,CAAgB+J,KAA/B;AACAyD,4BAAAA,KAAK,GAAGd,IAAI,GAAI,KAAK5M,UAAL,CAAiB8N,OAAjB,GAA2B,KAAK5N,SAAL,CAAgB+J,KAA3D;AACAyD,4BAAAA,KAAK,IAAK,KAAKlP,UAAL,CAAiBsP,OAAjB,GAA2B,KAAKtP,UAAL,CAAiByL,KAAtD;AACA;AACH;;AACD,6BAAK9O,MAAM,CAACkM,mBAAP,CAA2BE,aAAhC;AAA+C;AAC3CoF,4BAAAA,KAAK,GAAG,CAAC,KAAKrN,SAAN,GAAmB,CAAC,KAAKY,SAAL,CAAgB+J,KAAhB,GAAwB,KAAKxK,UAA9B,IAA4CuO,OAAvE;AACApB,4BAAAA,IAAI,GAAGD,KAAK,GAAG,KAAKzM,SAAL,CAAgB+J,KAA/B;AACAyD,4BAAAA,KAAK,GAAGd,IAAI,GAAI,KAAK5M,UAAL,CAAiB8N,OAAjB,GAA2B,KAAK5N,SAAL,CAAgB+J,KAA3D;AACAyD,4BAAAA,KAAK,IAAK,CAAC,IAAI,KAAKlP,UAAL,CAAiBsP,OAAtB,IAAiC,KAAKtP,UAAL,CAAiByL,KAA5D;AACA;AACH;AAdL;;AAgBA0D,sBAAAA,KAAK,GAAG,CAAC,KAAKtO,OAAN,GAAkB6N,EAAE,GAAG,KAAKvN,WAAX,IAA2B,KAAKO,SAAL,CAAgBgK,MAAhB,GAAyB,KAAKxK,QAAzD,CAAzB;;AACA,8BAAQ,KAAKtB,YAAb;AACI,6BAAKjD,MAAM,CAACsM,iBAAP,CAAyBC,aAA9B;AAA6C;AACzCiG,4BAAAA,KAAK,IAAK,CAAC,IAAI,KAAK3N,UAAL,CAAiB+N,OAAtB,IAAiC,KAAK7N,SAAL,CAAgBgK,MAA3D;AACAyD,4BAAAA,KAAK,IAAK,CAAC,IAAI,KAAKnP,UAAL,CAAiBuP,OAAtB,IAAiC,KAAKvP,UAAL,CAAiB0L,MAA5D;AACA;AACH;;AACD,6BAAK/O,MAAM,CAACsM,iBAAP,CAAyBE,aAA9B;AAA6C;AACzCgG,4BAAAA,KAAK,IAAM,KAAK3N,UAAL,CAAiB+N,OAAlB,GAA6B,KAAK7N,SAAL,CAAgBgK,MAAvD;AACAyD,4BAAAA,KAAK,IAAK,KAAKnP,UAAL,CAAiBuP,OAAjB,GAA2B,KAAKvP,UAAL,CAAiB0L,MAAtD;AACAyD,4BAAAA,KAAK,IAAI,CAAC,CAAV;AACA;AACH;AAXL;;AAaA,6BAAO;AACHT,wBAAAA,EAAE,EAAEA,EADD;AAEHN,wBAAAA,IAAI,EAAEA,IAFH;AAGHD,wBAAAA,KAAK,EAAEA,KAHJ;AAIHhB,wBAAAA,CAAC,EAAE+B,KAJA;AAKHhC,wBAAAA,CAAC,EAAEiC;AALA,uBAAP;AAOH;AA5EL;;AA8EA;AACH;AA3LL;AA6LH,SA5tCuC,CA6tCxC;;;AACAM,QAAAA,iBAAiB,CAACf,EAAD,EAAa;AAC1B,cAAIvK,IAAS,GAAG,KAAKE,eAAL,CAAqBqK,EAArB,CAAhB;AACA,cAAI,CAACvK,IAAL,EACI,OAAO,IAAP;AACJ,cAAIuL,EAAe,GAAGvL,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAAtB;AACA,cAAIyS,GAAS,GAAGxL,IAAI,CAAC8I,WAAL,EAAhB;AACA,cAAI2C,IAAS,GAAG;AACZlB,YAAAA,EAAE,EAAEA,EADQ;AAEZvB,YAAAA,CAAC,EAAEwC,GAAG,CAACxC,CAFK;AAGZD,YAAAA,CAAC,EAAEyC,GAAG,CAACzC;AAHK,WAAhB;;AAKA,cAAI,KAAKvL,SAAT,EAAoB;AAChBiO,YAAAA,IAAI,CAACrB,GAAL,GAAWoB,GAAG,CAACzC,CAAJ,GAASwC,EAAE,CAAChE,MAAH,IAAa,IAAIgE,EAAE,CAACH,OAApB,CAApB;AACAK,YAAAA,IAAI,CAACtB,MAAL,GAAcqB,GAAG,CAACzC,CAAJ,GAASwC,EAAE,CAAChE,MAAH,GAAYgE,EAAE,CAACH,OAAtC;AACH,WAHD,MAGO;AACHK,YAAAA,IAAI,CAACxB,IAAL,GAAYuB,GAAG,CAACxC,CAAJ,GAASuC,EAAE,CAACjE,KAAH,GAAWiE,EAAE,CAACJ,OAAnC;AACAM,YAAAA,IAAI,CAACzB,KAAL,GAAawB,GAAG,CAACxC,CAAJ,GAASuC,EAAE,CAACjE,KAAH,IAAY,IAAIiE,EAAE,CAACJ,OAAnB,CAAtB;AACH;;AACD,iBAAOM,IAAP;AACH,SAjvCuC,CAkvCxC;;;AACAC,QAAAA,UAAU,CAACnB,EAAD,EAAa;AACnB,cAAI,KAAK9K,QAAT,EACI,OAAO,KAAKsK,YAAL,CAAkBQ,EAAlB,CAAP,CADJ,KAEK;AACD,gBAAI,KAAKnJ,qBAAT,EACI,OAAO,KAAK2I,YAAL,CAAkBQ,EAAlB,CAAP,CADJ,KAGI,OAAO,KAAKe,iBAAL,CAAuBf,EAAvB,CAAP;AACP;AACJ,SA5vCuC,CA6vCxC;;;AACAvC,QAAAA,aAAa,CAAC2D,MAAD,EAAkB;AAC3B,cAAI,CAAC,KAAKlO,WAAV,EACI,OAAO,IAAP;AACJ,cAAIkO,MAAM,IAAI,IAAV,IAAkBA,MAAM,IAAI1D,SAAhC,EACI0D,MAAM,GAAG,KAAKjM,SAAd;AACJ,cAAIqI,KAAa,GAAG,CAApB;AACA,cAAIG,KAAa,GAAG,CAApB;;AACA,eAAK,IAAIqC,EAAT,IAAe,KAAK9M,WAApB,EAAiC;AAC7B,gBAAImO,QAAQ,CAACrB,EAAD,CAAR,GAAeoB,MAAnB,EAA2B;AACvB5D,cAAAA,KAAK,IAAI,KAAKtK,WAAL,CAAiB8M,EAAjB,CAAT;AACArC,cAAAA,KAAK;AACR;AACJ;;AACD,iBAAO;AACH1N,YAAAA,GAAG,EAAEuN,KADF;AAEHG,YAAAA,KAAK,EAAEA;AAFJ,WAAP;AAIH,SA/wCuC,CAgxCxC;;;AACAlF,QAAAA,cAAc,GAAG;AACb,eAAKlE,SAAL,GAAiB,KAAKtB,SAAL,GAAiB,KAAKQ,OAAtB,GAAgC,KAAKG,QAAtD;AACH,SAnxCuC,CAoxCxC;;;AACA8E,QAAAA,cAAc,GAAG;AACb,cAAIlD,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAACf,iBAAF,GAAsB,KAAtB;;AACA,cAAIe,CAAC,CAACtB,cAAF,IAAoB,IAAxB,EAA8B;AAC1B,gBAAIuB,IAAS,GAAGD,CAAC,CAACG,eAAF,CAAkBH,CAAC,CAACtB,cAApB,CAAhB;AACAsB,YAAAA,CAAC,CAACtB,cAAF,GAAmB,IAAnB;;AACA,gBAAIuB,IAAJ,EAAU;AACNlH,cAAAA,KAAK,CAACkH,IAAD,CAAL,CACK6L,EADL,CACQ,EADR,EACY;AAAExJ,gBAAAA,KAAK,EAAE;AAAT,eADZ,EAEKwJ,EAFL,CAEQ,EAFR,EAEY;AAAExJ,gBAAAA,KAAK,EAAE;AAAT,eAFZ,EAGKyJ,KAHL;AAIH;AACJ;;AACD/L,UAAAA,CAAC,CAACJ,YAAF;;AAEA,cAAII,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACuK,QAA1B,IACA,CAACnE,CAAC,CAACrB,QADP,EAEE;AACE;AACAqB,YAAAA,CAAC,CAACgM,MAAF;AACH,WALD,MAKO,IAAIhM,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACY,IAA9B,EAAoC;AACvC,gBAAIwF,CAAC,CAACjB,SAAF,IAAe,IAAf,IAAuBiB,CAAC,CAACf,iBAA7B,EAAgD;AAC5C,mBAAKgN,WAAL;AACH,aAFD,MAEO;AACHjM,cAAAA,CAAC,CAACgM,MAAF;AACH;AACJ;AACJ,SAhzCuC,CAizCxC;;;AACAnJ,QAAAA,aAAa,CAACgG,EAAD,EAAUqD,gBAAV,EAAiC;AAC1C,cAAI,KAAK1P,WAAL,CAAkB,qBAAlB,EAAyCqM,EAAzC,EAA6CqD,gBAA7C,CAAJ,EACI;AACJ,eAAKjN,iBAAL,GAAyB,IAAzB,CAH0C,CAI1C;;AACA,cAAIkN,IAAI,GAAGtD,EAAE,CAACuD,MAAH,KAAc,KAAK3J,IAA9B;;AACA,cAAI,CAAC0J,IAAL,EAAW;AACP,gBAAIE,QAAa,GAAGxD,EAAE,CAACuD,MAAvB;;AACA,mBAAOC,QAAQ,CAACC,OAAT,IAAoB,IAApB,IAA4BD,QAAQ,CAACE,MAA5C,EACIF,QAAQ,GAAGA,QAAQ,CAACE,MAApB;;AACJ,iBAAK/M,WAAL,GAAmB6M,QAAQ,CAACC,OAAT,IAAoB,IAApB,GAA2BD,QAA3B,GAAsCxD,EAAE,CAACuD,MAA5D;AACH;AACJ,SA9zCuC,CA+zCxC;;;AACAtJ,QAAAA,UAAU,GAAG;AACT,cAAI9C,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAAChB,UAAF,GAAe,IAAf;;AACA,cAAIgB,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACuK,QAA9B,EAAwC;AACpC,gBAAI,KAAKxF,QAAT,EACI,KAAKC,gBAAL,GAAwB,IAAxB;AACJoB,YAAAA,CAAC,CAACgM,MAAF;AACH,WAJD,MAIO,IAAIhM,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACY,IAA9B,EAAoC;AACvC,gBAAIwF,CAAC,CAACjB,SAAF,IAAe,IAAnB,EAAyB;AACrB,mBAAKkN,WAAL;AACH,aAFD,MAEO;AACHjM,cAAAA,CAAC,CAACgM,MAAF;AACH;AACJ;;AACD,eAAKxM,WAAL,GAAmB,IAAnB;AACH;;AAEDwD,QAAAA,iBAAiB,CAAC6F,EAAD,EAAUqD,gBAAV,EAAiC;AAC9C,cAAIlM,CAAC,GAAG,IAAR;AACA,cAAIA,CAAC,CAACxD,WAAF,CAAe,qBAAf,EAAsCqM,EAAtC,EAA0CqD,gBAA1C,KAA+DrD,EAAE,CAAC2D,QAAtE,EACI;AAEJxM,UAAAA,CAAC,CAAChB,UAAF,GAAe,IAAf;;AACA,cAAIgB,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACuK,QAA9B,EAAwC;AACpC,gBAAInE,CAAC,CAACrB,QAAN,EACIqB,CAAC,CAACpB,gBAAF,GAAqB,IAArB;AACJoB,YAAAA,CAAC,CAACgM,MAAF;AACH,WAJD,MAIO,IAAIhM,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACY,IAA9B,EAAoC;AACvC,gBAAIwF,CAAC,CAACjB,SAAF,IAAe,IAAnB,EAAyB;AACrBiB,cAAAA,CAAC,CAACiM,WAAF;AACH,aAFD,MAEO;AACHjM,cAAAA,CAAC,CAACgM,MAAF;AACH;AACJ;;AACD,eAAKxM,WAAL,GAAmB,IAAnB;AACH,SAn2CuC,CAo2CxC;;;AACA4D,QAAAA,cAAc,GAAG;AACb,cAAI,KAAKnC,WAAL,CAAiB,KAAjB,CAAJ,EACI,KAAKrB,YAAL;AACP,SAx2CuC,CAy2CxC;;;AACA6M,QAAAA,eAAe,CAACxM,IAAD,EAAY;AACvB,cAAIuL,EAAe,GAAGvL,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAAtB,CADuB,CAEvB;;AACA,cACK,CAAC,KAAKyE,SAAN,IAAmB+N,EAAE,CAACjE,KAAH,IAAY,KAAK/J,SAAL,CAAgB+J,KAAhD,IACI,KAAK9J,SAAL,IAAkB+N,EAAE,CAAChE,MAAH,IAAa,KAAKhK,SAAL,CAAgBgK,MAFvD,EAGE;AACE,gBAAI,CAAC,KAAK9J,WAAV,EACI,KAAKA,WAAL,GAAmB,EAAnB;AACJ,gBAAIjD,GAAG,GAAG,KAAKgD,SAAL,GAAiB+N,EAAE,CAAChE,MAApB,GAA6BgE,EAAE,CAACjE,KAA1C;;AACA,gBAAI,KAAK7J,WAAL,CAAiBuC,IAAI,CAACqM,OAAtB,KAAkC7R,GAAtC,EAA2C;AACvC,mBAAKiD,WAAL,CAAiBuC,IAAI,CAACqM,OAAtB,IAAiC7R,GAAjC;;AACA,mBAAK2G,cAAL,GAFuC,CAGvC;AACA;AACA;;;AACA,mBAAKsL,SAAL,GANuC,CAOvC;;AACA,kBAAI,KAAKxN,eAAL,IAAwB,IAA5B,EAAkC;AAC9B,qBAAKF,UAAL,GAAkB,IAAlB;AACA,qBAAK2N,UAAL,CAAgB,KAAKvN,WAArB;AACA,qBAAKwN,QAAL,CAAc,KAAK1N,eAAnB,EAAoC6G,IAAI,CAAC8G,GAAL,CAAS,CAAT,EAAY,KAAK1N,gBAAL,GAA0B,IAAI2N,IAAJ,EAAD,CAAaC,OAAb,KAAyB,IAA9D,CAApC;AACH;AACJ;AACJ,WAxBsB,CAyBvB;;AACH,SAp4CuC,CAq4CxC;;;AACAd,QAAAA,WAAW,GAAG;AACV,cAAIjM,CAAC,GAAG,IAAR;AACA,cAAI,CAACA,CAAC,CAACpF,MAAH,KAAcoF,CAAC,CAAC1B,UAAF,GAAe,CAAf,IAAoB0B,CAAC,CAACzB,YAAF,GAAiB,CAArC,IAA0CyB,CAAC,CAACxB,aAAF,GAAkB,CAA5D,IAAiEwB,CAAC,CAACvB,WAAF,GAAgB,CAA/F,CAAJ,EACI;AACJ,cAAIuO,MAAM,GAAGhN,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAAC/B,OAAhB,GAA0B+B,CAAC,CAAC5B,QAAzC;AACA,cAAI6O,GAAG,GAAG,CAACjN,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACP,WAAF,CAAe+H,MAA7B,GAAsCxH,CAAC,CAACP,WAAF,CAAe8H,KAAtD,IAA+DvH,CAAC,CAACkN,YAA3E;AACA,cAAIC,OAAO,GAAGpH,IAAI,CAACQ,GAAL,CAASvG,CAAC,CAACjB,SAAF,CAAakK,CAAb,GAAiB+D,MAA1B,IAAoCC,GAAlD;;AACA,cAAIE,OAAJ,EAAa;AACT,gBAAIC,YAAY,GAAG,EAAnB;;AACA,oBAAQpN,CAAC,CAACpE,cAAV;AACI,mBAAK,CAAL,CADJ,CACW;;AACP,mBAAK,CAAL;AAAO;AACH,oBAAIoE,CAAC,CAACjB,SAAF,GAAciO,MAAlB,EAA0B;AACtBhN,kBAAAA,CAAC,CAACqN,OAAF,CAAUD,YAAV,EADsB,CAEtB;AACH,iBAHD,MAGO;AACHpN,kBAAAA,CAAC,CAACsN,QAAF,CAAWF,YAAX,EADG,CAEH;AACH;;AACD;;AACJ,mBAAK,CAAL,CAXJ,CAWW;;AACP,mBAAK,CAAL;AAAO;AACH,oBAAIpN,CAAC,CAACjB,SAAF,GAAciO,MAAlB,EAA0B;AACtBhN,kBAAAA,CAAC,CAACqN,OAAF,CAAUD,YAAV;AACH,iBAFD,MAEO;AACHpN,kBAAAA,CAAC,CAACsN,QAAF,CAAWF,YAAX;AACH;;AACD;AAlBR;AAoBH,WAtBD,MAsBO,IAAIpN,CAAC,CAAC1B,UAAF,IAAgB,CAAhB,IAAqB0B,CAAC,CAACzB,YAAF,IAAkB,CAAvC,IAA4CyB,CAAC,CAACxB,aAAF,IAAmB,CAA/D,IAAoEwB,CAAC,CAACvB,WAAF,IAAiB,CAAzF,EAA4F;AAC/FuB,YAAAA,CAAC,CAACgM,MAAF;AACH;;AACDhM,UAAAA,CAAC,CAACjB,SAAF,GAAc,IAAd;AACH,SAv6CuC,CAw6CxC;;;AACAiN,QAAAA,MAAM,GAAG;AACL,cAAIhM,CAAM,GAAG,IAAb;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,EAAL,EACI;AACJ,cAAIjB,CAAC,CAAC1B,UAAF,GAAe,CAAf,IAAoB0B,CAAC,CAACzB,YAAF,GAAiB,CAArC,IAA0CyB,CAAC,CAACxB,aAAF,GAAkB,CAA5D,IAAiEwB,CAAC,CAACvB,WAAF,GAAgB,CAArF,EACI;AACJuB,UAAAA,CAAC,CAACrB,QAAF,GAAa,IAAb;;AACAqB,UAAAA,CAAC,CAAC+K,gBAAF;;AACA,cAAII,MAAc,GAAG,CAACnL,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACrD,OAAhB,GAA0BqD,CAAC,CAAClD,QAA7B,KAA0CkD,CAAC,CAACvC,SAAF,GAAcuC,CAAC,CAACP,WAAF,CAAe+H,MAA7B,GAAsCxH,CAAC,CAACP,WAAF,CAAe8H,KAA/F,CAArB;AACA,cAAI6F,YAAoB,GAAG,EAA3B;AACApN,UAAAA,CAAC,CAAC4M,QAAF,CAAW5M,CAAC,CAACnB,aAAb,EAA4BuO,YAA5B,EAA0CjC,MAA1C;AACH,SAp7CuC,CAq7CxC;;;AACAoC,QAAAA,MAAM,GAAG;AACL,cAAI,KAAKlM,qBAAL,IAA8B,CAA9B,IAAmC,KAAKpF,WAA5C,EACI,OAFC,CAGL;;AACA,cAAI,KAAKyD,QAAT,EAAmB;AACf,gBAAI+B,GAAW,GAAI,KAAKvF,cAAL,GAAsB,KAAKmF,qBAA5B,GAAqD,KAAKrF,cAA1D,GAA2E,KAAKA,cAAhF,GAAkG,KAAKE,cAAL,GAAsB,KAAKmF,qBAA/I;;AACA,iBAAK,IAAIK,CAAS,GAAG,KAAKxF,cAA1B,EAA0CwF,CAAC,GAAGD,GAA9C,EAAmDC,CAAC,EAApD,EAAwD;AACpD,kBAAIgK,IAAS,GAAG,KAAKvO,WAAL,CAAiBuE,CAAjB,CAAhB;;AACA,kBAAIgK,IAAJ,EAAU;AACN,qBAAKZ,mBAAL,CAAyBY,IAAzB;AACH;AACJ;;AAED,gBAAI,KAAKxP,cAAL,IAAuB,KAAKF,cAAL,GAAsB,CAAjD,EAAoD;AAAE;AAClD,kBAAI,KAAKqC,gBAAT,EAA2B;AACvB,qBAAKnC,cAAL,GAAsB,CAAtB;AACA,qBAAKD,WAAL,GAAmB,KAAnB,CAFuB,CAGvB;;AACA,qBAAKoC,gBAAL,GAAwB,KAAxB;AACH,eALD,MAKO;AACH,qBAAKpC,WAAL,GAAmB,IAAnB;;AACA,qBAAKuF,iBAAL;;AACA,qBAAKjG,YAAL,GAAoB,KAApB;;AACA,qBAAKwP,gBAAL;;AACA,oBAAI,KAAKrQ,SAAL,IAAkBd,SAAS,CAACY,IAAhC,EACI,KAAKsE,UAAL,GAAkB,KAAKD,aAAvB;AACP;AACJ,aAdD,MAcO;AACH,mBAAK3C,cAAL,IAAuB,KAAKmF,qBAA5B;AACH;AACJ,WA1BD,MA0BO;AACH,gBAAI,KAAKnF,cAAL,GAAsB,KAAKyD,SAA/B,EAA0C;AACtC,kBAAI8B,IAAW,GAAI,KAAKvF,cAAL,GAAsB,KAAKmF,qBAA5B,GAAqD,KAAK1B,SAA1D,GAAsE,KAAKA,SAA3E,GAAwF,KAAKzD,cAAL,GAAsB,KAAKmF,qBAArI;;AACA,mBAAK,IAAIK,GAAS,GAAG,KAAKxF,cAA1B,EAA0CwF,GAAC,GAAGD,IAA9C,EAAmDC,GAAC,EAApD,EAAwD;AACpD,qBAAKC,oBAAL,CAA0BD,GAA1B;AACH;;AACD,mBAAKxF,cAAL,IAAuB,KAAKmF,qBAA5B;AACH,aAND,MAMO;AACH,mBAAKpF,WAAL,GAAmB,IAAnB;;AACA,mBAAK8O,gBAAL;;AACA,kBAAI,KAAKrQ,SAAL,IAAkBd,SAAS,CAACY,IAAhC,EACI,KAAKsE,UAAL,GAAkB,KAAKD,aAAvB;AACP;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACIiM,QAAAA,mBAAmB,CAACY,IAAD,EAAY;AAC3B,cAAIzL,IAAS,GAAG,KAAKE,eAAL,CAAqBuL,IAAI,CAAClB,EAA1B,CAAhB;;AACA,cAAI,CAACvK,IAAL,EAAW;AAAE;AACT,gBAAIuN,MAAe,GAAG,KAAKpQ,KAAL,CAAYqQ,IAAZ,KAAqB,CAA3C;;AACA,gBAAID,MAAJ,EAAY;AACRvN,cAAAA,IAAI,GAAG,KAAK7C,KAAL,CAAYsQ,GAAZ,EAAP,CADQ,CAER;AACH,aAHD,MAGO;AACHzN,cAAAA,IAAI,GAAG1H,WAAW,CAAC,KAAK8E,QAAN,CAAlB,CADG,CAEH;AACH;;AACD,gBAAI,CAACmQ,MAAD,IAAW,CAAChV,OAAO,CAACyH,IAAD,CAAvB,EAA+B;AAC3BA,cAAAA,IAAI,GAAG1H,WAAW,CAAC,KAAK8E,QAAN,CAAlB;AACAmQ,cAAAA,MAAM,GAAG,KAAT;AACH;;AACD,gBAAIvN,IAAI,CAACqM,OAAL,IAAgBZ,IAAI,CAAClB,EAAzB,EAA6B;AACzBvK,cAAAA,IAAI,CAACqM,OAAL,GAAeZ,IAAI,CAAClB,EAApB;AACA,kBAAIgB,EAAe,GAAGvL,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAAtB;AACAwS,cAAAA,EAAE,CAACmC,cAAH,CAAkB,KAAKnQ,SAAvB;AACH;;AACDyC,YAAAA,IAAI,CAACoJ,WAAL,CAAiB,IAAIpQ,IAAJ,CAASyS,IAAI,CAACzC,CAAd,EAAiByC,IAAI,CAAC1C,CAAtB,EAAyB,CAAzB,CAAjB;;AACA,iBAAK4E,cAAL,CAAoB3N,IAApB;;AACA,iBAAKpE,OAAL,CAAcgS,QAAd,CAAuB5N,IAAvB;;AACA,gBAAIuN,MAAM,IAAI,KAAKjQ,iBAAnB,EAAsC;AAClC,kBAAIuQ,MAAc,GAAG7N,IAAI,CAACI,YAAL,CAAkBnH,MAAlB,CAArB;AACA,kBAAI4U,MAAJ,EACIA,MAAM,CAACC,eAAP;AACP;;AACD9N,YAAAA,IAAI,CAAC+N,eAAL,CAAqB,KAAKnS,OAAL,CAAcoS,QAAd,CAAuB9D,MAAvB,GAAgC,CAArD;AAEA,gBAAI/J,QAAkB,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,qCAAzB;AACAJ,YAAAA,IAAI,CAAC,UAAD,CAAJ,GAAmBG,QAAnB;;AACA,gBAAIA,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACwL,MAAT,GAAkBF,IAAI,CAAClB,EAAvB;AACApK,cAAAA,QAAQ,CAAC8N,IAAT,GAAgB,IAAhB;;AACA9N,cAAAA,QAAQ,CAACgC,cAAT;AACH;;AACD,gBAAI,KAAK+L,WAAT,EAAsB;AAClB7V,cAAAA,YAAY,CAACmI,UAAb,CAAwB,CAAC,KAAK0N,WAAN,CAAxB,EAA4ClO,IAA5C,EAAkDyL,IAAI,CAAClB,EAAL,GAAU,KAAKrO,eAAjE;AACH;AACJ,WAtCD,MAsCO,IAAI,KAAKZ,YAAL,IAAqB,KAAK4S,WAA9B,EAA2C;AAAE;AAChDlO,YAAAA,IAAI,CAACoJ,WAAL,CAAiB,IAAIpQ,IAAJ,CAASyS,IAAI,CAACzC,CAAd,EAAiByC,IAAI,CAAC1C,CAAtB,EAAyB,CAAzB,CAAjB;;AACA,iBAAK4E,cAAL,CAAoB3N,IAApB,EAF8C,CAG9C;;;AACA,gBAAI,KAAKkO,WAAT,EAAsB;AAClB7V,cAAAA,YAAY,CAACmI,UAAb,CAAwB,CAAC,KAAK0N,WAAN,CAAxB,EAA4ClO,IAA5C,EAAkDyL,IAAI,CAAClB,EAAL,GAAU,KAAKrO,eAAjE;AACH;AACJ;;AACD,eAAKyR,cAAL,CAAoB3N,IAApB;;AAEA,eAAKmO,eAAL,CAAqBnO,IAAI,CAAC,UAAD,CAAzB;;AACA,cAAI,KAAK/C,gBAAL,CAAsB2D,OAAtB,CAA8B6K,IAAI,CAAClB,EAAnC,IAAyC,CAA7C,EAAgD;AAC5C,iBAAKtN,gBAAL,CAAsB4D,IAAtB,CAA2B4K,IAAI,CAAClB,EAAhC;AACH;AACJ,SA7hDuC,CA8hDxC;;;AACA7I,QAAAA,oBAAoB,CAACiK,MAAD,EAAiB;AACjC,cAAI3L,IAAS,GAAG,KAAKpE,OAAL,CAAcoS,QAAd,CAAuBrC,MAAvB,CAAhB;AACA,cAAIxL,QAAyB,GAAG,IAAhC;;AACA,cAAI,CAACH,IAAL,EAAW;AAAE;AACTA,YAAAA,IAAI,GAAG1H,WAAW,CAAC,KAAK8E,QAAN,CAAlB;AACA4C,YAAAA,IAAI,CAACqM,OAAL,GAAeV,MAAf;AACA,iBAAK/P,OAAL,CAAcgS,QAAd,CAAuB5N,IAAvB;AACAG,YAAAA,QAAQ,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,qCAAX;AACAJ,YAAAA,IAAI,CAAC,UAAD,CAAJ,GAAmBG,QAAnB;;AACA,gBAAIA,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACwL,MAAT,GAAkBA,MAAlB;AACAxL,cAAAA,QAAQ,CAAC8N,IAAT,GAAgB,IAAhB;;AACA9N,cAAAA,QAAQ,CAACgC,cAAT;AACH;;AACD,gBAAI,KAAK+L,WAAT,EAAsB;AAClB7V,cAAAA,YAAY,CAACmI,UAAb,CAAwB,CAAC,KAAK0N,WAAN,CAAxB,EAA4ClO,IAA5C,EAAkD2L,MAAM,GAAG,KAAKzP,eAAhE;AACH;AACJ,WAdD,MAcO,IAAI,KAAKZ,YAAL,IAAqB,KAAK4S,WAA9B,EAA2C;AAAE;AAChDlO,YAAAA,IAAI,CAACqM,OAAL,GAAeV,MAAf;AACAxL,YAAAA,QAAQ,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,qCAAX;;AACA,gBAAID,QAAJ,EAAc;AACVA,cAAAA,QAAQ,CAACwL,MAAT,GAAkBA,MAAlB;AACH;;AACD,gBAAI,KAAKuC,WAAT,EAAsB;AAClB7V,cAAAA,YAAY,CAACmI,UAAb,CAAwB,CAAC,KAAK0N,WAAN,CAAxB,EAA4ClO,IAA5C,EAAkD2L,MAAM,GAAG,KAAKzP,eAAhE;AACH;AACJ;;AACD,eAAKiS,eAAL,CAAqBhO,QAArB;;AACA,cAAI,KAAKlD,gBAAL,CAAsB2D,OAAtB,CAA8B+K,MAA9B,IAAwC,CAA5C,EAA+C;AAC3C,iBAAK1O,gBAAL,CAAsB4D,IAAtB,CAA2B8K,MAA3B;AACH;AACJ;;AAEDwC,QAAAA,eAAe,CAAChO,QAAD,EAAqB;AAChC,cAAI,CAACA,QAAL,EACI;;AACJ,cAAI,KAAKpF,YAAL,GAAoBnB,YAAY,CAACoB,IAArC,EAA2C;AACvC,gBAAIgF,IAAS,GAAGG,QAAQ,CAACqC,IAAzB;;AACA,oBAAQ,KAAKzH,YAAb;AACI,mBAAKnB,YAAY,CAACqB,MAAlB;AACIkF,gBAAAA,QAAQ,CAACE,QAAT,GAAoB,KAAKP,UAAL,IAAmBE,IAAI,CAACqM,OAA5C;AACA;;AACJ,mBAAKzS,YAAY,CAAC6G,IAAlB;AACIN,gBAAAA,QAAQ,CAACE,QAAT,GAAoB,KAAKhF,YAAL,CAAkBuF,OAAlB,CAA0BZ,IAAI,CAACqM,OAA/B,KAA2C,CAA/D;AACA;AANR;AAQH;AACJ,SA9kDuC,CA+kDxC;;;AACAsB,QAAAA,cAAc,CAAC3N,IAAD,EAAY;AACtB;AACA,cAAIwN,IAAJ;AACA,cAAIjC,EAAe,GAAGvL,IAAI,CAACI,YAAL,CAAkBrH,WAAlB,CAAtB;;AACA,cAAI,KAAK0E,WAAL,IAAoB,KAAKA,WAAL,CAAiBuC,IAAI,CAACqM,OAAtB,CAAxB,EAAwD;AACpDmB,YAAAA,IAAI,GAAG,KAAK/P,WAAL,CAAiBuC,IAAI,CAACqM,OAAtB,CAAP;AACH,WAFD,MAEO;AACH,gBAAI,KAAKrP,WAAL,GAAmB,CAAvB,EACIuO,EAAE,CAACmC,cAAH,CAAkB,KAAKnQ,SAAvB,EADJ,KAGIiQ,IAAI,GAAG,KAAKhQ,SAAL,GAAiB,KAAKD,SAAL,CAAgBgK,MAAjC,GAA0C,KAAKhK,SAAL,CAAgB+J,KAAjE;AACP;;AACD,cAAIkG,IAAJ,EAAU;AACN,gBAAI,KAAKhQ,SAAT,EACI+N,EAAE,CAAChE,MAAH,GAAYiG,IAAZ,CADJ,KAGIjC,EAAE,CAACjE,KAAH,GAAWkG,IAAX;AACP;AACJ;AACD;AACJ;AACA;AACA;;;AACIY,QAAAA,cAAc,CAACC,YAAD,EAAoB;AAC9B,cAAIrO,IAAS,GAAGsO,KAAK,CAACD,YAAD,CAAL,GAAsBA,YAAtB,GAAqC,KAAKnO,eAAL,CAAqBmO,YAArB,CAArD;AACA,cAAI7C,GAAQ,GAAG,KAAKE,UAAL,CAAgB1L,IAAI,CAACqM,OAArB,CAAf;AACArM,UAAAA,IAAI,CAACoJ,WAAL,CAAiBoC,GAAG,CAACxC,CAArB,EAAwBwC,GAAG,CAACzC,CAA5B;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIwF,QAAAA,eAAe,CAACC,IAAD,EAAY9N,IAAZ,EAA2B;AACtC,cAAIX,CAAM,GAAG,IAAb;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,EAAL,EACI;;AACJ,cAAI,CAACyN,KAAK,CAACC,OAAN,CAAcF,IAAd,CAAL,EAA0B;AACtBA,YAAAA,IAAI,GAAG,CAACA,IAAD,CAAP;AACH;;AACD,cAAI9N,IAAI,IAAI,IAAZ,EAAkB;AACdX,YAAAA,CAAC,CAAC1E,YAAF,GAAiBmT,IAAjB;AACH,WAFD,MAEO;AACH,gBAAI7C,MAAJ,EAAoBhL,GAApB;;AACA,gBAAID,IAAJ,EAAU;AACN,mBAAK,IAAIe,CAAS,GAAG+M,IAAI,CAACtE,MAAL,GAAc,CAAnC,EAAsCzI,CAAC,IAAI,CAA3C,EAA8CA,CAAC,EAA/C,EAAmD;AAC/CkK,gBAAAA,MAAM,GAAG6C,IAAI,CAAC/M,CAAD,CAAb;AACAd,gBAAAA,GAAG,GAAGZ,CAAC,CAAC1E,YAAF,CAAeuF,OAAf,CAAuB+K,MAAvB,CAAN;;AACA,oBAAIhL,GAAG,GAAG,CAAV,EAAa;AACTZ,kBAAAA,CAAC,CAAC1E,YAAF,CAAewF,IAAf,CAAoB8K,MAApB;AACH;AACJ;AACJ,aARD,MAQO;AACH,mBAAK,IAAIlK,GAAS,GAAG+M,IAAI,CAACtE,MAAL,GAAc,CAAnC,EAAsCzI,GAAC,IAAI,CAA3C,EAA8CA,GAAC,EAA/C,EAAmD;AAC/CkK,gBAAAA,MAAM,GAAG6C,IAAI,CAAC/M,GAAD,CAAb;AACAd,gBAAAA,GAAG,GAAGZ,CAAC,CAAC1E,YAAF,CAAeuF,OAAf,CAAuB+K,MAAvB,CAAN;;AACA,oBAAIhL,GAAG,IAAI,CAAX,EAAc;AACVZ,kBAAAA,CAAC,CAAC1E,YAAF,CAAeyF,MAAf,CAAsBH,GAAtB,EAA2B,CAA3B;AACH;AACJ;AACJ;AACJ;;AACDZ,UAAAA,CAAC,CAACzE,YAAF,GAAiB,IAAjB;;AACAyE,UAAAA,CAAC,CAACJ,YAAF;AACH;AACD;AACJ;AACA;AACA;;;AACIgP,QAAAA,eAAe,GAAG;AACd,iBAAO,KAAKtT,YAAZ;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIuT,QAAAA,eAAe,CAACjD,MAAD,EAAiB;AAC5B,iBAAO,KAAKtQ,YAAL,IAAqB,KAAKA,YAAL,CAAkBuF,OAAlB,CAA0B+K,MAA1B,KAAqC,CAAjE;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIkD,QAAAA,UAAU,CAACL,IAAD,EAAY;AAClB,cAAI,CAAC,KAAKxN,WAAL,EAAL,EACI;;AACJ,cAAI,CAACyN,KAAK,CAACC,OAAN,CAAcF,IAAd,CAAL,EAA0B;AACtBA,YAAAA,IAAI,GAAG,CAACA,IAAD,CAAP;AACH;;AACD,eAAK,IAAI/M,CAAS,GAAG,CAAhB,EAAmBD,GAAW,GAAGgN,IAAI,CAACtE,MAA3C,EAAmDzI,CAAC,GAAGD,GAAvD,EAA4DC,CAAC,EAA7D,EAAiE;AAC7D,gBAAIkK,MAAc,GAAG6C,IAAI,CAAC/M,CAAD,CAAzB;AACA,gBAAIzB,IAAS,GAAG,KAAKE,eAAL,CAAqByL,MAArB,CAAhB;AACA,gBAAI3L,IAAJ,EACI3H,YAAY,CAACmI,UAAb,CAAwB,CAAC,KAAK0N,WAAN,CAAxB,EAA4ClO,IAA5C,EAAkD2L,MAAM,GAAG,KAAKzP,eAAhE;AACP;AACJ;AACD;AACJ;AACA;;;AACIuQ,QAAAA,SAAS,GAAG;AACR,cAAI,CAAC,KAAKzL,WAAL,EAAL,EACI;AACJ,eAAKD,QAAL,GAAgB,KAAKA,QAArB;AACH;AACD;AACJ;AACA;AACA;AACA;;;AACIb,QAAAA,eAAe,CAACyL,MAAD,EAAiB;AAC5B,cAAI,KAAK/P,OAAT,EAAkB;AACd,iBAAK,IAAI6F,CAAS,GAAG,KAAK7F,OAAL,CAAcoS,QAAd,CAAuB9D,MAAvB,GAAgC,CAArD,EAAwDzI,CAAC,IAAI,CAA7D,EAAgEA,CAAC,EAAjE,EAAqE;AACjE,kBAAIzB,IAAS,GAAG,KAAKpE,OAAL,CAAcoS,QAAd,CAAuBvM,CAAvB,CAAhB;AACA,kBAAIzB,IAAI,CAACqM,OAAL,IAAgBV,MAApB,EACI,OAAO3L,IAAP;AACP;AACJ;AACJ;AACD;AACJ;AACA;AACA;;;AACI8O,QAAAA,eAAe,GAAG;AACd,cAAI9O,IAAJ;AACA,cAAI8H,MAAa,GAAG,EAApB;;AACA,eAAK,IAAIrG,CAAS,GAAG,KAAK7F,OAAL,CAAcoS,QAAd,CAAuB9D,MAAvB,GAAgC,CAArD,EAAwDzI,CAAC,IAAI,CAA7D,EAAgEA,CAAC,EAAjE,EAAqE;AACjEzB,YAAAA,IAAI,GAAG,KAAKpE,OAAL,CAAcoS,QAAd,CAAuBvM,CAAvB,CAAP;;AACA,gBAAI,CAAC,KAAKvE,WAAL,CAAiB6R,IAAjB,CAAsBC,CAAC,IAAIA,CAAC,CAACzE,EAAF,IAAQvK,IAAI,CAACqM,OAAxC,CAAL,EAAuD;AACnDvE,cAAAA,MAAM,CAACjH,IAAP,CAAYb,IAAZ;AACH;AACJ;;AACD,iBAAO8H,MAAP;AACH,SAttDuC,CAutDxC;;;AACAvG,QAAAA,iBAAiB,GAAG;AAChB,cAAI,KAAK9B,QAAT,EAAmB;AACf,gBAAIwP,GAAU,GAAG,KAAKH,eAAL,EAAjB;;AACA,iBAAK,IAAIrN,CAAS,GAAGwN,GAAG,CAAC/E,MAAJ,GAAa,CAAlC,EAAqCzI,CAAC,IAAI,CAA1C,EAA6CA,CAAC,EAA9C,EAAkD;AAC9C,kBAAIzB,IAAS,GAAGiP,GAAG,CAACxN,CAAD,CAAnB;AACA,kBAAI,KAAKlC,WAAL,IAAoBS,IAAI,CAACqM,OAAL,IAAgB,KAAK9M,WAAL,CAAiB8M,OAAzD,EACI;AACJrM,cAAAA,IAAI,CAACkP,QAAL,GAAgB,IAAhB;;AACA,mBAAK/R,KAAL,CAAYgS,GAAZ,CAAgBnP,IAAhB;;AACA,mBAAK,IAAIoP,CAAS,GAAG,KAAKnS,gBAAL,CAAsBiN,MAAtB,GAA+B,CAApD,EAAuDkF,CAAC,IAAI,CAA5D,EAA+DA,CAAC,EAAhE,EAAoE;AAChE,oBAAI,KAAKnS,gBAAL,CAAsBmS,CAAtB,KAA4BpP,IAAI,CAACqM,OAArC,EAA8C;AAC1C,uBAAKpP,gBAAL,CAAsB6D,MAAtB,CAA6BsO,CAA7B,EAAgC,CAAhC;;AACA;AACH;AACJ;AACJ,aAdc,CAef;;AACH,WAhBD,MAgBO;AACH,mBAAO,KAAKxT,OAAL,CAAcoS,QAAd,CAAuB9D,MAAvB,GAAgC,KAAKxK,SAA5C,EAAuD;AACnD,mBAAK2P,cAAL,CAAoB,KAAKzT,OAAL,CAAcoS,QAAd,CAAuB,KAAKpS,OAAL,CAAcoS,QAAd,CAAuB9D,MAAvB,GAAgC,CAAvD,CAApB;AACH;AACJ;AACJ,SA9uDuC,CA+uDxC;;;AACAmF,QAAAA,cAAc,CAACrP,IAAD,EAAY;AACtB;AACAA,UAAAA,IAAI,CAACsP,gBAAL;AACA,cAAItP,IAAI,CAAC+B,OAAT,EACI/B,IAAI,CAAC+B,OAAL;AACJ/B,UAAAA,IAAI,GAAG,IAAP;AACH;AACD;AACJ;AACA;AACA;;;AACIuP,QAAAA,UAAU,CAAC5D,MAAD,EAAiB6D,QAAjB,EAAqCC,OAArC,EAAsD;AAC5D,cAAI1P,CAAM,GAAG,IAAb;AAEA,cAAI,CAACA,CAAC,CAACiB,WAAF,EAAD,IAAoBjB,CAAC,CAACpF,MAAtB,IAAgC,CAACoF,CAAC,CAACN,QAAvC,EACI,OAAOwB,OAAO,CAACC,KAAR,CAAc,4CAAd,CAAP;AAEJ,cAAI,CAACsO,QAAL,EACI,OAAOvO,OAAO,CAACC,KAAR,CAAc,oHAAd,CAAP;AAEJ,cAAInB,CAAC,CAACpC,aAAN,EACI,OAAOsD,OAAO,CAACyO,IAAR,CAAa,iDAAb,CAAP;AAEJ,cAAI1P,IAAS,GAAGD,CAAC,CAACG,eAAF,CAAkByL,MAAlB,CAAhB;AACA,cAAIxL,QAAJ;;AACA,cAAI,CAACH,IAAL,EAAW;AACPwP,YAAAA,QAAQ,CAAC7D,MAAD,CAAR;AACA;AACH,WAHD,MAGO;AACHxL,YAAAA,QAAQ,GAAGH,IAAI,CAACI,YAAL;AAAA;AAAA,qCAAX;AACH;;AACDL,UAAAA,CAAC,CAACpC,aAAF,GAAkB,IAAlB;AACAoC,UAAAA,CAAC,CAACnC,SAAF,GAAc4R,QAAd;AACAzP,UAAAA,CAAC,CAAClC,WAAF,GAAgBmC,IAAhB;AACAD,UAAAA,CAAC,CAACjC,gBAAF,GAAqBkC,IAAI,CAACoC,QAA1B;AACArC,UAAAA,CAAC,CAAChC,kBAAF,GAAuBiC,IAAI,CAACqC,KAA5B;AACA,cAAIsN,SAAiB,GAAG5P,CAAC,CAAC7C,WAAF,CAAc6C,CAAC,CAAC7C,WAAF,CAAcgN,MAAd,GAAuB,CAArC,EAAwCK,EAAhE;AACA,cAAIqF,eAAwB,GAAGzP,QAAQ,CAACE,QAAxC;AACAF,UAAAA,QAAQ,CAAC0P,OAAT,CAAiBJ,OAAjB,EAA0B,MAAM;AAC5B;AACA,gBAAIK,KAAa,GAAG,CAApB;;AACA,gBAAIH,SAAS,GAAG5P,CAAC,CAACL,SAAF,GAAc,CAA9B,EAAiC;AAC7BoQ,cAAAA,KAAK,GAAGH,SAAS,GAAG,CAApB;AACH;;AACD,gBAAIG,KAAK,IAAI,IAAb,EAAmB;AACf,kBAAIC,OAAY,GAAGhQ,CAAC,CAACgK,YAAF,CAAe+F,KAAf,CAAnB;;AACA/P,cAAAA,CAAC,CAAC7C,WAAF,CAAc2D,IAAd,CAAmBkP,OAAnB;AACA,kBAAIhQ,CAAC,CAACN,QAAN,EACIM,CAAC,CAAC8K,mBAAF,CAAsBkF,OAAtB,EADJ,KAGIhQ,CAAC,CAAC2B,oBAAF,CAAuBoO,KAAvB;AACP,aAPD,MAQI/P,CAAC,CAACL,SAAF;;AACJ,gBAAIK,CAAC,CAAChF,YAAF,IAAkBnB,YAAY,CAACqB,MAAnC,EAA2C;AACvC,kBAAI2U,eAAJ,EAAqB;AACjB7P,gBAAAA,CAAC,CAAC5E,WAAF,GAAgB,CAAC,CAAjB;AACH,eAFD,MAEO,IAAI4E,CAAC,CAAC5E,WAAF,GAAgB,CAAhB,IAAqB,CAAzB,EAA4B;AAC/B4E,gBAAAA,CAAC,CAAC5E,WAAF;AACH;AACJ,aAND,MAMO,IAAI4E,CAAC,CAAChF,YAAF,IAAkBnB,YAAY,CAAC6G,IAA/B,IAAuCV,CAAC,CAAC1E,YAAF,CAAe6O,MAA1D,EAAkE;AACrE,kBAAIvJ,GAAW,GAAGZ,CAAC,CAAC1E,YAAF,CAAeuF,OAAf,CAAuB+K,MAAvB,CAAlB;;AACA,kBAAIhL,GAAG,IAAI,CAAX,EAAc;AACVZ,gBAAAA,CAAC,CAAC1E,YAAF,CAAeyF,MAAf,CAAsBH,GAAtB,EAA2B,CAA3B;AACH,eAJoE,CAKrE;;;AACA,mBAAK,IAAIc,CAAS,GAAG1B,CAAC,CAAC1E,YAAF,CAAe6O,MAAf,GAAwB,CAA7C,EAAgDzI,CAAC,IAAI,CAArD,EAAwDA,CAAC,EAAzD,EAA6D;AACzD,oBAAI8I,EAAU,GAAGxK,CAAC,CAAC1E,YAAF,CAAeoG,CAAf,CAAjB;AACA,oBAAI8I,EAAE,IAAIoB,MAAV,EACI5L,CAAC,CAAC1E,YAAF,CAAeoG,CAAf;AACP;AACJ;;AACD,gBAAI1B,CAAC,CAACtC,WAAN,EAAmB;AACf,kBAAIsC,CAAC,CAACtC,WAAF,CAAckO,MAAd,CAAJ,EACI,OAAO5L,CAAC,CAACtC,WAAF,CAAckO,MAAd,CAAP;AACJ,kBAAIqE,aAAkB,GAAG,EAAzB;AACA,kBAAIxC,IAAJ;;AACA,mBAAK,IAAIjD,GAAT,IAAexK,CAAC,CAACtC,WAAjB,EAA8B;AAC1B+P,gBAAAA,IAAI,GAAGzN,CAAC,CAACtC,WAAF,CAAc8M,GAAd,CAAP;AACA,oBAAI0F,QAAgB,GAAGrE,QAAQ,CAACrB,GAAD,CAA/B;AACAyF,gBAAAA,aAAa,CAACC,QAAQ,IAAIA,QAAQ,IAAItE,MAAZ,GAAqB,CAArB,GAAyB,CAA7B,CAAT,CAAb,GAAyD6B,IAAzD;AACH;;AACDzN,cAAAA,CAAC,CAACtC,WAAF,GAAgBuS,aAAhB;AACH,aA5C2B,CA6C5B;;;AACA,gBAAIE,GAAW,GAAG,KAAlB;AACA,gBAAIC,GAAJ;AAAA,gBAAsBC,MAAe,GAAG,KAAxC;;AACA,iBAAK,IAAI3O,GAAS,GAAGqO,KAAK,IAAI,IAAT,GAAgBA,KAAhB,GAAwBH,SAA7C,EAAwDlO,GAAC,IAAIkK,MAAM,GAAG,CAAtE,EAAyElK,GAAC,EAA1E,EAA8E;AAC1EzB,cAAAA,IAAI,GAAGD,CAAC,CAACG,eAAF,CAAkBuB,GAAlB,CAAP;;AACA,kBAAIzB,IAAJ,EAAU;AACN,oBAAIqQ,OAAY,GAAGtQ,CAAC,CAACgK,YAAF,CAAetI,GAAC,GAAG,CAAnB,CAAnB;;AACA0O,gBAAAA,GAAG,GAAGrX,KAAK,CAACkH,IAAD,CAAL,CACD6L,EADC,CACEqE,GADF,EACO;AAAE9N,kBAAAA,QAAQ,EAAE,IAAIpJ,IAAJ,CAASqX,OAAO,CAACrH,CAAjB,EAAoBqH,OAAO,CAACtH,CAA5B,EAA+B,CAA/B;AAAZ,iBADP,CAAN;;AAGA,oBAAItH,GAAC,IAAIkK,MAAM,GAAG,CAAlB,EAAqB;AACjByE,kBAAAA,MAAM,GAAG,IAAT;AACAD,kBAAAA,GAAG,CAACG,IAAJ,CAAS,MAAM;AACXvQ,oBAAAA,CAAC,CAACpC,aAAF,GAAkB,KAAlB;AACA6R,oBAAAA,QAAQ,CAAC7D,MAAD,CAAR;AACA,2BAAO5L,CAAC,CAACnC,SAAT;AACH,mBAJD;AAKH;;AACDuS,gBAAAA,GAAG,CAACrE,KAAJ;AACH;AACJ;;AACD,gBAAI,CAACsE,MAAL,EAAa;AACTrQ,cAAAA,CAAC,CAACpC,aAAF,GAAkB,KAAlB;AACA6R,cAAAA,QAAQ,CAAC7D,MAAD,CAAR;AACA5L,cAAAA,CAAC,CAACnC,SAAF,GAAc,IAAd;AACH;AACJ,WAvED,EAuEG,IAvEH;AAwEH;AACD;AACJ;AACA;AACA;AACA;AACA;AACA;;;AACI+O,QAAAA,QAAQ,CAAChB,MAAD,EAAiBwB,YAAjB,EAA4CjC,MAA5C,EAAgEqF,UAAhE,EAA6F;AAAA,cAA5EpD,YAA4E;AAA5EA,YAAAA,YAA4E,GAArD,EAAqD;AAAA;;AAAA,cAAjDjC,MAAiD;AAAjDA,YAAAA,MAAiD,GAAhC,CAAgC;AAAA;;AAAA,cAA7BqF,UAA6B;AAA7BA,YAAAA,UAA6B,GAAP,KAAO;AAAA;;AACjG,cAAIxQ,CAAC,GAAG,IAAR;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,CAAc,KAAd,CAAL,EACI,OAH6F,CAIjG;;AACA,cAAImM,YAAY,IAAI,IAApB,EAA4B;AACxBA,YAAAA,YAAY,GAAG,EAAf,CADJ,KAEK,IAAIA,YAAY,GAAG,CAAnB,EACDA,YAAY,GAAG,CAAf;AACJ,cAAIxB,MAAM,GAAG,CAAb,EACIA,MAAM,GAAG,CAAT,CADJ,KAEK,IAAIA,MAAM,IAAI5L,CAAC,CAACL,SAAhB,EACDiM,MAAM,GAAG5L,CAAC,CAACL,SAAF,GAAc,CAAvB,CAZ6F,CAajG;;AACA,cAAI,CAACK,CAAC,CAACN,QAAH,IAAeM,CAAC,CAACvD,OAAjB,IAA4BuD,CAAC,CAACvD,OAAF,CAAU8E,OAA1C,EACIvB,CAAC,CAACvD,OAAF,CAAUgU,YAAV;AAEJ,cAAIhF,GAAG,GAAGzL,CAAC,CAAC2L,UAAF,CAAaC,MAAb,CAAV;;AACA,cAAI,CAACH,GAAL,EAAU;AACN,mBAAOtS,GAAG,IAAI+H,OAAO,CAACC,KAAR,CAAc,aAAd,EAA6ByK,MAA7B,CAAd;AACH;;AACD,cAAI8E,OAAJ,EAAqBC,OAArB;;AAEA,kBAAQ3Q,CAAC,CAACpE,cAAV;AACI,iBAAK,CAAL;AAAO;AACH8U,cAAAA,OAAO,GAAGjF,GAAG,CAACvB,IAAd;AACA,kBAAIiB,MAAM,IAAI,IAAd,EACIuF,OAAO,IAAI1Q,CAAC,CAACP,WAAF,CAAe8H,KAAf,GAAuB4D,MAAlC,CADJ,KAGIuF,OAAO,IAAI1Q,CAAC,CAAClD,QAAb;AACJ2O,cAAAA,GAAG,GAAG,IAAIxS,IAAJ,CAASyX,OAAT,EAAkB,CAAlB,EAAqB,CAArB,CAAN;AACA;;AACJ,iBAAK,CAAL;AAAO;AACHA,cAAAA,OAAO,GAAGjF,GAAG,CAACxB,KAAJ,GAAYjK,CAAC,CAACP,WAAF,CAAe8H,KAArC;AACA,kBAAI4D,MAAM,IAAI,IAAd,EACIuF,OAAO,IAAI1Q,CAAC,CAACP,WAAF,CAAe8H,KAAf,GAAuB4D,MAAlC,CADJ,KAGIuF,OAAO,IAAI1Q,CAAC,CAACpD,SAAb;AACJ6O,cAAAA,GAAG,GAAG,IAAIxS,IAAJ,CAASyX,OAAO,GAAG1Q,CAAC,CAAClE,UAAF,CAAcyL,KAAjC,EAAwC,CAAxC,EAA2C,CAA3C,CAAN;AACA;;AACJ,iBAAK,CAAL;AAAO;AACHoJ,cAAAA,OAAO,GAAGlF,GAAG,CAACpB,GAAd;AACA,kBAAIc,MAAM,IAAI,IAAd,EACIwF,OAAO,IAAI3Q,CAAC,CAACP,WAAF,CAAe+H,MAAf,GAAwB2D,MAAnC,CADJ,KAGIwF,OAAO,IAAI3Q,CAAC,CAACrD,OAAb;AACJ8O,cAAAA,GAAG,GAAG,IAAIxS,IAAJ,CAAS,CAAT,EAAY,CAAC0X,OAAb,EAAsB,CAAtB,CAAN;AACA;;AACJ,iBAAK,CAAL;AAAO;AACHA,cAAAA,OAAO,GAAGlF,GAAG,CAACrB,MAAJ,GAAapK,CAAC,CAACP,WAAF,CAAe+H,MAAtC;AACA,kBAAI2D,MAAM,IAAI,IAAd,EACIwF,OAAO,IAAI3Q,CAAC,CAACP,WAAF,CAAe+H,MAAf,GAAwB2D,MAAnC,CADJ,KAGIwF,OAAO,IAAI3Q,CAAC,CAACnD,UAAb;AACJ4O,cAAAA,GAAG,GAAG,IAAIxS,IAAJ,CAAS,CAAT,EAAY,CAAC0X,OAAD,GAAW3Q,CAAC,CAAClE,UAAF,CAAc0L,MAArC,EAA6C,CAA7C,CAAN;AACA;AAhCR;;AAkCA,cAAIoJ,OAAY,GAAG5Q,CAAC,CAACnE,OAAF,CAAWkN,WAAX,EAAnB;AACA6H,UAAAA,OAAO,GAAG7K,IAAI,CAACQ,GAAL,CAASvG,CAAC,CAACvC,SAAF,GAAcmT,OAAO,CAAC5H,CAAtB,GAA0B4H,OAAO,CAAC3H,CAA3C,CAAV;AAEA,cAAI4H,UAAU,GAAG7Q,CAAC,CAACvC,SAAF,GAAcgO,GAAG,CAACzC,CAAlB,GAAsByC,GAAG,CAACxC,CAA3C;AACA,cAAI6H,SAAS,GAAG/K,IAAI,CAACQ,GAAL,CAAS,CAACvG,CAAC,CAAChB,UAAF,IAAgB,IAAhB,GAAuBgB,CAAC,CAAChB,UAAzB,GAAsC4R,OAAvC,IAAkDC,UAA3D,IAAyE,EAAzF,CA7DiG,CA8DjG;AAEA;;AACA,cAAIC,SAAJ,EAAe;AACX9Q,YAAAA,CAAC,CAACxD,WAAF,CAAeuU,cAAf,CAA8BtF,GAA9B,EAAmC2B,YAAnC;;AACApN,YAAAA,CAAC,CAACd,eAAF,GAAoB0M,MAApB;AACA5L,YAAAA,CAAC,CAACb,gBAAF,GAAuB,IAAI2N,IAAJ,EAAD,CAAaC,OAAb,KAAyB,IAA1B,GAAkCK,YAAvD,CAHW,CAIX;;AACApN,YAAAA,CAAC,CAACZ,WAAF,GAAgBY,CAAC,CAACgR,YAAF,CAAe,MAAM;AACjC,kBAAI,CAAChR,CAAC,CAACpB,gBAAP,EAAyB;AACrBoB,gBAAAA,CAAC,CAACrB,QAAF,GAAaqB,CAAC,CAACpB,gBAAF,GAAqB,KAAlC;AACH;;AACDoB,cAAAA,CAAC,CAAChB,UAAF,GACIgB,CAAC,CAACd,eAAF,GACAc,CAAC,CAACb,gBAAF,GACAa,CAAC,CAACZ,WAAF,GACA,IAJJ,CAJiC,CASjC;;AACA,kBAAIoR,UAAJ,EAAgB;AACZ;AACA,oBAAIvQ,IAAI,GAAGD,CAAC,CAACG,eAAF,CAAkByL,MAAlB,CAAX;;AACA,oBAAI3L,IAAJ,EAAU;AACNlH,kBAAAA,KAAK,CAACkH,IAAD,CAAL,CACK6L,EADL,CACQ,EADR,EACY;AAAExJ,oBAAAA,KAAK,EAAE;AAAT,mBADZ,EAEKwJ,EAFL,CAEQ,EAFR,EAEY;AAAExJ,oBAAAA,KAAK,EAAE;AAAT,mBAFZ,EAGKyJ,KAHL;AAIH;AACJ;AACJ,aApBe,EAoBbqB,YAAY,GAAG,EApBF,CAAhB;;AAsBA,gBAAIA,YAAY,IAAI,CAApB,EAAuB;AACnBpN,cAAAA,CAAC,CAACJ,YAAF;AACH;AACJ;AACJ;AACD;AACJ;AACA;;;AACImL,QAAAA,gBAAgB,GAAG;AACf,cAAI/K,CAAM,GAAG,IAAb;AACAA,UAAAA,CAAC,CAACnB,aAAF,GAAkB,IAAlB;AACA,cAAI6M,IAAJ,EAAeuF,MAAf;AAEA,cAAIjR,CAAC,CAACN,QAAN,EACIM,CAAC,CAACuJ,YAAF;AAEJ,cAAIC,IAAJ,EAAkBC,MAAlB,EAAkCC,OAAlC,EAAmDC,KAAnD;AACAH,UAAAA,IAAI,GAAGxJ,CAAC,CAAC/B,OAAT;AACAwL,UAAAA,MAAM,GAAGzJ,CAAC,CAAC9B,SAAX;AACAwL,UAAAA,OAAO,GAAG1J,CAAC,CAAC7B,UAAZ;AACAwL,UAAAA,KAAK,GAAG3J,CAAC,CAAC5B,QAAV;AAEA,cAAI2L,QAAiB,GAAG,KAAxB;;AACA,eAAK,IAAIrI,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG1B,CAAC,CAACnE,OAAF,CAAWoS,QAAX,CAAoB9D,MAAxB,IAAkC,CAACJ,QAAnD,EAA6DrI,CAAC,IAAI1B,CAAC,CAAC/C,WAApE,EAAiF;AAC7EyO,YAAAA,IAAI,GAAG1L,CAAC,CAACN,QAAF,GAAaM,CAAC,CAAC7C,WAAF,CAAcuE,CAAd,CAAb,GAAgC1B,CAAC,CAACuL,iBAAF,CAAoB7J,CAApB,CAAvC;;AACA,gBAAIgK,IAAJ,EAAU;AACNuF,cAAAA,MAAM,GAAGjR,CAAC,CAACvC,SAAF,GAAe,CAACiO,IAAI,CAACrB,GAAL,GAAWqB,IAAI,CAACtB,MAAjB,IAA2B,CAA1C,GAAgD6G,MAAM,GAAG,CAACvF,IAAI,CAACxB,IAAL,GAAYwB,IAAI,CAACzB,KAAlB,IAA2B,CAA7F;;AACA,sBAAQjK,CAAC,CAACpE,cAAV;AACI,qBAAK,CAAL;AAAO;AACH,sBAAI8P,IAAI,CAACzB,KAAL,IAAcN,KAAlB,EAAyB;AACrB3J,oBAAAA,CAAC,CAACnB,aAAF,GAAkB6M,IAAI,CAAClB,EAAvB;AACA,wBAAIb,KAAK,GAAGsH,MAAZ,EACIjR,CAAC,CAACnB,aAAF,IAAmBmB,CAAC,CAAC/C,WAArB;AACJ8M,oBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,qBAAK,CAAL;AAAO;AACH,sBAAI2B,IAAI,CAACxB,IAAL,IAAaT,MAAjB,EAAyB;AACrBzJ,oBAAAA,CAAC,CAACnB,aAAF,GAAkB6M,IAAI,CAAClB,EAAvB;AACA,wBAAIf,MAAM,GAAGwH,MAAb,EACIjR,CAAC,CAACnB,aAAF,IAAmBmB,CAAC,CAAC/C,WAArB;AACJ8M,oBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,qBAAK,CAAL;AAAO;AACH,sBAAI2B,IAAI,CAACtB,MAAL,IAAeZ,IAAnB,EAAyB;AACrBxJ,oBAAAA,CAAC,CAACnB,aAAF,GAAkB6M,IAAI,CAAClB,EAAvB;AACA,wBAAIhB,IAAI,GAAGyH,MAAX,EACIjR,CAAC,CAACnB,aAAF,IAAmBmB,CAAC,CAAC/C,WAArB;AACJ8M,oBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;;AACJ,qBAAK,CAAL;AAAO;AACH,sBAAI2B,IAAI,CAACrB,GAAL,IAAYX,OAAhB,EAAyB;AACrB1J,oBAAAA,CAAC,CAACnB,aAAF,GAAkB6M,IAAI,CAAClB,EAAvB;AACA,wBAAId,OAAO,GAAGuH,MAAd,EACIjR,CAAC,CAACnB,aAAF,IAAmBmB,CAAC,CAAC/C,WAArB;AACJ8M,oBAAAA,QAAQ,GAAG,IAAX;AACH;;AACD;AAhCR;AAkCH;AACJ,WAtDc,CAuDf;;;AACA2B,UAAAA,IAAI,GAAG1L,CAAC,CAACN,QAAF,GAAaM,CAAC,CAAC7C,WAAF,CAAc6C,CAAC,CAAChE,cAAF,GAAmB,CAAjC,CAAb,GAAmDgE,CAAC,CAACuL,iBAAF,CAAoBvL,CAAC,CAACL,SAAF,GAAc,CAAlC,CAA1D;;AACA,cAAI+L,IAAI,IAAIA,IAAI,CAAClB,EAAL,IAAWxK,CAAC,CAACL,SAAF,GAAc,CAArC,EAAwC;AACpCsR,YAAAA,MAAM,GAAGjR,CAAC,CAACvC,SAAF,GAAe,CAACiO,IAAI,CAACrB,GAAL,GAAWqB,IAAI,CAACtB,MAAjB,IAA2B,CAA1C,GAAgD6G,MAAM,GAAG,CAACvF,IAAI,CAACxB,IAAL,GAAYwB,IAAI,CAACzB,KAAlB,IAA2B,CAA7F;;AACA,oBAAQjK,CAAC,CAACpE,cAAV;AACI,mBAAK,CAAL;AAAO;AACH,oBAAI6N,MAAM,GAAGwH,MAAb,EACIjR,CAAC,CAACnB,aAAF,GAAkB6M,IAAI,CAAClB,EAAvB;AACJ;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAIb,KAAK,GAAGsH,MAAZ,EACIjR,CAAC,CAACnB,aAAF,GAAkB6M,IAAI,CAAClB,EAAvB;AACJ;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAId,OAAO,GAAGuH,MAAd,EACIjR,CAAC,CAACnB,aAAF,GAAkB6M,IAAI,CAAClB,EAAvB;AACJ;;AACJ,mBAAK,CAAL;AAAO;AACH,oBAAIhB,IAAI,GAAGyH,MAAX,EACIjR,CAAC,CAACnB,aAAF,GAAkB6M,IAAI,CAAClB,EAAvB;AACJ;AAhBR;AAkBH,WA7Ec,CA8Ef;;AACH,SAzhEuC,CA0hExC;;;AACA6C,QAAAA,OAAO,CAACD,YAAD,EAA4B;AAAA,cAA3BA,YAA2B;AAA3BA,YAAAA,YAA2B,GAAJ,EAAI;AAAA;;AAC/B;AACA,cAAI,CAAC,KAAKnM,WAAL,EAAL,EACI;AACJ,eAAKiQ,QAAL,CAAc,KAAKpS,UAAL,GAAkB,CAAhC,EAAmCsO,YAAnC;AACH,SAhiEuC,CAiiExC;;;AACAE,QAAAA,QAAQ,CAACF,YAAD,EAA4B;AAAA,cAA3BA,YAA2B;AAA3BA,YAAAA,YAA2B,GAAJ,EAAI;AAAA;;AAChC;AACA,cAAI,CAAC,KAAKnM,WAAL,EAAL,EACI;AACJ,eAAKiQ,QAAL,CAAc,KAAKpS,UAAL,GAAkB,CAAhC,EAAmCsO,YAAnC;AACH,SAviEuC,CAwiExC;;;AACA8D,QAAAA,QAAQ,CAACC,OAAD,EAAkB/D,YAAlB,EAAwC;AAC5C,cAAIpN,CAAM,GAAG,IAAb;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,EAAL,EACI;AACJ,cAAIjB,CAAC,CAACzF,UAAF,IAAgBX,SAAS,CAACY,IAA9B,EACI,OAAO0G,OAAO,CAACC,KAAR,CAAc,mEAAd,CAAP;AACJ,cAAIgQ,OAAO,GAAG,CAAV,IAAeA,OAAO,IAAInR,CAAC,CAACL,SAAhC,EACI;AACJ,cAAIK,CAAC,CAAClB,UAAF,IAAgBqS,OAApB,EACI,OATwC,CAU5C;;AACAnR,UAAAA,CAAC,CAAClB,UAAF,GAAeqS,OAAf;;AACA,cAAInR,CAAC,CAACoR,eAAN,EAAuB;AACnB9Y,YAAAA,YAAY,CAACmI,UAAb,CAAwB,CAACT,CAAC,CAACoR,eAAH,CAAxB,EAA6CD,OAA7C;AACH;;AACDnR,UAAAA,CAAC,CAAC4M,QAAF,CAAWuE,OAAX,EAAoB/D,YAApB;AACH,SAzjEuC,CA0jExC;;;AACAiE,QAAAA,cAAc,CAACrQ,QAAD,EAAmB;AAC7B,cAAIhB,CAAM,GAAG,IAAb;AACA,cAAI,CAACA,CAAC,CAACiB,WAAF,EAAL,EACI;AACJ,cAAI,CAACjB,CAAC,CAAC3C,QAAP,EACI,OAAO6D,OAAO,CAACC,KAAR,CAAc,sBAAd,CAAP;AACJ,cAAI,CAACnB,CAAC,CAACmO,WAAP,EACI,OAAOjN,OAAO,CAACC,KAAR,CAAc,qBAAd,CAAP;AACJnB,UAAAA,CAAC,CAACtC,WAAF,GAAgB,EAAhB;AACA,cAAI4T,IAAS,GAAG/Y,WAAW,CAACyH,CAAC,CAAC3C,QAAH,CAA3B;AACA,cAAImO,EAAe,GAAG8F,IAAI,CAACjR,YAAL,CAAkBrH,WAAlB,CAAtB;AACAgH,UAAAA,CAAC,CAACnE,OAAF,CAAWgS,QAAX,CAAoByD,IAApB;;AACA,eAAK,IAAI5P,CAAS,GAAG,CAArB,EAAwBA,CAAC,GAAGV,QAA5B,EAAsCU,CAAC,EAAvC,EAA2C;AACvCpJ,YAAAA,YAAY,CAACmI,UAAb,CAAwB,CAACT,CAAC,CAACmO,WAAH,CAAxB,EAAyCmD,IAAzC,EAA+C5P,CAA/C;;AACA,gBAAI8J,EAAE,CAAChE,MAAH,IAAaxH,CAAC,CAACxC,SAAF,CAAYgK,MAAzB,IAAmCgE,EAAE,CAACjE,KAAH,IAAYvH,CAAC,CAACxC,SAAF,CAAY+J,KAA/D,EAAsE;AAClEvH,cAAAA,CAAC,CAACtC,WAAF,CAAcgE,CAAd,IAAmB1B,CAAC,CAACvC,SAAF,GAAc+N,EAAE,CAAChE,MAAjB,GAA0BgE,EAAE,CAACjE,KAAhD;AACH;AACJ;;AACD,cAAI,CAACgK,MAAM,CAACC,IAAP,CAAYxR,CAAC,CAACtC,WAAd,EAA2ByM,MAAhC,EACInK,CAAC,CAACtC,WAAF,GAAgB,IAAhB;AACJ4T,UAAAA,IAAI,CAAC/B,gBAAL;AACA,cAAI+B,IAAI,CAACtP,OAAT,EACIsP,IAAI,CAACtP,OAAL;AACJ,iBAAOhC,CAAC,CAACtC,WAAT;AACH;;AAnlEuC,O;;;;;iBAGH/D,YAAY,CAACQ,I;;;;;;;iBAQ3B,I;;;;;;;iBAQI,I;;;;;;;iBAGKP,SAAS,CAACe,M;;;;;;;iBAoBZ,E;;;;;;;iBAQU,IAAIrC,YAAJ,E;;;;;;;iBAGZ,I;;;;;;;iBA4BH,K;;;;;;;iBAOI,K;;;;;;;iBAcD,K;;;;;;;iBAGE,C;;;;;;;iBAsBS,C;;;;;;;iBAMJ,IAAIA,YAAJ,E;;;;;;;iBAMCuB,YAAY,CAACoB,I;;;;;;;iBAQZ,IAAI3C,YAAJ,E;;;;;;;iBAMD,K;;;;;;;iBAmFR,C", "sourcesContent": ["/******************************************\n * <AUTHOR> <<EMAIL>>\n * @date 2020/12/9\n * @doc 列表组件.\n * @end\n ******************************************/\nconst { ccclass, property, disallowMultiple, menu, executionOrder, requireComponent } = _decorator;\nimport { _decorator, CCBoolean, CCFloat, CCInteger, Component, Enum, EventHandler, instantiate, isValid, Layout, Node, NodePool, Prefab, ScrollView, Size, tween, Tween, UITransform, Vec2, Vec3, Widget } from 'cc';\nimport { DEV } from 'cc/env';\nimport ListItem from './ListItem';\n\nenum TemplateType {\n    NODE = 1,\n    PREFAB = 2,\n}\n\nenum SlideType {\n    NORMAL = 1,//普通\n    ADHERING = 2,//粘附模式，将强制关闭滚动惯性\n    PAGE = 3,//页面模式，将强制关闭滚动惯性\n}\n\nenum SelectedType {\n    NONE = 0,\n    SINGLE = 1,//单选\n    MULT = 2,//多选\n}\n\n@ccclass\n@disallowMultiple()\n@menu('List')\n@requireComponent(ScrollView)\n//脚本生命周期回调的执行优先级。小于 0 的脚本将优先执行，大于 0 的脚本将最后执行。该优先级只对 onLoad, onEnable, start, update 和 lateUpdate 有效，对 onDisable 和 onDestroy 无效。\n@executionOrder(-5000)\nexport default class List extends Component {\n    //模板类型\n    @property({ type: Enum(TemplateType), tooltip: DEV ? '模板类型' : \"\", })\n    private templateType: TemplateType = TemplateType.NODE;\n    //模板Item（Node）\n    @property({\n        type: Node,\n        tooltip: DEV ? '模板Item' : \"\",\n        // @ts-ignore\n        visible() { return this.templateType == TemplateType.NODE; }\n    })\n    tmpNode: Node | null = null;\n    //模板Item（Prefab）\n    @property({\n        type: Prefab,\n        tooltip: DEV ? '模板Item' : \"\",\n        // @ts-ignore\n        visible() { return this.templateType == TemplateType.PREFAB; }\n    })\n    tmpPrefab: Prefab | null = null;\n    //滑动模式\n    @property({})\n    private _slideMode: SlideType = SlideType.NORMAL;\n    @property({\n        type: Enum(SlideType),\n        tooltip: DEV ? '滑动模式' : \"\"\n    })\n    set slideMode(val: SlideType) {\n        this._slideMode = val;\n    }\n    get slideMode() {\n        return this._slideMode;\n    }\n    //翻页作用距离\n    @property({\n        type: CCFloat,\n        range: [0, 1, .1],\n        tooltip: DEV ? '翻页作用距离' : \"\",\n        slide: true,\n        // @ts-ignore\n        visible() { return this._slideMode == SlideType.PAGE; }\n    })\n    public pageDistance: number = .3;\n    //页面改变事件\n    @property({\n        type: EventHandler,\n        tooltip: DEV ? '页面改变事件' : '',\n        // @ts-ignore\n        visible() { return this._slideMode == SlideType.PAGE; }\n    })\n    private pageChangeEvent: EventHandler = new EventHandler();\n    //是否为虚拟列表（动态列表）\n    @property({})\n    private _virtual: boolean = true;\n    @property({\n        type: CCBoolean,\n        tooltip: DEV ? '是否为虚拟列表（动态列表）' : ''\n    })\n    set virtual(val: boolean) {\n        if (val != null)\n            this._virtual = val;\n        if (!DEV && this._numItems != 0) {\n            this._onScrolling();\n        }\n    }\n    get virtual() {\n        return this._virtual;\n    }\n    //是否为循环列表\n    @property({\n        tooltip: DEV ? '是否为循环列表' : \"\",\n        visible() {\n            // @ts-ignore\n            let val: boolean = /*this.virtual &&*/ this.slideMode == SlideType.NORMAL;\n            if (!val) {\n                // @ts-ignore\n                this.cyclic = false;\n            }\n            return val;\n        }\n    })\n    public cyclic: boolean = false;\n    //缺省居中\n    @property({\n        tooltip: DEV ? 'Item数量不足以填满Content时，是否居中显示Item（不支持Grid布局）' : \"\",\n        // @ts-ignore\n        visible() { return this.virtual; }\n    })\n    public lackCenter: boolean = false;\n    //缺省可滑动\n    @property({\n        tooltip: DEV ? 'Item数量不足以填满Content时，是否可滑动' : \"\",\n        visible() {\n            // @ts-ignore\n            let val: boolean = this.virtual && !this.lackCenter;\n            if (!val) {\n                // @ts-ignore\n                this.lackSlide = false;\n            }\n            return val;\n        }\n    })\n    public lackSlide: boolean = false;\n    //刷新频率\n    @property({ type: CCInteger })\n    private _updateRate: number = 0;\n    @property({\n        type: CCInteger,\n        range: [0, 6, 1],\n        tooltip: DEV ? '刷新频率（值越大刷新频率越低、性能越高）' : \"\",\n        slide: true,\n    })\n    set updateRate(val: number) {\n        if (val >= 0 && val <= 6) {\n            this._updateRate = val;\n        }\n    }\n    get updateRate() {\n        return this._updateRate;\n    }\n    //分帧渲染（每帧渲染的Item数量（<=0时关闭分帧渲染））\n    @property({\n        type: CCInteger,\n        range: [0, 12, 1],\n        tooltip: DEV ? '逐帧渲染时，每帧渲染的Item数量（<=0时关闭分帧渲染）' : \"\",\n        slide: true,\n    })\n    public frameByFrameRenderNum: number = 0;\n    //渲染事件（渲染器）\n    @property({\n        type: EventHandler,\n        tooltip: DEV ? '渲染事件（渲染器）' : \"\",\n    })\n    public renderEvent: EventHandler = new EventHandler();\n    //选择模式\n    @property({\n        type: Enum(SelectedType),\n        tooltip: DEV ? '选择模式' : ''\n    })\n    public selectedMode: SelectedType = SelectedType.NONE;\n    //触发选择事件\n    @property({\n        type: EventHandler,\n        tooltip: DEV ? '触发选择事件' : '',\n        // @ts-ignore\n        visible() { return this.selectedMode > SelectedType.NONE; }\n    })\n    public selectedEvent: EventHandler = new EventHandler();\n    @property({\n        tooltip: DEV ? '是否重复响应单选事件' : '',\n        // @ts-ignore\n        visible() { return this.selectedMode == SelectedType.SINGLE; }\n    })\n    public repeatEventSingle: boolean = false;\n\n    //当前选择id\n    private _selectedId: number = -1;\n    private _lastSelectedId: number = -1;\n    private multSelected: number[] = [];\n    set selectedId(val: number) {\n        let t: any = this;\n        let item: any;\n        switch (t.selectedMode) {\n            case SelectedType.SINGLE: {\n                if (!t.repeatEventSingle && val == t._selectedId)\n                    return;\n                item = t.getItemByListId(val);\n                // if (!item && val >= 0)\n                //     return;\n                let listItem: ListItem;\n                if (t._selectedId >= 0)\n                    t._lastSelectedId = t._selectedId;\n                else //如果＜0则取消选择，把_lastSelectedId也置空吧，如果以后有特殊需求再改吧。\n                    t._lastSelectedId = null;\n                t._selectedId = val;\n                if (item) {\n                    listItem = item.getComponent(ListItem);\n                    listItem.selected = true;\n                }\n                if (t._lastSelectedId >= 0 && t._lastSelectedId != t._selectedId) {\n                    let lastItem: any = t.getItemByListId(t._lastSelectedId);\n                    if (lastItem) {\n                        lastItem.getComponent(ListItem).selected = false;\n                    }\n                }\n                if (t.selectedEvent) {\n                    EventHandler.emitEvents([t.selectedEvent], item, val % this._actualNumItems, t._lastSelectedId == null ? null : (t._lastSelectedId % this._actualNumItems));\n                }\n                break;\n            }\n            case SelectedType.MULT: {\n                item = t.getItemByListId(val);\n                if (!item)\n                    return;\n                let listItem = item.getComponent(ListItem);\n                if (t._selectedId >= 0)\n                    t._lastSelectedId = t._selectedId;\n                t._selectedId = val;\n                let bool: boolean = !listItem.selected;\n                listItem.selected = bool;\n                let sub: number = t.multSelected.indexOf(val);\n                if (bool && sub < 0) {\n                    t.multSelected.push(val);\n                } else if (!bool && sub >= 0) {\n                    t.multSelected.splice(sub, 1);\n                }\n                if (t.selectedEvent) {\n                    EventHandler.emitEvents([t.selectedEvent], item, val % this._actualNumItems, t._lastSelectedId == null ? null : (t._lastSelectedId % this._actualNumItems), bool);\n                }\n                break;\n            }\n        }\n    }\n    get selectedId() {\n        return this._selectedId;\n    }\n    private _forceUpdate: boolean = false;\n    private _align: number = 0;\n    private _horizontalDir: number = 0;\n    private _verticalDir: number = 0;\n    private _startAxis: number = 0;\n    private _alignCalcType: number = 0;\n    public content: Node | null = null;\n    private _contentUt: UITransform | null = null;\n    private firstListId: number = 0;\n    public displayItemNum: number = 0;\n    private _updateDone: boolean = true;\n    private _updateCounter: number = 0;\n    public _actualNumItems: number = 0;\n    private _cyclicNum: number = 0;\n    private _cyclicPos1: number = 0;\n    private _cyclicPos2: number = 0;\n    //列表数量\n    @property({\n        serializable: false\n    })\n    private _numItems: number = 0;\n    set numItems(val: number) {\n        let t = this;\n        if (!t.checkInited(false))\n            return;\n        if (val == null || val < 0) {\n            console.error('numItems set the wrong::', val);\n            return;\n        }\n        t._actualNumItems = t._numItems = val;\n        t._forceUpdate = true;\n\n        if (t._virtual) {\n            t._resizeContent();\n            if (t.cyclic) {\n                t._numItems = t._cyclicNum * t._numItems;\n            }\n            t._onScrolling();\n            if (!t.frameByFrameRenderNum && t.slideMode == SlideType.PAGE)\n                t.curPageNum = t.nearestListId;\n        } else {\n            if (t.cyclic) {\n                t._resizeContent();\n                t._numItems = t._cyclicNum * t._numItems;\n            }\n            let layout: Layout | null = t.content?.getComponent(Layout)!;\n            if (layout) {\n                layout.enabled = true;\n            }\n            t._delRedundantItem();\n\n            t.firstListId = 0;\n            if (t.frameByFrameRenderNum > 0) {\n                //先渲染几个出来\n                let len: number = t.frameByFrameRenderNum > t._numItems ? t._numItems : t.frameByFrameRenderNum;\n                for (let n: number = 0; n < len; n++) {\n                    t._createOrUpdateItem2(n);\n                }\n                if (t.frameByFrameRenderNum < t._numItems) {\n                    t._updateCounter = t.frameByFrameRenderNum;\n                    t._updateDone = false;\n                }\n            } else {\n                for (let n: number = 0; n < t._numItems; n++) {\n                    t._createOrUpdateItem2(n);\n                }\n                t.displayItemNum = t._numItems;\n            }\n        }\n    }\n    get numItems() {\n        return this._actualNumItems;\n    }\n\n    private _inited: boolean = false;\n    private _scrollView: ScrollView | null = null;\n    get scrollView() {\n        return this._scrollView;\n    }\n    private _layout: Layout | null = null;\n    private _resizeMode: number = 0;\n    private _topGap: number = 0;\n    private _rightGap: number = 0;\n    private _bottomGap: number = 0;\n    private _leftGap: number = 0;\n\n    private _columnGap: number = 0;\n    private _lineGap: number = 0;\n    private _colLineNum: number = 0;\n\n    private _lastDisplayData: number[] = [];\n    public displayData: any[] = [];\n    private _pool: NodePool | null = null;\n\n    private _itemTmp: any;\n    private _itemTmpUt: UITransform | null = null;\n    private _needUpdateWidget: boolean = false;\n    private _itemSize: Size | null = null;\n    private _sizeType: boolean = false;\n\n    public _customSize: any;\n\n    private frameCount: number = 0;\n    private _aniDelRuning: boolean = false;\n    private _aniDelCB: Function | null = null;\n    private _aniDelItem: any;\n    private _aniDelBeforePos: Vec2 | null = null;\n    private _aniDelBeforeScale: number = 0;\n    private viewTop: number = 0;\n    private viewRight: number = 0;\n    private viewBottom: number = 0;\n    private viewLeft: number = 0;\n\n    private _doneAfterUpdate: boolean = false;\n\n    private elasticTop: number = 0;\n    private elasticRight: number = 0;\n    private elasticBottom: number = 0;\n    private elasticLeft: number = 0;\n\n    private scrollToListId: number = 0;\n\n    private adhering: boolean = false;\n\n    private _adheringBarrier: boolean = false;\n    private nearestListId: number = 0;\n\n    public curPageNum: number = 0;\n    private _beganPos: any;\n    private _scrollPos: any;\n    private _curScrollIsTouch: boolean = false;//当前滑动是否为手动\n\n    private _scrollToListId: any;\n    private _scrollToEndTime: any;\n    private _scrollToSo: any;\n\n    private _lack: boolean = false;\n    private _allItemSize: number = 0;\n    private _allItemSizeNoEdge: number = 0;\n\n    private _scrollItem: any;//当前控制 ScrollView 滚动的 Item\n\n    private _thisNodeUt: UITransform | null = null;\n\n    //----------------------------------------------------------------------------\n\n    onLoad() {\n        this._init();\n    }\n\n    onDestroy() {\n        let t: any = this;\n        if (isValid(t._itemTmp))\n            t._itemTmp.destroy();\n        if (isValid(t.tmpNode))\n            t.tmpNode.destroy();\n        t._pool && t._pool.clear();\n    }\n\n    onEnable() {\n        // if (!EDITOR) \n        this._registerEvent();\n        this._init();\n        // 处理重新显示后，有可能上一次的动画移除还未播放完毕，导致动画卡住的问题\n        if (this._aniDelRuning) {\n            this._aniDelRuning = false;\n            if (this._aniDelItem) {\n                if (this._aniDelBeforePos) {\n                    this._aniDelItem.position = this._aniDelBeforePos;\n                    //delete this._aniDelBeforePos;\n                    this._aniDelBeforePos = null;\n                }\n                if (this._aniDelBeforeScale) {\n                    this._aniDelItem.scale = this._aniDelBeforeScale;\n                    //delete this._aniDelBeforeScale;\n                    this._aniDelBeforeScale = 0;\n                }\n                delete this._aniDelItem;\n            }\n            if (this._aniDelCB) {\n                this._aniDelCB();\n                //delete this._aniDelCB;\n                this._aniDelCB = null;\n            }\n        }\n    }\n\n    onDisable() {\n        // if (!EDITOR) \n        this._unregisterEvent();\n    }\n    //注册事件\n    _registerEvent() {\n        let t: any = this;\n        t.node.on(Node.EventType.TOUCH_START, t._onTouchStart, t);\n        t.node.on('touch-up', t._onTouchUp, t);\n        t.node.on(Node.EventType.TOUCH_CANCEL, t._onTouchCancelled, t);\n        t.node.on('scroll-began', t._onScrollBegan, t);\n        t.node.on('scroll-ended', t._onScrollEnded, t);\n        t.node.on('scrolling', t._onScrolling, t);\n        t.node.on(Node.EventType.SIZE_CHANGED, t._onSizeChanged, t);\n    }\n    //卸载事件\n    _unregisterEvent() {\n        let t: any = this;\n        t.node.off(Node.EventType.TOUCH_START, t._onTouchStart, t);\n        t.node.off('touch-up', t._onTouchUp, t);\n        t.node.off(Node.EventType.TOUCH_CANCEL, t._onTouchCancelled, t);\n        t.node.off('scroll-began', t._onScrollBegan, t);\n        t.node.off('scroll-ended', t._onScrollEnded, t);\n        t.node.off('scrolling', t._onScrolling, t);\n        t.node.off(Node.EventType.SIZE_CHANGED, t._onSizeChanged, t);\n    }\n    //初始化各种..\n    _init() {\n        let t: any = this;\n        if (t._inited)\n            return;\n\n        t._thisNodeUt = t.node.getComponent(UITransform);\n        t._scrollView = t.node.getComponent(ScrollView);\n\n        t.content = t._scrollView.content;\n        t._contentUt = t.content!.getComponent(UITransform);\n        if (!t.content) {\n            console.error(t.node.name + \"'s ScrollView unset content!\");\n            return;\n        }\n\n        t._layout = t.content!.getComponent(Layout);\n\n        t._align = t._layout.type; //排列模式\n        t._resizeMode = t._layout.resizeMode; //自适应模式\n        t._startAxis = t._layout.startAxis;\n\n        t._topGap = t._layout.paddingTop; //顶边距\n        t._rightGap = t._layout.paddingRight; //右边距\n        t._bottomGap = t._layout.paddingBottom; //底边距\n        t._leftGap = t._layout.paddingLeft; //左边距\n\n        t._columnGap = t._layout.spacingX; //列距\n        t._lineGap = t._layout.spacingY; //行距\n\n        t._colLineNum; //列数或行数（非GRID模式则=1，表示单列或单行）;\n\n        t._verticalDir = t._layout.verticalDirection; //垂直排列子节点的方向\n        t._horizontalDir = t._layout.horizontalDirection; //水平排列子节点的方向\n\n        t.setTemplateItem(instantiate(t.templateType == TemplateType.PREFAB ? t.tmpPrefab : t.tmpNode));\n\n        // 特定的滑动模式处理\n        if (t._slideMode == SlideType.ADHERING || t._slideMode == SlideType.PAGE) {\n            t._scrollView.inertia = false;\n            t._scrollView._onMouseWheel = function () {\n                return;\n            };\n        }\n        if (!t.virtual)         // lackCenter 仅支持 Virtual 模式\n            t.lackCenter = false;\n\n        t._lastDisplayData = []; //最后一次刷新的数据\n        t.displayData = []; //当前数据\n        t._pool = new NodePool();    //这是个池子..\n        t._forceUpdate = false;         //是否强制更新\n        t._updateCounter = 0;           //当前分帧渲染帧数\n        t._updateDone = true;           //分帧渲染是否完成\n\n        t.curPageNum = 0;               //当前页数\n\n        if (t.cyclic || 0) {\n            t._scrollView._processAutoScrolling = this._processAutoScrolling.bind(t);\n            t._scrollView._startBounceBackIfNeeded = function () {\n                return false;\n            }\n        }\n\n        switch (t._align) {\n            case Layout.Type.HORIZONTAL: {\n                switch (t._horizontalDir) {\n                    case Layout.HorizontalDirection.LEFT_TO_RIGHT:\n                        t._alignCalcType = 1;\n                        break;\n                    case Layout.HorizontalDirection.RIGHT_TO_LEFT:\n                        t._alignCalcType = 2;\n                        break;\n                }\n                break;\n            }\n            case Layout.Type.VERTICAL: {\n                switch (t._verticalDir) {\n                    case Layout.VerticalDirection.TOP_TO_BOTTOM:\n                        t._alignCalcType = 3;\n                        break;\n                    case Layout.VerticalDirection.BOTTOM_TO_TOP:\n                        t._alignCalcType = 4;\n                        break;\n                }\n                break;\n            }\n            case Layout.Type.GRID: {\n                switch (t._startAxis) {\n                    case Layout.AxisDirection.HORIZONTAL:\n                        switch (t._verticalDir) {\n                            case Layout.VerticalDirection.TOP_TO_BOTTOM:\n                                t._alignCalcType = 3;\n                                break;\n                            case Layout.VerticalDirection.BOTTOM_TO_TOP:\n                                t._alignCalcType = 4;\n                                break;\n                        }\n                        break;\n                    case Layout.AxisDirection.VERTICAL:\n                        switch (t._horizontalDir) {\n                            case Layout.HorizontalDirection.LEFT_TO_RIGHT:\n                                t._alignCalcType = 1;\n                                break;\n                            case Layout.HorizontalDirection.RIGHT_TO_LEFT:\n                                t._alignCalcType = 2;\n                                break;\n                        }\n                        break;\n                }\n                break;\n            }\n        }\n        // 清空 content\n        // t.content!.children.forEach((child: Node) => {\n        //     child.removeFromParent();\n        //     if (child != t.tmpNode && child.isValid)\n        //         child.destroy();\n        // });\n        t.content!.removeAllChildren();\n        t._inited = true;\n    }\n    /**\n     * 为了实现循环列表，必须覆写cc.ScrollView的某些函数\n     * @param {Number} dt\n     */\n    _processAutoScrolling(dt: number) {\n\n        // ------------- scroll-view 里定义的一些常量 -------------\n        const OUT_OF_BOUNDARY_BREAKING_FACTOR = 0.05;\n        const EPSILON = 1e-4;\n        const ZERO = new Vec3();\n        const quintEaseOut = (time: number) => {\n            time -= 1;\n            return (time * time * time * time * time + 1);\n        };\n        // ------------- scroll-view 里定义的一些常量 -------------\n\n        let sv: ScrollView = this._scrollView!;\n\n        const isAutoScrollBrake = sv['_isNecessaryAutoScrollBrake']();\n        const brakingFactor = isAutoScrollBrake ? OUT_OF_BOUNDARY_BREAKING_FACTOR : 1;\n        sv['_autoScrollAccumulatedTime'] += dt * (1 / brakingFactor);\n\n        let percentage = Math.min(1, sv['_autoScrollAccumulatedTime'] / sv['_autoScrollTotalTime']);\n        if (sv['_autoScrollAttenuate']) {\n            percentage = quintEaseOut(percentage);\n        }\n\n        const clonedAutoScrollTargetDelta = sv['_autoScrollTargetDelta'].clone();\n        clonedAutoScrollTargetDelta.multiplyScalar(percentage);\n        const clonedAutoScrollStartPosition = sv['_autoScrollStartPosition'].clone();\n        clonedAutoScrollStartPosition.add(clonedAutoScrollTargetDelta);\n        let reachedEnd = Math.abs(percentage - 1) <= EPSILON;\n\n        const fireEvent = Math.abs(percentage - 1) <= sv['getScrollEndedEventTiming']();\n        if (fireEvent && !sv['_isScrollEndedWithThresholdEventFired']) {\n            sv['_dispatchEvent'](ScrollView.EventType.SCROLL_ENG_WITH_THRESHOLD);\n            sv['_isScrollEndedWithThresholdEventFired'] = true;\n        }\n\n        if (sv['elastic']) {\n            const brakeOffsetPosition = clonedAutoScrollStartPosition.clone();\n            brakeOffsetPosition.subtract(sv['_autoScrollBrakingStartPosition']);\n            if (isAutoScrollBrake) {\n                brakeOffsetPosition.multiplyScalar(brakingFactor);\n            }\n            clonedAutoScrollStartPosition.set(sv['_autoScrollBrakingStartPosition']);\n            clonedAutoScrollStartPosition.add(brakeOffsetPosition);\n        } else {\n            const moveDelta = clonedAutoScrollStartPosition.clone();\n            //moveDelta.subtract(sv['_getContentPosition']());\n            moveDelta.subtract(sv.content!.position);\n            const outOfBoundary = sv['_getHowMuchOutOfBoundary'](moveDelta);\n            if (!outOfBoundary.equals(ZERO, EPSILON)) {\n                clonedAutoScrollStartPosition.add(outOfBoundary);\n                reachedEnd = true;\n            }\n        }\n\n        if (reachedEnd) {\n            sv['_autoScrolling'] = false;\n        }\n\n        const deltaMove = new Vec3(clonedAutoScrollStartPosition);\n        //deltaMove.subtract(sv['_getContentPosition']());\n        deltaMove.subtract(sv.content!.position);\n        sv['_clampDelta'](deltaMove);\n        sv['_moveContent'](deltaMove, reachedEnd);\n        sv['_dispatchEvent'](ScrollView.EventType.SCROLLING);\n\n        if (!sv['_autoScrolling']) {\n            sv['_isBouncing'] = false;\n            sv['_scrolling'] = false;\n            sv['_dispatchEvent'](ScrollView.EventType.SCROLL_ENDED);\n        }\n    }\n    //设置模板Item\n    setTemplateItem(item: any) {\n        if (!item)\n            return;\n        let t: any = this;\n        t._itemTmp = item;\n        t._itemTmpUt = item.getComponent(UITransform);\n\n        if (t._resizeMode == Layout.ResizeMode.CHILDREN)\n            t._itemSize = t._layout.cellSize;\n        else {\n            let itemUt: UITransform = item.getComponent(UITransform);\n            t._itemSize = new Size(itemUt.width, itemUt.height);\n        }\n\n        //获取ListItem，如果没有就取消选择模式\n        let com: any = item.getComponent(ListItem);\n        let remove = false;\n        if (!com)\n            remove = true;\n        // if (com) {\n        //     if (!com._btnCom && !item.getComponent(cc.Button)) {\n        //         remove = true;\n        //     }\n        // }\n        if (remove) {\n            t.selectedMode = SelectedType.NONE;\n        }\n        com = item.getComponent(Widget);\n        if (com && com.enabled) {\n            t._needUpdateWidget = true;\n        }\n        if (t.selectedMode == SelectedType.MULT)\n            t.multSelected = [];\n\n        switch (t._align) {\n            case Layout.Type.HORIZONTAL:\n                t._colLineNum = 1;\n                t._sizeType = false;\n                break;\n            case Layout.Type.VERTICAL:\n                t._colLineNum = 1;\n                t._sizeType = true;\n                break;\n            case Layout.Type.GRID:\n                switch (t._startAxis) {\n                    case Layout.AxisDirection.HORIZONTAL:\n                        //计算列数\n                        let trimW: number = t._contentUt!.width - t._leftGap - t._rightGap;\n                        t._colLineNum = Math.floor((trimW + t._columnGap) / (t._itemSize.width + t._columnGap));\n                        t._sizeType = true;\n                        break;\n                    case Layout.AxisDirection.VERTICAL:\n                        //计算行数\n                        let trimH: number = t._contentUt!.height - t._topGap - t._bottomGap;\n                        t._colLineNum = Math.floor((trimH + t._lineGap) / (t._itemSize.height + t._lineGap));\n                        t._sizeType = false;\n                        break;\n                }\n                break;\n        }\n    }\n    /**\n     * 检查是否初始化\n     * @param {Boolean} printLog 是否打印错误信息\n     * @returns\n     */\n    checkInited(printLog: boolean = true) {\n        if (!this._inited) {\n            if (printLog)\n                console.error('List initialization not completed!');\n            return false;\n        }\n        return true;\n    }\n    //禁用 Layout 组件，自行计算 Content Size\n    _resizeContent() {\n        let t: any = this;\n        let result: number = 0;\n\n        switch (t._align) {\n            case Layout.Type.HORIZONTAL: {\n                if (t._customSize) {\n                    let fixed: any = t._getFixedSize(undefined);\n                    result = t._leftGap + fixed.val + (t._itemSize!.width * (t._numItems - fixed.count)) + (t._columnGap * (t._numItems - 1)) + t._rightGap;\n                } else {\n                    result = t._leftGap + (t._itemSize!.width * t._numItems) + (t._columnGap * (t._numItems - 1)) + t._rightGap;\n                }\n                break;\n            }\n            case Layout.Type.VERTICAL: {\n                if (t._customSize) {\n                    let fixed: any = t._getFixedSize(undefined);\n                    result = t._topGap + fixed.val + (t._itemSize!.height * (t._numItems - fixed.count)) + (t._lineGap * (t._numItems - 1)) + t._bottomGap;\n                } else {\n                    result = t._topGap + (t._itemSize!.height * t._numItems) + (t._lineGap * (t._numItems - 1)) + t._bottomGap;\n                }\n                break;\n            }\n            case Layout.Type.GRID: {\n                //网格模式不支持居中\n                if (t.lackCenter)\n                    t.lackCenter = false;\n                switch (t._startAxis) {\n                    case Layout.AxisDirection.HORIZONTAL:\n                        let lineNum: number = Math.ceil(t._numItems / t._colLineNum);\n                        result = t._topGap + (t._itemSize!.height * lineNum) + (t._lineGap * (lineNum - 1)) + t._bottomGap;\n                        break;\n                    case Layout.AxisDirection.VERTICAL:\n                        let colNum: number = Math.ceil(t._numItems / t._colLineNum);\n                        result = t._leftGap + (t._itemSize!.width * colNum) + (t._columnGap * (colNum - 1)) + t._rightGap;\n                        break;\n                }\n                break;\n            }\n        }\n\n        let layout: Layout = t.content!.getComponent(Layout)!;\n        if (layout)\n            layout.enabled = false;\n\n        t._allItemSize = result;\n        t._allItemSizeNoEdge = t._allItemSize - (t._sizeType ? (t._topGap + t._bottomGap) : (t._leftGap + t._rightGap));\n\n        if (t.cyclic) {\n            let totalSize: number = (t._sizeType ? t._thisNodeUt!.height : t._thisNodeUt!.width);\n\n            t._cyclicPos1 = 0;\n            totalSize -= t._cyclicPos1;\n            t._cyclicNum = Math.ceil(totalSize / t._allItemSizeNoEdge) + 1;\n            let spacing: number = t._sizeType ? t._lineGap : t._columnGap;\n            t._cyclicPos2 = t._cyclicPos1 + t._allItemSizeNoEdge + spacing;\n            t._cyclicAllItemSize = t._allItemSize + (t._allItemSizeNoEdge * (t._cyclicNum - 1)) + (spacing * (t._cyclicNum - 1));\n            t._cycilcAllItemSizeNoEdge = t._allItemSizeNoEdge * t._cyclicNum;\n            t._cycilcAllItemSizeNoEdge += spacing * (t._cyclicNum - 1);\n            // cc.log('_cyclicNum ->', t._cyclicNum, t._allItemSizeNoEdge, t._allItemSize, t._cyclicPos1, t._cyclicPos2);\n        }\n\n        t._lack = !t.cyclic && t._allItemSize < (t._sizeType ? t._thisNodeUt!.height : t._thisNodeUt!.width);\n        let slideOffset: number = ((!t._lack || !t.lackCenter) && t.lackSlide) ? 0 : .1;\n\n        let targetWH: number = t._lack ? ((t._sizeType ? t._thisNodeUt!.height : t._thisNodeUt!.width) - slideOffset) : (t.cyclic ? t._cyclicAllItemSize : t._allItemSize);\n        if (targetWH < 0)\n            targetWH = 0;\n\n        if (t._sizeType) {\n            t._contentUt!.height = targetWH;\n        } else {\n            t._contentUt!.width = targetWH;\n        }\n\n        // cc.log('_resizeContent()  numItems =', t._numItems, '，content =', t.content);\n    }\n\n    //滚动进行时...\n    _onScrolling(ev?: Event /*= null*/) {\n        if (this.frameCount == null)\n            this.frameCount = this._updateRate;\n        if (!this._forceUpdate && (ev && ev.type != 'scroll-ended') && this.frameCount > 0) {\n            this.frameCount--;\n            return;\n        } else\n            this.frameCount = this._updateRate;\n\n        if (this._aniDelRuning)\n            return;\n\n        //循环列表处理\n        if (this.cyclic) {\n            let scrollPos: any = this.content!.getPosition();\n            scrollPos = this._sizeType ? scrollPos.y : scrollPos.x;\n\n            let addVal = this._allItemSizeNoEdge + (this._sizeType ? this._lineGap : this._columnGap);\n            let add: any = this._sizeType ? new Vec3(0, addVal, 0) : new Vec3(addVal, 0, 0);\n\n            let contentPos = this.content!.getPosition();\n\n            switch (this._alignCalcType) {\n                case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                    if (scrollPos > -this._cyclicPos1) {\n                        contentPos.set(-this._cyclicPos2, contentPos.y, contentPos.z);\n                        this.content!.setPosition(contentPos);\n                        if (this._scrollView!.isAutoScrolling()) {\n                            this._scrollView!['_autoScrollStartPosition'] = this._scrollView!['_autoScrollStartPosition'].subtract(add);\n                        }\n                        // if (this._beganPos) {\n                        //     this._beganPos += add;\n                        // }\n                    } else if (scrollPos < -this._cyclicPos2) {\n                        contentPos.set(-this._cyclicPos1, contentPos.y, contentPos.z);\n                        this.content!.setPosition(contentPos);\n                        if (this._scrollView!.isAutoScrolling()) {\n                            this._scrollView!['_autoScrollStartPosition'] = this._scrollView!['_autoScrollStartPosition'].add(add);\n                        }\n                        // if (this._beganPos) {\n                        //     this._beganPos -= add;\n                        // }\n                    }\n                    break;\n                case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                    if (scrollPos < this._cyclicPos1) {\n                        contentPos.set(this._cyclicPos2, contentPos.y, contentPos.z);\n                        this.content!.setPosition(contentPos);\n                        if (this._scrollView!.isAutoScrolling()) {\n                            this._scrollView!['_autoScrollStartPosition'] = this._scrollView!['_autoScrollStartPosition'].add(add);\n                        }\n                    } else if (scrollPos > this._cyclicPos2) {\n                        contentPos.set(this._cyclicPos1, contentPos.y, contentPos.z);\n                        this.content!.setPosition(contentPos);\n                        if (this._scrollView!.isAutoScrolling()) {\n                            this._scrollView!['_autoScrollStartPosition'] = this._scrollView!['_autoScrollStartPosition'].subtract(add);\n                        }\n                    }\n                    break;\n                case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                    if (scrollPos < this._cyclicPos1) {\n                        contentPos.set(contentPos.x, this._cyclicPos2, contentPos.z);\n                        this.content!.setPosition(contentPos);\n                        if (this._scrollView!.isAutoScrolling()) {\n                            this._scrollView!['_autoScrollStartPosition'] = this._scrollView!['_autoScrollStartPosition'].add(add);\n                        }\n                    } else if (scrollPos > this._cyclicPos2) {\n                        contentPos.set(contentPos.x, this._cyclicPos1, contentPos.z);\n                        this.content!.setPosition(contentPos);\n                        if (this._scrollView!.isAutoScrolling()) {\n                            this._scrollView!['_autoScrollStartPosition'] = this._scrollView!['_autoScrollStartPosition'].subtract(add);\n                        }\n                    }\n                    break;\n                case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                    if (scrollPos > -this._cyclicPos1) {\n                        contentPos.set(contentPos.x, -this._cyclicPos2, contentPos.z);\n                        this.content!.setPosition(contentPos);\n                        if (this._scrollView!.isAutoScrolling()) {\n                            this._scrollView!['_autoScrollStartPosition'] = this._scrollView!['_autoScrollStartPosition'].subtract(add);\n                        }\n                    } else if (scrollPos < -this._cyclicPos2) {\n                        contentPos.set(contentPos.x, -this._cyclicPos1, contentPos.z);\n                        this.content!.setPosition(contentPos);\n                        if (this._scrollView!.isAutoScrolling()) {\n                            this._scrollView!['_autoScrollStartPosition'] = this._scrollView!['_autoScrollStartPosition'].add(add);\n                        }\n                    }\n                    break;\n            }\n        }\n\n        this._calcViewPos();\n\n        let vTop: number = 0, vRight: number = 0, vBottom: number = 0, vLeft: number = 0;\n        if (this._sizeType) {\n            vTop = this.viewTop;\n            vBottom = this.viewBottom;\n        } else {\n            vRight = this.viewRight;\n            vLeft = this.viewLeft;\n        }\n\n        if (this._virtual) {\n            this.displayData = [];\n            let itemPos: any;\n\n            let curId: number = 0;\n            let endId: number = this._numItems - 1;\n\n            if (this._customSize) {\n                let breakFor: boolean = false;\n                //如果该item的位置在可视区域内，就推入displayData\n                for (; curId <= endId && !breakFor; curId++) {\n                    itemPos = this._calcItemPos(curId);\n                    switch (this._align) {\n                        case Layout.Type.HORIZONTAL:\n                            if (itemPos.right >= vLeft && itemPos.left <= vRight) {\n                                this.displayData.push(itemPos);\n                            } else if (curId != 0 && this.displayData.length > 0) {\n                                breakFor = true;\n                            }\n                            break;\n                        case Layout.Type.VERTICAL:\n                            if (itemPos.bottom <= vTop && itemPos.top >= vBottom) {\n                                this.displayData.push(itemPos);\n                            } else if (curId != 0 && this.displayData.length > 0) {\n                                breakFor = true;\n                            }\n                            break;\n                        case Layout.Type.GRID:\n                            switch (this._startAxis) {\n                                case Layout.AxisDirection.HORIZONTAL:\n                                    if (itemPos.bottom <= vTop && itemPos.top >= vBottom) {\n                                        this.displayData.push(itemPos);\n                                    } else if (curId != 0 && this.displayData.length > 0) {\n                                        breakFor = true;\n                                    }\n                                    break;\n                                case Layout.AxisDirection.VERTICAL:\n                                    if (itemPos.right >= vLeft && itemPos.left <= vRight) {\n                                        this.displayData.push(itemPos);\n                                    } else if (curId != 0 && this.displayData.length > 0) {\n                                        breakFor = true;\n                                    }\n                                    break;\n                            }\n                            break;\n                    }\n                }\n            } else {\n                let ww: number = this._itemSize!.width + this._columnGap;\n                let hh: number = this._itemSize!.height + this._lineGap;\n                switch (this._alignCalcType) {\n                    case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                        curId = (vLeft - this._leftGap) / ww;\n                        endId = (vRight - this._leftGap) / ww;\n                        break;\n                    case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                        curId = (-vRight - this._rightGap) / ww;\n                        endId = (-vLeft - this._rightGap) / ww;\n                        break;\n                    case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                        curId = (-vTop - this._topGap) / hh;\n                        endId = (-vBottom - this._topGap) / hh;\n                        break;\n                    case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                        curId = (vBottom - this._bottomGap) / hh;\n                        endId = (vTop - this._bottomGap) / hh;\n                        break;\n                }\n                curId = Math.floor(curId) * this._colLineNum;\n                endId = Math.ceil(endId) * this._colLineNum;\n                endId--;\n                if (curId < 0)\n                    curId = 0;\n                if (endId >= this._numItems)\n                    endId = this._numItems - 1;\n                for (; curId <= endId; curId++) {\n                    this.displayData.push(this._calcItemPos(curId));\n                }\n            }\n            this._delRedundantItem();\n            if (this.displayData.length <= 0 || !this._numItems) { //if none, delete all.\n                this._lastDisplayData = [];\n                return;\n            }\n            this.firstListId = this.displayData[0].id;\n            this.displayItemNum = this.displayData.length;\n\n            let len: number = this._lastDisplayData.length;\n\n            let haveDataChange: boolean = this.displayItemNum != len;\n            if (haveDataChange) {\n                // 如果是逐帧渲染，需要排序\n                if (this.frameByFrameRenderNum > 0) {\n                    this._lastDisplayData.sort((a, b) => { return a - b });\n                }\n                // 因List的显示数据是有序的，所以只需要判断数组长度是否相等，以及头、尾两个元素是否相等即可。\n                haveDataChange = this.firstListId != this._lastDisplayData[0] || this.displayData[this.displayItemNum - 1].id != this._lastDisplayData[len - 1];\n            }\n\n            if (this._forceUpdate || haveDataChange) {    //如果是强制更新\n                if (this.frameByFrameRenderNum > 0) {\n                    // if (this._updateDone) {\n                    // this._lastDisplayData = [];\n                    //逐帧渲染\n                    if (this._numItems > 0) {\n                        if (!this._updateDone) {\n                            this._doneAfterUpdate = true;\n                        } else {\n                            this._updateCounter = 0;\n                        }\n                        this._updateDone = false;\n                    } else {\n                        this._updateCounter = 0;\n                        this._updateDone = true;\n                    }\n                    // }\n                } else {\n                    //直接渲染\n                    this._lastDisplayData = [];\n                    // cc.log('List Display Data II::', this.displayData);\n                    for (let c = 0; c < this.displayItemNum; c++) {\n                        this._createOrUpdateItem(this.displayData[c]);\n                    }\n                    this._forceUpdate = false;\n                }\n            }\n            this._calcNearestItem();\n        }\n    }\n    //计算可视范围\n    _calcViewPos() {\n        let scrollPos: any = this.content!.getPosition();\n        switch (this._alignCalcType) {\n            case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                this.elasticLeft = scrollPos.x > 0 ? scrollPos.x : 0;\n                this.viewLeft = (scrollPos.x < 0 ? -scrollPos.x : 0) - this.elasticLeft;\n\n                this.viewRight = this.viewLeft + this._thisNodeUt!.width;\n                this.elasticRight = this.viewRight > this._contentUt!.width ? Math.abs(this.viewRight - this._contentUt!.width) : 0;\n                this.viewRight += this.elasticRight;\n                // cc.log(this.elasticLeft, this.elasticRight, this.viewLeft, this.viewRight);\n                break;\n            case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                this.elasticRight = scrollPos.x < 0 ? -scrollPos.x : 0;\n                this.viewRight = (scrollPos.x > 0 ? -scrollPos.x : 0) + this.elasticRight;\n                this.viewLeft = this.viewRight - this._thisNodeUt!.width;\n                this.elasticLeft = this.viewLeft < -this._contentUt!.width ? Math.abs(this.viewLeft + this._contentUt!.width) : 0;\n                this.viewLeft -= this.elasticLeft;\n                // cc.log(this.elasticLeft, this.elasticRight, this.viewLeft, this.viewRight);\n                break;\n            case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                this.elasticTop = scrollPos.y < 0 ? Math.abs(scrollPos.y) : 0;\n                this.viewTop = (scrollPos.y > 0 ? -scrollPos.y : 0) + this.elasticTop;\n                this.viewBottom = this.viewTop - this._thisNodeUt!.height;\n                this.elasticBottom = this.viewBottom < -this._contentUt!.height ? Math.abs(this.viewBottom + this._contentUt!.height) : 0;\n                this.viewBottom += this.elasticBottom;\n                // cc.log(this.elasticTop, this.elasticBottom, this.viewTop, this.viewBottom);\n                break;\n            case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                this.elasticBottom = scrollPos.y > 0 ? Math.abs(scrollPos.y) : 0;\n                this.viewBottom = (scrollPos.y < 0 ? -scrollPos.y : 0) - this.elasticBottom;\n                this.viewTop = this.viewBottom + this._thisNodeUt!.height;\n                this.elasticTop = this.viewTop > this._contentUt!.height ? Math.abs(this.viewTop - this._contentUt!.height) : 0;\n                this.viewTop -= this.elasticTop;\n                // cc.log(this.elasticTop, this.elasticBottom, this.viewTop, this.viewBottom);\n                break;\n        }\n    }\n    //计算位置 根据id\n    _calcItemPos(id: number) {\n        let width: number, height: number, top: number, bottom: number, left: number, right: number, itemX: number, itemY: number;\n        width = height = top = bottom = left = right = itemX = itemY = 0\n        switch (this._align) {\n            case Layout.Type.HORIZONTAL:\n                switch (this._horizontalDir) {\n                    case Layout.HorizontalDirection.LEFT_TO_RIGHT: {\n                        if (this._customSize) {\n                            let fixed: any = this._getFixedSize(id);\n                            left = this._leftGap + ((this._itemSize!.width + this._columnGap) * (id - fixed.count)) + (fixed.val + (this._columnGap * fixed.count));\n                            let cs: number = this._customSize[id];\n                            width = (cs > 0 ? cs : this._itemSize!.width);\n                        } else {\n                            left = this._leftGap + ((this._itemSize!.width + this._columnGap) * id);\n                            width = this._itemSize!.width;\n                        }\n                        if (this.lackCenter) {\n                            left -= this._leftGap;\n                            let offset: number = (this._contentUt!.width / 2) - (this._allItemSizeNoEdge / 2);\n                            left += offset;\n                        }\n                        right = left + width;\n                        return {\n                            id: id,\n                            left: left,\n                            right: right,\n                            x: left + (this._itemTmpUt!.anchorX * width),\n                            y: this._itemTmp.y,\n                        };\n                    }\n                    case Layout.HorizontalDirection.RIGHT_TO_LEFT: {\n                        if (this._customSize) {\n                            let fixed: any = this._getFixedSize(id);\n                            right = -this._rightGap - ((this._itemSize!.width + this._columnGap) * (id - fixed.count)) - (fixed.val + (this._columnGap * fixed.count));\n                            let cs: number = this._customSize[id];\n                            width = (cs > 0 ? cs : this._itemSize!.width);\n                        } else {\n                            right = -this._rightGap - ((this._itemSize!.width + this._columnGap) * id);\n                            width = this._itemSize!.width;\n                        }\n                        if (this.lackCenter) {\n                            right += this._rightGap;\n                            let offset: number = (this._contentUt!.width / 2) - (this._allItemSizeNoEdge / 2);\n                            right -= offset;\n                        }\n                        left = right - width;\n                        return {\n                            id: id,\n                            right: right,\n                            left: left,\n                            x: left + (this._itemTmpUt!.anchorX * width),\n                            y: this._itemTmp.y,\n                        };\n                    }\n                }\n                break;\n            case Layout.Type.VERTICAL: {\n                switch (this._verticalDir) {\n                    case Layout.VerticalDirection.TOP_TO_BOTTOM: {\n                        if (this._customSize) {\n                            let fixed: any = this._getFixedSize(id);\n                            top = -this._topGap - ((this._itemSize!.height + this._lineGap) * (id - fixed.count)) - (fixed.val + (this._lineGap * fixed.count));\n                            let cs: number = this._customSize[id];\n                            height = (cs > 0 ? cs : this._itemSize!.height);\n                        } else {\n                            top = -this._topGap - ((this._itemSize!.height + this._lineGap) * id);\n                            height = this._itemSize!.height;\n                        }\n                        if (this.lackCenter) {\n                            top += this._topGap;\n                            let offset: number = (this._contentUt!.height / 2) - (this._allItemSizeNoEdge / 2);\n                            top -= offset;\n                        }\n                        bottom = top - height;\n                        return {\n                            id: id,\n                            top: top,\n                            bottom: bottom,\n                            x: this._itemTmp.x,\n                            y: bottom + (this._itemTmpUt!.anchorY * height),\n                        };\n                    }\n                    case Layout.VerticalDirection.BOTTOM_TO_TOP: {\n                        if (this._customSize) {\n                            let fixed: any = this._getFixedSize(id);\n                            bottom = this._bottomGap + ((this._itemSize!.height + this._lineGap) * (id - fixed.count)) + (fixed.val + (this._lineGap * fixed.count));\n                            let cs: number = this._customSize[id];\n                            height = (cs > 0 ? cs : this._itemSize!.height);\n                        } else {\n                            bottom = this._bottomGap + ((this._itemSize!.height + this._lineGap) * id);\n                            height = this._itemSize!.height;\n                        }\n                        if (this.lackCenter) {\n                            bottom -= this._bottomGap;\n                            let offset: number = (this._contentUt!.height / 2) - (this._allItemSizeNoEdge / 2);\n                            bottom += offset;\n                        }\n                        top = bottom + height;\n                        return {\n                            id: id,\n                            top: top,\n                            bottom: bottom,\n                            x: this._itemTmp.x,\n                            y: bottom + (this._itemTmpUt!.anchorY * height),\n                        };\n                        break;\n                    }\n                }\n            }\n            case Layout.Type.GRID: {\n                let colLine: number = Math.floor(id / this._colLineNum);\n                switch (this._startAxis) {\n                    case Layout.AxisDirection.HORIZONTAL: {\n                        switch (this._verticalDir) {\n                            case Layout.VerticalDirection.TOP_TO_BOTTOM: {\n                                top = -this._topGap - ((this._itemSize!.height + this._lineGap) * colLine);\n                                bottom = top - this._itemSize!.height;\n                                itemY = bottom + (this._itemTmpUt!.anchorY * this._itemSize!.height);\n                                break;\n                            }\n                            case Layout.VerticalDirection.BOTTOM_TO_TOP: {\n                                bottom = this._bottomGap + ((this._itemSize!.height + this._lineGap) * colLine);\n                                top = bottom + this._itemSize!.height;\n                                itemY = bottom + (this._itemTmpUt!.anchorY * this._itemSize!.height);\n                                break;\n                            }\n                        }\n                        itemX = this._leftGap + ((id % this._colLineNum) * (this._itemSize!.width + this._columnGap));\n                        switch (this._horizontalDir) {\n                            case Layout.HorizontalDirection.LEFT_TO_RIGHT: {\n                                itemX += (this._itemTmpUt!.anchorX * this._itemSize!.width);\n                                itemX -= (this._contentUt!.anchorX * this._contentUt!.width);\n                                break;\n                            }\n                            case Layout.HorizontalDirection.RIGHT_TO_LEFT: {\n                                itemX += ((1 - this._itemTmpUt!.anchorX) * this._itemSize!.width);\n                                itemX -= ((1 - this._contentUt!.anchorX) * this._contentUt!.width);\n                                itemX *= -1;\n                                break;\n                            }\n                        }\n                        return {\n                            id: id,\n                            top: top,\n                            bottom: bottom,\n                            x: itemX,\n                            y: itemY,\n                        };\n                    }\n                    case Layout.AxisDirection.VERTICAL: {\n                        switch (this._horizontalDir) {\n                            case Layout.HorizontalDirection.LEFT_TO_RIGHT: {\n                                left = this._leftGap + ((this._itemSize!.width + this._columnGap) * colLine);\n                                right = left + this._itemSize!.width;\n                                itemX = left + (this._itemTmpUt!.anchorX * this._itemSize!.width);\n                                itemX -= (this._contentUt!.anchorX * this._contentUt!.width);\n                                break;\n                            }\n                            case Layout.HorizontalDirection.RIGHT_TO_LEFT: {\n                                right = -this._rightGap - ((this._itemSize!.width + this._columnGap) * colLine);\n                                left = right - this._itemSize!.width;\n                                itemX = left + (this._itemTmpUt!.anchorX * this._itemSize!.width);\n                                itemX += ((1 - this._contentUt!.anchorX) * this._contentUt!.width);\n                                break;\n                            }\n                        }\n                        itemY = -this._topGap - ((id % this._colLineNum) * (this._itemSize!.height + this._lineGap));\n                        switch (this._verticalDir) {\n                            case Layout.VerticalDirection.TOP_TO_BOTTOM: {\n                                itemY -= ((1 - this._itemTmpUt!.anchorY) * this._itemSize!.height);\n                                itemY += ((1 - this._contentUt!.anchorY) * this._contentUt!.height);\n                                break;\n                            }\n                            case Layout.VerticalDirection.BOTTOM_TO_TOP: {\n                                itemY -= ((this._itemTmpUt!.anchorY) * this._itemSize!.height);\n                                itemY += (this._contentUt!.anchorY * this._contentUt!.height);\n                                itemY *= -1;\n                                break;\n                            }\n                        }\n                        return {\n                            id: id,\n                            left: left,\n                            right: right,\n                            x: itemX,\n                            y: itemY,\n                        };\n                    }\n                }\n                break;\n            }\n        }\n    }\n    //计算已存在的Item的位置\n    _calcExistItemPos(id: number) {\n        let item: any = this.getItemByListId(id);\n        if (!item)\n            return null;\n        let ut: UITransform = item.getComponent(UITransform);\n        let pos: Vec3 = item.getPosition();\n        let data: any = {\n            id: id,\n            x: pos.x,\n            y: pos.y,\n        }\n        if (this._sizeType) {\n            data.top = pos.y + (ut.height * (1 - ut.anchorY));\n            data.bottom = pos.y - (ut.height * ut.anchorY);\n        } else {\n            data.left = pos.x - (ut.width * ut.anchorX);\n            data.right = pos.x + (ut.width * (1 - ut.anchorX));\n        }\n        return data;\n    }\n    //获取Item位置\n    getItemPos(id: number) {\n        if (this._virtual)\n            return this._calcItemPos(id);\n        else {\n            if (this.frameByFrameRenderNum)\n                return this._calcItemPos(id);\n            else\n                return this._calcExistItemPos(id);\n        }\n    }\n    //获取固定尺寸\n    _getFixedSize(listId?: number) {\n        if (!this._customSize)\n            return null;\n        if (listId == null || listId == undefined)\n            listId = this._numItems;\n        let fixed: number = 0;\n        let count: number = 0;\n        for (let id in this._customSize) {\n            if (parseInt(id) < listId) {\n                fixed += this._customSize[id];\n                count++;\n            }\n        }\n        return {\n            val: fixed,\n            count: count,\n        }\n    }\n    //滚动结束时..\n    _onScrollBegan() {\n        this._beganPos = this._sizeType ? this.viewTop : this.viewLeft;\n    }\n    //滚动结束时..\n    _onScrollEnded() {\n        let t: any = this;\n        t._curScrollIsTouch = false;\n        if (t.scrollToListId != null) {\n            let item: any = t.getItemByListId(t.scrollToListId);\n            t.scrollToListId = null;\n            if (item) {\n                tween(item)\n                    .to(.1, { scale: 1.06 })\n                    .to(.1, { scale: 1 })\n                    .start();\n            }\n        }\n        t._onScrolling();\n\n        if (t._slideMode == SlideType.ADHERING &&\n            !t.adhering\n        ) {\n            //cc.log(t.adhering, t._scrollView.isAutoScrolling(), t._scrollView.isScrolling());\n            t.adhere();\n        } else if (t._slideMode == SlideType.PAGE) {\n            if (t._beganPos != null && t._curScrollIsTouch) {\n                this._pageAdhere();\n            } else {\n                t.adhere();\n            }\n        }\n    }\n    // 触摸时\n    _onTouchStart(ev: any, captureListeners: any) {\n        if (this._scrollView!['_hasNestedViewGroup'](ev, captureListeners))\n            return;\n        this._curScrollIsTouch = true;\n        //let isMe = ev.eventPhase === Event.AT_TARGET && ev.target === this.node;\n        let isMe = ev.target === this.node;\n        if (!isMe) {\n            let itemNode: any = ev.target;\n            while (itemNode._listId == null && itemNode.parent)\n                itemNode = itemNode.parent;\n            this._scrollItem = itemNode._listId != null ? itemNode : ev.target;\n        }\n    }\n    //触摸抬起时..\n    _onTouchUp() {\n        let t: any = this;\n        t._scrollPos = null;\n        if (t._slideMode == SlideType.ADHERING) {\n            if (this.adhering)\n                this._adheringBarrier = true;\n            t.adhere();\n        } else if (t._slideMode == SlideType.PAGE) {\n            if (t._beganPos != null) {\n                this._pageAdhere();\n            } else {\n                t.adhere();\n            }\n        }\n        this._scrollItem = null;\n    }\n\n    _onTouchCancelled(ev: any, captureListeners: any) {\n        let t = this;\n        if (t._scrollView!['_hasNestedViewGroup'](ev, captureListeners) || ev.simulate)\n            return;\n\n        t._scrollPos = null;\n        if (t._slideMode == SlideType.ADHERING) {\n            if (t.adhering)\n                t._adheringBarrier = true;\n            t.adhere();\n        } else if (t._slideMode == SlideType.PAGE) {\n            if (t._beganPos != null) {\n                t._pageAdhere();\n            } else {\n                t.adhere();\n            }\n        }\n        this._scrollItem = null;\n    }\n    //当尺寸改变\n    _onSizeChanged() {\n        if (this.checkInited(false))\n            this._onScrolling();\n    }\n    //当Item自适应\n    _onItemAdaptive(item: any) {\n        let ut: UITransform = item.getComponent(UITransform);\n        // if (this.checkInited(false)) {\n        if (\n            (!this._sizeType && ut.width != this._itemSize!.width)\n            || (this._sizeType && ut.height != this._itemSize!.height)\n        ) {\n            if (!this._customSize)\n                this._customSize = {};\n            let val = this._sizeType ? ut.height : ut.width;\n            if (this._customSize[item._listId] != val) {\n                this._customSize[item._listId] = val;\n                this._resizeContent();\n                // this.content!.children.forEach((child: Node) => {\n                //     this._updateItemPos(child);\n                // });\n                this.updateAll();\n                // 如果当前正在运行 scrollTo，肯定会不准确，在这里做修正\n                if (this._scrollToListId != null) {\n                    this._scrollPos = null;\n                    this.unschedule(this._scrollToSo);\n                    this.scrollTo(this._scrollToListId, Math.max(0, this._scrollToEndTime - ((new Date()).getTime() / 1000)));\n                }\n            }\n        }\n        // }\n    }\n    //PAGE粘附\n    _pageAdhere() {\n        let t = this;\n        if (!t.cyclic && (t.elasticTop > 0 || t.elasticRight > 0 || t.elasticBottom > 0 || t.elasticLeft > 0))\n            return;\n        let curPos = t._sizeType ? t.viewTop : t.viewLeft;\n        let dis = (t._sizeType ? t._thisNodeUt!.height : t._thisNodeUt!.width) * t.pageDistance;\n        let canSkip = Math.abs(t._beganPos!.x - curPos) > dis;\n        if (canSkip) {\n            let timeInSecond = .5;\n            switch (t._alignCalcType) {\n                case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                    if (t._beganPos > curPos) {\n                        t.prePage(timeInSecond);\n                        // cc.log('_pageAdhere   PPPPPPPPPPPPPPP');\n                    } else {\n                        t.nextPage(timeInSecond);\n                        // cc.log('_pageAdhere   NNNNNNNNNNNNNNN');\n                    }\n                    break;\n                case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                    if (t._beganPos < curPos) {\n                        t.prePage(timeInSecond);\n                    } else {\n                        t.nextPage(timeInSecond);\n                    }\n                    break;\n            }\n        } else if (t.elasticTop <= 0 && t.elasticRight <= 0 && t.elasticBottom <= 0 && t.elasticLeft <= 0) {\n            t.adhere();\n        }\n        t._beganPos = null;\n    }\n    //粘附\n    adhere() {\n        let t: any = this;\n        if (!t.checkInited())\n            return;\n        if (t.elasticTop > 0 || t.elasticRight > 0 || t.elasticBottom > 0 || t.elasticLeft > 0)\n            return;\n        t.adhering = true;\n        t._calcNearestItem();\n        let offset: number = (t._sizeType ? t._topGap : t._leftGap) / (t._sizeType ? t._thisNodeUt!.height : t._thisNodeUt!.width);\n        let timeInSecond: number = .7;\n        t.scrollTo(t.nearestListId, timeInSecond, offset);\n    }\n    //Update..\n    update() {\n        if (this.frameByFrameRenderNum <= 0 || this._updateDone)\n            return;\n        // cc.log(this.displayData.length, this._updateCounter, this.displayData[this._updateCounter]);\n        if (this._virtual) {\n            let len: number = (this._updateCounter + this.frameByFrameRenderNum) > this.displayItemNum ? this.displayItemNum : (this._updateCounter + this.frameByFrameRenderNum);\n            for (let n: number = this._updateCounter; n < len; n++) {\n                let data: any = this.displayData[n];\n                if (data) {\n                    this._createOrUpdateItem(data);\n                }\n            }\n\n            if (this._updateCounter >= this.displayItemNum - 1) { //最后一个\n                if (this._doneAfterUpdate) {\n                    this._updateCounter = 0;\n                    this._updateDone = false;\n                    // if (!this._scrollView.isScrolling())\n                    this._doneAfterUpdate = false;\n                } else {\n                    this._updateDone = true;\n                    this._delRedundantItem();\n                    this._forceUpdate = false;\n                    this._calcNearestItem();\n                    if (this.slideMode == SlideType.PAGE)\n                        this.curPageNum = this.nearestListId;\n                }\n            } else {\n                this._updateCounter += this.frameByFrameRenderNum;\n            }\n        } else {\n            if (this._updateCounter < this._numItems) {\n                let len: number = (this._updateCounter + this.frameByFrameRenderNum) > this._numItems ? this._numItems : (this._updateCounter + this.frameByFrameRenderNum);\n                for (let n: number = this._updateCounter; n < len; n++) {\n                    this._createOrUpdateItem2(n);\n                }\n                this._updateCounter += this.frameByFrameRenderNum;\n            } else {\n                this._updateDone = true;\n                this._calcNearestItem();\n                if (this.slideMode == SlideType.PAGE)\n                    this.curPageNum = this.nearestListId;\n            }\n        }\n    }\n    /**\n     * 创建或更新Item（虚拟列表用）\n     * @param {Object} data 数据\n     */\n    _createOrUpdateItem(data: any) {\n        let item: any = this.getItemByListId(data.id);\n        if (!item) { //如果不存在\n            let canGet: boolean = this._pool!.size() > 0;\n            if (canGet) {\n                item = this._pool!.get();\n                // cc.log('从池中取出::   旧id =', item['_listId'], '，新id =', data.id, item);\n            } else {\n                item = instantiate(this._itemTmp);\n                // cc.log('新建::', data.id, item);\n            }\n            if (!canGet || !isValid(item)) {\n                item = instantiate(this._itemTmp);\n                canGet = false;\n            }\n            if (item._listId != data.id) {\n                item._listId = data.id;\n                let ut: UITransform = item.getComponent(UITransform);\n                ut.setContentSize(this._itemSize!);\n            }\n            item.setPosition(new Vec3(data.x, data.y, 0));\n            this._resetItemSize(item);\n            this.content!.addChild(item);\n            if (canGet && this._needUpdateWidget) {\n                let widget: Widget = item.getComponent(Widget);\n                if (widget)\n                    widget.updateAlignment();\n            }\n            item.setSiblingIndex(this.content!.children.length - 1);\n\n            let listItem: ListItem = item.getComponent(ListItem);\n            item['listItem'] = listItem;\n            if (listItem) {\n                listItem.listId = data.id;\n                listItem.list = this;\n                listItem._registerEvent();\n            }\n            if (this.renderEvent) {\n                EventHandler.emitEvents([this.renderEvent], item, data.id % this._actualNumItems);\n            }\n        } else if (this._forceUpdate && this.renderEvent) { //强制更新\n            item.setPosition(new Vec3(data.x, data.y, 0));\n            this._resetItemSize(item);\n            // cc.log('ADD::', data.id, item);\n            if (this.renderEvent) {\n                EventHandler.emitEvents([this.renderEvent], item, data.id % this._actualNumItems);\n            }\n        }\n        this._resetItemSize(item);\n\n        this._updateListItem(item['listItem']);\n        if (this._lastDisplayData.indexOf(data.id) < 0) {\n            this._lastDisplayData.push(data.id);\n        }\n    }\n    //创建或更新Item（非虚拟列表用）\n    _createOrUpdateItem2(listId: number) {\n        let item: any = this.content!.children[listId];\n        let listItem: ListItem | null = null;\n        if (!item) { //如果不存在\n            item = instantiate(this._itemTmp);\n            item._listId = listId;\n            this.content!.addChild(item);\n            listItem = item.getComponent(ListItem);\n            item['listItem'] = listItem;\n            if (listItem) {\n                listItem.listId = listId;\n                listItem.list = this;\n                listItem._registerEvent();\n            }\n            if (this.renderEvent) {\n                EventHandler.emitEvents([this.renderEvent], item, listId % this._actualNumItems);\n            }\n        } else if (this._forceUpdate && this.renderEvent) { //强制更新\n            item._listId = listId;\n            listItem = item.getComponent(ListItem);\n            if (listItem) {\n                listItem.listId = listId;\n            }\n            if (this.renderEvent) {\n                EventHandler.emitEvents([this.renderEvent], item, listId % this._actualNumItems);\n            }\n        }\n        this._updateListItem(listItem!);\n        if (this._lastDisplayData.indexOf(listId) < 0) {\n            this._lastDisplayData.push(listId);\n        }\n    }\n\n    _updateListItem(listItem: ListItem) {\n        if (!listItem)\n            return;\n        if (this.selectedMode > SelectedType.NONE) {\n            let item: any = listItem.node;\n            switch (this.selectedMode) {\n                case SelectedType.SINGLE:\n                    listItem.selected = this.selectedId == item._listId;\n                    break;\n                case SelectedType.MULT:\n                    listItem.selected = this.multSelected.indexOf(item._listId) >= 0;\n                    break;\n            }\n        }\n    }\n    //仅虚拟列表用\n    _resetItemSize(item: any) {\n        return;\n        let size: number;\n        let ut: UITransform = item.getComponent(UITransform);\n        if (this._customSize && this._customSize[item._listId]) {\n            size = this._customSize[item._listId];\n        } else {\n            if (this._colLineNum > 1)\n                ut.setContentSize(this._itemSize!);\n            else\n                size = this._sizeType ? this._itemSize!.height : this._itemSize!.width;\n        }\n        if (size) {\n            if (this._sizeType)\n                ut.height = size;\n            else\n                ut.width = size;\n        }\n    }\n    /**\n     * 更新Item位置\n     * @param {Number||Node} listIdOrItem\n     */\n    _updateItemPos(listIdOrItem: any) {\n        let item: any = isNaN(listIdOrItem) ? listIdOrItem : this.getItemByListId(listIdOrItem);\n        let pos: any = this.getItemPos(item._listId);\n        item.setPosition(pos.x, pos.y);\n    }\n    /**\n     * 设置多选\n     * @param {Array} args 可以是单个listId，也可是个listId数组\n     * @param {Boolean} bool 值，如果为null的话，则直接用args覆盖\n     */\n    setMultSelected(args: any, bool: boolean) {\n        let t: any = this;\n        if (!t.checkInited())\n            return;\n        if (!Array.isArray(args)) {\n            args = [args];\n        }\n        if (bool == null) {\n            t.multSelected = args;\n        } else {\n            let listId: number, sub: number;\n            if (bool) {\n                for (let n: number = args.length - 1; n >= 0; n--) {\n                    listId = args[n];\n                    sub = t.multSelected.indexOf(listId);\n                    if (sub < 0) {\n                        t.multSelected.push(listId);\n                    }\n                }\n            } else {\n                for (let n: number = args.length - 1; n >= 0; n--) {\n                    listId = args[n];\n                    sub = t.multSelected.indexOf(listId);\n                    if (sub >= 0) {\n                        t.multSelected.splice(sub, 1);\n                    }\n                }\n            }\n        }\n        t._forceUpdate = true;\n        t._onScrolling();\n    }\n    /**\n     * 获取多选数据\n     * @returns\n     */\n    getMultSelected() {\n        return this.multSelected;\n    }\n    /**\n     * 多选是否有选择\n     * @param {number} listId 索引\n     * @returns\n     */\n    hasMultSelected(listId: number) {\n        return this.multSelected && this.multSelected.indexOf(listId) >= 0;\n    }\n    /**\n     * 更新指定的Item\n     * @param {Array} args 单个listId，或者数组\n     * @returns\n     */\n    updateItem(args: any) {\n        if (!this.checkInited())\n            return;\n        if (!Array.isArray(args)) {\n            args = [args];\n        }\n        for (let n: number = 0, len: number = args.length; n < len; n++) {\n            let listId: number = args[n];\n            let item: any = this.getItemByListId(listId);\n            if (item)\n                EventHandler.emitEvents([this.renderEvent], item, listId % this._actualNumItems);\n        }\n    }\n    /**\n     * 更新全部\n     */\n    updateAll() {\n        if (!this.checkInited())\n            return;\n        this.numItems = this.numItems;\n    }\n    /**\n     * 根据ListID获取Item\n     * @param {Number} listId\n     * @returns\n     */\n    getItemByListId(listId: number) {\n        if (this.content) {\n            for (let n: number = this.content!.children.length - 1; n >= 0; n--) {\n                let item: any = this.content!.children[n];\n                if (item._listId == listId)\n                    return item;\n            }\n        }\n    }\n    /**\n     * 获取在显示区域外的Item\n     * @returns\n     */\n    _getOutsideItem() {\n        let item: any;\n        let result: any[] = [];\n        for (let n: number = this.content!.children.length - 1; n >= 0; n--) {\n            item = this.content!.children[n];\n            if (!this.displayData.find(d => d.id == item._listId)) {\n                result.push(item);\n            }\n        }\n        return result;\n    }\n    //删除显示区域以外的Item\n    _delRedundantItem() {\n        if (this._virtual) {\n            let arr: any[] = this._getOutsideItem();\n            for (let n: number = arr.length - 1; n >= 0; n--) {\n                let item: any = arr[n];\n                if (this._scrollItem && item._listId == this._scrollItem._listId)\n                    continue;\n                item.isCached = true;\n                this._pool!.put(item);\n                for (let m: number = this._lastDisplayData.length - 1; m >= 0; m--) {\n                    if (this._lastDisplayData[m] == item._listId) {\n                        this._lastDisplayData.splice(m, 1);\n                        break;\n                    }\n                }\n            }\n            // cc.log('存入::', str, '    pool.length =', this._pool!.length);\n        } else {\n            while (this.content!.children.length > this._numItems) {\n                this._delSingleItem(this.content!.children[this.content!.children.length - 1]);\n            }\n        }\n    }\n    //删除单个Item\n    _delSingleItem(item: any) {\n        // cc.log('DEL::', item['_listId'], item);\n        item.removeFromParent();\n        if (item.destroy)\n            item.destroy();\n        item = null;\n    }\n    /** \n     * 动效删除Item（此方法只适用于虚拟列表，即_virtual=true）\n     * 一定要在回调函数里重新设置新的numItems进行刷新，毕竟本List是靠数据驱动的。\n     */\n    aniDelItem(listId: number, callFunc: Function, aniType: number) {\n        let t: any = this;\n\n        if (!t.checkInited() || t.cyclic || !t._virtual)\n            return console.error('This function is not allowed to be called!');\n\n        if (!callFunc)\n            return console.error('CallFunc are not allowed to be NULL, You need to delete the corresponding index in the data array in the CallFunc!');\n\n        if (t._aniDelRuning)\n            return console.warn('Please wait for the current deletion to finish!');\n\n        let item: any = t.getItemByListId(listId);\n        let listItem: ListItem;\n        if (!item) {\n            callFunc(listId);\n            return;\n        } else {\n            listItem = item.getComponent(ListItem);\n        }\n        t._aniDelRuning = true;\n        t._aniDelCB = callFunc;\n        t._aniDelItem = item;\n        t._aniDelBeforePos = item.position;\n        t._aniDelBeforeScale = item.scale;\n        let curLastId: number = t.displayData[t.displayData.length - 1].id;\n        let resetSelectedId: boolean = listItem.selected;\n        listItem.showAni(aniType, () => {\n            //判断有没有下一个，如果有的话，创建粗来\n            let newId: number = 0;\n            if (curLastId < t._numItems - 2) {\n                newId = curLastId + 1;\n            }\n            if (newId != null) {\n                let newData: any = t._calcItemPos(newId);\n                t.displayData.push(newData);\n                if (t._virtual)\n                    t._createOrUpdateItem(newData);\n                else\n                    t._createOrUpdateItem2(newId);\n            } else\n                t._numItems--;\n            if (t.selectedMode == SelectedType.SINGLE) {\n                if (resetSelectedId) {\n                    t._selectedId = -1;\n                } else if (t._selectedId - 1 >= 0) {\n                    t._selectedId--;\n                }\n            } else if (t.selectedMode == SelectedType.MULT && t.multSelected.length) {\n                let sub: number = t.multSelected.indexOf(listId);\n                if (sub >= 0) {\n                    t.multSelected.splice(sub, 1);\n                }\n                //多选的数据，在其后的全部减一\n                for (let n: number = t.multSelected.length - 1; n >= 0; n--) {\n                    let id: number = t.multSelected[n];\n                    if (id >= listId)\n                        t.multSelected[n]--;\n                }\n            }\n            if (t._customSize) {\n                if (t._customSize[listId])\n                    delete t._customSize[listId];\n                let newCustomSize: any = {};\n                let size: number;\n                for (let id in t._customSize) {\n                    size = t._customSize[id];\n                    let idNumber: number = parseInt(id);\n                    newCustomSize[idNumber - (idNumber >= listId ? 1 : 0)] = size;\n                }\n                t._customSize = newCustomSize;\n            }\n            //后面的Item向前怼的动效\n            let sec: number = .2333;\n            let twe: Tween<Node>, haveCB: boolean = false;\n            for (let n: number = newId != null ? newId : curLastId; n >= listId + 1; n--) {\n                item = t.getItemByListId(n);\n                if (item) {\n                    let posData: any = t._calcItemPos(n - 1);\n                    twe = tween(item)\n                        .to(sec, { position: new Vec3(posData.x, posData.y, 0) });\n\n                    if (n <= listId + 1) {\n                        haveCB = true;\n                        twe.call(() => {\n                            t._aniDelRuning = false;\n                            callFunc(listId);\n                            delete t._aniDelCB;\n                        });\n                    }\n                    twe.start();\n                }\n            }\n            if (!haveCB) {\n                t._aniDelRuning = false;\n                callFunc(listId);\n                t._aniDelCB = null;\n            }\n        }, true);\n    }\n    /**\n     * 滚动到..\n     * @param {Number} listId 索引（如果<0，则滚到首个Item位置，如果>=_numItems，则滚到最末Item位置）\n     * @param {Number} timeInSecond 时间\n     * @param {Number} offset 索引目标位置偏移，0-1\n     * @param {Boolean} overStress 滚动后是否强调该Item（这只是个实验功能）\n     */\n    scrollTo(listId: number, timeInSecond: number = .5, offset: number = 0, overStress: boolean = false) {\n        let t = this;\n        if (!t.checkInited(false))\n            return;\n        // t._scrollView.stopAutoScroll();\n        if (timeInSecond == null)   //默认0.5\n            timeInSecond = .5;\n        else if (timeInSecond < 0)\n            timeInSecond = 0;\n        if (listId < 0)\n            listId = 0;\n        else if (listId >= t._numItems)\n            listId = t._numItems - 1;\n        // 以防设置了numItems之后layout的尺寸还未更新\n        if (!t._virtual && t._layout && t._layout.enabled)\n            t._layout.updateLayout();\n\n        let pos = t.getItemPos(listId);\n        if (!pos) {\n            return DEV && console.error('pos is null', listId);\n        }\n        let targetX: number, targetY: number;\n\n        switch (t._alignCalcType) {\n            case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                targetX = pos.left;\n                if (offset != null)\n                    targetX -= t._thisNodeUt!.width * offset;\n                else\n                    targetX -= t._leftGap;\n                pos = new Vec3(targetX, 0, 0);\n                break;\n            case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                targetX = pos.right - t._thisNodeUt!.width;\n                if (offset != null)\n                    targetX += t._thisNodeUt!.width * offset;\n                else\n                    targetX += t._rightGap;\n                pos = new Vec3(targetX + t._contentUt!.width, 0, 0);\n                break;\n            case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                targetY = pos.top;\n                if (offset != null)\n                    targetY += t._thisNodeUt!.height * offset;\n                else\n                    targetY += t._topGap;\n                pos = new Vec3(0, -targetY, 0);\n                break;\n            case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                targetY = pos.bottom + t._thisNodeUt!.height;\n                if (offset != null)\n                    targetY -= t._thisNodeUt!.height * offset;\n                else\n                    targetY -= t._bottomGap;\n                pos = new Vec3(0, -targetY + t._contentUt!.height, 0);\n                break;\n        }\n        let viewPos: any = t.content!.getPosition();\n        viewPos = Math.abs(t._sizeType ? viewPos.y : viewPos.x);\n\n        let comparePos = t._sizeType ? pos.y : pos.x;\n        let runScroll = Math.abs((t._scrollPos != null ? t._scrollPos : viewPos) - comparePos) > .5;\n        // cc.log(runScroll, t._scrollPos, viewPos, comparePos)\n\n        // t._scrollView.stopAutoScroll();\n        if (runScroll) {\n            t._scrollView!.scrollToOffset(pos, timeInSecond);\n            t._scrollToListId = listId;\n            t._scrollToEndTime = ((new Date()).getTime() / 1000) + timeInSecond;\n            // cc.log(listId, t.content!.width, t.content!.getPosition(), pos);\n            t._scrollToSo = t.scheduleOnce(() => {\n                if (!t._adheringBarrier) {\n                    t.adhering = t._adheringBarrier = false;\n                }\n                t._scrollPos =\n                    t._scrollToListId =\n                    t._scrollToEndTime =\n                    t._scrollToSo =\n                    null;\n                //cc.log('2222222222', t._adheringBarrier)\n                if (overStress) {\n                    // t.scrollToListId = listId;\n                    let item = t.getItemByListId(listId);\n                    if (item) {\n                        tween(item)\n                            .to(.1, { scale: 1.05 })\n                            .to(.1, { scale: 1 })\n                            .start();\n                    }\n                }\n            }, timeInSecond + .1);\n\n            if (timeInSecond <= 0) {\n                t._onScrolling();\n            }\n        }\n    }\n    /**\n     * 计算当前滚动窗最近的Item\n     */\n    _calcNearestItem() {\n        let t: any = this;\n        t.nearestListId = null;\n        let data: any, center: number;\n\n        if (t._virtual)\n            t._calcViewPos();\n\n        let vTop: number, vRight: number, vBottom: number, vLeft: number;\n        vTop = t.viewTop;\n        vRight = t.viewRight;\n        vBottom = t.viewBottom;\n        vLeft = t.viewLeft;\n\n        let breakFor: boolean = false;\n        for (let n = 0; n < t.content!.children.length && !breakFor; n += t._colLineNum) {\n            data = t._virtual ? t.displayData[n] : t._calcExistItemPos(n);\n            if (data) {\n                center = t._sizeType ? ((data.top + data.bottom) / 2) : (center = (data.left + data.right) / 2);\n                switch (t._alignCalcType) {\n                    case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                        if (data.right >= vLeft) {\n                            t.nearestListId = data.id;\n                            if (vLeft > center)\n                                t.nearestListId += t._colLineNum;\n                            breakFor = true;\n                        }\n                        break;\n                    case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                        if (data.left <= vRight) {\n                            t.nearestListId = data.id;\n                            if (vRight < center)\n                                t.nearestListId += t._colLineNum;\n                            breakFor = true;\n                        }\n                        break;\n                    case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                        if (data.bottom <= vTop) {\n                            t.nearestListId = data.id;\n                            if (vTop < center)\n                                t.nearestListId += t._colLineNum;\n                            breakFor = true;\n                        }\n                        break;\n                    case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                        if (data.top >= vBottom) {\n                            t.nearestListId = data.id;\n                            if (vBottom > center)\n                                t.nearestListId += t._colLineNum;\n                            breakFor = true;\n                        }\n                        break;\n                }\n            }\n        }\n        //判断最后一个Item。。。（哎，这些判断真心恶心，判断了前面的还要判断最后一个。。。一开始呢，就只有一个布局（单列布局），那时候代码才三百行，后来就想着完善啊，艹..这坑真深，现在这行数都一千五了= =||）\n        data = t._virtual ? t.displayData[t.displayItemNum - 1] : t._calcExistItemPos(t._numItems - 1);\n        if (data && data.id == t._numItems - 1) {\n            center = t._sizeType ? ((data.top + data.bottom) / 2) : (center = (data.left + data.right) / 2);\n            switch (t._alignCalcType) {\n                case 1://单行HORIZONTAL（LEFT_TO_RIGHT）、网格VERTICAL（LEFT_TO_RIGHT）\n                    if (vRight > center)\n                        t.nearestListId = data.id;\n                    break;\n                case 2://单行HORIZONTAL（RIGHT_TO_LEFT）、网格VERTICAL（RIGHT_TO_LEFT）\n                    if (vLeft < center)\n                        t.nearestListId = data.id;\n                    break;\n                case 3://单列VERTICAL（TOP_TO_BOTTOM）、网格HORIZONTAL（TOP_TO_BOTTOM）\n                    if (vBottom < center)\n                        t.nearestListId = data.id;\n                    break;\n                case 4://单列VERTICAL（BOTTOM_TO_TOP）、网格HORIZONTAL（BOTTOM_TO_TOP）\n                    if (vTop > center)\n                        t.nearestListId = data.id;\n                    break;\n            }\n        }\n        // cc.log('t.nearestListId =', t.nearestListId);\n    }\n    //上一页\n    prePage(timeInSecond: number = .5) {\n        // cc.log('👈');\n        if (!this.checkInited())\n            return;\n        this.skipPage(this.curPageNum - 1, timeInSecond);\n    }\n    //下一页\n    nextPage(timeInSecond: number = .5) {\n        // cc.log('👉');\n        if (!this.checkInited())\n            return;\n        this.skipPage(this.curPageNum + 1, timeInSecond);\n    }\n    //跳转到第几页\n    skipPage(pageNum: number, timeInSecond: number) {\n        let t: any = this;\n        if (!t.checkInited())\n            return;\n        if (t._slideMode != SlideType.PAGE)\n            return console.error('This function is not allowed to be called, Must SlideMode = PAGE!');\n        if (pageNum < 0 || pageNum >= t._numItems)\n            return;\n        if (t.curPageNum == pageNum)\n            return;\n        // cc.log(pageNum);\n        t.curPageNum = pageNum;\n        if (t.pageChangeEvent) {\n            EventHandler.emitEvents([t.pageChangeEvent], pageNum);\n        }\n        t.scrollTo(pageNum, timeInSecond);\n    }\n    //计算 CustomSize（这个函数还是保留吧，某些罕见的情况的确还是需要手动计算customSize的）\n    calcCustomSize(numItems: number) {\n        let t: any = this;\n        if (!t.checkInited())\n            return;\n        if (!t._itemTmp)\n            return console.error('Unset template item!');\n        if (!t.renderEvent)\n            return console.error('Unset Render-Event!');\n        t._customSize = {};\n        let temp: any = instantiate(t._itemTmp);\n        let ut: UITransform = temp.getComponent(UITransform);\n        t.content!.addChild(temp);\n        for (let n: number = 0; n < numItems; n++) {\n            EventHandler.emitEvents([t.renderEvent], temp, n);\n            if (ut.height != t._itemSize.height || ut.width != t._itemSize.width) {\n                t._customSize[n] = t._sizeType ? ut.height : ut.width;\n            }\n        }\n        if (!Object.keys(t._customSize).length)\n            t._customSize = null;\n        temp.removeFromParent();\n        if (temp.destroy)\n            temp.destroy();\n        return t._customSize;\n    }\n}"]}