System.register(["__unresolved_0", "cc", "long", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, Long, csproto, MyApp, logError, DataEvent, EventMgr, EquipSlots, _crd;

  function _reportPossibleCrUseOfLong(extras) {
    _reporterNs.report("Long", "long", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "../../../../../scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../../../scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogError(extras) {
    _reporterNs.report("logError", "../../../../../scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfDataEvent(extras) {
    _reporterNs.report("DataEvent", "../../event/DataEvent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEventMgr(extras) {
    _reporterNs.report("EventMgr", "../../event/EventManager", _context.meta, extras);
  }

  _export("EquipSlots", void 0);

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
    }, function (_long) {
      Long = _long.default;
    }, function (_unresolved_2) {
      csproto = _unresolved_2.default;
    }, function (_unresolved_3) {
      MyApp = _unresolved_3.MyApp;
    }, function (_unresolved_4) {
      logError = _unresolved_4.logError;
    }, function (_unresolved_5) {
      DataEvent = _unresolved_5.DataEvent;
    }, function (_unresolved_6) {
      EventMgr = _unresolved_6.EventMgr;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0f11bTO3IdAJJsWdMyXv9zX", "EquipSlots", undefined);

      _export("EquipSlots", EquipSlots = class EquipSlots {
        constructor() {
          this.slots = [];
        }

        init() {
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_EQUIP_SLOT_INFO, this.onGetEquipSlotInfoMsg, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_EQUIP_SLOT_INSTALL, this.onEquipSlotInstallMsg, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.registerHandler((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_EQUIP_SLOT_UNINSTALL, this.onEquipSlotUnInstallMsg, this);
          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_GET_EQUIP_SLOT_INFO, {
            get_equip_slot_info: {}
          });
        }

        onGetEquipSlotInfoMsg(msg) {
          this.slots = msg.body.get_equip_slot_info.slots || [];
          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).EquipSlotRefresh);
        }

        onEquipSlotInstallMsg(msg) {
          var m = msg.body.equip_slot_install;
          var slot = this.slots.find(slot => slot.slot_id == m.slot_id);

          if (slot) {
            slot.equip_id = m.equip_id;
            slot.guid = m.guid;
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).EquipSlotRefresh);
        }

        onEquipSlotUnInstallMsg(msg) {
          var m = msg.body.equip_slot_uninstall;
          var slot = this.slots.find(slot => slot.slot_id == m.slot_id);

          if (slot) {
            slot.equip_id = 0;
            slot.guid = (_crd && Long === void 0 ? (_reportPossibleCrUseOfLong({
              error: Error()
            }), Long) : Long).ZERO;
          }

          (_crd && EventMgr === void 0 ? (_reportPossibleCrUseOfEventMgr({
            error: Error()
          }), EventMgr) : EventMgr).emit((_crd && DataEvent === void 0 ? (_reportPossibleCrUseOfDataEvent({
            error: Error()
          }), DataEvent) : DataEvent).EquipSlotRefresh);
        }

        getEmptySlots() {
          return this.slots.filter(slot => slot.equip_id == 0);
        }

        getEquippedSlots() {
          return this.slots.filter(slot => slot.equip_id != 0);
        }

        getEmptySlotByClass(equipClass) {
          return this.slots.find(slot => slot.equip_class == equipClass && slot.guid && slot.guid.eq(0)) || null;
        }

        getEquipSlotInfo(slotID) {
          return this.slots.find(slot => slot.slot_id == slotID) || null;
        }

        equip(item) {
          var equipCfg = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanMgr.table.TbEquip.get(item.item_id);
          var slots = this.slots.filter(slot => slot.equip_class == (equipCfg == null ? void 0 : equipCfg.equipClass));

          if (slots.length == 0) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("EquipSlots", " not found slot item:" + item.item_id + " " + item.guid + " equipClass:" + (equipCfg == null ? void 0 : equipCfg.equipClass));
            return;
          }

          var equippedSlot = slots[0];

          if (slots.length > 1) {
            var emptySlot = slots.find(slot => slot.guid.eq(0));

            if (emptySlot) {
              equippedSlot = emptySlot;
            } else {
              equippedSlot = slots[0];
            }
          }

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_EQUIP_SLOT_INSTALL, {
            equip_slot_install: {
              slot_id: equippedSlot.slot_id,
              guid: item.guid
            }
          });
        }

        unequip(guid) {
          var slot = this.slots.find(slot => slot.guid.eq(guid));

          if (!slot) {
            (_crd && logError === void 0 ? (_reportPossibleCrUseOflogError({
              error: Error()
            }), logError) : logError)("PlaneUI", "unequip fail guid:" + guid);
            return;
          }

          (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).netMgr.sendMessage((_crd && csproto === void 0 ? (_reportPossibleCrUseOfcsproto({
            error: Error()
          }), csproto) : csproto).cs.CS_CMD.CS_CMD_EQUIP_SLOT_UNINSTALL, {
            equip_slot_uninstall: {
              slot_id: slot.slot_id
            }
          });
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0d0ee137a6db3c2b607b477bcfd024ac1cc6a92d.js.map