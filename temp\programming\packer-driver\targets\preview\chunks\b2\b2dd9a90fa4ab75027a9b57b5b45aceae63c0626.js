System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, PlaneUIEvent;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "502ddcb8LpJpK37euyePoye", "PlaneUIEvent", undefined);

      _export("PlaneUIEvent", PlaneUIEvent = {
        TabChange: 'PlaneUIEvent_TabChange',
        SortTypeChange: 'PlaneUIEvent_SortTypeChange',
        BagItemClick: 'PlaneUIEvent_BagItemClick',
        UpdateBagGrids: 'PlaneUIEvent_UpdateBagGrids',
        UpdateMergeEquipStatus: 'PlaneUIEvent_UpdateEquipMergeStatus'
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=b2dd9a90fa4ab75027a9b57b5b45aceae63c0626.js.map