{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/randTerrain/RandTerrain.ts"], "names": ["_decorator", "assetManager", "CCInteger", "Component", "instantiate", "Prefab", "v2", "Vec2", "EDITOR", "ccclass", "property", "executeInEditMode", "TerrainElem", "displayName", "RandTerrain", "type", "onLoad", "node", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_loadElems", "update", "isCountMatch", "children", "length", "terrain", "isUUIDMatch", "i", "nodeUUID", "_prefab", "asset", "_uuid", "elemUUID", "elem", "uuid", "for<PERSON>ach", "child", "index", "offSet", "position", "x", "y", "onDestroy", "loadAny", "err", "prefab", "console", "error", "setPosition", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAoBC,MAAAA,Y,OAAAA,Y;AAAcC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,E,OAAAA,E;AAAIC,MAAAA,I,OAAAA,I;;AACjFC,MAAAA,M,UAAAA,M;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2CX,U;;6BAGpCY,W,WADZH,OAAO,CAAC,aAAD,C,UAEHC,QAAQ,CAACR,SAAD,C,UAERQ,QAAQ,CAACL,MAAD,C,UAERK,QAAQ,CAAC;AAACG,QAAAA,WAAW,EAAE;AAAd,OAAD,C,2BANb,MACaD,WADb,CACyB;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEG,C;;;;;;;iBAEK,I;;;;;;;iBAEP,IAAIL,IAAJ,CAAS,CAAT,EAAY,CAAZ,C;;;;6BAKbO,W,YAFZL,OAAO,CAAC,aAAD,C,UACPE,iBAAiB,E,UAEbD,QAAQ,CAAC;AAACK,QAAAA,IAAI,EAAE,CAACH,WAAD;AAAP,OAAD,C,6CAHb,MAEaE,WAFb,SAEiCX,SAFjC,CAE2C;AAAA;AAAA;;AAAA;AAAA;;AAI7Ba,QAAAA,MAAM,GAAS;AACrB,cAAIR,MAAJ,EAAY;AACR,iBAAKS,IAAL,CAAUC,iBAAV;;AACA,iBAAKC,UAAL;AACH;AACJ;;AAESC,QAAAA,MAAM,GAAS;AACrB,cAAIZ,MAAJ,EAAY;AACR,gBAAMa,YAAY,GAAG,KAAKJ,IAAL,CAAUK,QAAV,CAAmBC,MAAnB,KAA8B,KAAKC,OAAL,CAAaD,MAAhE;AACA,gBAAIE,WAAW,GAAG,IAAlB;;AACA,iBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKT,IAAL,CAAUK,QAAV,CAAmBC,MAAvC,EAA+CG,CAAC,EAAhD,EAAoD;AAAA;;AAChD,kBAAMT,IAAI,GAAG,KAAKA,IAAL,CAAUK,QAAV,CAAmBI,CAAnB,CAAb,CADgD,CAEhD;;AACA,kBAAIC,QAAQ,oBAAGV,IAAI,CAACW,OAAR,8BAAG,cAAcC,KAAjB,qBAAG,cAAqBC,KAApC;AACA,kBAAIC,QAAQ,2BAAG,KAAKP,OAAL,CAAaE,CAAb,EAAgBM,IAAnB,qBAAG,qBAAsBC,IAArC;;AAEA,kBAAIN,QAAQ,IAAII,QAAhB,EAA0B;AACtBN,gBAAAA,WAAW,GAAG,KAAd;AACA;AACH;AACJ;;AAED,gBAAI,CAACJ,YAAD,IAAiB,CAACI,WAAtB,EAAmC;AAC/B,mBAAKN,UAAL;AACH,aAFD,MAEO;AACH,mBAAKF,IAAL,CAAUK,QAAV,CAAmBY,OAAnB,CAA2B,CAACC,KAAD,EAAQC,KAAR,KAAkB;AACzC,qBAAKZ,OAAL,CAAaY,KAAb,EAAoBC,MAApB,GAA6B/B,EAAE,CAAC6B,KAAK,CAACG,QAAN,CAAeC,CAAhB,EAAmBJ,KAAK,CAACG,QAAN,CAAeE,CAAlC,CAA/B;AACH,eAFD;AAGH;AACJ;AACJ;;AAESC,QAAAA,SAAS,GAAS;AACxB,eAAKxB,IAAL,CAAUC,iBAAV;AACH;;AAEOC,QAAAA,UAAU,GAAS;AACvB,eAAKK,OAAL,CAAaU,OAAb,CAAsBF,IAAD,IAAU;AAAA;;AAC3B,gBAAIA,IAAI,CAACA,IAAL,IAAa,IAAjB,EAAuB;AACnB;AACH;;AAED,iBAAKf,IAAL,CAAUC,iBAAV;AACAjB,YAAAA,YAAY,CAACyC,OAAb,CAAqB;AAACT,cAAAA,IAAI,gBAACD,IAAI,CAACA,IAAN,qBAAC,WAAWC;AAAjB,aAArB,EAA6C,CAACU,GAAD,EAAMC,MAAN,KAAwB;AACjE,kBAAID,GAAJ,EAAS;AACLE,gBAAAA,OAAO,CAACC,KAAR,CAAc,yCAAd,EAAyDH,GAAzD;AACA;AACH;;AAED,kBAAI1B,IAAI,GAAGb,WAAW,CAACwC,MAAD,CAAtB;AACA3B,cAAAA,IAAI,CAAC8B,WAAL,CAAiBf,IAAI,CAACK,MAAL,CAAYE,CAA7B,EAAgCP,IAAI,CAACK,MAAL,CAAYG,CAA5C,EAA+C,CAA/C;AACA,mBAAKvB,IAAL,CAAW+B,QAAX,CAAoB/B,IAApB;AACH,aATD;AAUH,WAhBD;AAiBH;;AA3DsC,O;;;;;iBAEP,E", "sourcesContent": ["import { _decorator, assert, asset<PERSON>anager, CCInteger, Component, instantiate, Prefab, v2, Vec2 } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\n@ccclass('TerrainElem')\r\nexport class TerrainElem {\r\n    @property(CCInteger)\r\n    public weight: number = 0;\r\n    @property(Prefab)\r\n    public elem: Prefab | null = null;\r\n    @property({displayName: \"坐标偏移\"})\r\n    public offSet: Vec2 = new Vec2(0, 0);\r\n}\r\n\r\n@ccclass('RandTerrain')\r\n@executeInEditMode()\r\nexport class RandTerrain extends Component {\r\n    @property({type: [TerrainElem]})\r\n    public terrain: TerrainElem[] = [];\r\n\r\n    protected onLoad(): void {\r\n        if (EDITOR) {\r\n            this.node.removeAllChildren();\r\n            this._loadElems();\r\n        }\r\n    }\r\n\r\n    protected update(): void {\r\n        if (EDITOR) {\r\n            const isCountMatch = this.node.children.length === this.terrain.length;\r\n            let isUUIDMatch = true;\r\n            for (let i = 0; i < this.node.children.length; i++) {\r\n                const node = this.node.children[i];\r\n                // @ts-ignore\r\n                var nodeUUID = node._prefab?.asset?._uuid;\r\n                var elemUUID = this.terrain[i].elem?.uuid;\r\n\r\n                if (nodeUUID != elemUUID) {\r\n                    isUUIDMatch = false;\r\n                    break;\r\n                }\r\n            }\r\n\r\n            if (!isCountMatch || !isUUIDMatch) {\r\n                this._loadElems();\r\n            } else {\r\n                this.node.children.forEach((child, index) => {\r\n                    this.terrain[index].offSet = v2(child.position.x, child.position.y);\r\n                });\r\n            }\r\n        }\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n        this.node.removeAllChildren();\r\n    }\r\n\r\n    private _loadElems(): void {\r\n        this.terrain.forEach((elem) => {\r\n            if (elem.elem == null) {\r\n                return;\r\n            }\r\n\r\n            this.node.removeAllChildren();\r\n            assetManager.loadAny({uuid:elem.elem?.uuid}, (err, prefab:Prefab) => { \r\n                if (err) {\r\n                    console.error(\"RandTerrain load TerrainElem prefab err\", err);\r\n                    return;\r\n                }\r\n\r\n                var node = instantiate(prefab);\r\n                node.setPosition(elem.offSet.x, elem.offSet.y, 0);\r\n                this.node!.addChild(node);\r\n            });\r\n        })\r\n    }\r\n}\r\n\r\n"]}