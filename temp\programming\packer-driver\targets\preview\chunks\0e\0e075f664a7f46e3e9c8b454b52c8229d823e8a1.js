System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10", "__unresolved_11", "__unresolved_12"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Sprite, tween, UIOpacity, Tools, GameIns, GameEnum, EnemyShootComponent, EnemyShootData, TrackComponent, EnemyPlaneRole, PlaneBase, FBoxCollider, ColliderGroupType, Bullet, Movable, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, EnemyPlane;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyShootComponent(extras) {
    _reporterNs.report("EnemyShootComponent", "./EnemyShootComponent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneData(extras) {
    _reporterNs.report("EnemyPlaneData", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyShootData(extras) {
    _reporterNs.report("EnemyShootData", "../../../data/EnemyData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackComponent(extras) {
    _reporterNs.report("TrackComponent", "../../base/TrackComponent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneRole(extras) {
    _reporterNs.report("EnemyPlaneRole", "./EnemyPlaneRole", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackGroup(extras) {
    _reporterNs.report("TrackGroup", "../../../data/EnemyWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackData(extras) {
    _reporterNs.report("TrackData", "../../../data/TrackData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "../PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "../../../collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../bullet/Bullet", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMovable(extras) {
    _reporterNs.report("Movable", "db://assets/scripts/Game/move/Movable", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
      UIOpacity = _cc.UIOpacity;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }, function (_unresolved_3) {
      GameIns = _unresolved_3.GameIns;
    }, function (_unresolved_4) {
      GameEnum = _unresolved_4.GameEnum;
    }, function (_unresolved_5) {
      EnemyShootComponent = _unresolved_5.default;
    }, function (_unresolved_6) {
      EnemyShootData = _unresolved_6.EnemyShootData;
    }, function (_unresolved_7) {
      TrackComponent = _unresolved_7.default;
    }, function (_unresolved_8) {
      EnemyPlaneRole = _unresolved_8.default;
    }, function (_unresolved_9) {
      PlaneBase = _unresolved_9.default;
    }, function (_unresolved_10) {
      FBoxCollider = _unresolved_10.default;
    }, function (_unresolved_11) {
      ColliderGroupType = _unresolved_11.ColliderGroupType;
    }, function (_unresolved_12) {
      Bullet = _unresolved_12.default;
    }, function (_unresolved_13) {
      Movable = _unresolved_13.Movable;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0a450DDnFNJmY3HY0he9lIp", "EnemyPlane", undefined);

      __checkObsolete__(['_decorator', 'Node', 'Sprite', 'Vec2', 'tween', 'UIOpacity']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyPlane = (_dec = ccclass('EnemyPlane'), _dec2 = property(_crd && EnemyPlaneRole === void 0 ? (_reportPossibleCrUseOfEnemyPlaneRole({
        error: Error()
      }), EnemyPlaneRole) : EnemyPlaneRole), _dec3 = property(Sprite), _dec4 = property(Sprite), _dec5 = property(Sprite), _dec(_class = (_class2 = class EnemyPlane extends (_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
        error: Error()
      }), PlaneBase) : PlaneBase) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "role", _descriptor, this);

          _initializerDefineProperty(this, "hpBg", _descriptor2, this);

          _initializerDefineProperty(this, "hpSpr", _descriptor3, this);

          _initializerDefineProperty(this, "hpWhite", _descriptor4, this);

          this._data = null;
          // 这个将要废弃
          this._trackCom = null;
          this._shootCom = null;
          this._moveCom = null;
          this.removeAble = false;
          this._curAction = 0;
          this._removeCount = 0;
          this._removeTime = 0;
          this._rotateSpeed = 0;
          this._leaveAct = -1;
          this._roleIndex = 1;
          this._curFormIndex = 0;
          this._curTrackType = -1;
          this._dieAnimEnd = false;
          this._hpWhiteTween = null;
          this.bullets = [];
          this._countTime = 0;
          this._bStandBy = false;
          this._standByTime = 0;
          this._standByEnd = false;
        }

        get moveCom() {
          return this._moveCom;
        }

        onLoad() {
          this._trackCom = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(this.node, _crd && TrackComponent === void 0 ? (_reportPossibleCrUseOfTrackComponent({
            error: Error()
          }), TrackComponent) : TrackComponent);
          this._shootCom = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(this.node, _crd && EnemyShootComponent === void 0 ? (_reportPossibleCrUseOfEnemyShootComponent({
            error: Error()
          }), EnemyShootComponent) : EnemyShootComponent); // 添加碰撞组件并初始化

          this.collideComp = this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider) || this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider);
          this.collideComp.init(this);
          this.collideComp.groupType = (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).ENEMY_NORMAL;
          this.colliderEnabled = false; // 添加移动组件

          this._moveCom = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(this.node, _crd && Movable === void 0 ? (_reportPossibleCrUseOfMovable({
            error: Error()
          }), Movable) : Movable);
        }

        setCollideAble(isEnabled) {
          this.collideComp.isEnable = isEnabled;
        }

        get collideAble() {
          return this.collideComp.isEnable;
        }

        initPlane(data) {
          var _superprop_getInit = () => super.init,
              _this = this;

          return _asyncToGenerator(function* () {
            _superprop_getInit().call(_this);

            _this._reset();

            _this._data = data;

            _this._refreshProperty();
          })();
        }

        _reset() {
          this._curAction = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyAction.Track;
        }

        _refreshProperty() {
          this.curHp = this._data.hp;
          this.maxHp = this._data.hp;
          this.attack = 10;
        }

        initComps() {
          // 调用父类的组件初始化方法
          super.initComps(); // 初始化射击组件

          this._shootCom.init(this, this.node, null, false); // 设置攻击开始的回调


          this._shootCom.setAtkStartCall(() => {
            this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.AttackPrepare);
          }); // 设置攻击结束的回调


          this._shootCom.setAtkOverCall(() => {
            this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.AttackOver);
          });
        }

        setAction(action) {
          if (this._curAction !== action) {
            this._curAction = action; // 停止射击并启用轨迹

            this._shootCom.setIsShooting(false);

            this._trackCom.setTrackAble(true);

            switch (this._curAction) {
              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.Sneak:
                // 潜行行为
                this.hpBg.node.getComponent(UIOpacity).opacity = 0; // this.role.playSneakAnim();

                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.Track:
                // 跟踪行为
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.Transform:
                // 变形行为
                this._trackCom.setTrackAble(false);

                this._shootCom.stopShoot();

                this._roleIndex++; // this.role!.playAnim("transform", () => {
                //     this.role!.playAnim("idle" + this._roleIndex);
                //     this.setAction(GameEnum.EnemyAction.Track);
                //     this._shootCom!.setNextShootAtOnce();
                // }) || (

                this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyAction.Track);

                this._shootCom.setNextShootAtOnce();

                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.AttackPrepare:
                // 准备攻击行为
                this.playAtkAnim();
                this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyAction.AttackIng);
                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.AttackIng:
                // 攻击中行为
                this._shootCom.startShoot();

                break;

              case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).EnemyAction.AttackOver:
                this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyAction.Track);
                break;

              default:
                break;
            }
          }
        }
        /**
         * 播放攻击动画
         */


        playAtkAnim() {
          this.role.playAnim("atk" + this._roleIndex, false, () => {
            this.role.playAnim("idle" + this._roleIndex);
          });
        }
        /**
         * 设置形态索引
         * @param {number} index 形态索引
         */


        setFormIndex(index) {
          this._curFormIndex = index;

          if (this._curFormIndex >= 0 && this._data.bAttackAbles[this._curFormIndex]) {
            var shootData = new (_crd && EnemyShootData === void 0 ? (_reportPossibleCrUseOfEnemyShootData({
              error: Error()
            }), EnemyShootData) : EnemyShootData)();
            shootData.attackInterval = this._data.attackInterval;
            shootData.attackNum = this._data.attackNum;
            shootData.attackPointArr = this._data.attackPointArr[this._curFormIndex];
            shootData.attackArrNum = shootData.attackPointArr.length;

            this._shootCom.setShootData(shootData);
          } else {
            this._shootCom.setShootData(null);
          }
        }

        _updateAction(deltaTime) {
          this._shootCom.setNextAble(false);

          switch (this._curAction) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Sneak:
              // this._updateCurDir(deltaTime);
              this.colliderEnabled = false;
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Track:
              // this._updateCurDir(deltaTime);
              this._shootCom.setNextAble(this._trackCom.isMoving && this._data.bMoveAttack || !this._trackCom.isMoving && this._data.bStayAttack);

              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Transform:
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.AttackPrepare:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.AttackIng:
              // this._updateCurDir(deltaTime);
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Leave:
              if (this._leaveAct === 0) {
                this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                  error: Error()
                }), GameEnum) : GameEnum).EnemyDestroyType.TimeOver);
              } else if (this._leaveAct > 0) {// this._updateCurDir(deltaTime);
              }

              break;
          }
        }
        /**
         * 初始化轨迹
         * @param {TrackData} trackData 轨迹数据
         * @param {number} offsetX X 轴偏移
         * @param {number} offsetY Y 轴偏移
         * @param {boolean} isLoop 是否循环
         * @param {number} rotateSpeed 旋转速度
         */


        initTrack(trackData, trackParams, offsetX, offsetY, rotateSpeed) {
          if (rotateSpeed === void 0) {
            rotateSpeed = 0;
          }

          this._rotateSpeed = rotateSpeed;

          this._trackCom.init(this, trackData, trackParams, offsetX, offsetY);

          this._trackCom.setTrackGroupOverCall(groupType => {});

          this._trackCom.setTrackOverCall(() => {
            this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TrackOver);
          });

          this._trackCom.setTrackLeaveCall(() => {
            this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Leave);
          });

          this._trackCom.setTrackStartCall(trackData => {});
        }
        /**
        * 设置首次射击的延迟时间
        * @param {number} delay 延迟时间
        */


        setFirstShootDelay(delay) {
          this._shootCom.setFirstShootDelay(delay);
        }
        /**
         * 开始战斗
         */


        startBattle() {
          this.colliderEnabled = true;

          this._refreshHpBar();

          this._trackCom.setTrackAble(true);

          this._trackCom.startTrack();
        }
        /**
         * 更新游戏逻辑
         * @param {number} deltaTime 帧间隔时间
         */


        updateGameLogic(deltaTime) {
          if (!this.isDead && this.checkStandby(deltaTime)) {
            // 更新所有组件
            this.m_comps.forEach(comp => {
              comp.update(deltaTime);
            });
          }

          if (this.isDead) {
            this._checkRemoveAble(deltaTime);
          } else if (!this._bStandBy) {
            this._trackCom.updateGameLogic(deltaTime);

            this._shootCom.updateGameLogic(deltaTime);

            this._moveCom.tick(deltaTime);

            this._updateAction(deltaTime);
          }
        }

        onCollide(collider) {
          if (!this.isDead) {
            if (collider.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
              error: Error()
            }), Bullet) : Bullet)) {
              var attack = collider.entity.getAttack();
              (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                error: Error()
              }), GameIns) : GameIns).hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), attack);
              this.hurt(-attack);
            }
          }
        }

        hurt(damage) {
          if (this.isDead) {
            return false;
          }

          this.curHp += damage;

          if (this.curHp < 0) {
            this.curHp = 0;
          } else if (this.curHp > this.maxHp) {
            this.curHp = this.maxHp;
          }

          this._refreshHpBar();

          this.checkHp();

          if (!this.isDead) {// this.role.winkWhite();
          }
        }

        checkHp() {
          if (this.curHp <= 0) {
            this.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Die);
            return true;
          }

          return false;
        }

        _refreshHpBar() {
          var hpRatio = this.curHp / this.maxHp;
          var isDecreasing = hpRatio < this.hpSpr.fillRange; // 更新血条显示

          this.hpSpr.fillRange = hpRatio; // 停止之前的血条动画

          if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();

            this._hpWhiteTween = null;
          } // 如果血量减少，播放白色血条的动画


          if (isDecreasing) {
            var duration = Math.abs(this.hpWhite.fillRange - this.hpSpr.fillRange);
            this._hpWhiteTween = tween(this.hpWhite).to(duration, {
              fillRange: this.hpSpr.fillRange
            }).call(() => {
              this._hpWhiteTween = null;
            }).start();
          } else {
            this.hpWhite.fillRange = hpRatio;
          }
        }
        /**
         * 处理敌人死亡逻辑
         * @param {GameEnum.EnemyDestroyType} destroyType 敌人销毁类型
         */


        die(destroyType) {
          if (!super.toDie()) {
            return false;
          }

          this.colliderEnabled = false;
          this.onDie(destroyType);
        }
        /**
         * 敌机死亡时的处理
         * @param {number} destroyType 销毁类型
         */


        onDie(destroyType) {
          this.hpBg.node.getComponent(UIOpacity).opacity = 0;
          this.willRemove();

          switch (destroyType) {
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Die:
              // this.playDieAnim();
              break;

            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Leave:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TrackOver:
            case (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.TimeOver:
              this._dieAnimEnd = true;
              break;
          }
        }
        /**
         * 准备移除敌机
         */


        willRemove() {
          if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();

            this._hpWhiteTween = null;
          }

          this.hpWhite.fillRange = 0;
        }
        /**
         * 检查是否可以存活
         * @returns {boolean} 是否可以存活
         */


        checkLiveAble() {
          if (this._curAction === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).EnemyAction.Track) {
            this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyAction.Leave);
            return true;
          }

          return false;
        }
        /**
         * 检查是否可以移除
         * @param {number} deltaTime 帧间隔时间
         */


        _checkRemoveAble(deltaTime) {
          this.removeAble = true;
        }

        addBullet(bullet) {
          if (this.bullets) {
            this.bullets.push(bullet);
          }
        }
        /**
         * 从敌人移除子弹
         * @param {Bullet} bullet 子弹对象
         */


        removeBullet(bullet) {
          if (this.bullets) {
            var index = this.bullets.indexOf(bullet);

            if (index >= 0) {
              this.bullets.splice(index, 1);
            }
          }
        }
        /**
         * 检查待机状态
         * @param {number} deltaTime 帧间隔时间
         * @returns {boolean} 是否处于待机状态
         */


        checkStandby(deltaTime) {
          this._countTime += deltaTime;

          if (this._bStandBy) {
            if (this._countTime > this._standByTime) {
              this._bStandBy = false;
              this._countTime = 0;
              this._standByEnd = true;
              this.node.getComponent(UIOpacity).opacity = 255;
              this.startBattle();
            }

            return true;
          }

          return false;
        }

        setStandByTime(time) {
          this._bStandBy = true;
          this._standByTime = time;
          this.node.getComponent(UIOpacity).opacity = 0;
        }

        setPos(x, y) {
          this.node.setPosition(x, y);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "role", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "hpBg", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "hpSpr", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "hpWhite", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=0e075f664a7f46e3e9c8b454b52c8229d823e8a1.js.map