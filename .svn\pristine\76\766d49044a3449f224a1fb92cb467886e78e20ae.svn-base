[{"task_id": 1002901, "group_id": 10029, "task_class": 3, "prev_id": 0, "period_type": 0, "open_type": 1, "open_value": 10, "goal_type": 10, "goal_params": [15], "accumulate": false, "award_id": 10090, "orbit_id": 1001, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959"}, {"task_id": 1002902, "group_id": 10029, "task_class": 3, "prev_id": 1002901, "period_type": 0, "open_type": 1, "open_value": 15, "goal_type": 10, "goal_params": [20], "accumulate": false, "award_id": 10091, "orbit_id": 1001, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959"}, {"task_id": 1002903, "group_id": 10029, "task_class": 3, "prev_id": 1002902, "period_type": 0, "open_type": 1, "open_value": 20, "goal_type": 10, "goal_params": [25], "accumulate": false, "award_id": 10092, "orbit_id": 1001, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959"}, {"task_id": 1002904, "group_id": 10029, "task_class": 3, "prev_id": 1002903, "period_type": 0, "open_type": 1, "open_value": 20, "goal_type": 10, "goal_params": [30], "accumulate": false, "award_id": 10093, "orbit_id": 1001, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959"}, {"task_id": 1002905, "group_id": 10029, "task_class": 3, "prev_id": 1002904, "period_type": 0, "open_type": 1, "open_value": 20, "goal_type": 10, "goal_params": [35], "accumulate": false, "award_id": 10094, "orbit_id": 1001, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959"}, {"task_id": 1002906, "group_id": 10029, "task_class": 3, "prev_id": 1002905, "period_type": 0, "open_type": 1, "open_value": 20, "goal_type": 10, "goal_params": [40], "accumulate": false, "award_id": 10095, "orbit_id": 1001, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959"}, {"task_id": 1002907, "group_id": 10029, "task_class": 3, "prev_id": 1002906, "period_type": 0, "open_type": 1, "open_value": 20, "goal_type": 10, "goal_params": [45], "accumulate": false, "award_id": 10096, "orbit_id": 1001, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959"}, {"task_id": 1002908, "group_id": 10029, "task_class": 3, "prev_id": 1002907, "period_type": 0, "open_type": 1, "open_value": 20, "goal_type": 10, "goal_params": [50], "accumulate": false, "award_id": 10097, "orbit_id": 1001, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959"}, {"task_id": 1002909, "group_id": 10029, "task_class": 3, "prev_id": 1002908, "period_type": 0, "open_type": 1, "open_value": 20, "goal_type": 10, "goal_params": [55], "accumulate": false, "award_id": 10098, "orbit_id": 1001, "orbit_value": 10, "open_date": "20250909", "open_time": "000000", "close_date": "20251111", "close_time": "235959"}, {"task_id": 1003901, "group_id": 10039, "task_class": 1, "prev_id": 0, "period_type": 1, "open_type": 0, "open_value": 0, "goal_type": 1, "goal_params": [1001, 3], "accumulate": false, "award_id": 10099, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}, {"task_id": 1003902, "group_id": 10039, "task_class": 1, "prev_id": 0, "period_type": 1, "open_type": 0, "open_value": 0, "goal_type": 2, "goal_params": [20090089, 4], "accumulate": false, "award_id": 10100, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}, {"task_id": 1003903, "group_id": 10039, "task_class": 1, "prev_id": 0, "period_type": 1, "open_type": 0, "open_value": 0, "goal_type": 3, "goal_params": [1, 100], "accumulate": false, "award_id": 10101, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}, {"task_id": 1003904, "group_id": 10039, "task_class": 1, "prev_id": 0, "period_type": 1, "open_type": 0, "open_value": 0, "goal_type": 14, "goal_params": [5], "accumulate": false, "award_id": 10102, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}, {"task_id": 1003905, "group_id": 10039, "task_class": 1, "prev_id": 0, "period_type": 1, "open_type": 0, "open_value": 0, "goal_type": 15, "goal_params": [5], "accumulate": false, "award_id": 10103, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}, {"task_id": 1003906, "group_id": 10039, "task_class": 1, "prev_id": 0, "period_type": 1, "open_type": 0, "open_value": 0, "goal_type": 8, "goal_params": [10], "accumulate": false, "award_id": 10104, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}, {"task_id": 1004901, "group_id": 10049, "task_class": 2, "prev_id": 0, "period_type": 2, "open_type": 0, "open_value": 0, "goal_type": 3, "goal_params": [1, 1000], "accumulate": true, "award_id": 10105, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}, {"task_id": 1004902, "group_id": 10049, "task_class": 2, "prev_id": 1004901, "period_type": 2, "open_type": 0, "open_value": 0, "goal_type": 3, "goal_params": [1, 2000], "accumulate": true, "award_id": 10106, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}, {"task_id": 1004903, "group_id": 10049, "task_class": 2, "prev_id": 1004902, "period_type": 2, "open_type": 0, "open_value": 0, "goal_type": 3, "goal_params": [1, 3000], "accumulate": true, "award_id": 10107, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}, {"task_id": 1004904, "group_id": 10049, "task_class": 2, "prev_id": 1004903, "period_type": 2, "open_type": 0, "open_value": 0, "goal_type": 3, "goal_params": [1, 4000], "accumulate": true, "award_id": 10108, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}, {"task_id": 1004905, "group_id": 10049, "task_class": 2, "prev_id": 1004904, "period_type": 2, "open_type": 0, "open_value": 0, "goal_type": 3, "goal_params": [1, 5000], "accumulate": true, "award_id": 10109, "orbit_id": 0, "orbit_value": 0, "open_date": "", "open_time": "", "close_date": "", "close_time": ""}]