{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/utils.ts"], "names": ["LevelEditorUtils", "_decorator", "CCFloat", "CCInteger", "Enum", "Node", "Prefab", "LayerType", "ccclass", "property", "LevelScrollLayerUI", "type", "displayName", "LevelRandTerrainUI", "LevelRandTerrainsLayerUI", "LevelRandTerrainsLayersUI", "<PERSON><PERSON><PERSON><PERSON>", "visible", "<PERSON><PERSON>", "Random", "Background", "LevelBackgroundLayer", "backgroundsNode", "getOrAddNode", "node_parent", "name", "node", "getChildByName", "<PERSON><PERSON><PERSON><PERSON>", "getOrAddComp", "classConstructor", "comp", "getComponent", "addComponent"], "mappings": ";;;8IAwEaA,gB;;;;;;;;;;;;;;;;;;;;;AAxEOC,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,S,OAAAA,S;AAAsBC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AAClEC,MAAAA,S,iBAAAA,S;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;oCAGjBS,kB,WADZF,OAAO,CAAC,0BAAD,C,UAEHC,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAEL,MAAP;AAAeM,QAAAA,WAAW,EAAE;AAA5B,OAAD,C,UAGRH,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAER,SAAP;AAAkBS,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,2BALb,MACaF,kBADb,CACgC;AAAA;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAES,I;;;;;;;iBAGb,G;;;;oCAIfG,kB,YADZL,OAAO,CAAC,0BAAD,C,UAEHC,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAER,SAAP;AAAkBS,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRH,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAE,CAACL,MAAD,CAAP;AAAiBM,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,6BALb,MACaC,kBADb,CACgC;AAAA;AAAA;;AAAA;AAAA,UAKkB;;;AALlB,O;;;;;iBAEJ,G;;;;;;;iBAGgB,I;;;;0CAI/BC,wB,YADZN,OAAO,CAAC,gCAAD,C,UAEHC,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAER,SAAP;AAAkBS,QAAAA,WAAW,EAAE;AAA/B,OAAD,C,UAGRH,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAE,CAACE,kBAAD,CAAP;AAA6BD,QAAAA,WAAW,EAAE;AAA1C,OAAD,C,6BALb,MACaE,wBADb,CACsC;AAAA;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEV,G;;;;;;;iBAGuB,E;;;;2CAItCC,yB,aADZP,OAAO,CAAC,iCAAD,C,WAEHC,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAE,CAACG,wBAAD,CAAP;AAAmCF,QAAAA,WAAW,EAAE;AAAhD,OAAD,C,gCAFb,MACaG,yBADb,CACuC;AAAA;AAAA;AAAA;;AAAA,O;;;;;iBAEkB,E;;;;4BAI5CC,U,aADZR,OAAO,CAAC,kBAAD,C,WAEHC,QAAQ,CAACJ,IAAD,C,WAERI,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAACT,OAAN;AAAeU,QAAAA,WAAW,EAAC;AAA3B,OAAD,C,WAERH,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAEP,IAAI;AAAA;AAAA,mCAAX;AAAwBQ,QAAAA,WAAW,EAAC;AAApC,OAAD,C,WAERH,QAAQ,CAAC;AACNE,QAAAA,IAAI,EAAE,CAACD,kBAAD,CADA;AAENE,QAAAA,WAAW,EAAE,KAFP;AAGNK,QAAAA,OAAO,EAAE,mBAA2B;AAChC,iBAAO,KAAKN,IAAL,KAAc;AAAA;AAAA,sCAAUO,MAA/B;AACH;AALK,OAAD,C,WASRT,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAE,CAACI,yBAAD,CAAP;AACNH,QAAAA,WAAW,EAAC,KADN;AAENK,QAAAA,OAAO,EAAE,mBAA2B;AAChC,iBAAO,KAAKN,IAAL,KAAc;AAAA;AAAA,sCAAUQ,MAA/B;AACH;AAJK,OAAD,C,gCAjBb,MACaH,UADb,CACwB;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAAA,O;;;;;iBAEO,I;;;;;;;iBAEJ,E;;;;;;;iBAEE;AAAA;AAAA,sCAAUI,U;;;;;;;iBAQS,E;;;;;;;iBAQO,E;;;;sCAI1CC,oB,aADZb,OAAO,CAAC,4BAAD,C,WAEHC,QAAQ,CAAC;AAACE,QAAAA,IAAI,EAAE,CAACL,MAAD,CAAP;AAAiBM,QAAAA,WAAW,EAAE;AAA9B,OAAD,C,gCAFb,MACaS,oBADb,SAC0CL,UAD1C,CACqD;AAAA;AAAA;;AAAA;;AAAA,eAI1CM,eAJ0C,GAIb,IAJa;AAAA;;AAAA,O;;;;;iBAElB,E;;;;kCAKtBtB,gB,GAAN,MAAMA,gBAAN,CAAuB;AACA,eAAZuB,YAAY,CAACC,WAAD,EAAoBC,IAApB,EAAwC;AAC9D,cAAIC,IAAI,GAAGF,WAAW,CAACG,cAAZ,CAA2BF,IAA3B,CAAX;;AACA,cAAIC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAG,IAAIrB,IAAJ,CAASoB,IAAT,CAAP;AACAD,YAAAA,WAAW,CAACI,QAAZ,CAAqBF,IAArB;AACH;;AACD,iBAAOA,IAAP;AACH;;AACyB,eAAZG,YAAY,CAAsBH,IAAtB,EAAkCI,gBAAlC,EAAkG;AACxH,cAAIC,IAAI,GAAGL,IAAI,CAACM,YAAL,CAAkBF,gBAAlB,CAAX;;AACA,cAAIC,IAAI,IAAI,IAAZ,EAAkB;AACdA,YAAAA,IAAI,GAAGL,IAAI,CAACO,YAAL,CAAkBH,gBAAlB,CAAP;AACH;;AACD,iBAAOC,IAAP;AACH;;AAfyB,O", "sourcesContent": ["import { __private, _decorator, CCFloat, CCInteger, Component, Enum, Node, Prefab } from \"cc\";\r\nimport { LayerType } from \"db://assets/scripts/leveldata/leveldata\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('LevelEditorScrollLayerUI')\r\nexport class LevelScrollLayerUI { \r\n    @property({type: Prefab, displayName: '滚动体'})\r\n    public scrollPrefab: Prefab | null = null;\r\n\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    public weight: number = 100;\r\n}\r\n\r\n@ccclass('LevelEditorRandTerrainUI')\r\nexport class LevelRandTerrainUI {\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    public weight: number = 100;\r\n\r\n    @property({type: [Prefab], displayName: \"地形组预制体\"})\r\n    public terrainElements: Prefab | null = null; // 可以是TerrainElem、DynamicTerrains的预制体\r\n}\r\n\r\n@ccclass('LevelEditorRandTerrainsLayerUI')\r\nexport class LevelRandTerrainsLayerUI {\r\n    @property({type: CCInteger, displayName: \"权重\"})\r\n    public weight: number = 100;\r\n\r\n    @property({type: [LevelRandTerrainUI], displayName: \"地形策略\"})\r\n    public dynamicTerrains: LevelRandTerrainUI[] = []; \r\n}\r\n\r\n@ccclass('LevelEditorRandTerrainsLayersUI')\r\nexport class LevelRandTerrainsLayersUI {\r\n    @property({type: [LevelRandTerrainsLayerUI], displayName: \"地形策略组\"})\r\n    public dynamicTerrains: LevelRandTerrainsLayerUI[] = []; \r\n}\r\n\r\n@ccclass('LevelEditorLayer')\r\nexport class LevelLayer {\r\n    @property(Node)\r\n    public node: Node | null = null;\r\n    @property({type:CCFloat, displayName:\"速度\"})\r\n    public speed: number = 10;\r\n    @property({type: Enum(LayerType), displayName:\"地形类型\"})\r\n    public type: LayerType = LayerType.Background;\r\n    @property({ \r\n        type: [LevelScrollLayerUI], \r\n        displayName: \"滚动组\",\r\n        visible: function(this: LevelLayer) {\r\n            return this.type === LayerType.Scroll;\r\n        }\r\n    })\r\n    public scrollLayers: LevelScrollLayerUI[] = [];\r\n\r\n    @property({type: [LevelRandTerrainsLayersUI], \r\n        displayName:\"随机组\",\r\n        visible: function(this: LevelLayer) {\r\n            return this.type === LayerType.Random;\r\n        }\r\n    })\r\n    public randomLayers: LevelRandTerrainsLayersUI[] = [];\r\n}\r\n\r\n@ccclass('LevelEditorBackgroundLayer')\r\nexport class LevelBackgroundLayer extends LevelLayer {\r\n    @property({type: [Prefab], displayName: '背景组'})\r\n    public backgrounds: Prefab[] = [];\r\n\r\n    public backgroundsNode: Node|null = null;\r\n}\r\n\r\nexport class LevelEditorUtils {\r\n    public static getOrAddNode(node_parent: Node, name: string): Node {\r\n        var node = node_parent.getChildByName(name);\r\n        if (node == null) {\r\n            node = new Node(name);\r\n            node_parent.addChild(node);\r\n        }\r\n        return node;\r\n    }\r\n    public static getOrAddComp<T extends Component>(node: Node, classConstructor: __private.__types_globals__Constructor<T>): T {\r\n        var comp = node.getComponent(classConstructor);\r\n        if (comp == null) {\r\n            comp = node.addComponent(classConstructor);\r\n        }\r\n        return comp;\r\n    }\r\n}\r\n\r\n"]}