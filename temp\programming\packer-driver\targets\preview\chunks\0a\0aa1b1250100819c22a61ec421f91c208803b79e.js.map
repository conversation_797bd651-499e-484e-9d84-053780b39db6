{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/PKRewardIcon.ts"], "names": ["_decorator", "Component", "Label", "Sprite", "ccclass", "property", "PKRewardIcon", "onLoad", "setData", "num", "rewardIcon", "node", "setScale", "rewardNum", "string", "toString"], "mappings": ";;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;;;;;;;;OACjC;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;8BAGjBM,Y,WADZF,OAAO,CAAC,cAAD,C,UAGHC,QAAQ,CAACH,KAAD,C,UAERG,QAAQ,CAACF,MAAD,C,UAERE,QAAQ,CAACH,KAAD,C,2BAPb,MACaI,YADb,SACkCL,SADlC,CAC4C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AAS9BM,QAAAA,MAAM,GAAS,CAExB;;AAEMC,QAAAA,OAAO,CAACC,GAAD,EAAoB;AAC9B,eAAKC,UAAL,CAAiBC,IAAjB,CAAsBC,QAAtB,CAA+B,GAA/B,EAAoC,GAApC;AACA,eAAKC,SAAL,CAAgBC,MAAhB,GAAyBL,GAAG,CAACM,QAAJ,EAAzB;AACH;;AAhBuC,O;;;;;iBAGb,I;;;;;;;iBAEC,I;;;;;;;iBAEF,I", "sourcesContent": ["import { _decorator, Component, Label, Sprite } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PKRewardIcon')\r\nexport class PKRewardIcon extends Component {\r\n\r\n    @property(Label)\r\n    rewardText: Label | null = null;\r\n    @property(Sprite)\r\n    rewardIcon: Sprite | null = null;\r\n    @property(Label)\r\n    rewardNum: Label | null = null;\r\n\r\n    protected onLoad(): void {\r\n\r\n    }\r\n\r\n    public setData(num: number): void {\r\n        this.rewardIcon!.node.setScale(0.5, 0.5);\r\n        this.rewardNum!.string = num.toString();\r\n    }\r\n}\r\n\r\n\r\n"]}