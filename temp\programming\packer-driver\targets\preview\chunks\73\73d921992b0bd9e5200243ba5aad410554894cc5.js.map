{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/move/IMovable.ts"], "names": ["_decorator", "ccclass", "property", "eMoveModifier", "eEasing"], "mappings": ";;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBF,U;AAE9B;AACA;AACA;;+BAQYG,a,0BAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;AAAAA,QAAAA,a,CAAAA,a;eAAAA,a;;;yBAIAC,O,0BAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;AAAAA,QAAAA,O,CAAAA,O;eAAAA,O", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nconst { ccclass, property } = _decorator;\r\n\r\n/**\r\n * Base interface for all move-able objects\r\n */\r\nexport interface IMovable {\r\n    speed : number;                 // 速度\r\n    speedAngle : number;            // 速度方向 (用角度表示)\r\n    acceleration : number;          // 加速度\r\n    accelerationAngle : number;     // 加速度方向 (用角度表示)\r\n}\r\n\r\nexport enum eMoveModifier {\r\n    Speed, SpeedAngle, Acceleration, AccelerationAngle\r\n}\r\n\r\nexport enum eEasing {\r\n    Linear,\r\n    InSine, OutSine, InOutSine,\r\n    InQuad, OutQuad, InOutQuad\r\n}\r\n"]}