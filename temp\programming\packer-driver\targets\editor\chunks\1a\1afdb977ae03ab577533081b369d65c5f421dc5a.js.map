{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts"], "names": ["Boss", "buffer", "Bullet", "Chapter", "ConsumeItem", "ConsumeMoney", "Enemy", "EnemyUI", "EquipProp", "GameMap", "GameMode", "GlobalAttr", "Level", "LevelGroup", "MainPlane", "MainPlaneLv", "PlaneEffect", "PlaneMaterial", "PlaneProperty", "PropInc", "ResEffect", "ResEquip", "ResEquipUpgrade", "ResGM", "ResItem", "ResPlane", "ResWeapon", "ResWhiteList", "skill", "Stage", "Task", "Track", "Unit", "Wave", "TbGM", "TbPlane", "TbEffect", "TbWeapon", "TbEquipUpgrade", "TbEquip", "TbItem", "TbGlobalAttr", "TbBoss", "<PERSON><PERSON><PERSON><PERSON>", "TbBullet", "TbChapter", "TbEnemy", "TbEnemyUI", "TbGameMap", "TbGameMode", "TbLevel", "TbLevelGroup", "TbMainPlane", "TbMainPlaneLv", "Tbskill", "TbStage", "TbTask", "TbTrack", "TbUnit", "TbWave", "Tables", "BuffType", "res", "EffectType", "EquipClass", "GMTabID", "ItemEffectType", "ItemUseType", "ModeType", "MoneyType", "PlayCycle", "PropName", "QualityType", "SkillConditionType", "TargetType", "TargetScanStrategy", "TaskClass", "TaskCondType", "TaskObjectType", "TaskPeriodType", "constructor", "_json_", "id", "bId", "sId", "app", "ta", "ft", "leave", "exp", "rid", "sk", "blp", "us", "ua", "va", "sv", "fl", "loot", "adsorb", "lp0", "lp1", "dh", "atk", "col", "tway", "way", "wi", "sp", "ai", "ra", "a0", "a1", "a2", "a3", "a4", "a5", "a6", "a7", "a8", "a9", "a10", "a11", "a12", "a13", "a14", "a15", "a16", "a17", "a18", "a19", "a20", "a21", "a22", "a100", "a101", "undefined", "Error", "resolve", "tables", "buffType", "duration", "durationBonus", "maxStack", "refreshType", "cycle", "cycleTimes", "effects", "conditionID", "_ele0", "_e0", "builtin", "EffectParam", "push", "A<PERSON><PERSON><PERSON><PERSON>", "target", "buff<PERSON>", "ConParam", "con", "param", "type", "randStrategy", "ID", "Weight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rating", "SkillCondition", "vector2", "x", "y", "vector3", "z", "vector4", "w", "name", "am", "image", "bustyle", "angleSpeed", "waittime", "initialve", "spdiff", "scale", "retrieve", "disappear", "shiftingbody", "body", "exstyle1", "exstyle2", "time", "accnumber", "acc", "offset", "para", "levelCount", "levelGroupCount", "strategy", "damageBonus", "life<PERSON><PERSON><PERSON>", "strategyList", "_e", "num", "uiId", "hp", "collideLevel", "turn", "hpShow", "collide<PERSON><PERSON><PERSON>", "bCollideDead", "bMoveAttack", "bStayAttack", "attackInterval", "attackNum", "attackData", "dieShoot", "dieBullet", "isAm", "collider", "hpParam", "blastSound", "blastDurations", "blastShake", "damageParam", "extraParam0", "extraParam1", "skillResistUIDict", "lootParam0", "lootParam1", "showParam", "sneakAnim", "value", "level", "floorRes", "hideImg", "skyRes", "imageSqueRes", "floorSpeed", "skySpeed", "imageSqueSpeed", "floorLayer", "skyLayer", "imageSqueLayer", "imageSqueNodeMove", "imageSquePos", "skyNodeMove", "linkYDistance", "skyAngle", "skyLayout", "inMapItem", "startY", "totalRules", "floor_res", "hide_img", "sky_res", "imageSque_res", "floor_speed", "sky_speed", "imageSque_speed", "floor_layer", "sky_layer", "imageSque_layer", "imageSqueNode_move", "imageSque_pos", "skyNode_move", "link_y_distance", "sky_angle", "sky_layout", "in_map_item", "start_y", "total_rules", "modeType", "chapterID", "order", "resourceID", "description", "conList", "times", "monType", "costParam1", "costParam2", "rebirthTimes", "rebirthCost", "power", "rogueID", "LevelLimit", "rogueFirst", "sweepLimit", "rewardID1", "rewardID2", "ratingList", "GoldProducion", "MaxEnergy", "EnergyRecoverInterval", "EnergyRecoverValue", "ItemPickUpRadius", "PostHitProtection", "prefab", "forbidFire", "forbidNBomb", "forbidActSkill", "planeCollisionScaling", "levelType", "normLevelCount", "normLevelST", "normSTList", "bossLevelCount", "bossLevelST", "bossSTList", "transSrc", "transExt", "zjdmtxzb", "transatk1", "shiftingatk1", "effectId", "effect_id", "materialId", "materialCount", "material_id", "material_count", "propType", "propValue", "prop_type", "prop_value", "inc", "effectType", "effectValue", "effectParams", "effect_type", "effect_value", "effect_params", "quality", "qualitySub", "equipClass", "props", "consumeItems", "quality_sub", "equip_class", "consume_items", "levelFrom", "levelTo", "propInc", "consumeMoney", "level_from", "level_to", "prop_inc", "consume_money", "tabID", "tabName", "cmd", "desc", "useType", "effectParam1", "effectParam2", "maxStack<PERSON>um", "use_type", "effect_param1", "effect_param2", "max_stack_num", "starLevel", "properties", "materials", "star_level", "openid", "password", "status", "privilege", "icon", "cd", "CostID", "CostNum", "ApplyBuffs", "mainStage", "subStage", "enemyGroupID", "delay", "enemyNorRate", "taskId", "groupId", "taskClass", "prevId", "periodType", "openType", "openValue", "goalType", "goalParams", "accumulate", "awardId", "orbitId", "orbitValue", "openDate", "openTime", "closeDate", "closeTime", "task_id", "group_id", "task_class", "prev_id", "period_type", "open_type", "open_value", "goal_type", "goal_params", "award_id", "orbit_id", "orbit_value", "open_date", "open_time", "close_date", "close_time", "tpe", "uId", "im", "imp", "dam", "pos", "hpp", "sco", "hc", "hs", "bla", "so", "act", "mix", "planeType", "planeId", "interval", "offsetPos", "track", "trackParams", "rotatioSpeed", "FirstShootDelay", "_dataList", "_json2_", "_v", "getDataList", "get", "index", "data", "_dataMap", "Map", "set", "getDataMap", "key", "_data", "length", "getData", "_TbGM", "_TbPlane", "_TbEffect", "_TbWeapon", "_TbEquipUpgrade", "_TbEquip", "_TbItem", "_TbGlobalAttr", "_TbBoss", "_<PERSON><PERSON><PERSON>er", "_TbBullet", "_TbChapter", "_TbEnemy", "_TbEnemyUI", "_TbGameMap", "_TbGameMode", "_TbLevel", "_TbLevelGroup", "_TbMainPlane", "_TbMainPlaneLv", "_Tbskill", "_TbStage", "_TbTask", "_TbTrack", "_TbUnit", "_TbWave", "loader"], "mappings": ";;;iBAgpBaA,I,EAwYAC,M,EAgTAC,M,EAiKAC,O,EA+DAC,W,EAsBAC,Y,EA4BAC,K,EAqIAC,O,EAwIAC,S,EAsBAC,O,EAiKAC,Q,EAkKAC,U,EAwDAC,K,EA+DAC,U,EAyDAC,S,EAsEAC,W,EAsCAC,W,EAqBAC,a,EAyBAC,a,EAyBAC,O,EA4BAC,S,EAyCAC,Q,EA6CAC,e,EA+CAC,K,EAqCAC,O,EAqDAC,Q,EAiDAC,S,EAsBAC,Y,EA0CAC,K,EA0EAC,K,EA+DAC,I,EAqIAC,K,EAmCAC,I,EAmJAC,I,EA6GAC,I,EA+BAC,O,EA+BAC,Q,EAkCAC,Q,EAkCAC,c,EA+BAC,O,EAkCAC,M,EA+BAC,Y,EA6CAC,M,EA+BAC,Q,EA+BAC,Q,EA+BAC,S,EA+BAC,O,EA+BAC,S,EA+BAC,S,EA+BAC,U,EA+BAC,O,EA+BAC,Y,EA+BAC,W,EA+BAC,a,EA+BAC,O,EA+BAC,O,EA+BAC,M,EA+BAC,O,EA+BAC,M,EA+BAC,M,EAiCAC,M;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAjvIDC,Q,0BAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;iBAAAA,Q;;;;SAJKC,G,mBAAAA,G;;;YAwBLC,U,0BAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;iBAAAA,U;;;;SAJKD,G,mBAAAA,G;;;YAgLLE,U,0BAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;iBAAAA,U;;;;SAJKF,G,mBAAAA,G;;;YAgCLG,O,0BAAAA,O;AAAAA,UAAAA,O,CAAAA,O;AAAAA,UAAAA,O,CAAAA,O;iBAAAA,O;;;;SAJKH,G,mBAAAA,G;;;YAoBLI,c,0BAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;AAAAA,UAAAA,c,CAAAA,c;iBAAAA,c;;;;SAJKJ,G,mBAAAA,G;;;YAwCLK,W,0BAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;iBAAAA,W;;;;SAJKL,G,mBAAAA,G;;;YAwBLM,Q,0BAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;iBAAAA,Q;;;;SAJKN,G,mBAAAA,G;;;YAgCLO,S,0BAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;iBAAAA,S;;;;SAJKP,G,mBAAAA,G;;;YAgCLQ,S,0BAAAA,S;AAAAA,UAAAA,S,CAAAA,S;AAAAA,UAAAA,S,CAAAA,S;iBAAAA,S;;;;SAJKR,G,mBAAAA,G;;;YAoBLS,Q,0BAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;AAAAA,UAAAA,Q,CAAAA,Q;iBAAAA,Q;;;;SAJKT,G,mBAAAA,G;;;YAwBLU,W,0BAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;AAAAA,UAAAA,W,CAAAA,W;iBAAAA,W;;;;SAJKV,G,mBAAAA,G;;;YAwCLW,kB,0BAAAA,kB;AAAAA,UAAAA,kB,CAAAA,kB;iBAAAA,kB;;;;SAJKX,G,mBAAAA,G;;;YAgBLY,U,0BAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;AAAAA,UAAAA,U,CAAAA,U;iBAAAA,U;;;;SAJKZ,G,mBAAAA,G;;oCAiCLa,kB,0BAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;AAAAA,QAAAA,kB,CAAAA,kB;eAAAA,kB;;;2BAaAC,S,0BAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;AAAAA,QAAAA,S,CAAAA,S;eAAAA,S;;;8BAyBAC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;;gCAyBAC,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;gCA6FAC,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;;;sBAyBC/E,I,GAAN,MAAMA,IAAN,CAAW;AAEdgF,QAAAA,WAAW,CAACC,MAAD,EAAc;AA+GzB;AACJ;AACA;AAjH6B,eAkHhBC,EAlHgB;;AAmHzB;AACJ;AACA;AArH6B,eAsHhBC,GAtHgB;;AAuHzB;AACJ;AACA;AAzH6B,eA0HhBC,GA1HgB;;AA2HzB;AACJ;AACA;AA7H6B,eA8HhBC,GA9HgB;;AA+HzB;AACJ;AACA;AAjI6B,eAkIhBC,EAlIgB;;AAmIzB;AACJ;AACA;AArI6B,eAsIhBC,EAtIgB;;AAuIzB;AACJ;AACA;AAzI6B,eA0IhBC,KA1IgB;;AA2IzB;AACJ;AACA;AA7I6B,eA8IhBC,GA9IgB;;AA+IzB;AACJ;AACA;AAjJ6B,eAkJhBC,GAlJgB;;AAmJzB;AACJ;AACA;AArJ6B,eAsJhBC,EAtJgB;;AAuJzB;AACJ;AACA;AAzJ6B,eA0JhBC,GA1JgB;;AA2JzB;AACJ;AACA;AA7J6B,eA8JhBC,EA9JgB;;AA+JzB;AACJ;AACA;AAjK6B,eAkKhBC,EAlKgB;;AAmKzB;AACJ;AACA;AArK6B,eAsKhBC,EAtKgB;;AAuKzB;AACJ;AACA;AAzK6B,eA0KhBC,EA1KgB;;AA2KzB;AACJ;AACA;AA7K6B,eA8KhBC,EA9KgB;;AA+KzB;AACJ;AACA;AAjL6B,eAkLhBC,IAlLgB;;AAmLzB;AACJ;AACA;AArL6B,eAsLhBC,MAtLgB;;AAuLzB;AACJ;AACA;AAzL6B,eA0LhBC,GA1LgB;;AA2LzB;AACJ;AACA;AA7L6B,eA8LhBC,GA9LgB;;AA+LzB;AACJ;AACA;AAjM6B,eAkMhBC,EAlMgB;;AAmMzB;AACJ;AACA;AArM6B,eAsMhBC,GAtMgB;;AAuMzB;AACJ;AACA;AAzM6B,eA0MhBC,GA1MgB;;AA2MzB;AACJ;AACA;AA7M6B,eA8MhBC,IA9MgB;;AA+MzB;AACJ;AACA;AAjN6B,eAkNhBC,GAlNgB;;AAmNzB;AACJ;AACA;AArN6B,eAsNhBC,EAtNgB;;AAuNzB;AACJ;AACA;AAzN6B,eA0NhBC,EA1NgB;;AA2NzB;AACJ;AACA;AA7N6B,eA8NhBC,EA9NgB;;AA+NzB;AACJ;AACA;AAjO6B,eAkOhBC,EAlOgB;;AAmOzB;AACJ;AACA;AArO6B,eAsOhBC,EAtOgB;;AAuOzB;AACJ;AACA;AAzO6B,eA0OhBC,EA1OgB;;AA2OzB;AACJ;AACA;AA7O6B,eA8OhBC,EA9OgB;;AA+OzB;AACJ;AACA;AAjP6B,eAkPhBC,EAlPgB;;AAmPzB;AACJ;AACA;AArP6B,eAsPhBC,EAtPgB;;AAuPzB;AACJ;AACA;AAzP6B,eA0PhBC,EA1PgB;;AA2PzB;AACJ;AACA;AA7P6B,eA8PhBC,EA9PgB;;AA+PzB;AACJ;AACA;AAjQ6B,eAkQhBC,EAlQgB;;AAmQzB;AACJ;AACA;AArQ6B,eAsQhBC,EAtQgB;;AAuQzB;AACJ;AACA;AAzQ6B,eA0QhBC,EA1QgB;;AA2QzB;AACJ;AACA;AA7Q6B,eA8QhBC,GA9QgB;;AA+QzB;AACJ;AACA;AAjR6B,eAkRhBC,GAlRgB;;AAmRzB;AACJ;AACA;AArR6B,eAsRhBC,GAtRgB;;AAuRzB;AACJ;AACA;AAzR6B,eA0RhBC,GA1RgB;;AA2RzB;AACJ;AACA;AA7R6B,eA8RhBC,GA9RgB;;AA+RzB;AACJ;AACA;AAjS6B,eAkShBC,GAlSgB;;AAmSzB;AACJ;AACA;AArS6B,eAsShBC,GAtSgB;;AAuSzB;AACJ;AACA;AAzS6B,eA0ShBC,GA1SgB;;AA2SzB;AACJ;AACA;AA7S6B,eA8ShBC,GA9SgB;;AA+SzB;AACJ;AACA;AAjT6B,eAkThBC,GAlTgB;;AAmTzB;AACJ;AACA;AArT6B,eAsThBC,GAtTgB;;AAuTzB;AACJ;AACA;AAzT6B,eA0ThBC,GA1TgB;;AA2TzB;AACJ;AACA;AA7T6B,eA8ThBC,GA9TgB;;AA+TzB;AACJ;AACA;AAjU6B,eAkUhBC,IAlUgB;;AAmUzB;AACJ;AACA;AArU6B,eAsUhBC,IAtUgB;;AACrB,cAAItD,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACE,GAAP,KAAeqD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKtD,GAAL,GAAWF,MAAM,CAACE,GAAlB;;AACA,cAAIF,MAAM,CAACG,GAAP,KAAeoD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKrD,GAAL,GAAWH,MAAM,CAACG,GAAlB;;AACA,cAAIH,MAAM,CAACI,GAAP,KAAemD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKpD,GAAL,GAAWJ,MAAM,CAACI,GAAlB;;AACA,cAAIJ,MAAM,CAACK,EAAP,KAAckD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKnD,EAAL,GAAUL,MAAM,CAACK,EAAjB;;AACA,cAAIL,MAAM,CAACM,EAAP,KAAciD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKlD,EAAL,GAAUN,MAAM,CAACM,EAAjB;;AACA,cAAIN,MAAM,CAACO,KAAP,KAAiBgD,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKjD,KAAL,GAAaP,MAAM,CAACO,KAApB;;AACA,cAAIP,MAAM,CAACQ,GAAP,KAAe+C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKhD,GAAL,GAAWR,MAAM,CAACQ,GAAlB;;AACA,cAAIR,MAAM,CAACS,GAAP,KAAe8C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK/C,GAAL,GAAWT,MAAM,CAACS,GAAlB;;AACA,cAAIT,MAAM,CAACU,EAAP,KAAc6C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK9C,EAAL,GAAUV,MAAM,CAACU,EAAjB;;AACA,cAAIV,MAAM,CAACW,GAAP,KAAe4C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK7C,GAAL,GAAWX,MAAM,CAACW,GAAlB;;AACA,cAAIX,MAAM,CAACY,EAAP,KAAc2C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK5C,EAAL,GAAUZ,MAAM,CAACY,EAAjB;;AACA,cAAIZ,MAAM,CAACa,EAAP,KAAc0C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK3C,EAAL,GAAUb,MAAM,CAACa,EAAjB;;AACA,cAAIb,MAAM,CAACc,EAAP,KAAcyC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK1C,EAAL,GAAUd,MAAM,CAACc,EAAjB;;AACA,cAAId,MAAM,CAACe,EAAP,KAAcwC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKzC,EAAL,GAAUf,MAAM,CAACe,EAAjB;;AACA,cAAIf,MAAM,CAACgB,EAAP,KAAcuC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKxC,EAAL,GAAUhB,MAAM,CAACgB,EAAjB;;AACA,cAAIhB,MAAM,CAACiB,IAAP,KAAgBsC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKvC,IAAL,GAAYjB,MAAM,CAACiB,IAAnB;;AACA,cAAIjB,MAAM,CAACkB,MAAP,KAAkBqC,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKtC,MAAL,GAAclB,MAAM,CAACkB,MAArB;;AACA,cAAIlB,MAAM,CAACmB,GAAP,KAAeoC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKrC,GAAL,GAAWnB,MAAM,CAACmB,GAAlB;;AACA,cAAInB,MAAM,CAACoB,GAAP,KAAemC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKpC,GAAL,GAAWpB,MAAM,CAACoB,GAAlB;;AACA,cAAIpB,MAAM,CAACqB,EAAP,KAAckC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKnC,EAAL,GAAUrB,MAAM,CAACqB,EAAjB;;AACA,cAAIrB,MAAM,CAACsB,GAAP,KAAeiC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKlC,GAAL,GAAWtB,MAAM,CAACsB,GAAlB;;AACA,cAAItB,MAAM,CAACuB,GAAP,KAAegC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKjC,GAAL,GAAWvB,MAAM,CAACuB,GAAlB;;AACA,cAAIvB,MAAM,CAACwB,IAAP,KAAgB+B,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKhC,IAAL,GAAYxB,MAAM,CAACwB,IAAnB;;AACA,cAAIxB,MAAM,CAACyB,GAAP,KAAe8B,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK/B,GAAL,GAAWzB,MAAM,CAACyB,GAAlB;;AACA,cAAIzB,MAAM,CAAC0B,EAAP,KAAc6B,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK9B,EAAL,GAAU1B,MAAM,CAAC0B,EAAjB;;AACA,cAAI1B,MAAM,CAAC2B,EAAP,KAAc4B,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK7B,EAAL,GAAU3B,MAAM,CAAC2B,EAAjB;;AACA,cAAI3B,MAAM,CAAC4B,EAAP,KAAc2B,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK5B,EAAL,GAAU5B,MAAM,CAAC4B,EAAjB;;AACA,cAAI5B,MAAM,CAAC6B,EAAP,KAAc0B,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK3B,EAAL,GAAU7B,MAAM,CAAC6B,EAAjB;;AACA,cAAI7B,MAAM,CAAC8B,EAAP,KAAcyB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK1B,EAAL,GAAU9B,MAAM,CAAC8B,EAAjB;;AACA,cAAI9B,MAAM,CAAC+B,EAAP,KAAcwB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKzB,EAAL,GAAU/B,MAAM,CAAC+B,EAAjB;;AACA,cAAI/B,MAAM,CAACgC,EAAP,KAAcuB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKxB,EAAL,GAAUhC,MAAM,CAACgC,EAAjB;;AACA,cAAIhC,MAAM,CAACiC,EAAP,KAAcsB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvB,EAAL,GAAUjC,MAAM,CAACiC,EAAjB;;AACA,cAAIjC,MAAM,CAACkC,EAAP,KAAcqB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKtB,EAAL,GAAUlC,MAAM,CAACkC,EAAjB;;AACA,cAAIlC,MAAM,CAACmC,EAAP,KAAcoB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKrB,EAAL,GAAUnC,MAAM,CAACmC,EAAjB;;AACA,cAAInC,MAAM,CAACoC,EAAP,KAAcmB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKpB,EAAL,GAAUpC,MAAM,CAACoC,EAAjB;;AACA,cAAIpC,MAAM,CAACqC,EAAP,KAAckB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKnB,EAAL,GAAUrC,MAAM,CAACqC,EAAjB;;AACA,cAAIrC,MAAM,CAACsC,EAAP,KAAciB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKlB,EAAL,GAAUtC,MAAM,CAACsC,EAAjB;;AACA,cAAItC,MAAM,CAACuC,EAAP,KAAcgB,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKjB,EAAL,GAAUvC,MAAM,CAACuC,EAAjB;;AACA,cAAIvC,MAAM,CAACwC,GAAP,KAAee,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKhB,GAAL,GAAWxC,MAAM,CAACwC,GAAlB;;AACA,cAAIxC,MAAM,CAACyC,GAAP,KAAec,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKf,GAAL,GAAWzC,MAAM,CAACyC,GAAlB;;AACA,cAAIzC,MAAM,CAAC0C,GAAP,KAAea,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKd,GAAL,GAAW1C,MAAM,CAAC0C,GAAlB;;AACA,cAAI1C,MAAM,CAAC2C,GAAP,KAAeY,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKb,GAAL,GAAW3C,MAAM,CAAC2C,GAAlB;;AACA,cAAI3C,MAAM,CAAC4C,GAAP,KAAeW,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKZ,GAAL,GAAW5C,MAAM,CAAC4C,GAAlB;;AACA,cAAI5C,MAAM,CAAC6C,GAAP,KAAeU,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKX,GAAL,GAAW7C,MAAM,CAAC6C,GAAlB;;AACA,cAAI7C,MAAM,CAAC8C,GAAP,KAAeS,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKV,GAAL,GAAW9C,MAAM,CAAC8C,GAAlB;;AACA,cAAI9C,MAAM,CAAC+C,GAAP,KAAeQ,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKT,GAAL,GAAW/C,MAAM,CAAC+C,GAAlB;;AACA,cAAI/C,MAAM,CAACgD,GAAP,KAAeO,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKR,GAAL,GAAWhD,MAAM,CAACgD,GAAlB;;AACA,cAAIhD,MAAM,CAACiD,GAAP,KAAeM,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKP,GAAL,GAAWjD,MAAM,CAACiD,GAAlB;;AACA,cAAIjD,MAAM,CAACkD,GAAP,KAAeK,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKN,GAAL,GAAWlD,MAAM,CAACkD,GAAlB;;AACA,cAAIlD,MAAM,CAACmD,GAAP,KAAeI,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKL,GAAL,GAAWnD,MAAM,CAACmD,GAAlB;;AACA,cAAInD,MAAM,CAACoD,GAAP,KAAeG,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKJ,GAAL,GAAWpD,MAAM,CAACoD,GAAlB;;AACA,cAAIpD,MAAM,CAACqD,IAAP,KAAgBE,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKH,IAAL,GAAYrD,MAAM,CAACqD,IAAnB;;AACA,cAAIrD,MAAM,CAACsD,IAAP,KAAgBC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKF,IAAL,GAAYtD,MAAM,CAACsD,IAAnB;AACH;;AA2NDG,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAuDtB;;AAjYa,O;;wBAwYL1I,M,GAAN,MAAMA,MAAN,CAAa;AAEhB+E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,EA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhB0D,QA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBC,QAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,aAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,QA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,WA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,KAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhBC,UAtDgB;AAAA,eAuDhBC,OAvDgB;;AAwDzB;AACJ;AACA;AA1D6B,eA2DhBC,WA3DgB;;AACrB,cAAInE,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC2D,QAAP,KAAoBJ,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKG,QAAL,GAAgB3D,MAAM,CAAC2D,QAAvB;;AACA,cAAI3D,MAAM,CAAC4D,QAAP,KAAoBL,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKI,QAAL,GAAgB5D,MAAM,CAAC4D,QAAvB;;AACA,cAAI5D,MAAM,CAAC6D,aAAP,KAAyBN,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKK,aAAL,GAAqB7D,MAAM,CAAC6D,aAA5B;;AACA,cAAI7D,MAAM,CAAC8D,QAAP,KAAoBP,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKM,QAAL,GAAgB9D,MAAM,CAAC8D,QAAvB;;AACA,cAAI9D,MAAM,CAAC+D,WAAP,KAAuBR,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKO,WAAL,GAAmB/D,MAAM,CAAC+D,WAA1B;;AACA,cAAI/D,MAAM,CAACgE,KAAP,KAAiBT,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKQ,KAAL,GAAahE,MAAM,CAACgE,KAApB;;AACA,cAAIhE,MAAM,CAACiE,UAAP,KAAsBV,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKS,UAAL,GAAkBjE,MAAM,CAACiE,UAAzB;;AACA,cAAIjE,MAAM,CAACkE,OAAP,KAAmBX,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAKU,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAIE,KAAR,IAAiBpE,MAAM,CAACkE,OAAxB,EAAiC;AAAE,kBAAIG,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIC,OAAO,CAACC,WAAZ,CAAwBH,KAAxB,CAAN;AAAsC,mBAAKF,OAAL,CAAaM,IAAb,CAAkBH,GAAlB;AAAwB;AAAC;;AAChI,cAAIrE,MAAM,CAACmE,WAAP,KAAuBZ,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKW,WAAL,GAAmBnE,MAAM,CAACmE,WAA1B;AACH;;AAwCDV,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAWtB;;AA1Ee,O;;;AAiFb,cAAMe,SAAN,CAAgB;AAEnB1E,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhB0E,MAPgB;AAAA,iBAQhBC,MARgB;;AACrB,gBAAI3E,MAAM,CAAC0E,MAAP,KAAkBnB,SAAtB,EAAiC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,iBAAKkB,MAAL,GAAc1E,MAAM,CAAC0E,MAArB;;AACA,gBAAI1E,MAAM,CAAC2E,MAAP,KAAkBpB,SAAtB,EAAiC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,iBAAKmB,MAAL,GAAc3E,MAAM,CAAC2E,MAArB;AACH;;AAKDlB,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB;;;SADNY,O,uBAAAA,O;;;AAuBV,cAAMM,QAAN,CAAe;AAElB7E,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhB6E,GAPgB;AAAA,iBAQhBC,KARgB;;AACrB,gBAAI9E,MAAM,CAAC6E,GAAP,KAAetB,SAAnB,EAA8B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,iBAAKqB,GAAL,GAAW7E,MAAM,CAAC6E,GAAlB;;AACA,gBAAI7E,MAAM,CAAC8E,KAAP,KAAiBvB,SAArB,EAAgC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,iBAAKsB,KAAL,GAAa9E,MAAM,CAAC8E,KAApB;AACH;;AAKDrB,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfiB;;;SADLY,O,uBAAAA,O;;;AAuBV,cAAMC,WAAN,CAAkB;AAErBxE,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAShB+E,IATgB;AAAA,iBAUhBL,MAVgB;AAAA,iBAWhBI,KAXgB;;AACrB,gBAAI9E,MAAM,CAAC+E,IAAP,KAAgBxB,SAApB,EAA+B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,iBAAKuB,IAAL,GAAY/E,MAAM,CAAC+E,IAAnB;;AACA,gBAAI/E,MAAM,CAAC0E,MAAP,KAAkBnB,SAAtB,EAAiC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,iBAAKkB,MAAL,GAAc1E,MAAM,CAAC0E,MAArB;;AACA,gBAAI1E,MAAM,CAAC8E,KAAP,KAAiBvB,SAArB,EAAgC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,mBAAKsB,KAAL,GAAa,EAAb;;AAAiB,mBAAI,IAAIV,KAAR,IAAiBpE,MAAM,CAAC8E,KAAxB,EAA+B;AAAE,oBAAIT,GAAJ;;AAASA,gBAAAA,GAAG,GAAGD,KAAN;AAAa,qBAAKU,KAAL,CAAWN,IAAX,CAAgBH,GAAhB;AAAsB;AAAC;AACpG;;AAMDZ,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBoB;;;SADRY,O,uBAAAA,O;;;AA8BV,cAAMU,YAAN,CAAmB;AAEtBjF,UAAAA,WAAW,CAACC,MAAD,EAAc;AAOzB;AACJ;AACA;AAT6B,iBAUhBiF,EAVgB;;AAWzB;AACJ;AACA;AAb6B,iBAchBC,MAdgB;;AACrB,gBAAIlF,MAAM,CAACiF,EAAP,KAAc1B,SAAlB,EAA6B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,iBAAKyB,EAAL,GAAUjF,MAAM,CAACiF,EAAjB;;AACA,gBAAIjF,MAAM,CAACkF,MAAP,KAAkB3B,SAAtB,EAAiC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,iBAAK0B,MAAL,GAAclF,MAAM,CAACkF,MAArB;AACH;;AAWDzB,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AArBqB;;;SAJTY,O,uBAAAA,O;;;AAgCV,cAAMa,WAAN,CAAkB;AAErBpF,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhBoF,MAPgB;AAAA,iBAQhBN,KARgB;;AACrB,gBAAI9E,MAAM,CAACoF,MAAP,KAAkB7B,SAAtB,EAAiC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,iBAAK4B,MAAL,GAAcpF,MAAM,CAACoF,MAArB;;AACA,gBAAIpF,MAAM,CAAC8E,KAAP,KAAiBvB,SAArB,EAAgC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,iBAAKsB,KAAL,GAAa9E,MAAM,CAAC8E,KAApB;AACH;;AAKDrB,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfoB;;;SADRY,O,uBAAAA,O;;;AAuBV,cAAMe,cAAN,CAAqB;AAExBtF,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhB+E,IAPgB;AAAA,iBAQhBD,KARgB;;AACrB,gBAAI9E,MAAM,CAAC+E,IAAP,KAAgBxB,SAApB,EAA+B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,iBAAKuB,IAAL,GAAY/E,MAAM,CAAC+E,IAAnB;;AACA,gBAAI/E,MAAM,CAAC8E,KAAP,KAAiBvB,SAArB,EAAgC;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,mBAAKsB,KAAL,GAAa,EAAb;;AAAiB,mBAAI,IAAIV,KAAR,IAAiBpE,MAAM,CAAC8E,KAAxB,EAA+B;AAAE,oBAAIT,GAAJ;;AAASA,gBAAAA,GAAG,GAAGD,KAAN;AAAa,qBAAKU,KAAL,CAAWN,IAAX,CAAgBH,GAAhB;AAAsB;AAAC;AACpG;;AAKDZ,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfuB;;;SADXY,O,uBAAAA,O;;;AAuBV,cAAMgB,OAAN,CAAc;AAEjBvF,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAOhBuF,CAPgB;AAAA,iBAQhBC,CARgB;;AACrB,gBAAIxF,MAAM,CAACuF,CAAP,KAAahC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAK+B,CAAL,GAASvF,MAAM,CAACuF,CAAhB;;AACA,gBAAIvF,MAAM,CAACwF,CAAP,KAAajC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKgC,CAAL,GAASxF,MAAM,CAACwF,CAAhB;AACH;;AAKD/B,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfgB;;;SADJY,O,uBAAAA,O;;;AAuBV,cAAMmB,OAAN,CAAc;AAEjB1F,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAShBuF,CATgB;AAAA,iBAUhBC,CAVgB;AAAA,iBAWhBE,CAXgB;;AACrB,gBAAI1F,MAAM,CAACuF,CAAP,KAAahC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAK+B,CAAL,GAASvF,MAAM,CAACuF,CAAhB;;AACA,gBAAIvF,MAAM,CAACwF,CAAP,KAAajC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKgC,CAAL,GAASxF,MAAM,CAACwF,CAAhB;;AACA,gBAAIxF,MAAM,CAAC0F,CAAP,KAAanC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKkC,CAAL,GAAS1F,MAAM,CAAC0F,CAAhB;AACH;;AAMDjC,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AAnBgB;;;SADJY,O,uBAAAA,O;;;AA2BV,cAAMqB,OAAN,CAAc;AAEjB5F,UAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,iBAWhBuF,CAXgB;AAAA,iBAYhBC,CAZgB;AAAA,iBAahBE,CAbgB;AAAA,iBAchBE,CAdgB;;AACrB,gBAAI5F,MAAM,CAACuF,CAAP,KAAahC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAK+B,CAAL,GAASvF,MAAM,CAACuF,CAAhB;;AACA,gBAAIvF,MAAM,CAACwF,CAAP,KAAajC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKgC,CAAL,GAASxF,MAAM,CAACwF,CAAhB;;AACA,gBAAIxF,MAAM,CAAC0F,CAAP,KAAanC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKkC,CAAL,GAAS1F,MAAM,CAAC0F,CAAhB;;AACA,gBAAI1F,MAAM,CAAC4F,CAAP,KAAarC,SAAjB,EAA4B;AAAE,oBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjD,iBAAKoC,CAAL,GAAS5F,MAAM,CAAC4F,CAAhB;AACH;;AAODnC,UAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAvBgB;;;SADJY,O,uBAAAA,O;;wBA+BJrJ,M,GAAN,MAAMA,MAAN,CAAa;AAEhB8E,QAAAA,WAAW,CAACC,MAAD,EAAc;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,EAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhB4F,IApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,EAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,KA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,OAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,UApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,QAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,SA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,MAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,KApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,QAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,SA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,YAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,IApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,QAxGgB;;AAyGzB;AACJ;AACA;AA3G6B,eA4GhBC,QA5GgB;;AA6GzB;AACJ;AACA;AA/G6B,eAgHhBC,IAhHgB;;AAiHzB;AACJ;AACA;AAnH6B,eAoHhBC,SApHgB;;AAqHzB;AACJ;AACA;AAvH6B,eAwHhBC,GAxHgB;;AAyHzB;AACJ;AACA;AA3H6B,eA4HhBC,MA5HgB;;AA6HzB;AACJ;AACA;AA/H6B,eAgIhBC,IAhIgB;;AACrB,cAAIhH,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC6F,IAAP,KAAgBtC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqC,IAAL,GAAY7F,MAAM,CAAC6F,IAAnB;;AACA,cAAI7F,MAAM,CAAC8F,EAAP,KAAcvC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKsC,EAAL,GAAU9F,MAAM,CAAC8F,EAAjB;;AACA,cAAI9F,MAAM,CAAC+F,KAAP,KAAiBxC,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKuC,KAAL,GAAa/F,MAAM,CAAC+F,KAApB;;AACA,cAAI/F,MAAM,CAACgG,OAAP,KAAmBzC,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKwC,OAAL,GAAehG,MAAM,CAACgG,OAAtB;;AACA,cAAIhG,MAAM,CAACiG,UAAP,KAAsB1C,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyC,UAAL,GAAkBjG,MAAM,CAACiG,UAAzB;;AACA,cAAIjG,MAAM,CAACkG,QAAP,KAAoB3C,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAK0C,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAI9B,KAAR,IAAiBpE,MAAM,CAACkG,QAAxB,EAAkC;AAAE,kBAAI7B,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK8B,QAAL,CAAc1B,IAAd,CAAmBH,GAAnB;AAAyB;AAAC;;AAC1G,cAAIrE,MAAM,CAACmG,SAAP,KAAqB5C,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK2C,SAAL,GAAiBnG,MAAM,CAACmG,SAAxB;;AACA,cAAInG,MAAM,CAACoG,MAAP,KAAkB7C,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAK4C,MAAL,GAAcpG,MAAM,CAACoG,MAArB;;AACA,cAAIpG,MAAM,CAACqG,KAAP,KAAiB9C,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK6C,KAAL,GAAarG,MAAM,CAACqG,KAApB;;AACA,cAAIrG,MAAM,CAACsG,QAAP,KAAoB/C,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK8C,QAAL,GAAgBtG,MAAM,CAACsG,QAAvB;;AACA,cAAItG,MAAM,CAACuG,SAAP,KAAqBhD,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK+C,SAAL,GAAiBvG,MAAM,CAACuG,SAAxB;;AACA,cAAIvG,MAAM,CAACwG,YAAP,KAAwBjD,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAKgD,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIpC,KAAR,IAAiBpE,MAAM,CAACwG,YAAxB,EAAsC;AAAE,kBAAInC,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKoC,YAAL,CAAkBhC,IAAlB,CAAuBH,GAAvB;AAA6B;AAAC;;AACtH,cAAIrE,MAAM,CAACyG,IAAP,KAAgBlD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKiD,IAAL,GAAYzG,MAAM,CAACyG,IAAnB;;AACA,cAAIzG,MAAM,CAAC0G,QAAP,KAAoBnD,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkD,QAAL,GAAgB1G,MAAM,CAAC0G,QAAvB;;AACA,cAAI1G,MAAM,CAAC2G,QAAP,KAAoBpD,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKmD,QAAL,GAAgB3G,MAAM,CAAC2G,QAAvB;;AACA,cAAI3G,MAAM,CAAC4G,IAAP,KAAgBrD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKoD,IAAL,GAAY5G,MAAM,CAAC4G,IAAnB;;AACA,cAAI5G,MAAM,CAAC6G,SAAP,KAAqBtD,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKqD,SAAL,GAAiB7G,MAAM,CAAC6G,SAAxB;;AACA,cAAI7G,MAAM,CAAC8G,GAAP,KAAevD,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKsD,GAAL,GAAW9G,MAAM,CAAC8G,GAAlB;;AACA,cAAI9G,MAAM,CAAC+G,MAAP,KAAkBxD,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD;AAAE,iBAAKuD,MAAL,GAAc,EAAd;;AAAkB,iBAAI,IAAI3C,KAAR,IAAiBpE,MAAM,CAAC+G,MAAxB,EAAgC;AAAE,kBAAI1C,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK2C,MAAL,CAAYvC,IAAZ,CAAiBH,GAAjB;AAAuB;AAAC;;AACpG,cAAIrE,MAAM,CAACgH,IAAP,KAAgBzD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD;AAAE,iBAAKwD,IAAL,GAAY,EAAZ;;AAAgB,iBAAI,IAAI5C,KAAR,IAAiBpE,MAAM,CAACgH,IAAxB,EAA8B;AAAE,kBAAI3C,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK4C,IAAL,CAAUxC,IAAV,CAAeH,GAAf;AAAqB;AAAC;AACjG;;AAuFDZ,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAsBtB;;AA1Je,O;;yBAiKPxI,O,GAAN,MAAMA,OAAN,CAAc;AAEjB6E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBgH,UAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,eA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,QAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,WApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,UAxCgB;AAAA,eAyChBC,YAzCgB;;AACrB,cAAItH,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACiH,UAAP,KAAsB1D,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyD,UAAL,GAAkBjH,MAAM,CAACiH,UAAzB;;AACA,cAAIjH,MAAM,CAACkH,eAAP,KAA2B3D,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAK0D,eAAL,GAAuBlH,MAAM,CAACkH,eAA9B;;AACA,cAAIlH,MAAM,CAACmH,QAAP,KAAoB5D,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK2D,QAAL,GAAgBnH,MAAM,CAACmH,QAAvB;;AACA,cAAInH,MAAM,CAACoH,WAAP,KAAuB7D,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK4D,WAAL,GAAmBpH,MAAM,CAACoH,WAA1B;;AACA,cAAIpH,MAAM,CAACqH,UAAP,KAAsB9D,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK6D,UAAL,GAAkBrH,MAAM,CAACqH,UAAzB;;AACA,cAAIrH,MAAM,CAACsH,YAAP,KAAwB/D,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAK8D,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIlD,KAAR,IAAiBpE,MAAM,CAACsH,YAAxB,EAAsC;AAAE,kBAAIjD,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIC,OAAO,CAACU,YAAZ,CAAyBZ,KAAzB,CAAN;AAAuC,mBAAKkD,YAAL,CAAkB9C,IAAlB,CAAuBH,GAAvB;AAA6B;AAAC;AACnJ;;AA4BDZ,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAOnB,eAAK,IAAI6D,EAAT,IAAe,KAAKD,YAApB,EAAkC;AAAEC,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9D,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AArDgB,O;AA4DrB;AACA;AACA;;;6BACavI,W,GAAN,MAAMA,WAAN,CAAkB;AAErB4E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,EAPgB;AAAA,eAQhBuH,GARgB;;AACrB,cAAIxH,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACwH,GAAP,KAAejE,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKgE,GAAL,GAAWxH,MAAM,CAACwH,GAAlB;AACH;;AAKD/D,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfoB,O;;8BAsBZtI,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtB2E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAOzB;AACJ;AACA;AAT6B,eAUhB+E,IAVgB;;AAWzB;AACJ;AACA;AAb6B,eAchByC,GAdgB;;AACrB,cAAIxH,MAAM,CAAC+E,IAAP,KAAgBxB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKuB,IAAL,GAAY/E,MAAM,CAAC+E,IAAnB;;AACA,cAAI/E,MAAM,CAACwH,GAAP,KAAejE,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKgE,GAAL,GAAWxH,MAAM,CAACwH,GAAlB;AACH;;AAWD/D,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AArBqB,O;;uBA4BbrI,K,GAAN,MAAMA,KAAN,CAAY;AAEf0E,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,EAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBwH,IA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBnG,GAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBoG,EApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,YAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,IA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,MAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,aApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,YAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,WA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,WAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,cApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,SAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,UA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBtD,KAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBuD,QApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,SAxGgB;;AACrB,cAAItI,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACyH,IAAP,KAAgBlE,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKiE,IAAL,GAAYzH,MAAM,CAACyH,IAAnB;;AACA,cAAIzH,MAAM,CAACsB,GAAP,KAAeiC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKlC,GAAL,GAAWtB,MAAM,CAACsB,GAAlB;;AACA,cAAItB,MAAM,CAAC0H,EAAP,KAAcnE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKkE,EAAL,GAAU1H,MAAM,CAAC0H,EAAjB;;AACA,cAAI1H,MAAM,CAAC2H,YAAP,KAAwBpE,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKmE,YAAL,GAAoB3H,MAAM,CAAC2H,YAA3B;;AACA,cAAI3H,MAAM,CAAC4H,IAAP,KAAgBrE,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKoE,IAAL,GAAY5H,MAAM,CAAC4H,IAAnB;;AACA,cAAI5H,MAAM,CAAC6H,MAAP,KAAkBtE,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKqE,MAAL,GAAc7H,MAAM,CAAC6H,MAArB;;AACA,cAAI7H,MAAM,CAAC8H,aAAP,KAAyBvE,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKsE,aAAL,GAAqB9H,MAAM,CAAC8H,aAA5B;;AACA,cAAI9H,MAAM,CAAC+H,YAAP,KAAwBxE,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKuE,YAAL,GAAoB/H,MAAM,CAAC+H,YAA3B;;AACA,cAAI/H,MAAM,CAACgI,WAAP,KAAuBzE,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKwE,WAAL,GAAmBhI,MAAM,CAACgI,WAA1B;;AACA,cAAIhI,MAAM,CAACiI,WAAP,KAAuB1E,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKyE,WAAL,GAAmBjI,MAAM,CAACiI,WAA1B;;AACA,cAAIjI,MAAM,CAACkI,cAAP,KAA0B3E,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAK0E,cAAL,GAAsBlI,MAAM,CAACkI,cAA7B;;AACA,cAAIlI,MAAM,CAACmI,SAAP,KAAqB5E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK2E,SAAL,GAAiBnI,MAAM,CAACmI,SAAxB;;AACA,cAAInI,MAAM,CAACoI,UAAP,KAAsB7E,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK4E,UAAL,GAAkBpI,MAAM,CAACoI,UAAzB;;AACA,cAAIpI,MAAM,CAAC8E,KAAP,KAAiBvB,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKsB,KAAL,GAAa9E,MAAM,CAAC8E,KAApB;;AACA,cAAI9E,MAAM,CAACqI,QAAP,KAAoB9E,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK6E,QAAL,GAAgBrI,MAAM,CAACqI,QAAvB;;AACA,cAAIrI,MAAM,CAACsI,SAAP,KAAqB/E,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK8E,SAAL,GAAiBtI,MAAM,CAACsI,SAAxB;AACH;;AAuED7E,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAkBtB;;AA9Hc,O;;yBAqINpI,O,GAAN,MAAMA,OAAN,CAAc;AAEjByE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,EAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChB8F,KA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBwC,IAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,QApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,OAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,UA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhB/H,GAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBgI,cApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,UAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,WA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,WAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,WApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,iBAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,UA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,UAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,SApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,SAxGgB;;AACrB,cAAIpJ,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC+F,KAAP,KAAiBxC,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKuC,KAAL,GAAa/F,MAAM,CAAC+F,KAApB;;AACA,cAAI/F,MAAM,CAACuI,IAAP,KAAgBhF,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAK+E,IAAL,GAAYvI,MAAM,CAACuI,IAAnB;;AACA,cAAIvI,MAAM,CAACwI,QAAP,KAAoBjF,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKgF,QAAL,GAAgBxI,MAAM,CAACwI,QAAvB;;AACA,cAAIxI,MAAM,CAACyI,OAAP,KAAmBlF,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKiF,OAAL,GAAezI,MAAM,CAACyI,OAAtB;;AACA,cAAIzI,MAAM,CAAC0I,UAAP,KAAsBnF,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKkF,UAAL,GAAkB1I,MAAM,CAAC0I,UAAzB;;AACA,cAAI1I,MAAM,CAACW,GAAP,KAAe4C,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK7C,GAAL,GAAWX,MAAM,CAACW,GAAlB;;AACA,cAAIX,MAAM,CAAC2I,cAAP,KAA0BpF,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKmF,cAAL,GAAsB3I,MAAM,CAAC2I,cAA7B;;AACA,cAAI3I,MAAM,CAAC4I,UAAP,KAAsBrF,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKoF,UAAL,GAAkB5I,MAAM,CAAC4I,UAAzB;;AACA,cAAI5I,MAAM,CAAC6I,WAAP,KAAuBtF,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKqF,WAAL,GAAmB7I,MAAM,CAAC6I,WAA1B;;AACA,cAAI7I,MAAM,CAAC8I,WAAP,KAAuBvF,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKsF,WAAL,GAAmB9I,MAAM,CAAC8I,WAA1B;;AACA,cAAI9I,MAAM,CAAC+I,WAAP,KAAuBxF,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKuF,WAAL,GAAmB/I,MAAM,CAAC+I,WAA1B;;AACA,cAAI/I,MAAM,CAACgJ,iBAAP,KAA6BzF,SAAjC,EAA4C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjE,eAAKwF,iBAAL,GAAyBhJ,MAAM,CAACgJ,iBAAhC;;AACA,cAAIhJ,MAAM,CAACiJ,UAAP,KAAsB1F,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyF,UAAL,GAAkBjJ,MAAM,CAACiJ,UAAzB;;AACA,cAAIjJ,MAAM,CAACkJ,UAAP,KAAsB3F,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK0F,UAAL,GAAkBlJ,MAAM,CAACkJ,UAAzB;;AACA,cAAIlJ,MAAM,CAACmJ,SAAP,KAAqB5F,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK2F,SAAL,GAAiBnJ,MAAM,CAACmJ,SAAxB;;AACA,cAAInJ,MAAM,CAACoJ,SAAP,KAAqB7F,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK4F,SAAL,GAAiBpJ,MAAM,CAACoJ,SAAxB;AACH;;AAuED3F,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAkBtB;;AA9HgB,O;AAqIrB;AACA;AACA;;;2BACanI,S,GAAN,MAAMA,SAAN,CAAgB;AAEnBwE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,EAPgB;AAAA,eAQhBoJ,KARgB;;AACrB,cAAIrJ,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACqJ,KAAP,KAAiB9F,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK6F,KAAL,GAAarJ,MAAM,CAACqJ,KAApB;AACH;;AAKD5F,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB,O;;yBAsBVlI,O,GAAN,MAAMA,OAAN,CAAc;AAEjBuE,QAAAA,WAAW,CAACC,MAAD,EAAc;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,EAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBqJ,KApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,QAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,OA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,MAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,YApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,UAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,QA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,cAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,UApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,QAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,cA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,iBAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,YApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,WAxGgB;;AAyGzB;AACJ;AACA;AA3G6B,eA4GhBC,aA5GgB;;AA6GzB;AACJ;AACA;AA/G6B,eAgHhBC,QAhHgB;;AAiHzB;AACJ;AACA;AAnH6B,eAoHhBC,SApHgB;;AAqHzB;AACJ;AACA;AAvH6B,eAwHhBC,SAxHgB;;AAyHzB;AACJ;AACA;AA3H6B,eA4HhBC,MA5HgB;;AA6HzB;AACJ;AACA;AA/H6B,eAgIhBC,UAhIgB;;AACrB,cAAIzK,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACsJ,KAAP,KAAiB/F,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK8F,KAAL,GAAatJ,MAAM,CAACsJ,KAApB;;AACA,cAAItJ,MAAM,CAAC0K,SAAP,KAAqBnH,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAK+F,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAInF,KAAR,IAAiBpE,MAAM,CAAC0K,SAAxB,EAAmC;AAAE,kBAAIrG,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKmF,QAAL,CAAc/E,IAAd,CAAmBH,GAAnB;AAAyB;AAAC;;AAC3G,cAAIrE,MAAM,CAAC2K,QAAP,KAAoBpH,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAKgG,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAIpF,KAAR,IAAiBpE,MAAM,CAAC2K,QAAxB,EAAkC;AAAE,kBAAItG,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKoF,OAAL,CAAahF,IAAb,CAAkBH,GAAlB;AAAwB;AAAC;;AACxG,cAAIrE,MAAM,CAAC4K,OAAP,KAAmBrH,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAKiG,MAAL,GAAc,EAAd;;AAAkB,iBAAI,IAAIrF,KAAR,IAAiBpE,MAAM,CAAC4K,OAAxB,EAAiC;AAAE,kBAAIvG,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKqF,MAAL,CAAYjF,IAAZ,CAAiBH,GAAjB;AAAuB;AAAC;;AACrG,cAAIrE,MAAM,CAAC6K,aAAP,KAAyBtH,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAKkG,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAItF,KAAR,IAAiBpE,MAAM,CAAC6K,aAAxB,EAAuC;AAAE,kBAAIxG,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKsF,YAAL,CAAkBlF,IAAlB,CAAuBH,GAAvB;AAA6B;AAAC;;AACvH,cAAIrE,MAAM,CAAC8K,WAAP,KAAuBvH,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D;AAAE,iBAAKmG,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIvF,KAAR,IAAiBpE,MAAM,CAAC8K,WAAxB,EAAqC;AAAE,kBAAIzG,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKuF,UAAL,CAAgBnF,IAAhB,CAAqBH,GAArB;AAA2B;AAAC;;AACjH,cAAIrE,MAAM,CAAC+K,SAAP,KAAqBxH,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKoG,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIxF,KAAR,IAAiBpE,MAAM,CAAC+K,SAAxB,EAAmC;AAAE,kBAAI1G,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKwF,QAAL,CAAcpF,IAAd,CAAmBH,GAAnB;AAAyB;AAAC;;AAC3G,cAAIrE,MAAM,CAACgL,eAAP,KAA2BzH,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D;AAAE,iBAAKqG,cAAL,GAAsB,EAAtB;;AAA0B,iBAAI,IAAIzF,KAAR,IAAiBpE,MAAM,CAACgL,eAAxB,EAAyC;AAAE,kBAAI3G,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKyF,cAAL,CAAoBrF,IAApB,CAAyBH,GAAzB;AAA+B;AAAC;;AAC7H,cAAIrE,MAAM,CAACiL,WAAP,KAAuB1H,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D;AAAE,iBAAKsG,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI1F,KAAR,IAAiBpE,MAAM,CAACiL,WAAxB,EAAqC;AAAE,kBAAI5G,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK0F,UAAL,CAAgBtF,IAAhB,CAAqBH,GAArB;AAA2B;AAAC;;AACjH,cAAIrE,MAAM,CAACkL,SAAP,KAAqB3H,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAKuG,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAI3F,KAAR,IAAiBpE,MAAM,CAACkL,SAAxB,EAAmC;AAAE,kBAAI7G,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK2F,QAAL,CAAcvF,IAAd,CAAmBH,GAAnB;AAAyB;AAAC;;AAC3G,cAAIrE,MAAM,CAACmL,eAAP,KAA2B5H,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D;AAAE,iBAAKwG,cAAL,GAAsB,EAAtB;;AAA0B,iBAAI,IAAI5F,KAAR,IAAiBpE,MAAM,CAACmL,eAAxB,EAAyC;AAAE,kBAAI9G,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK4F,cAAL,CAAoBxF,IAApB,CAAyBH,GAAzB;AAA+B;AAAC;;AAC7H,cAAIrE,MAAM,CAACoL,kBAAP,KAA8B7H,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE;AAAE,iBAAKyG,iBAAL,GAAyB,EAAzB;;AAA6B,iBAAI,IAAI7F,KAAR,IAAiBpE,MAAM,CAACoL,kBAAxB,EAA4C;AAAE,kBAAI/G,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK6F,iBAAL,CAAuBzF,IAAvB,CAA4BH,GAA5B;AAAkC;AAAC;;AACtI,cAAIrE,MAAM,CAACqL,aAAP,KAAyB9H,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAK0G,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAI9F,KAAR,IAAiBpE,MAAM,CAACqL,aAAxB,EAAuC;AAAE,kBAAIhH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK8F,YAAL,CAAkB1F,IAAlB,CAAuBH,GAAvB;AAA6B;AAAC;;AACvH,cAAIrE,MAAM,CAACsL,YAAP,KAAwB/H,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAK2G,WAAL,GAAmB,EAAnB;;AAAuB,iBAAI,IAAI/F,KAAR,IAAiBpE,MAAM,CAACsL,YAAxB,EAAsC;AAAE,kBAAIjH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK+F,WAAL,CAAiB3F,IAAjB,CAAsBH,GAAtB;AAA4B;AAAC;;AACpH,cAAIrE,MAAM,CAACuL,eAAP,KAA2BhI,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D;AAAE,iBAAK4G,aAAL,GAAqB,EAArB;;AAAyB,iBAAI,IAAIhG,KAAR,IAAiBpE,MAAM,CAACuL,eAAxB,EAAyC;AAAE,kBAAIlH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKgG,aAAL,CAAmB5F,IAAnB,CAAwBH,GAAxB;AAA8B;AAAC;;AAC3H,cAAIrE,MAAM,CAACwL,SAAP,KAAqBjI,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAK6G,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIjG,KAAR,IAAiBpE,MAAM,CAACwL,SAAxB,EAAmC;AAAE,kBAAInH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKiG,QAAL,CAAc7F,IAAd,CAAmBH,GAAnB;AAAyB;AAAC;;AAC3G,cAAIrE,MAAM,CAACyL,UAAP,KAAsBlI,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK8G,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAIlG,KAAR,IAAiBpE,MAAM,CAACyL,UAAxB,EAAoC;AAAE,kBAAIpH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKkG,SAAL,CAAe9F,IAAf,CAAoBH,GAApB;AAA0B;AAAC;;AAC9G,cAAIrE,MAAM,CAAC0L,WAAP,KAAuBnI,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D;AAAE,iBAAK+G,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAInG,KAAR,IAAiBpE,MAAM,CAAC0L,WAAxB,EAAqC;AAAE,kBAAIrH,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKmG,SAAL,CAAe/F,IAAf,CAAoBH,GAApB;AAA0B;AAAC;;AAC/G,cAAIrE,MAAM,CAAC2L,OAAP,KAAmBpI,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKgH,MAAL,GAAcxK,MAAM,CAAC2L,OAArB;;AACA,cAAI3L,MAAM,CAAC4L,WAAP,KAAuBrI,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKiH,UAAL,GAAkBzK,MAAM,CAAC4L,WAAzB;AACH;;AAuFDnI,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAsBtB;;AA1JgB,O;;0BAiKRjI,Q,GAAN,MAAMA,QAAN,CAAe;AAElBsE,QAAAA,WAAW,CAACC,MAAD,EAAc;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBiF,EAlDgB;;AAmDzB;AACJ;AACA;AArD6B,eAsDhB4G,QAtDgB;;AAuDzB;AACJ;AACA;AAzD6B,eA0DhBC,SA1DgB;;AA2DzB;AACJ;AACA;AA7D6B,eA8DhBC,KA9DgB;;AA+DzB;AACJ;AACA;AAjE6B,eAkEhBC,UAlEgB;;AAmEzB;AACJ;AACA;AArE6B,eAsEhBC,WAtEgB;AAAA,eAuEhBC,OAvEgB;;AAwEzB;AACJ;AACA;AA1E6B,eA2EhBlI,KA3EgB;;AA4EzB;AACJ;AACA;AA9E6B,eA+EhBmI,KA/EgB;;AAgFzB;AACJ;AACA;AAlF6B,eAmFhBC,OAnFgB;;AAoFzB;AACJ;AACA;AAtF6B,eAuFhBC,UAvFgB;;AAwFzB;AACJ;AACA;AA1F6B,eA2FhBC,UA3FgB;;AA4FzB;AACJ;AACA;AA9F6B,eA+FhBC,YA/FgB;;AAgGzB;AACJ;AACA;AAlG6B,eAmGhBC,WAnGgB;;AAoGzB;AACJ;AACA;AAtG6B,eAuGhBC,KAvGgB;;AAwGzB;AACJ;AACA;AA1G6B,eA2GhBC,OA3GgB;;AA4GzB;AACJ;AACA;AA9G6B,eA+GhBC,UA/GgB;;AAgHzB;AACJ;AACA;AAlH6B,eAmHhBC,UAnHgB;;AAoHzB;AACJ;AACA;AAtH6B,eAuHhBC,UAvHgB;;AAwHzB;AACJ;AACA;AA1H6B,eA2HhBC,SA3HgB;;AA4HzB;AACJ;AACA;AA9H6B,eA+HhBC,SA/HgB;AAAA,eAgIhBC,UAhIgB;;AACrB,cAAIhN,MAAM,CAACiF,EAAP,KAAc1B,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKyB,EAAL,GAAUjF,MAAM,CAACiF,EAAjB;;AACA,cAAIjF,MAAM,CAAC6L,QAAP,KAAoBtI,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKqI,QAAL,GAAgB7L,MAAM,CAAC6L,QAAvB;;AACA,cAAI7L,MAAM,CAAC8L,SAAP,KAAqBvI,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsI,SAAL,GAAiB9L,MAAM,CAAC8L,SAAxB;;AACA,cAAI9L,MAAM,CAAC+L,KAAP,KAAiBxI,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKuI,KAAL,GAAa/L,MAAM,CAAC+L,KAApB;;AACA,cAAI/L,MAAM,CAACgM,UAAP,KAAsBzI,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKwI,UAAL,GAAkBhM,MAAM,CAACgM,UAAzB;;AACA,cAAIhM,MAAM,CAACiM,WAAP,KAAuB1I,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKyI,WAAL,GAAmBjM,MAAM,CAACiM,WAA1B;;AACA,cAAIjM,MAAM,CAACkM,OAAP,KAAmB3I,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAK0I,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAI9H,KAAR,IAAiBpE,MAAM,CAACkM,OAAxB,EAAiC;AAAE,kBAAI7H,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIC,OAAO,CAACM,QAAZ,CAAqBR,KAArB,CAAN;AAAmC,mBAAK8H,OAAL,CAAa1H,IAAb,CAAkBH,GAAlB;AAAwB;AAAC;;AAC7H,cAAIrE,MAAM,CAACgE,KAAP,KAAiBT,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKQ,KAAL,GAAahE,MAAM,CAACgE,KAApB;;AACA,cAAIhE,MAAM,CAACmM,KAAP,KAAiB5I,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK2I,KAAL,GAAanM,MAAM,CAACmM,KAApB;;AACA,cAAInM,MAAM,CAACoM,OAAP,KAAmB7I,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK4I,OAAL,GAAepM,MAAM,CAACoM,OAAtB;;AACA,cAAIpM,MAAM,CAACqM,UAAP,KAAsB9I,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK6I,UAAL,GAAkBrM,MAAM,CAACqM,UAAzB;;AACA,cAAIrM,MAAM,CAACsM,UAAP,KAAsB/I,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK8I,UAAL,GAAkBtM,MAAM,CAACsM,UAAzB;;AACA,cAAItM,MAAM,CAACuM,YAAP,KAAwBhJ,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK+I,YAAL,GAAoBvM,MAAM,CAACuM,YAA3B;;AACA,cAAIvM,MAAM,CAACwM,WAAP,KAAuBjJ,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKgJ,WAAL,GAAmBxM,MAAM,CAACwM,WAA1B;;AACA,cAAIxM,MAAM,CAACyM,KAAP,KAAiBlJ,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKiJ,KAAL,GAAazM,MAAM,CAACyM,KAApB;;AACA,cAAIzM,MAAM,CAAC0M,OAAP,KAAmBnJ,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKkJ,OAAL,GAAe1M,MAAM,CAAC0M,OAAtB;;AACA,cAAI1M,MAAM,CAAC2M,UAAP,KAAsBpJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKmJ,UAAL,GAAkB3M,MAAM,CAAC2M,UAAzB;;AACA,cAAI3M,MAAM,CAAC4M,UAAP,KAAsBrJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKoJ,UAAL,GAAkB5M,MAAM,CAAC4M,UAAzB;;AACA,cAAI5M,MAAM,CAAC6M,UAAP,KAAsBtJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKqJ,UAAL,GAAkB7M,MAAM,CAAC6M,UAAzB;;AACA,cAAI7M,MAAM,CAAC8M,SAAP,KAAqBvJ,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsJ,SAAL,GAAiB9M,MAAM,CAAC8M,SAAxB;;AACA,cAAI9M,MAAM,CAAC+M,SAAP,KAAqBxJ,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKuJ,SAAL,GAAiB/M,MAAM,CAAC+M,SAAxB;;AACA,cAAI/M,MAAM,CAACgN,UAAP,KAAsBzJ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKwJ,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI5I,KAAR,IAAiBpE,MAAM,CAACgN,UAAxB,EAAoC;AAAE,kBAAI3I,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIC,OAAO,CAACa,WAAZ,CAAwBf,KAAxB,CAAN;AAAsC,mBAAK4I,UAAL,CAAgBxI,IAAhB,CAAqBH,GAArB;AAA2B;AAAC;AAC5I;;AAqFDZ,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAuBtB;;AA3JiB,O;;4BAkKThI,U,GAAN,MAAMA,UAAN,CAAiB;AAEpBqE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAezB;AACJ;AACA;AAjB6B,eAkBhBiN,aAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBC,SAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,qBA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhBC,kBA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChBC,gBAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,iBAtCgB;;AACrB,cAAItN,MAAM,CAACiN,aAAP,KAAyB1J,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKyJ,aAAL,GAAqBjN,MAAM,CAACiN,aAA5B;;AACA,cAAIjN,MAAM,CAACkN,SAAP,KAAqB3J,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0J,SAAL,GAAiBlN,MAAM,CAACkN,SAAxB;;AACA,cAAIlN,MAAM,CAACmN,qBAAP,KAAiC5J,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAK2J,qBAAL,GAA6BnN,MAAM,CAACmN,qBAApC;;AACA,cAAInN,MAAM,CAACoN,kBAAP,KAA8B7J,SAAlC,EAA6C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClE,eAAK4J,kBAAL,GAA0BpN,MAAM,CAACoN,kBAAjC;;AACA,cAAIpN,MAAM,CAACqN,gBAAP,KAA4B9J,SAAhC,EAA2C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAChE,eAAK6J,gBAAL,GAAwBrN,MAAM,CAACqN,gBAA/B;;AACA,cAAIrN,MAAM,CAACsN,iBAAP,KAA6B/J,SAAjC,EAA4C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACjE,eAAK8J,iBAAL,GAAyBtN,MAAM,CAACsN,iBAAhC;AACH;;AA2BD7J,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAOtB;;AAjDmB,O;;uBAwDX/H,K,GAAN,MAAMA,KAAN,CAAY;AAEfoE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBsN,MAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,UA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,WAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBC,cApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,qBAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,SA5CgB;;AACrB,cAAI5N,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACuN,MAAP,KAAkBhK,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAK+J,MAAL,GAAcvN,MAAM,CAACuN,MAArB;;AACA,cAAIvN,MAAM,CAACwN,UAAP,KAAsBjK,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKgK,UAAL,GAAkBxN,MAAM,CAACwN,UAAzB;;AACA,cAAIxN,MAAM,CAACyN,WAAP,KAAuBlK,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKiK,WAAL,GAAmBzN,MAAM,CAACyN,WAA1B;;AACA,cAAIzN,MAAM,CAAC0N,cAAP,KAA0BnK,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKkK,cAAL,GAAsB1N,MAAM,CAAC0N,cAA7B;;AACA,cAAI1N,MAAM,CAAC2N,qBAAP,KAAiCpK,SAArC,EAAgD;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrE,eAAKmK,qBAAL,GAA6B3N,MAAM,CAAC2N,qBAApC;;AACA,cAAI3N,MAAM,CAAC4N,SAAP,KAAqBrK,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKoK,SAAL,GAAiB5N,MAAM,CAAC4N,SAAxB;AACH;;AA+BDnK,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDc,O;;4BA+DN9H,U,GAAN,MAAMA,UAAN,CAAiB;AAEpBmE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhB4N,cAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,WA5BgB;AAAA,eA6BhBC,UA7BgB;;AA8BzB;AACJ;AACA;AAhC6B,eAiChBC,cAjCgB;;AAkCzB;AACJ;AACA;AApC6B,eAqChBC,WArCgB;AAAA,eAsChBC,UAtCgB;;AACrB,cAAIlO,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC6N,cAAP,KAA0BtK,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKqK,cAAL,GAAsB7N,MAAM,CAAC6N,cAA7B;;AACA,cAAI7N,MAAM,CAAC8N,WAAP,KAAuBvK,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKsK,WAAL,GAAmB9N,MAAM,CAAC8N,WAA1B;;AACA,cAAI9N,MAAM,CAAC+N,UAAP,KAAsBxK,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKuK,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI3J,KAAR,IAAiBpE,MAAM,CAAC+N,UAAxB,EAAoC;AAAE,kBAAI1J,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIC,OAAO,CAACU,YAAZ,CAAyBZ,KAAzB,CAAN;AAAuC,mBAAK2J,UAAL,CAAgBvJ,IAAhB,CAAqBH,GAArB;AAA2B;AAAC;;AAC1I,cAAIrE,MAAM,CAACgO,cAAP,KAA0BzK,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKwK,cAAL,GAAsBhO,MAAM,CAACgO,cAA7B;;AACA,cAAIhO,MAAM,CAACiO,WAAP,KAAuB1K,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKyK,WAAL,GAAmBjO,MAAM,CAACiO,WAA1B;;AACA,cAAIjO,MAAM,CAACkO,UAAP,KAAsB3K,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK0K,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI9J,KAAR,IAAiBpE,MAAM,CAACkO,UAAxB,EAAoC;AAAE,kBAAI7J,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIC,OAAO,CAACU,YAAZ,CAAyBZ,KAAzB,CAAN;AAAuC,mBAAK8J,UAAL,CAAgB1J,IAAhB,CAAqBH,GAArB;AAA2B;AAAC;AAC7I;;AAyBDZ,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAInB,eAAK,IAAI6D,EAAT,IAAe,KAAKwG,UAApB,EAAgC;AAAExG,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9D,OAAJ,CAAYC,MAAZ;AAAsB;;AAGxD,eAAK,IAAI6D,EAAT,IAAe,KAAK2G,UAApB,EAAgC;AAAE3G,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9D,OAAJ,CAAYC,MAAZ;AAAsB;AAC3D;;AAlDmB,O;;2BAyDX7H,S,GAAN,MAAMA,SAAN,CAAgB;AAEnBkE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAmBzB;AACJ;AACA;AArB6B,eAsBhBC,EAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhB8E,IA1BgB;;AA2BzB;AACJ;AACA;AA7B6B,eA8BhB0B,IA9BgB;;AA+BzB;AACJ;AACA;AAjC6B,eAkChB0H,QAlCgB;;AAmCzB;AACJ;AACA;AArC6B,eAsChBC,QAtCgB;;AAuCzB;AACJ;AACA;AAzC6B,eA0ChBC,QA1CgB;;AA2CzB;AACJ;AACA;AA7C6B,eA8ChBC,SA9CgB;;AA+CzB;AACJ;AACA;AAjD6B,eAkDhBC,YAlDgB;;AACrB,cAAIvO,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC+E,IAAP,KAAgBxB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKuB,IAAL,GAAY/E,MAAM,CAAC+E,IAAnB;;AACA,cAAI/E,MAAM,CAACyG,IAAP,KAAgBlD,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD;AAAE,iBAAKiD,IAAL,GAAY,EAAZ;;AAAgB,iBAAI,IAAIrC,KAAR,IAAiBpE,MAAM,CAACyG,IAAxB,EAA8B;AAAE,kBAAIpC,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKqC,IAAL,CAAUjC,IAAV,CAAeH,GAAf;AAAqB;AAAC;;AAC9F,cAAIrE,MAAM,CAACmO,QAAP,KAAoB5K,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAK2K,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAI/J,KAAR,IAAiBpE,MAAM,CAACmO,QAAxB,EAAkC;AAAE,kBAAI9J,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK+J,QAAL,CAAc3J,IAAd,CAAmBH,GAAnB;AAAyB;AAAC;;AAC1G,cAAIrE,MAAM,CAACoO,QAAP,KAAoB7K,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAK4K,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIhK,KAAR,IAAiBpE,MAAM,CAACoO,QAAxB,EAAkC;AAAE,kBAAI/J,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKgK,QAAL,CAAc5J,IAAd,CAAmBH,GAAnB;AAAyB;AAAC;;AAC1G,cAAIrE,MAAM,CAACqO,QAAP,KAAoB9K,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAK6K,QAAL,GAAgB,EAAhB;;AAAoB,iBAAI,IAAIjK,KAAR,IAAiBpE,MAAM,CAACqO,QAAxB,EAAkC;AAAE,kBAAIhK,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKiK,QAAL,CAAc7J,IAAd,CAAmBH,GAAnB;AAAyB;AAAC;;AAC1G,cAAIrE,MAAM,CAACsO,SAAP,KAAqB/K,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAK8K,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAIlK,KAAR,IAAiBpE,MAAM,CAACsO,SAAxB,EAAmC;AAAE,kBAAIjK,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKkK,SAAL,CAAe9J,IAAf,CAAoBH,GAApB;AAA0B;AAAC;;AAC7G,cAAIrE,MAAM,CAACuO,YAAP,KAAwBhL,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D;AAAE,iBAAK+K,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAInK,KAAR,IAAiBpE,MAAM,CAACuO,YAAxB,EAAsC;AAAE,kBAAIlK,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAKmK,YAAL,CAAkB/J,IAAlB,CAAuBH,GAAvB;AAA6B;AAAC;AACzH;;AAmCDZ,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAStB;;AA/DkB,O;;6BAsEV5H,W,GAAN,MAAMA,WAAN,CAAkB;AAErBiE,QAAAA,WAAW,CAACC,MAAD,EAAc;AASzB;AACJ;AACA;AAX6B,eAYhBC,EAZgB;;AAazB;AACJ;AACA;AAf6B,eAgBhByH,EAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBpG,GApBgB;;AACrB,cAAItB,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC0H,EAAP,KAAcnE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKkE,EAAL,GAAU1H,MAAM,CAAC0H,EAAjB;;AACA,cAAI1H,MAAM,CAACsB,GAAP,KAAeiC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKlC,GAAL,GAAWtB,MAAM,CAACsB,GAAlB;AACH;;AAeDmC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AA5BoB,O;AAmCzB;AACA;AACA;;;6BACa3H,W,GAAN,MAAMA,WAAN,CAAkB;AAErBgE,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAKhBwO,QALgB;;AACrB,cAAIxO,MAAM,CAACyO,SAAP,KAAqBlL,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKgL,QAAL,GAAgBxO,MAAM,CAACyO,SAAvB;AACH;;AAIDhL,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAEtB;;AAXoB,O;AAkBzB;AACA;AACA;;;+BACa1H,a,GAAN,MAAMA,aAAN,CAAoB;AAEvB+D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhB0O,UAPgB;AAAA,eAQhBC,aARgB;;AACrB,cAAI3O,MAAM,CAAC4O,WAAP,KAAuBrL,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKkL,UAAL,GAAkB1O,MAAM,CAAC4O,WAAzB;;AACA,cAAI5O,MAAM,CAAC6O,cAAP,KAA0BtL,SAA9B,EAAyC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC9D,eAAKmL,aAAL,GAAqB3O,MAAM,CAAC6O,cAA5B;AACH;;AAKDpL,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfsB,O;AAsB3B;AACA;AACA;;;+BACazH,a,GAAN,MAAMA,aAAN,CAAoB;AAEvB8D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhB8O,QAPgB;AAAA,eAQhBC,SARgB;;AACrB,cAAI/O,MAAM,CAACgP,SAAP,KAAqBzL,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsL,QAAL,GAAgB9O,MAAM,CAACgP,SAAvB;;AACA,cAAIhP,MAAM,CAACiP,UAAP,KAAsB1L,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKuL,SAAL,GAAiB/O,MAAM,CAACiP,UAAxB;AACH;;AAKDxL,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfsB,O;AAsB3B;AACA;AACA;;;yBACaxH,O,GAAN,MAAMA,OAAN,CAAc;AAEjB6D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,EAPgB;;AAQzB;AACJ;AACA;AAV6B,eAWhBiP,GAXgB;;AACrB,cAAIlP,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACkP,GAAP,KAAe3L,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK0L,GAAL,GAAWlP,MAAM,CAACkP,GAAlB;AACH;;AAQDzL,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAlBgB,O;AAyBrB;AACA;AACA;;;2BACavH,S,GAAN,MAAMA,SAAN,CAAgB;AAEnB4D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAehBC,EAfgB;AAAA,eAgBhB4F,IAhBgB;AAAA,eAiBhBoG,WAjBgB;AAAA,eAkBhBkD,UAlBgB;AAAA,eAmBhBC,WAnBgB;AAAA,eAoBhBC,YApBgB;;AACrB,cAAIrP,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC6F,IAAP,KAAgBtC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqC,IAAL,GAAY7F,MAAM,CAAC6F,IAAnB;;AACA,cAAI7F,MAAM,CAACiM,WAAP,KAAuB1I,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKyI,WAAL,GAAmBjM,MAAM,CAACiM,WAA1B;;AACA,cAAIjM,MAAM,CAACsP,WAAP,KAAuB/L,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK2L,UAAL,GAAkBnP,MAAM,CAACsP,WAAzB;;AACA,cAAItP,MAAM,CAACuP,YAAP,KAAwBhM,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK4L,WAAL,GAAmBpP,MAAM,CAACuP,YAA1B;;AACA,cAAIvP,MAAM,CAACwP,aAAP,KAAyBjM,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK6L,YAAL,GAAoBrP,MAAM,CAACwP,aAA3B;AACH;;AASD/L,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAOtB;;AA/BkB,O;AAsCvB;AACA;AACA;;;0BACatH,Q,GAAN,MAAMA,QAAN,CAAe;AAElB2D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAiBhBC,EAjBgB;AAAA,eAkBhB4F,IAlBgB;AAAA,eAmBhB4J,OAnBgB;AAAA,eAoBhBC,UApBgB;AAAA,eAqBhBC,UArBgB;AAAA,eAsBhBC,KAtBgB;AAAA,eAuBhBC,YAvBgB;;AACrB,cAAI7P,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC6F,IAAP,KAAgBtC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqC,IAAL,GAAY7F,MAAM,CAAC6F,IAAnB;;AACA,cAAI7F,MAAM,CAACyP,OAAP,KAAmBlM,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKiM,OAAL,GAAezP,MAAM,CAACyP,OAAtB;;AACA,cAAIzP,MAAM,CAAC8P,WAAP,KAAuBvM,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKkM,UAAL,GAAkB1P,MAAM,CAAC8P,WAAzB;;AACA,cAAI9P,MAAM,CAAC+P,WAAP,KAAuBxM,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmM,UAAL,GAAkB3P,MAAM,CAAC+P,WAAzB;;AACA,cAAI/P,MAAM,CAAC4P,KAAP,KAAiBrM,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD;AAAE,iBAAKoM,KAAL,GAAa,EAAb;;AAAiB,iBAAI,IAAIxL,KAAR,IAAiBpE,MAAM,CAAC4P,KAAxB,EAA+B;AAAE,kBAAIvL,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAI9I,SAAJ,CAAc6I,KAAd,CAAN;AAA4B,mBAAKwL,KAAL,CAAWpL,IAAX,CAAgBH,GAAhB;AAAsB;AAAC;;AAChH,cAAIrE,MAAM,CAACgQ,aAAP,KAAyBzM,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAKqM,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIzL,KAAR,IAAiBpE,MAAM,CAACgQ,aAAxB,EAAuC;AAAE,kBAAI3L,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIlJ,WAAJ,CAAgBiJ,KAAhB,CAAN;AAA8B,mBAAKyL,YAAL,CAAkBrL,IAAlB,CAAuBH,GAAvB;AAA6B;AAAC;AAC3I;;AAUDZ,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAMnB,eAAK,IAAI6D,EAAT,IAAe,KAAKqI,KAApB,EAA2B;AAAErI,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9D,OAAJ,CAAYC,MAAZ;AAAsB;;AACnD,eAAK,IAAI6D,EAAT,IAAe,KAAKsI,YAApB,EAAkC;AAAEtI,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9D,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AAnCiB,O;AA0CtB;AACA;AACA;;;iCACarH,e,GAAN,MAAMA,eAAN,CAAsB;AAEzB0D,QAAAA,WAAW,CAACC,MAAD,EAAc;AAezB;AACJ;AACA;AAjB6B,eAkBhB2P,UAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBM,SAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,OA1BgB;AAAA,eA2BhBC,OA3BgB;AAAA,eA4BhBC,YA5BgB;AAAA,eA6BhBP,YA7BgB;;AACrB,cAAI7P,MAAM,CAAC+P,WAAP,KAAuBxM,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmM,UAAL,GAAkB3P,MAAM,CAAC+P,WAAzB;;AACA,cAAI/P,MAAM,CAACqQ,UAAP,KAAsB9M,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKyM,SAAL,GAAiBjQ,MAAM,CAACqQ,UAAxB;;AACA,cAAIrQ,MAAM,CAACsQ,QAAP,KAAoB/M,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK0M,OAAL,GAAelQ,MAAM,CAACsQ,QAAtB;;AACA,cAAItQ,MAAM,CAACuQ,QAAP,KAAoBhN,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD;AAAE,iBAAK2M,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAI/L,KAAR,IAAiBpE,MAAM,CAACuQ,QAAxB,EAAkC;AAAE,kBAAIlM,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAInI,OAAJ,CAAYkI,KAAZ,CAAN;AAA0B,mBAAK+L,OAAL,CAAa3L,IAAb,CAAkBH,GAAlB;AAAwB;AAAC;;AACrH,cAAIrE,MAAM,CAACwQ,aAAP,KAAyBjN,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAK4M,YAAL,GAAoB,IAAIhV,YAAJ,CAAiB4E,MAAM,CAACwQ,aAAxB,CAApB;;AACA,cAAIxQ,MAAM,CAACgQ,aAAP,KAAyBzM,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D;AAAE,iBAAKqM,YAAL,GAAoB,EAApB;;AAAwB,iBAAI,IAAIzL,KAAR,IAAiBpE,MAAM,CAACgQ,aAAxB,EAAuC;AAAE,kBAAI3L,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIlJ,WAAJ,CAAgBiJ,KAAhB,CAAN;AAA8B,mBAAKyL,YAAL,CAAkBrL,IAAlB,CAAuBH,GAAvB;AAA6B;AAAC;AAC3I;;AAkBDZ,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAAA;;AAInB,eAAK,IAAI6D,EAAT,IAAe,KAAK4I,OAApB,EAA6B;AAAE5I,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9D,OAAJ,CAAYC,MAAZ;AAAsB;;AACrD,qCAAK0M,YAAL,gCAAmB3M,OAAnB,CAA2BC,MAA3B;;AACA,eAAK,IAAI6D,EAAT,IAAe,KAAKsI,YAApB,EAAkC;AAAEtI,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9D,OAAJ,CAAYC,MAAZ;AAAsB;AAC7D;;AAxCwB,O;;uBA+ChBpH,K,GAAN,MAAMA,KAAN,CAAY;AAEfyD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAahByQ,KAbgB;AAAA,eAchBC,OAdgB;AAAA,eAehB7K,IAfgB;AAAA,eAgBhB8K,GAhBgB;AAAA,eAiBhBC,IAjBgB;;AACrB,cAAI5Q,MAAM,CAACyQ,KAAP,KAAiBlN,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKiN,KAAL,GAAazQ,MAAM,CAACyQ,KAApB;;AACA,cAAIzQ,MAAM,CAAC0Q,OAAP,KAAmBnN,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKkN,OAAL,GAAe1Q,MAAM,CAAC0Q,OAAtB;;AACA,cAAI1Q,MAAM,CAAC6F,IAAP,KAAgBtC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqC,IAAL,GAAY7F,MAAM,CAAC6F,IAAnB;;AACA,cAAI7F,MAAM,CAAC2Q,GAAP,KAAepN,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKmN,GAAL,GAAW3Q,MAAM,CAAC2Q,GAAlB;;AACA,cAAI3Q,MAAM,CAAC4Q,IAAP,KAAgBrN,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKoN,IAAL,GAAY5Q,MAAM,CAAC4Q,IAAnB;AACH;;AAQDnN,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAMtB;;AA3Bc,O;AAkCnB;AACA;AACA;;;yBACanH,O,GAAN,MAAMA,OAAN,CAAc;AAEjBwD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAqBhBC,EArBgB;AAAA,eAsBhB4F,IAtBgB;AAAA,eAuBhB4J,OAvBgB;AAAA,eAwBhBC,UAxBgB;AAAA,eAyBhBmB,OAzBgB;AAAA,eA0BhBrC,QA1BgB;AAAA,eA2BhBsC,YA3BgB;AAAA,eA4BhBC,YA5BgB;AAAA,eA6BhBC,WA7BgB;;AACrB,cAAIhR,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC6F,IAAP,KAAgBtC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqC,IAAL,GAAY7F,MAAM,CAAC6F,IAAnB;;AACA,cAAI7F,MAAM,CAACyP,OAAP,KAAmBlM,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKiM,OAAL,GAAezP,MAAM,CAACyP,OAAtB;;AACA,cAAIzP,MAAM,CAAC8P,WAAP,KAAuBvM,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKkM,UAAL,GAAkB1P,MAAM,CAAC8P,WAAzB;;AACA,cAAI9P,MAAM,CAACiR,QAAP,KAAoB1N,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKqN,OAAL,GAAe7Q,MAAM,CAACiR,QAAtB;;AACA,cAAIjR,MAAM,CAACyO,SAAP,KAAqBlL,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKgL,QAAL,GAAgBxO,MAAM,CAACyO,SAAvB;;AACA,cAAIzO,MAAM,CAACkR,aAAP,KAAyB3N,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKsN,YAAL,GAAoB9Q,MAAM,CAACkR,aAA3B;;AACA,cAAIlR,MAAM,CAACmR,aAAP,KAAyB5N,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKuN,YAAL,GAAoB/Q,MAAM,CAACmR,aAA3B;;AACA,cAAInR,MAAM,CAACoR,aAAP,KAAyB7N,SAA7B,EAAwC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC7D,eAAKwN,WAAL,GAAmBhR,MAAM,CAACoR,aAA1B;AACH;;AAYD3N,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAUtB;;AA3CgB,O;AAkDrB;AACA;AACA;;;0BACalH,Q,GAAN,MAAMA,QAAN,CAAe;AAElBuD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAmBhBC,EAnBgB;AAAA,eAoBhBoR,SApBgB;AAAA,eAqBhBxL,IArBgB;AAAA,eAsBhBoG,WAtBgB;AAAA,eAuBhBwD,OAvBgB;AAAA,eAwBhB6B,UAxBgB;AAAA,eAyBhBpN,OAzBgB;AAAA,eA0BhBqN,SA1BgB;;AACrB,cAAIvR,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACwR,UAAP,KAAsBjO,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK6N,SAAL,GAAiBrR,MAAM,CAACwR,UAAxB;;AACA,cAAIxR,MAAM,CAAC6F,IAAP,KAAgBtC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqC,IAAL,GAAY7F,MAAM,CAAC6F,IAAnB;;AACA,cAAI7F,MAAM,CAACiM,WAAP,KAAuB1I,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKyI,WAAL,GAAmBjM,MAAM,CAACiM,WAA1B;;AACA,cAAIjM,MAAM,CAACyP,OAAP,KAAmBlM,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKiM,OAAL,GAAezP,MAAM,CAACyP,OAAtB;;AACA,cAAIzP,MAAM,CAACsR,UAAP,KAAsB/N,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAK8N,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAIlN,KAAR,IAAiBpE,MAAM,CAACsR,UAAxB,EAAoC;AAAE,kBAAIjN,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIpI,aAAJ,CAAkBmI,KAAlB,CAAN;AAAgC,mBAAKkN,UAAL,CAAgB9M,IAAhB,CAAqBH,GAArB;AAA2B;AAAC;;AACnI,cAAIrE,MAAM,CAACkE,OAAP,KAAmBX,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD;AAAE,iBAAKU,OAAL,GAAe,EAAf;;AAAmB,iBAAI,IAAIE,KAAR,IAAiBpE,MAAM,CAACkE,OAAxB,EAAiC;AAAE,kBAAIG,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAItI,WAAJ,CAAgBqI,KAAhB,CAAN;AAA8B,mBAAKF,OAAL,CAAaM,IAAb,CAAkBH,GAAlB;AAAwB;AAAC;;AACxH,cAAIrE,MAAM,CAACuR,SAAP,KAAqBhO,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD;AAAE,iBAAK+N,SAAL,GAAiB,EAAjB;;AAAqB,iBAAI,IAAInN,KAAR,IAAiBpE,MAAM,CAACuR,SAAxB,EAAmC;AAAE,kBAAIlN,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIrI,aAAJ,CAAkBoI,KAAlB,CAAN;AAAgC,mBAAKmN,SAAL,CAAe/M,IAAf,CAAoBH,GAApB;AAA0B;AAAC;AACnI;;AAWDZ,QAAAA,OAAO,CAACC,MAAD,EAAgB;AAMnB,eAAK,IAAI6D,EAAT,IAAe,KAAK+J,UAApB,EAAgC;AAAE/J,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9D,OAAJ,CAAYC,MAAZ;AAAsB;;AACxD,eAAK,IAAI6D,EAAT,IAAe,KAAKrD,OAApB,EAA6B;AAAEqD,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9D,OAAJ,CAAYC,MAAZ;AAAsB;;AACrD,eAAK,IAAI6D,EAAT,IAAe,KAAKgK,SAApB,EAA+B;AAAEhK,YAAAA,EAAE,QAAF,IAAAA,EAAE,CAAE9D,OAAJ,CAAYC,MAAZ;AAAsB;AAC1D;;AAvCiB,O;AA8CtB;AACA;AACA;;;2BACajH,S,GAAN,MAAMA,SAAN,CAAgB;AAEnBsD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAOhBC,EAPgB;AAAA,eAQhB4F,IARgB;;AACrB,cAAI7F,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC6F,IAAP,KAAgBtC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqC,IAAL,GAAY7F,MAAM,CAAC6F,IAAnB;AACH;;AAKDpC,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAGtB;;AAfkB,O;;8BAsBVhH,Y,GAAN,MAAMA,YAAN,CAAmB;AAEtBqD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAWzB;AACJ;AACA;AAb6B,eAchByR,MAdgB;;AAezB;AACJ;AACA;AAjB6B,eAkBhBC,QAlBgB;;AAmBzB;AACJ;AACA;AArB6B,eAsBhBC,MAtBgB;;AAuBzB;AACJ;AACA;AAzB6B,eA0BhBC,SA1BgB;;AACrB,cAAI5R,MAAM,CAACyR,MAAP,KAAkBlO,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKiO,MAAL,GAAczR,MAAM,CAACyR,MAArB;;AACA,cAAIzR,MAAM,CAAC0R,QAAP,KAAoBnO,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKkO,QAAL,GAAgB1R,MAAM,CAAC0R,QAAvB;;AACA,cAAI1R,MAAM,CAAC2R,MAAP,KAAkBpO,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKmO,MAAL,GAAc3R,MAAM,CAAC2R,MAArB;;AACA,cAAI3R,MAAM,CAAC4R,SAAP,KAAqBrO,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKoO,SAAL,GAAiB5R,MAAM,CAAC4R,SAAxB;AACH;;AAmBDnO,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAKtB;;AAnCqB,O;;uBA0Cb/G,K,GAAN,MAAMA,KAAN,CAAY;AAEfoD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBC,EAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhB4F,IA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChB+K,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBiB,IApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,EAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,MA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,OAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhB7N,WApDgB;AAAA,eAqDhB8N,UArDgB;;AACrB,cAAIjS,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAAC6F,IAAP,KAAgBtC,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqC,IAAL,GAAY7F,MAAM,CAAC6F,IAAnB;;AACA,cAAI7F,MAAM,CAAC4Q,IAAP,KAAgBrN,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKoN,IAAL,GAAY5Q,MAAM,CAAC4Q,IAAnB;;AACA,cAAI5Q,MAAM,CAAC6R,IAAP,KAAgBtO,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKqO,IAAL,GAAY7R,MAAM,CAAC6R,IAAnB;;AACA,cAAI7R,MAAM,CAAC8R,EAAP,KAAcvO,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKsO,EAAL,GAAU9R,MAAM,CAAC8R,EAAjB;;AACA,cAAI9R,MAAM,CAAC+R,MAAP,KAAkBxO,SAAtB,EAAiC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACtD,eAAKuO,MAAL,GAAc/R,MAAM,CAAC+R,MAArB;;AACA,cAAI/R,MAAM,CAACgS,OAAP,KAAmBzO,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKwO,OAAL,GAAehS,MAAM,CAACgS,OAAtB;;AACA,cAAIhS,MAAM,CAACmE,WAAP,KAAuBZ,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKW,WAAL,GAAmBnE,MAAM,CAACmE,WAA1B;;AACA,cAAInE,MAAM,CAACiS,UAAP,KAAsB1O,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D;AAAE,iBAAKyO,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI7N,KAAR,IAAiBpE,MAAM,CAACiS,UAAxB,EAAoC;AAAE,kBAAI5N,GAAJ;;AAASA,cAAAA,GAAG,GAAG,IAAIC,OAAO,CAACG,SAAZ,CAAsBL,KAAtB,CAAN;AAAoC,mBAAK6N,UAAL,CAAgBzN,IAAhB,CAAqBH,GAArB;AAA2B;AAAC;AAC1I;;AAoCDZ,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAUtB;;AAnEc,O;;uBA0EN9G,K,GAAN,MAAMA,KAAN,CAAY;AAEfmD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBC,EApBgB;;AAqBzB;AACJ;AACA;AAvB6B,eAwBhBiS,SAxBgB;;AAyBzB;AACJ;AACA;AA3B6B,eA4BhBC,QA5BgB;;AA6BzB;AACJ;AACA;AA/B6B,eAgChBpN,IAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBqN,YApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,KAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,YA5CgB;;AACrB,cAAItS,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACkS,SAAP,KAAqB3O,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK0O,SAAL,GAAiBlS,MAAM,CAACkS,SAAxB;;AACA,cAAIlS,MAAM,CAACmS,QAAP,KAAoB5O,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK2O,QAAL,GAAgBnS,MAAM,CAACmS,QAAvB;;AACA,cAAInS,MAAM,CAAC+E,IAAP,KAAgBxB,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKuB,IAAL,GAAY/E,MAAM,CAAC+E,IAAnB;;AACA,cAAI/E,MAAM,CAACoS,YAAP,KAAwB7O,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK4O,YAAL,GAAoBpS,MAAM,CAACoS,YAA3B;;AACA,cAAIpS,MAAM,CAACqS,KAAP,KAAiB9O,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK6O,KAAL,GAAarS,MAAM,CAACqS,KAApB;;AACA,cAAIrS,MAAM,CAACsS,YAAP,KAAwB/O,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK8O,YAAL,GAAoBtS,MAAM,CAACsS,YAA3B;AACH;;AA+BD7O,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAQtB;;AAxDc,O;;sBA+DN7G,I,GAAN,MAAMA,IAAN,CAAW;AAEdkD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAqCzB;AACJ;AACA;AAvC6B,eAwChBuS,MAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,OA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,SAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,MApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,UAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBC,QA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBC,SAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBC,QApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,UAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,UA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,OAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBC,OApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,UAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,QA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,QAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,SApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBC,SAxGgB;;AACrB,cAAIvT,MAAM,CAACwT,OAAP,KAAmBjQ,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK+O,MAAL,GAAcvS,MAAM,CAACwT,OAArB;;AACA,cAAIxT,MAAM,CAACyT,QAAP,KAAoBlQ,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKgP,OAAL,GAAexS,MAAM,CAACyT,QAAtB;;AACA,cAAIzT,MAAM,CAAC0T,UAAP,KAAsBnQ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKiP,SAAL,GAAiBzS,MAAM,CAAC0T,UAAxB;;AACA,cAAI1T,MAAM,CAAC2T,OAAP,KAAmBpQ,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAKkP,MAAL,GAAc1S,MAAM,CAAC2T,OAArB;;AACA,cAAI3T,MAAM,CAAC4T,WAAP,KAAuBrQ,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmP,UAAL,GAAkB3S,MAAM,CAAC4T,WAAzB;;AACA,cAAI5T,MAAM,CAAC6T,SAAP,KAAqBtQ,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKoP,QAAL,GAAgB5S,MAAM,CAAC6T,SAAvB;;AACA,cAAI7T,MAAM,CAAC8T,UAAP,KAAsBvQ,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKqP,SAAL,GAAiB7S,MAAM,CAAC8T,UAAxB;;AACA,cAAI9T,MAAM,CAAC+T,SAAP,KAAqBxQ,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKsP,QAAL,GAAgB9S,MAAM,CAAC+T,SAAvB;;AACA,cAAI/T,MAAM,CAACgU,WAAP,KAAuBzQ,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D;AAAE,iBAAKuP,UAAL,GAAkB,EAAlB;;AAAsB,iBAAI,IAAI3O,KAAR,IAAiBpE,MAAM,CAACgU,WAAxB,EAAqC;AAAE,kBAAI3P,GAAJ;;AAASA,cAAAA,GAAG,GAAGD,KAAN;AAAa,mBAAK2O,UAAL,CAAgBvO,IAAhB,CAAqBH,GAArB;AAA2B;AAAC;;AACjH,cAAIrE,MAAM,CAACgT,UAAP,KAAsBzP,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAKwP,UAAL,GAAkBhT,MAAM,CAACgT,UAAzB;;AACA,cAAIhT,MAAM,CAACiU,QAAP,KAAoB1Q,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKyP,OAAL,GAAejT,MAAM,CAACiU,QAAtB;;AACA,cAAIjU,MAAM,CAACkU,QAAP,KAAoB3Q,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAK0P,OAAL,GAAelT,MAAM,CAACkU,QAAtB;;AACA,cAAIlU,MAAM,CAACmU,WAAP,KAAuB5Q,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAK2P,UAAL,GAAkBnT,MAAM,CAACmU,WAAzB;;AACA,cAAInU,MAAM,CAACoU,SAAP,KAAqB7Q,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK4P,QAAL,GAAgBpT,MAAM,CAACoU,SAAvB;;AACA,cAAIpU,MAAM,CAACqU,SAAP,KAAqB9Q,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK6P,QAAL,GAAgBrT,MAAM,CAACqU,SAAvB;;AACA,cAAIrU,MAAM,CAACsU,UAAP,KAAsB/Q,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK8P,SAAL,GAAiBtT,MAAM,CAACsU,UAAxB;;AACA,cAAItU,MAAM,CAACuU,UAAP,KAAsBhR,SAA1B,EAAqC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC1D,eAAK+P,SAAL,GAAiBvT,MAAM,CAACuU,UAAxB;AACH;;AAuED9Q,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAkBtB;;AA9Ha,O;;uBAqIL5G,K,GAAN,MAAMA,KAAN,CAAY;AAEfiD,QAAAA,WAAW,CAACC,MAAD,EAAc;AASzB;AACJ;AACA;AAX6B,eAYhBC,EAZgB;;AAazB;AACJ;AACA;AAf6B,eAgBhBuU,GAhBgB;;AAiBzB;AACJ;AACA;AAnB6B,eAoBhBnL,KApBgB;;AACrB,cAAIrJ,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACwU,GAAP,KAAejR,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKgR,GAAL,GAAWxU,MAAM,CAACwU,GAAlB;;AACA,cAAIxU,MAAM,CAACqJ,KAAP,KAAiB9F,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK6F,KAAL,GAAarJ,MAAM,CAACqJ,KAApB;AACH;;AAeD5F,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAItB;;AA5Bc,O;;sBAmCN3G,I,GAAN,MAAMA,IAAN,CAAW;AAEdgD,QAAAA,WAAW,CAACC,MAAD,EAAc;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBC,EA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBwU,GAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,EApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,GAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhB7O,EA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhB8O,GAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBlN,EApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBmN,GAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,GA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBvT,GAhFgB;;AAiFzB;AACJ;AACA;AAnF6B,eAoFhBwT,GApFgB;;AAqFzB;AACJ;AACA;AAvF6B,eAwFhBC,EAxFgB;;AAyFzB;AACJ;AACA;AA3F6B,eA4FhBC,EA5FgB;;AA6FzB;AACJ;AACA;AA/F6B,eAgGhBC,GAhGgB;;AAiGzB;AACJ;AACA;AAnG6B,eAoGhBC,EApGgB;;AAqGzB;AACJ;AACA;AAvG6B,eAwGhBzU,EAxGgB;;AAyGzB;AACJ;AACA;AA3G6B,eA4GhB0U,GA5GgB;;AA6GzB;AACJ;AACA;AA/G6B,eAgHhBC,GAhHgB;;AAiHzB;AACJ;AACA;AAnH6B,eAoHhBzN,IApHgB;;AACrB,cAAI5H,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACyU,GAAP,KAAelR,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKiR,GAAL,GAAWzU,MAAM,CAACyU,GAAlB;;AACA,cAAIzU,MAAM,CAAC0U,EAAP,KAAcnR,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKkR,EAAL,GAAU1U,MAAM,CAAC0U,EAAjB;;AACA,cAAI1U,MAAM,CAAC2U,GAAP,KAAepR,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKmR,GAAL,GAAW3U,MAAM,CAAC2U,GAAlB;;AACA,cAAI3U,MAAM,CAAC8F,EAAP,KAAcvC,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKsC,EAAL,GAAU9F,MAAM,CAAC8F,EAAjB;;AACA,cAAI9F,MAAM,CAAC4U,GAAP,KAAerR,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKoR,GAAL,GAAW5U,MAAM,CAAC4U,GAAlB;;AACA,cAAI5U,MAAM,CAAC0H,EAAP,KAAcnE,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKkE,EAAL,GAAU1H,MAAM,CAAC0H,EAAjB;;AACA,cAAI1H,MAAM,CAAC6U,GAAP,KAAetR,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKqR,GAAL,GAAW7U,MAAM,CAAC6U,GAAlB;;AACA,cAAI7U,MAAM,CAAC8U,GAAP,KAAevR,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKsR,GAAL,GAAW9U,MAAM,CAAC8U,GAAlB;;AACA,cAAI9U,MAAM,CAACuB,GAAP,KAAegC,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKjC,GAAL,GAAWvB,MAAM,CAACuB,GAAlB;;AACA,cAAIvB,MAAM,CAAC+U,GAAP,KAAexR,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKuR,GAAL,GAAW/U,MAAM,CAAC+U,GAAlB;;AACA,cAAI/U,MAAM,CAACgV,EAAP,KAAczR,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKwR,EAAL,GAAUhV,MAAM,CAACgV,EAAjB;;AACA,cAAIhV,MAAM,CAACiV,EAAP,KAAc1R,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKyR,EAAL,GAAUjV,MAAM,CAACiV,EAAjB;;AACA,cAAIjV,MAAM,CAACkV,GAAP,KAAe3R,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK0R,GAAL,GAAWlV,MAAM,CAACkV,GAAlB;;AACA,cAAIlV,MAAM,CAACmV,EAAP,KAAc5R,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK2R,EAAL,GAAUnV,MAAM,CAACmV,EAAjB;;AACA,cAAInV,MAAM,CAACU,EAAP,KAAc6C,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAK9C,EAAL,GAAUV,MAAM,CAACU,EAAjB;;AACA,cAAIV,MAAM,CAACoV,GAAP,KAAe7R,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK4R,GAAL,GAAWpV,MAAM,CAACoV,GAAlB;;AACA,cAAIpV,MAAM,CAACqV,GAAP,KAAe9R,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAK6R,GAAL,GAAWrV,MAAM,CAACqV,GAAlB;;AACA,cAAIrV,MAAM,CAAC4H,IAAP,KAAgBrE,SAApB,EAA+B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACpD,eAAKoE,IAAL,GAAY5H,MAAM,CAAC4H,IAAnB;AACH;;AA+EDnE,QAAAA,OAAO,CAACC,MAAD,EAAgB,CAoBtB;;AA5Ia,O;;sBAmJL1G,I,GAAN,MAAMA,IAAN,CAAW;AAEd+C,QAAAA,WAAW,CAACC,MAAD,EAAc;AA6BzB;AACJ;AACA;AA/B6B,eAgChBC,EAhCgB;;AAiCzB;AACJ;AACA;AAnC6B,eAoChBmS,YApCgB;;AAqCzB;AACJ;AACA;AAvC6B,eAwChBC,KAxCgB;;AAyCzB;AACJ;AACA;AA3C6B,eA4ChBiD,SA5CgB;;AA6CzB;AACJ;AACA;AA/C6B,eAgDhBC,OAhDgB;;AAiDzB;AACJ;AACA;AAnD6B,eAoDhBC,QApDgB;;AAqDzB;AACJ;AACA;AAvD6B,eAwDhBC,SAxDgB;;AAyDzB;AACJ;AACA;AA3D6B,eA4DhBjO,GA5DgB;;AA6DzB;AACJ;AACA;AA/D6B,eAgEhBqN,GAhEgB;;AAiEzB;AACJ;AACA;AAnE6B,eAoEhBa,KApEgB;;AAqEzB;AACJ;AACA;AAvE6B,eAwEhBC,WAxEgB;;AAyEzB;AACJ;AACA;AA3E6B,eA4EhBC,YA5EgB;;AA6EzB;AACJ;AACA;AA/E6B,eAgFhBC,eAhFgB;;AACrB,cAAI7V,MAAM,CAACC,EAAP,KAAcsD,SAAlB,EAA6B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAClD,eAAKvD,EAAL,GAAUD,MAAM,CAACC,EAAjB;;AACA,cAAID,MAAM,CAACoS,YAAP,KAAwB7O,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAK4O,YAAL,GAAoBpS,MAAM,CAACoS,YAA3B;;AACA,cAAIpS,MAAM,CAACqS,KAAP,KAAiB9O,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAK6O,KAAL,GAAarS,MAAM,CAACqS,KAApB;;AACA,cAAIrS,MAAM,CAACsV,SAAP,KAAqB/R,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAK8R,SAAL,GAAiBtV,MAAM,CAACsV,SAAxB;;AACA,cAAItV,MAAM,CAACuV,OAAP,KAAmBhS,SAAvB,EAAkC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACvD,eAAK+R,OAAL,GAAevV,MAAM,CAACuV,OAAtB;;AACA,cAAIvV,MAAM,CAACwV,QAAP,KAAoBjS,SAAxB,EAAmC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACxD,eAAKgS,QAAL,GAAgBxV,MAAM,CAACwV,QAAvB;;AACA,cAAIxV,MAAM,CAACyV,SAAP,KAAqBlS,SAAzB,EAAoC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACzD,eAAKiS,SAAL,GAAiBzV,MAAM,CAACyV,SAAxB;;AACA,cAAIzV,MAAM,CAACwH,GAAP,KAAejE,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKgE,GAAL,GAAWxH,MAAM,CAACwH,GAAlB;;AACA,cAAIxH,MAAM,CAAC6U,GAAP,KAAetR,SAAnB,EAA8B;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACnD,eAAKqR,GAAL,GAAW7U,MAAM,CAAC6U,GAAlB;;AACA,cAAI7U,MAAM,CAAC0V,KAAP,KAAiBnS,SAArB,EAAgC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AACrD,eAAKkS,KAAL,GAAa1V,MAAM,CAAC0V,KAApB;;AACA,cAAI1V,MAAM,CAAC2V,WAAP,KAAuBpS,SAA3B,EAAsC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC3D,eAAKmS,WAAL,GAAmB3V,MAAM,CAAC2V,WAA1B;;AACA,cAAI3V,MAAM,CAAC4V,YAAP,KAAwBrS,SAA5B,EAAuC;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC5D,eAAKoS,YAAL,GAAoB5V,MAAM,CAAC4V,YAA3B;;AACA,cAAI5V,MAAM,CAAC6V,eAAP,KAA2BtS,SAA/B,EAA0C;AAAE,kBAAM,IAAIC,KAAJ,EAAN;AAAmB;;AAC/D,eAAKqS,eAAL,GAAuB7V,MAAM,CAAC6V,eAA9B;AACH;;AAuDDpS,QAAAA,OAAO,CAACC,MAAD,EAAgB,CActB;;AAlGa,O;AA0GlB;AACA;AACA;;;sBACazG,I,GAAN,MAAMA,IAAN,CAAW;AAGd8C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8V,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI1Z,KAAJ,CAAUyZ,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAEhDI,QAAAA,GAAG,CAACC,KAAD,EAAmC;AAAE,iBAAO,KAAKL,SAAL,CAAeK,KAAf,CAAP;AAA8B;;AAEtE1S,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBa,O;AA4BlB;AACA;AACA;;;yBACaxG,O,GAAN,MAAMA,OAAN,CAAc;AAGjB6C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8V,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIxZ,QAAJ,CAAauZ,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAEnDI,QAAAA,GAAG,CAACC,KAAD,EAAsC;AAAE,iBAAO,KAAKL,SAAL,CAAeK,KAAf,CAAP;AAA8B;;AAEzE1S,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBgB,O;AA4BrB;AACA;AACA;;;0BACavG,Q,GAAN,MAAMA,QAAN,CAAe;AAGlB4C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI7Z,SAAJ,CAAc4Z,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DJ,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACO,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE1EhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBiB,O;AA+BtB;AACA;AACA;;;0BACatG,Q,GAAN,MAAMA,QAAN,CAAe;AAGlB2C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIvZ,SAAJ,CAAcsZ,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DJ,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACO,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE1EhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBiB,O;AA+BtB;AACA;AACA;;;gCACarG,c,GAAN,MAAMA,cAAN,CAAqB;AAGxB0C,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjB8V,SAEiB;AACrB,eAAKA,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI3Z,eAAJ,CAAoB0Z,OAApB,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;AACH;AACJ;;AAEDC,QAAAA,WAAW,GAAsB;AAAE,iBAAO,KAAKH,SAAZ;AAAuB;;AAE1DI,QAAAA,GAAG,CAACC,KAAD,EAA6C;AAAE,iBAAO,KAAKL,SAAL,CAAeK,KAAf,CAAP;AAA8B;;AAEhF1S,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AArBuB,O;AA4B5B;AACA;AACA;;;yBACapG,O,GAAN,MAAMA,OAAN,CAAc;AAGjByC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI5Z,QAAJ,CAAa2Z,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DJ,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACO,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEzEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;AA+BrB;AACA;AACA;;;wBACanG,M,GAAN,MAAMA,MAAN,CAAa;AAGhBwC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIzZ,OAAJ,CAAYwZ,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBe,O;;8BA+BPlG,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtBuC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eADjB0W,KACiB;AACrB,cAAI1W,MAAM,CAAC2W,MAAP,IAAiB,CAArB,EAAwB,MAAM,IAAInT,KAAJ,CAAU,+BAAV,CAAN;AACxB,eAAKkT,KAAL,GAAa,IAAIhb,UAAJ,CAAesE,MAAM,CAAC,CAAD,CAArB,CAAb;AACH;;AAED4W,QAAAA,OAAO,GAAe;AAAE,iBAAO,KAAKF,KAAZ;AAAoB;AAE5C;AACJ;AACA;;;AACsB,YAAbzJ,aAAa,GAAW;AAAE,iBAAO,KAAKyJ,KAAL,CAAWzJ,aAAlB;AAAkC;AACjE;AACJ;AACA;;;AACkB,YAATC,SAAS,GAAW;AAAE,iBAAO,KAAKwJ,KAAL,CAAWxJ,SAAlB;AAA8B;AACzD;AACJ;AACA;;;AAC8B,YAArBC,qBAAqB,GAAW;AAAE,iBAAO,KAAKuJ,KAAL,CAAWvJ,qBAAlB;AAA0C;AACjF;AACJ;AACA;;;AAC2B,YAAlBC,kBAAkB,GAAW;AAAE,iBAAO,KAAKsJ,KAAL,CAAWtJ,kBAAlB;AAAuC;AAC3E;AACJ;AACA;;;AACyB,YAAhBC,gBAAgB,GAAW;AAAE,iBAAO,KAAKqJ,KAAL,CAAWrJ,gBAAlB;AAAqC;AACvE;AACJ;AACA;;;AAC0B,YAAjBC,iBAAiB,GAAW;AAAE,iBAAO,KAAKoJ,KAAL,CAAWpJ,iBAAlB;AAAsC;;AAEzE7J,QAAAA,OAAO,CAACC,MAAD,EACP;AACI,eAAKgT,KAAL,CAAWjT,OAAX,CAAmBC,MAAnB;AACH;;AAtCqB,O;;wBA6CbjG,M,GAAN,MAAMA,MAAN,CAAa;AAGhBsC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIjb,IAAJ,CAASgb,OAAT,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAsB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACzDJ,QAAAA,WAAW,GAAW;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEhDI,QAAAA,GAAG,CAACO,GAAD,EAAgC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAErEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBe,O;;0BA+BPhG,Q,GAAN,MAAMA,QAAN,CAAe;AAGlBqC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIhb,MAAJ,CAAW+a,OAAX,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAwB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC3DJ,QAAAA,WAAW,GAAa;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAElDI,QAAAA,GAAG,CAACO,GAAD,EAAkC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEvEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBiB,O;;0BA+BT/F,Q,GAAN,MAAMA,QAAN,CAAe;AAGlBoC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI/a,MAAJ,CAAW8a,OAAX,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAwB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC3DJ,QAAAA,WAAW,GAAa;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAElDI,QAAAA,GAAG,CAACO,GAAD,EAAkC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEvEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBiB,O;;2BA+BT9F,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBmC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI9a,OAAJ,CAAY6a,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;yBA+BV7F,O,GAAN,MAAMA,OAAN,CAAc;AAGjBkC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI3a,KAAJ,CAAU0a,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAuB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC1DJ,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEjDI,QAAAA,GAAG,CAACO,GAAD,EAAiC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEtEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;2BA+BR5F,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBiC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAI1a,OAAJ,CAAYya,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;2BA+BV3F,S,GAAN,MAAMA,SAAN,CAAgB;AAGnBgC,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIxa,OAAJ,CAAYua,OAAZ,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAyB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC5DJ,QAAAA,WAAW,GAAc;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEnDI,QAAAA,GAAG,CAACO,GAAD,EAAmC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAExEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBkB,O;;4BA+BV1F,U,GAAN,MAAMA,UAAN,CAAiB;AAGpB+B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIva,QAAJ,CAAasa,OAAb,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/Q,EAArB,EAAyB+Q,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA0B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC7DJ,QAAAA,WAAW,GAAe;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEpDI,QAAAA,GAAG,CAACO,GAAD,EAAoC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEzEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBmB,O;;yBA+BXzF,O,GAAN,MAAMA,OAAN,CAAc;AAGjB8B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIra,KAAJ,CAAUoa,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAuB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC1DJ,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEjDI,QAAAA,GAAG,CAACO,GAAD,EAAiC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEtEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;8BA+BRxF,Y,GAAN,MAAMA,YAAN,CAAmB;AAGtB6B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIpa,UAAJ,CAAema,OAAf,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA4B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC/DJ,QAAAA,WAAW,GAAiB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEtDI,QAAAA,GAAG,CAACO,GAAD,EAAsC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE3EhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBqB,O;;6BA+BbvF,W,GAAN,MAAMA,WAAN,CAAkB;AAGrB4B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIna,SAAJ,CAAcka,OAAd,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA2B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC9DJ,QAAAA,WAAW,GAAgB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAErDI,QAAAA,GAAG,CAACO,GAAD,EAAqC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE1EhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBoB,O;;+BA+BZtF,a,GAAN,MAAMA,aAAN,CAAoB;AAGvB2B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIla,WAAJ,CAAgBia,OAAhB,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAA6B;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAChEJ,QAAAA,WAAW,GAAkB;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEvDI,QAAAA,GAAG,CAACO,GAAD,EAAuC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAE5EhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBsB,O;;yBA+BdrF,O,GAAN,MAAMA,OAAN,CAAc;AAGjB0B,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIrZ,KAAJ,CAAUoZ,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAuB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC1DJ,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEjDI,QAAAA,GAAG,CAACO,GAAD,EAAiC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEtEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;yBA+BRpF,O,GAAN,MAAMA,OAAN,CAAc;AAGjByB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIpZ,KAAJ,CAAUmZ,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAuB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC1DJ,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEjDI,QAAAA,GAAG,CAACO,GAAD,EAAiC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEtEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;wBA+BRnF,M,GAAN,MAAMA,MAAN,CAAa;AAGhBwB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAInZ,IAAJ,CAASkZ,OAAT,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAACzD,MAArB,EAA6ByD,EAA7B;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAsB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACzDJ,QAAAA,WAAW,GAAW;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEhDI,QAAAA,GAAG,CAACO,GAAD,EAAgC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAErEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBe,O;;yBA+BPlF,O,GAAN,MAAMA,OAAN,CAAc;AAGjBuB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIlZ,KAAJ,CAAUiZ,OAAV,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAuB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AAC1DJ,QAAAA,WAAW,GAAY;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEjDI,QAAAA,GAAG,CAACO,GAAD,EAAiC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAEtEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBgB,O;;wBA+BRjF,M,GAAN,MAAMA,MAAN,CAAa;AAGhBsB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIjZ,IAAJ,CAASgZ,OAAT,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAsB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACzDJ,QAAAA,WAAW,GAAW;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEhDI,QAAAA,GAAG,CAACO,GAAD,EAAgC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAErEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBe,O;;wBA+BPhF,M,GAAN,MAAMA,MAAN,CAAa;AAGhBqB,QAAAA,WAAW,CAACC,MAAD,EAAc;AAAA,eAFjBqW,QAEiB;AAAA,eADjBP,SACiB;AACrB,eAAKO,QAAL,GAAgB,IAAIC,GAAJ,EAAhB;AACA,eAAKR,SAAL,GAAiB,EAAjB;;AACA,eAAI,IAAIC,OAAR,IAAmB/V,MAAnB,EAA2B;AACvB,gBAAIgW,EAAJ;;AACAA,YAAAA,EAAE,GAAG,IAAIhZ,IAAJ,CAAS+Y,OAAT,CAAL;;AACA,iBAAKD,SAAL,CAAetR,IAAf,CAAoBwR,EAApB;;AACA,iBAAKK,QAAL,CAAcE,GAAd,CAAkBP,EAAE,CAAC/V,EAArB,EAAyB+V,EAAzB;AACH;AACJ;;AAEDQ,QAAAA,UAAU,GAAsB;AAAE,iBAAO,KAAKH,QAAZ;AAAuB;;AACzDJ,QAAAA,WAAW,GAAW;AAAE,iBAAO,KAAKH,SAAZ;AAAwB;;AAEhDI,QAAAA,GAAG,CAACO,GAAD,EAAgC;AAAE,iBAAO,KAAKJ,QAAL,CAAcH,GAAd,CAAkBO,GAAlB,CAAP;AAAgC;;AAErEhT,QAAAA,OAAO,CAACC,MAAD,EAAgB;AACnB,eAAI,IAAK0S,IAAT,IAAiB,KAAKN,SAAtB,EACA;AACIM,YAAAA,IAAI,CAAC3S,OAAL,CAAaC,MAAb;AACH;AACJ;;AAxBe,O;;wBAiCP/E,M,GAAN,MAAMA,MAAN,CAAa;AAEhB;AACJ;AACA;AACY,YAAJ1B,IAAI,GAAU;AAAE,iBAAO,KAAK4Z,KAAZ;AAAmB;;AAEvC;AACJ;AACA;AACe,YAAP3Z,OAAO,GAAa;AAAE,iBAAO,KAAK4Z,QAAZ;AAAsB;;AAEhD;AACJ;AACA;AACgB,YAAR3Z,QAAQ,GAAc;AAAE,iBAAO,KAAK4Z,SAAZ;AAAuB;;AAEnD;AACJ;AACA;AACgB,YAAR3Z,QAAQ,GAAc;AAAE,iBAAO,KAAK4Z,SAAZ;AAAuB;;AAEnD;AACJ;AACA;AACsB,YAAd3Z,cAAc,GAAoB;AAAE,iBAAO,KAAK4Z,eAAZ;AAA6B;;AAErE;AACJ;AACA;AACe,YAAP3Z,OAAO,GAAa;AAAE,iBAAO,KAAK4Z,QAAZ;AAAsB;;AAEhD;AACJ;AACA;AACc,YAAN3Z,MAAM,GAAY;AAAE,iBAAO,KAAK4Z,OAAZ;AAAqB;;AAE7B,YAAZ3Z,YAAY,GAAkB;AAAE,iBAAO,KAAK4Z,aAAZ;AAA2B;;AAErD,YAAN3Z,MAAM,GAAY;AAAE,iBAAO,KAAK4Z,OAAZ;AAAqB;;AAEjC,YAAR3Z,QAAQ,GAAc;AAAE,iBAAO,KAAK4Z,SAAZ;AAAuB;;AAEvC,YAAR3Z,QAAQ,GAAc;AAAE,iBAAO,KAAK4Z,SAAZ;AAAuB;;AAEtC,YAAT3Z,SAAS,GAAe;AAAE,iBAAO,KAAK4Z,UAAZ;AAAwB;;AAE3C,YAAP3Z,OAAO,GAAa;AAAE,iBAAO,KAAK4Z,QAAZ;AAAsB;;AAEnC,YAAT3Z,SAAS,GAAe;AAAE,iBAAO,KAAK4Z,UAAZ;AAAwB;;AAEzC,YAAT3Z,SAAS,GAAe;AAAE,iBAAO,KAAK4Z,UAAZ;AAAwB;;AAExC,YAAV3Z,UAAU,GAAgB;AAAE,iBAAO,KAAK4Z,WAAZ;AAAyB;;AAE9C,YAAP3Z,OAAO,GAAa;AAAE,iBAAO,KAAK4Z,QAAZ;AAAsB;;AAEhC,YAAZ3Z,YAAY,GAAkB;AAAE,iBAAO,KAAK4Z,aAAZ;AAA2B;;AAEhD,YAAX3Z,WAAW,GAAiB;AAAE,iBAAO,KAAK4Z,YAAZ;AAA0B;;AAE3C,YAAb3Z,aAAa,GAAmB;AAAE,iBAAO,KAAK4Z,cAAZ;AAA4B;;AAEvD,YAAP3Z,OAAO,GAAa;AAAE,iBAAO,KAAK4Z,QAAZ;AAAsB;;AAErC,YAAP3Z,OAAO,GAAa;AAAE,iBAAO,KAAK4Z,QAAZ;AAAsB;;AAEtC,YAAN3Z,MAAM,GAAY;AAAE,iBAAO,KAAK4Z,OAAZ;AAAqB;;AAElC,YAAP3Z,OAAO,GAAa;AAAE,iBAAO,KAAK4Z,QAAZ;AAAsB;;AAEtC,YAAN3Z,MAAM,GAAY;AAAE,iBAAO,KAAK4Z,OAAZ;AAAqB;;AAEnC,YAAN3Z,MAAM,GAAY;AAAE,iBAAO,KAAK4Z,OAAZ;AAAqB;;AAE7CvY,QAAAA,WAAW,CAACwY,MAAD,EAAqB;AAAA,eA1ExB1B,KA0EwB;AAAA,eArExBC,QAqEwB;AAAA,eAhExBC,SAgEwB;AAAA,eA3DxBC,SA2DwB;AAAA,eAtDxBC,eAsDwB;AAAA,eAjDxBC,QAiDwB;AAAA,eA5CxBC,OA4CwB;AAAA,eAvCxBC,aAuCwB;AAAA,eArCxBC,OAqCwB;AAAA,eAnCxBC,SAmCwB;AAAA,eAjCxBC,SAiCwB;AAAA,eA/BxBC,UA+BwB;AAAA,eA7BxBC,QA6BwB;AAAA,eA3BxBC,UA2BwB;AAAA,eAzBxBC,UAyBwB;AAAA,eAvBxBC,WAuBwB;AAAA,eArBxBC,QAqBwB;AAAA,eAnBxBC,aAmBwB;AAAA,eAjBxBC,YAiBwB;AAAA,eAfxBC,cAewB;AAAA,eAbxBC,QAawB;AAAA,eAXxBC,QAWwB;AAAA,eATxBC,OASwB;AAAA,eAPxBC,QAOwB;AAAA,eALxBC,OAKwB;AAAA,eAHxBC,OAGwB;AAC5B,eAAKzB,KAAL,GAAa,IAAI5Z,IAAJ,CAASsb,MAAM,CAAC,MAAD,CAAf,CAAb;AACA,eAAKzB,QAAL,GAAgB,IAAI5Z,OAAJ,CAAYqb,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKxB,SAAL,GAAiB,IAAI5Z,QAAJ,CAAaob,MAAM,CAAC,UAAD,CAAnB,CAAjB;AACA,eAAKvB,SAAL,GAAiB,IAAI5Z,QAAJ,CAAamb,MAAM,CAAC,UAAD,CAAnB,CAAjB;AACA,eAAKtB,eAAL,GAAuB,IAAI5Z,cAAJ,CAAmBkb,MAAM,CAAC,gBAAD,CAAzB,CAAvB;AACA,eAAKrB,QAAL,GAAgB,IAAI5Z,OAAJ,CAAYib,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKpB,OAAL,GAAe,IAAI5Z,MAAJ,CAAWgb,MAAM,CAAC,QAAD,CAAjB,CAAf;AACA,eAAKnB,aAAL,GAAqB,IAAI5Z,YAAJ,CAAiB+a,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKlB,OAAL,GAAe,IAAI5Z,MAAJ,CAAW8a,MAAM,CAAC,QAAD,CAAjB,CAAf;AACA,eAAKjB,SAAL,GAAiB,IAAI5Z,QAAJ,CAAa6a,MAAM,CAAC,UAAD,CAAnB,CAAjB;AACA,eAAKhB,SAAL,GAAiB,IAAI5Z,QAAJ,CAAa4a,MAAM,CAAC,UAAD,CAAnB,CAAjB;AACA,eAAKf,UAAL,GAAkB,IAAI5Z,SAAJ,CAAc2a,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKd,QAAL,GAAgB,IAAI5Z,OAAJ,CAAY0a,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKb,UAAL,GAAkB,IAAI5Z,SAAJ,CAAcya,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKZ,UAAL,GAAkB,IAAI5Z,SAAJ,CAAcwa,MAAM,CAAC,WAAD,CAApB,CAAlB;AACA,eAAKX,WAAL,GAAmB,IAAI5Z,UAAJ,CAAeua,MAAM,CAAC,YAAD,CAArB,CAAnB;AACA,eAAKV,QAAL,GAAgB,IAAI5Z,OAAJ,CAAYsa,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKT,aAAL,GAAqB,IAAI5Z,YAAJ,CAAiBqa,MAAM,CAAC,cAAD,CAAvB,CAArB;AACA,eAAKR,YAAL,GAAoB,IAAI5Z,WAAJ,CAAgBoa,MAAM,CAAC,aAAD,CAAtB,CAApB;AACA,eAAKP,cAAL,GAAsB,IAAI5Z,aAAJ,CAAkBma,MAAM,CAAC,eAAD,CAAxB,CAAtB;AACA,eAAKN,QAAL,GAAgB,IAAI5Z,OAAJ,CAAYka,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKL,QAAL,GAAgB,IAAI5Z,OAAJ,CAAYia,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKJ,OAAL,GAAe,IAAI5Z,MAAJ,CAAWga,MAAM,CAAC,QAAD,CAAjB,CAAf;AACA,eAAKH,QAAL,GAAgB,IAAI5Z,OAAJ,CAAY+Z,MAAM,CAAC,SAAD,CAAlB,CAAhB;AACA,eAAKF,OAAL,GAAe,IAAI5Z,MAAJ,CAAW8Z,MAAM,CAAC,QAAD,CAAjB,CAAf;AACA,eAAKD,OAAL,GAAe,IAAI5Z,MAAJ,CAAW6Z,MAAM,CAAC,QAAD,CAAjB,CAAf;;AAEA,eAAK1B,KAAL,CAAWpT,OAAX,CAAmB,IAAnB;;AACA,eAAKqT,QAAL,CAAcrT,OAAd,CAAsB,IAAtB;;AACA,eAAKsT,SAAL,CAAetT,OAAf,CAAuB,IAAvB;;AACA,eAAKuT,SAAL,CAAevT,OAAf,CAAuB,IAAvB;;AACA,eAAKwT,eAAL,CAAqBxT,OAArB,CAA6B,IAA7B;;AACA,eAAKyT,QAAL,CAAczT,OAAd,CAAsB,IAAtB;;AACA,eAAK0T,OAAL,CAAa1T,OAAb,CAAqB,IAArB;;AACA,eAAK2T,aAAL,CAAmB3T,OAAnB,CAA2B,IAA3B;;AACA,eAAK4T,OAAL,CAAa5T,OAAb,CAAqB,IAArB;;AACA,eAAK6T,SAAL,CAAe7T,OAAf,CAAuB,IAAvB;;AACA,eAAK8T,SAAL,CAAe9T,OAAf,CAAuB,IAAvB;;AACA,eAAK+T,UAAL,CAAgB/T,OAAhB,CAAwB,IAAxB;;AACA,eAAKgU,QAAL,CAAchU,OAAd,CAAsB,IAAtB;;AACA,eAAKiU,UAAL,CAAgBjU,OAAhB,CAAwB,IAAxB;;AACA,eAAKkU,UAAL,CAAgBlU,OAAhB,CAAwB,IAAxB;;AACA,eAAKmU,WAAL,CAAiBnU,OAAjB,CAAyB,IAAzB;;AACA,eAAKoU,QAAL,CAAcpU,OAAd,CAAsB,IAAtB;;AACA,eAAKqU,aAAL,CAAmBrU,OAAnB,CAA2B,IAA3B;;AACA,eAAKsU,YAAL,CAAkBtU,OAAlB,CAA0B,IAA1B;;AACA,eAAKuU,cAAL,CAAoBvU,OAApB,CAA4B,IAA5B;;AACA,eAAKwU,QAAL,CAAcxU,OAAd,CAAsB,IAAtB;;AACA,eAAKyU,QAAL,CAAczU,OAAd,CAAsB,IAAtB;;AACA,eAAK0U,OAAL,CAAa1U,OAAb,CAAqB,IAArB;;AACA,eAAK2U,QAAL,CAAc3U,OAAd,CAAsB,IAAtB;;AACA,eAAK4U,OAAL,CAAa5U,OAAb,CAAqB,IAArB;;AACA,eAAK6U,OAAL,CAAa7U,OAAb,CAAqB,IAArB;AACH;;AAjIe,O", "sourcesContent": ["\n//------------------------------------------------------------------------------\n// <auto-generated>\n//     This code was generated by a tool.\n//     Changes to this file may cause incorrect behavior and will be lost if\n//     the code is regenerated.\n// </auto-generated>\n//------------------------------------------------------------------------------\n\n\nexport namespace res { \n/**\n * buff类型\n */\nexport enum BuffType {\n    /**\n     * 正面\n     */\n    Positive = 0,\n    /**\n     * 中性\n     */\n    Neutral = 1,\n    /**\n     * 负面\n     */\n    Negative = 2,\n}\n\n} \nexport namespace res { \n/**\n * 效果类型\n */\nexport enum EffectType {\n    /**\n     * 最大生命值%\n     */\n    AttrMaxHPPer = 1,\n    /**\n     * 最大生命值+\n     */\n    AttrMaxHPAdd = 2,\n    /**\n     * 生命恢复速度%\n     */\n    AttrHPRecoveryPer = 3,\n    /**\n     * 生命恢复速度+\n     */\n    AttrHPRecoveryAdd = 4,\n    /**\n     * 基于最大生命值的恢复速度\n     */\n    AttrHPRecoveryMaxHPPerAdd = 5,\n    /**\n     * 回复最大生命值%\n     */\n    RecoveryMaxHPPer = 6,\n    /**\n     * 回复已损失生命%\n     */\n    RecoveryLoseHPPer = 7,\n    /**\n     * 回复指定生命值+\n     */\n    RecoveryHP = 8,\n    /**\n     * 攻击力%\n     */\n    AttrAttackPer = 9,\n    /**\n     * 攻击力+\n     */\n    AttrAttackAdd = 10,\n    /**\n     * 对Boss攻击力%\n     */\n    AttrAttackBossPer = 11,\n    /**\n     * 对普通怪物攻击力%\n     */\n    AttrAttackNormalPer = 12,\n    /**\n     * 幸运值%\n     */\n    AttrFortunatePer = 13,\n    /**\n     * 幸运值+\n     */\n    AttrFortunateAdd = 14,\n    /**\n     * 闪避率+\n     */\n    AttrMissAdd = 15,\n    /**\n     * 子弹伤害抗性%\n     */\n    AttrBulletHurtResistancePer = 16,\n    /**\n     * 子弹伤害抗性+\n     */\n    AttrBulletHurtResistanceAdd = 17,\n    /**\n     * 撞击伤害抗性%\n     */\n    AttrCollisionHurtResistancePer = 18,\n    /**\n     * 撞击伤害抗性+\n     */\n    AttrCollisionHurtResistanceAdd = 19,\n    /**\n     * 结算得分%\n     */\n    AttrFinalScoreAdd = 20,\n    /**\n     * 击杀得分%\n     */\n    AttrKillScoreAdd = 21,\n    /**\n     * 能量回复%\n     */\n    AttrEnergyRecoveryPerAdd = 22,\n    /**\n     * 能量回复+\n     */\n    AttrEnergyRecoveryAdd = 23,\n    /**\n     * 拾取范围%\n     */\n    AttrPickRadiusPer = 24,\n    /**\n     * 拾取范围+\n     */\n    AttrPickRadius = 25,\n    /**\n     * 添加Buff\n     */\n    ApplyBuff = 26,\n    /**\n     * 免疫子弹伤害\n     */\n    ImmuneBulletHurt = 27,\n    /**\n     * 免疫撞击伤害\n     */\n    ImmuneCollisionHurt = 28,\n    /**\n     * 无视子弹\n     */\n    IgnoreBullet = 29,\n    /**\n     * 无视撞击\n     */\n    IgnoreCollision = 30,\n    /**\n     * 免疫核弹伤害\n     */\n    ImmuneBombHurt = 31,\n    /**\n     * 免疫主动技能\n     */\n    ImmuneActiveSkillHurt = 32,\n    /**\n     * 无敌\n     */\n    Invincible = 33,\n    /**\n     * 核弹携带上限+\n     */\n    AttrBombMax = 34,\n    /**\n     * 子弹伤害\n     */\n    BulletHurt = 35,\n    /**\n     * 基于最大生命值系数的伤害\n     */\n    HurtMaxHPPer = 36,\n    /**\n     * 基于当前生命值系数的伤害\n     */\n    HurtCurHPPer = 37,\n    /**\n     * 核弹伤害+\n     */\n    AttrBombHurtAdd = 38,\n    /**\n     * 核弹伤害%\n     */\n    AttrBombHurtPer = 39,\n    /**\n     * 击杀\n     */\n    Kill = 40,\n    /**\n     * 伤害\n     */\n    Hurt = 41,\n}\n\n} \nexport namespace res { \n/**\n * 装备部位\n */\nexport enum EquipClass {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 火力核心\n     */\n    WEAPON = 1,\n    /**\n     * 副武器\n     */\n    SUB_WEAPON = 2,\n    /**\n     * 装甲核心\n     */\n    ARMOR = 3,\n    /**\n     * 科技核心\n     */\n    TECHNIC = 4,\n}\n\n} \nexport namespace res { \n/**\n * GM命令页签\n */\nexport enum GMTabID {\n    /**\n     * 通用\n     */\n    COMMON = 0,\n    /**\n     * 战斗\n     */\n    BATTLE = 1,\n}\n\n} \nexport namespace res { \n/**\n * 道具的使用效果\n */\nexport enum ItemEffectType {\n    /**\n     * 无效果\n     */\n    NONE = 0,\n    /**\n     * 使用后消耗道具，并掉落相应的ID的随机道具，参数1为掉落ID\n     */\n    DROP = 1,\n    /**\n     * 使用后消耗道具，并掉落相应数量的金币，参数1为每个1道具对应多少个金币\n     */\n    GEN_GOLD = 2,\n    /**\n     * 使用后消耗道具，并掉落相应数量的钻石，参数1为每个1道具对应多少个钻石\n     */\n    GEN_DIAMOND = 3,\n    /**\n     * 使用后消耗道具，并掉落相应数量的经验，参数1为每个1道具对应多少经验值\n     */\n    GEN_XP = 4,\n    /**\n     * 使用后消耗道具，并掉落相应数量的体力，参数1为每个1道具对应多少体力值\n     */\n    GEN_ENERGY = 5,\n    /**\n     * 使用后消耗道具，并掉落相应数量的另一个道具，参数1为新道具ID，参数2为每个1道具对应多少个新道具\n     */\n    GEN_ITEM = 6,\n}\n\n} \nexport namespace res { \n/**\n * 道具的使用类型\n */\nexport enum ItemUseType {\n    /**\n     * 不可直接从背包来使用的道具\n     */\n    NONE = 0,\n    /**\n     * 先放入背包内，然后由玩家从背包手动选择后使用\n     */\n    MANUAL = 1,\n    /**\n     * 当进入背包时立刻触发使用，并产生效果，一般不直接占用背包格子\n     */\n    AUTO = 2,\n}\n\n} \nexport namespace res { \n/**\n * 模式类型\n */\nexport enum ModeType {\n    /**\n     * 无尽\n     */\n    ENDLESS = 0,\n    /**\n     * 剧情\n     */\n    STORY = 1,\n    /**\n     * 远征\n     */\n    EXPEDITION = 2,\n    /**\n     * 无尽PK\n     */\n    ENDLESSPK = 3,\n    /**\n     * 好友PK\n     */\n    FRIENDPK = 4,\n}\n\n} \nexport namespace res { \n/**\n * 货币类型\n */\nexport enum MoneyType {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 金币\n     */\n    GOLD = 1,\n    /**\n     * 钻石\n     */\n    DIAMOND = 2,\n    /**\n     * 体力\n     */\n    POWER = 3,\n    /**\n     * 道具\n     */\n    ITEM = 4,\n}\n\n} \nexport namespace res { \n/**\n * 模式类型\n */\nexport enum PlayCycle {\n    /**\n     * 每日\n     */\n    DAY = 0,\n    /**\n     * 每周\n     */\n    WEEK = 1,\n}\n\n} \nexport namespace res { \n/**\n * 装备属性名称\n */\nexport enum PropName {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 攻击力\n     */\n    HURT = 1,\n    /**\n     * 生命值\n     */\n    HP = 2,\n}\n\n} \nexport namespace res { \n/**\n * 普通(Common)、精良(Uncommon)、稀有(Rare)、史诗(Epic)、传说(Legendary)、神话(Mythic)\n */\nexport enum QualityType {\n    /**\n     * 无效值\n     */\n    NONE = 0,\n    /**\n     * 普通\n     */\n    COMMON = 1,\n    /**\n     * 精良\n     */\n    UNCOMMON = 2,\n    /**\n     * 稀有\n     */\n    RACE = 3,\n    /**\n     * 史诗\n     */\n    EPIC = 4,\n    /**\n     * 传说\n     */\n    LEGENDARY = 5,\n    /**\n     * 神话\n     */\n    MYTHIC = 6,\n}\n\n} \nexport namespace res { \n/**\n * 技能/buff条件\n */\nexport enum SkillConditionType {\n    /**\n     * 无条件\n     */\n    NONE = 0,\n}\n\n} \nexport namespace res { \n/**\n * 目标类型\n */\nexport enum TargetType {\n    /**\n     * 自己\n     */\n    Self = 0,\n    /**\n     * 玩家\n     */\n    Main = 1,\n    /**\n     * 玩家方\n     */\n    MainFriendly = 2,\n    /**\n     * 敌方\n     */\n    Enemy = 3,\n    /**\n     * Boss敌方\n     */\n    BossEnemy = 4,\n    /**\n     * 普通敌方\n     */\n    NormalEnemy = 5,\n}\n\n} \n \nexport enum TargetScanStrategy {\n    /**\n     * 更新\n     */\n    Refresh = 0,\n    /**\n     * 保持\n     */\n    Keep = 1,\n}\n\n \n \nexport enum TaskClass {\n    /**\n     * 日常任务\n     */\n    DAILY_TASK = 1,\n    /**\n     * 周常任务\n     */\n    WEEKLY_TASK = 2,\n    /**\n     * 挑战任务\n     */\n    CHALLENGE = 3,\n    /**\n     * 成就\n     */\n    ACHIEVEMENT = 4,\n    /**\n     * 活动\n     */\n    ACTIVITY = 5,\n}\n\n \n \nexport enum TaskCondType {\n    /**\n     * 可以不填，默认无条件限制\n     */\n    NONE = 0,\n    /**\n     * 等级\n     */\n    LEVEL = 1,\n    /**\n     * 关卡\n     */\n    STAGE = 2,\n    /**\n     * 星级\n     */\n    STAR = 3,\n    /**\n     * 战力\n     */\n    FORCE = 4,\n}\n\n \n \nexport enum TaskObjectType {\n    /**\n     * 通关或参与某模型大类达到指定次数/模式ID/次数\n     */\n    PLAYER_MODE_TIMES = 1,\n    /**\n     * 通关或参与某指定关卡达到指定次数/关卡ID/次数\n     */\n    PLAYER_STAGE_TIMES = 2,\n    /**\n     * 消灭指定类型的怪物数量达到目标/怪物类型/数量\n     */\n    KILL_MONSTER_CLASS_COUNT = 3,\n    /**\n     * 商城指定抽奖操作达到指定次数/抽奖ID/次数\n     */\n    LOTTERY_TIMES = 4,\n    /**\n     * 领取挂机奖励达到指定次数/次数\n     */\n    AFK_TIMES = 5,\n    /**\n     * 充值达到指定金额/金额\n     */\n    CHARGE_AMOUNT = 6,\n    /**\n     * 购买指定商品（包括体力等）达到指定数量/商品ID/数量\n     */\n    BUY_ITEM_COUNT = 7,\n    /**\n     * 看广告达到指定次数/次数\n     */\n    WATCH_AD_TIMES = 8,\n    /**\n     * 累计登录达到指定天数/天数\n     */\n    LOGIN_DAYS = 9,\n    /**\n     * 玩家达到指定等级\n     */\n    ROLE_LEVEL = 10,\n    /**\n     * 消耗指定资源达到指定数额\n     */\n    CONSOME = 11,\n    /**\n     * 拥有指定品质的装备达到指定数量\n     */\n    EQUIP_QUALITY = 12,\n    /**\n     * 拥有指定等级的装备达到指定数量\n     */\n    EQUIP_LEVEL = 13,\n    /**\n     * 执行装备合成达到指定次数\n     */\n    EQUIP_COMB_TIMES = 14,\n    /**\n     * 执行装备升级达到指定次数\n     */\n    EQUIP_UNDRAGE_TIMES = 15,\n    /**\n     * 公会捐献达到指定次数\n     */\n    GUILD_DONATE_TIMES = 16,\n    /**\n     * 拥有战机（已解锁）达到指定数量\n     */\n    FIGHTER_UNLOCK_COUNT = 17,\n    /**\n     * 所有已解锁战机星级累计到指定数量\n     */\n    FIGHTER_STAR_TOTAL = 18,\n    /**\n     * 角色战力达到指定数值\n     */\n    ROLE_FORCE = 19,\n    /**\n     * 消耗指定金额的货币获得奖励\n     */\n    SPECIFY_BUG_CONSUME = 20,\n    /**\n     * 使用特定道具达到指定次数\n     */\n    USE_ITEM_TIMES = 21,\n    /**\n     * 执行签到操作\n     */\n    CHECK_IN = 22,\n}\n\n \n \nexport enum TaskPeriodType {\n    /**\n     * 单次完成，不重置， 默认，可不填\n     */\n    SINGLE = 0,\n    /**\n     * 每日 0时重置\n     */\n    DAILY = 1,\n    /**\n     * 每周 周一0时重置\n     */\n    WEEKLY = 2,\n    /**\n     * 每月 1日0时重置\n     */\n    MONTHLY = 3,\n}\n\n \n\n\n\n\n\nexport class Boss {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.bId === undefined) { throw new Error() }\n        this.bId = _json_.bId\n        if (_json_.sId === undefined) { throw new Error() }\n        this.sId = _json_.sId\n        if (_json_.app === undefined) { throw new Error() }\n        this.app = _json_.app\n        if (_json_.ta === undefined) { throw new Error() }\n        this.ta = _json_.ta\n        if (_json_.ft === undefined) { throw new Error() }\n        this.ft = _json_.ft\n        if (_json_.leave === undefined) { throw new Error() }\n        this.leave = _json_.leave\n        if (_json_.exp === undefined) { throw new Error() }\n        this.exp = _json_.exp\n        if (_json_.rid === undefined) { throw new Error() }\n        this.rid = _json_.rid\n        if (_json_.sk === undefined) { throw new Error() }\n        this.sk = _json_.sk\n        if (_json_.blp === undefined) { throw new Error() }\n        this.blp = _json_.blp\n        if (_json_.us === undefined) { throw new Error() }\n        this.us = _json_.us\n        if (_json_.ua === undefined) { throw new Error() }\n        this.ua = _json_.ua\n        if (_json_.va === undefined) { throw new Error() }\n        this.va = _json_.va\n        if (_json_.sv === undefined) { throw new Error() }\n        this.sv = _json_.sv\n        if (_json_.fl === undefined) { throw new Error() }\n        this.fl = _json_.fl\n        if (_json_.loot === undefined) { throw new Error() }\n        this.loot = _json_.loot\n        if (_json_.adsorb === undefined) { throw new Error() }\n        this.adsorb = _json_.adsorb\n        if (_json_.lp0 === undefined) { throw new Error() }\n        this.lp0 = _json_.lp0\n        if (_json_.lp1 === undefined) { throw new Error() }\n        this.lp1 = _json_.lp1\n        if (_json_.dh === undefined) { throw new Error() }\n        this.dh = _json_.dh\n        if (_json_.atk === undefined) { throw new Error() }\n        this.atk = _json_.atk\n        if (_json_.col === undefined) { throw new Error() }\n        this.col = _json_.col\n        if (_json_.tway === undefined) { throw new Error() }\n        this.tway = _json_.tway\n        if (_json_.way === undefined) { throw new Error() }\n        this.way = _json_.way\n        if (_json_.wi === undefined) { throw new Error() }\n        this.wi = _json_.wi\n        if (_json_.sp === undefined) { throw new Error() }\n        this.sp = _json_.sp\n        if (_json_.ai === undefined) { throw new Error() }\n        this.ai = _json_.ai\n        if (_json_.ra === undefined) { throw new Error() }\n        this.ra = _json_.ra\n        if (_json_.a0 === undefined) { throw new Error() }\n        this.a0 = _json_.a0\n        if (_json_.a1 === undefined) { throw new Error() }\n        this.a1 = _json_.a1\n        if (_json_.a2 === undefined) { throw new Error() }\n        this.a2 = _json_.a2\n        if (_json_.a3 === undefined) { throw new Error() }\n        this.a3 = _json_.a3\n        if (_json_.a4 === undefined) { throw new Error() }\n        this.a4 = _json_.a4\n        if (_json_.a5 === undefined) { throw new Error() }\n        this.a5 = _json_.a5\n        if (_json_.a6 === undefined) { throw new Error() }\n        this.a6 = _json_.a6\n        if (_json_.a7 === undefined) { throw new Error() }\n        this.a7 = _json_.a7\n        if (_json_.a8 === undefined) { throw new Error() }\n        this.a8 = _json_.a8\n        if (_json_.a9 === undefined) { throw new Error() }\n        this.a9 = _json_.a9\n        if (_json_.a10 === undefined) { throw new Error() }\n        this.a10 = _json_.a10\n        if (_json_.a11 === undefined) { throw new Error() }\n        this.a11 = _json_.a11\n        if (_json_.a12 === undefined) { throw new Error() }\n        this.a12 = _json_.a12\n        if (_json_.a13 === undefined) { throw new Error() }\n        this.a13 = _json_.a13\n        if (_json_.a14 === undefined) { throw new Error() }\n        this.a14 = _json_.a14\n        if (_json_.a15 === undefined) { throw new Error() }\n        this.a15 = _json_.a15\n        if (_json_.a16 === undefined) { throw new Error() }\n        this.a16 = _json_.a16\n        if (_json_.a17 === undefined) { throw new Error() }\n        this.a17 = _json_.a17\n        if (_json_.a18 === undefined) { throw new Error() }\n        this.a18 = _json_.a18\n        if (_json_.a19 === undefined) { throw new Error() }\n        this.a19 = _json_.a19\n        if (_json_.a20 === undefined) { throw new Error() }\n        this.a20 = _json_.a20\n        if (_json_.a21 === undefined) { throw new Error() }\n        this.a21 = _json_.a21\n        if (_json_.a22 === undefined) { throw new Error() }\n        this.a22 = _json_.a22\n        if (_json_.a100 === undefined) { throw new Error() }\n        this.a100 = _json_.a100\n        if (_json_.a101 === undefined) { throw new Error() }\n        this.a101 = _json_.a101\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 飞机id\n     */\n    readonly bId: number\n    /**\n     * 子类型\n     */\n    readonly sId: number\n    /**\n     * 出场参数\n     */\n    readonly app: string\n    /**\n     * inAudio\n     */\n    readonly ta: string\n    /**\n     * 死亡掉落延迟\n     */\n    readonly ft: number\n    /**\n     * leave\n     */\n    readonly leave: number\n    /**\n     * exp\n     */\n    readonly exp: number\n    /**\n     * rid\n     */\n    readonly rid: string\n    /**\n     * 爆炸震动参数\n     */\n    readonly sk: string\n    /**\n     * 爆炸参数\n     */\n    readonly blp: string\n    /**\n     * us\n     */\n    readonly us: string\n    /**\n     * ua\n     */\n    readonly ua: string\n    /**\n     * va\n     */\n    readonly va: string\n    /**\n     * sv\n     */\n    readonly sv: string\n    /**\n     * fl\n     */\n    readonly fl: string\n    /**\n     * loot\n     */\n    readonly loot: string\n    /**\n     * adsorb\n     */\n    readonly adsorb: string\n    /**\n     * lp0\n     */\n    readonly lp0: string\n    /**\n     * lp1\n     */\n    readonly lp1: string\n    /**\n     * dh\n     */\n    readonly dh: string\n    /**\n     * atk\n     */\n    readonly atk: number\n    /**\n     * col\n     */\n    readonly col: number\n    /**\n     * tway\n     */\n    readonly tway: string\n    /**\n     * way\n     */\n    readonly way: string\n    /**\n     * wi\n     */\n    readonly wi: string\n    /**\n     * sp\n     */\n    readonly sp: string\n    /**\n     * ai\n     */\n    readonly ai: string\n    /**\n     * ra\n     */\n    readonly ra: string\n    /**\n     * a0\n     */\n    readonly a0: string\n    /**\n     * a1\n     */\n    readonly a1: string\n    /**\n     * a2\n     */\n    readonly a2: string\n    /**\n     * a3\n     */\n    readonly a3: string\n    /**\n     * a4\n     */\n    readonly a4: string\n    /**\n     * a5\n     */\n    readonly a5: string\n    /**\n     * a6\n     */\n    readonly a6: string\n    /**\n     * a7\n     */\n    readonly a7: string\n    /**\n     * a8\n     */\n    readonly a8: string\n    /**\n     * a9\n     */\n    readonly a9: string\n    /**\n     * a10\n     */\n    readonly a10: string\n    /**\n     * a11\n     */\n    readonly a11: string\n    /**\n     * a12\n     */\n    readonly a12: string\n    /**\n     * a13\n     */\n    readonly a13: string\n    /**\n     * a14\n     */\n    readonly a14: string\n    /**\n     * a15\n     */\n    readonly a15: string\n    /**\n     * a16\n     */\n    readonly a16: string\n    /**\n     * a17\n     */\n    readonly a17: string\n    /**\n     * a18\n     */\n    readonly a18: string\n    /**\n     * a19\n     */\n    readonly a19: string\n    /**\n     * a20\n     */\n    readonly a20: string\n    /**\n     * a21\n     */\n    readonly a21: string\n    /**\n     * a22\n     */\n    readonly a22: string\n    /**\n     * a100\n     */\n    readonly a100: string\n    /**\n     * a101\n     */\n    readonly a101: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class buffer {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.buffType === undefined) { throw new Error() }\n        this.buffType = _json_.buffType\n        if (_json_.duration === undefined) { throw new Error() }\n        this.duration = _json_.duration\n        if (_json_.durationBonus === undefined) { throw new Error() }\n        this.durationBonus = _json_.durationBonus\n        if (_json_.maxStack === undefined) { throw new Error() }\n        this.maxStack = _json_.maxStack\n        if (_json_.refreshType === undefined) { throw new Error() }\n        this.refreshType = _json_.refreshType\n        if (_json_.cycle === undefined) { throw new Error() }\n        this.cycle = _json_.cycle\n        if (_json_.cycleTimes === undefined) { throw new Error() }\n        this.cycleTimes = _json_.cycleTimes\n        if (_json_.effects === undefined) { throw new Error() }\n        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new builtin.EffectParam(_ele0); this.effects.push(_e0);}}\n        if (_json_.conditionID === undefined) { throw new Error() }\n        this.conditionID = _json_.conditionID\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 类别\n     */\n    readonly buffType: res.BuffType\n    /**\n     * 持续时间\n     */\n    readonly duration: number\n    /**\n     * 持续时间加成\n     */\n    readonly durationBonus: number\n    /**\n     * 最大叠加次数\n     */\n    readonly maxStack: number\n    /**\n     * 叠加刷新策略\n     */\n    readonly refreshType: boolean\n    /**\n     * 周期\n     */\n    readonly cycle: number\n    /**\n     * 周期计数\n     */\n    readonly cycleTimes: number\n    readonly effects: builtin.EffectParam[]\n    /**\n     * 禁用条件\n     */\n    readonly conditionID: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\nexport namespace builtin {\nexport class ApplyBuff {\n\n    constructor(_json_: any) {\n        if (_json_.target === undefined) { throw new Error() }\n        this.target = _json_.target\n        if (_json_.buffID === undefined) { throw new Error() }\n        this.buffID = _json_.buffID\n    }\n\n    readonly target: res.TargetType\n    readonly buffID: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class ConParam {\n\n    constructor(_json_: any) {\n        if (_json_.con === undefined) { throw new Error() }\n        this.con = _json_.con\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n    }\n\n    readonly con: number\n    readonly param: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class EffectParam {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.target === undefined) { throw new Error() }\n        this.target = _json_.target\n        if (_json_.param === undefined) { throw new Error() }\n        { this.param = []; for(let _ele0 of _json_.param) { let _e0; _e0 = _ele0; this.param.push(_e0);}}\n    }\n\n    readonly type: res.EffectType\n    readonly target: res.TargetType\n    readonly param: number[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\n/**\n * 随机策略\n */\nexport class randStrategy {\n\n    constructor(_json_: any) {\n        if (_json_.ID === undefined) { throw new Error() }\n        this.ID = _json_.ID\n        if (_json_.Weight === undefined) { throw new Error() }\n        this.Weight = _json_.Weight\n    }\n\n    /**\n     * 随机策略ID\n     */\n    readonly ID: number\n    /**\n     * ID的权重\n     */\n    readonly Weight: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class RatingParam {\n\n    constructor(_json_: any) {\n        if (_json_.rating === undefined) { throw new Error() }\n        this.rating = _json_.rating\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n    }\n\n    readonly rating: number\n    readonly param: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class SkillCondition {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.param === undefined) { throw new Error() }\n        { this.param = []; for(let _ele0 of _json_.param) { let _e0; _e0 = _ele0; this.param.push(_e0);}}\n    }\n\n    readonly type: res.SkillConditionType\n    readonly param: number[]\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector2 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n    }\n\n    readonly x: number\n    readonly y: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector3 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n}\n\n\nexport namespace builtin {\nexport class vector4 {\n\n    constructor(_json_: any) {\n        if (_json_.x === undefined) { throw new Error() }\n        this.x = _json_.x\n        if (_json_.y === undefined) { throw new Error() }\n        this.y = _json_.y\n        if (_json_.z === undefined) { throw new Error() }\n        this.z = _json_.z\n        if (_json_.w === undefined) { throw new Error() }\n        this.w = _json_.w\n    }\n\n    readonly x: number\n    readonly y: number\n    readonly z: number\n    readonly w: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n}\n\n\n\nexport class Bullet {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.am === undefined) { throw new Error() }\n        this.am = _json_.am\n        if (_json_.image === undefined) { throw new Error() }\n        this.image = _json_.image\n        if (_json_.bustyle === undefined) { throw new Error() }\n        this.bustyle = _json_.bustyle\n        if (_json_.angleSpeed === undefined) { throw new Error() }\n        this.angleSpeed = _json_.angleSpeed\n        if (_json_.waittime === undefined) { throw new Error() }\n        { this.waittime = []; for(let _ele0 of _json_.waittime) { let _e0; _e0 = _ele0; this.waittime.push(_e0);}}\n        if (_json_.initialve === undefined) { throw new Error() }\n        this.initialve = _json_.initialve\n        if (_json_.spdiff === undefined) { throw new Error() }\n        this.spdiff = _json_.spdiff\n        if (_json_.scale === undefined) { throw new Error() }\n        this.scale = _json_.scale\n        if (_json_.retrieve === undefined) { throw new Error() }\n        this.retrieve = _json_.retrieve\n        if (_json_.disappear === undefined) { throw new Error() }\n        this.disappear = _json_.disappear\n        if (_json_.shiftingbody === undefined) { throw new Error() }\n        { this.shiftingbody = []; for(let _ele0 of _json_.shiftingbody) { let _e0; _e0 = _ele0; this.shiftingbody.push(_e0);}}\n        if (_json_.body === undefined) { throw new Error() }\n        this.body = _json_.body\n        if (_json_.exstyle1 === undefined) { throw new Error() }\n        this.exstyle1 = _json_.exstyle1\n        if (_json_.exstyle2 === undefined) { throw new Error() }\n        this.exstyle2 = _json_.exstyle2\n        if (_json_.time === undefined) { throw new Error() }\n        this.time = _json_.time\n        if (_json_.accnumber === undefined) { throw new Error() }\n        this.accnumber = _json_.accnumber\n        if (_json_.acc === undefined) { throw new Error() }\n        this.acc = _json_.acc\n        if (_json_.offset === undefined) { throw new Error() }\n        { this.offset = []; for(let _ele0 of _json_.offset) { let _e0; _e0 = _ele0; this.offset.push(_e0);}}\n        if (_json_.para === undefined) { throw new Error() }\n        { this.para = []; for(let _ele0 of _json_.para) { let _e0; _e0 = _ele0; this.para.push(_e0);}}\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * name\n     */\n    readonly name: string\n    /**\n     * am\n     */\n    readonly am: string\n    /**\n     * image\n     */\n    readonly image: string\n    /**\n     * 子弹类型\n     */\n    readonly bustyle: number\n    /**\n     * 旋转角度\n     */\n    readonly angleSpeed: number\n    /**\n     * 等待时间\n     */\n    readonly waittime: number[]\n    /**\n     * 速度\n     */\n    readonly initialve: number\n    /**\n     * 速度随机变量\n     */\n    readonly spdiff: number\n    /**\n     * 缩放\n     */\n    readonly scale: number\n    /**\n     * 子弹存活时间\n     */\n    readonly retrieve: number\n    /**\n     * 是否穿透\n     */\n    readonly disappear: number\n    /**\n     * 碰撞宽高\n     */\n    readonly shiftingbody: number[]\n    /**\n     * 碰撞\n     */\n    readonly body: number\n    /**\n     * 伤害粒子效果\n     */\n    readonly exstyle1: string\n    /**\n     * 伤害粒子缩放\n     */\n    readonly exstyle2: string\n    /**\n     * time\n     */\n    readonly time: number\n    /**\n     * accnumber\n     */\n    readonly accnumber: number\n    /**\n     * acc\n     */\n    readonly acc: number\n    /**\n     * offset\n     */\n    readonly offset: number[]\n    /**\n     * para\n     */\n    readonly para: number[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Chapter {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.levelCount === undefined) { throw new Error() }\n        this.levelCount = _json_.levelCount\n        if (_json_.levelGroupCount === undefined) { throw new Error() }\n        this.levelGroupCount = _json_.levelGroupCount\n        if (_json_.strategy === undefined) { throw new Error() }\n        this.strategy = _json_.strategy\n        if (_json_.damageBonus === undefined) { throw new Error() }\n        this.damageBonus = _json_.damageBonus\n        if (_json_.lifeBounus === undefined) { throw new Error() }\n        this.lifeBounus = _json_.lifeBounus\n        if (_json_.strategyList === undefined) { throw new Error() }\n        { this.strategyList = []; for(let _ele0 of _json_.strategyList) { let _e0; _e0 = new builtin.randStrategy(_ele0); this.strategyList.push(_e0);}}\n    }\n\n    /**\n     * 章节ID\n     */\n    readonly id: number\n    /**\n     * 章节关卡数量\n     */\n    readonly levelCount: number\n    /**\n     * 章节关卡组数量\n     */\n    readonly levelGroupCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly strategy: number\n    /**\n     * 章节伤害加成\n     */\n    readonly damageBonus: number\n    /**\n     * 章节生命加成\n     */\n    readonly lifeBounus: number\n    readonly strategyList: builtin.randStrategy[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        for (let _e of this.strategyList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\n/**\n * 消耗的材料\n */\nexport class ConsumeItem {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n    }\n\n    readonly id: number\n    readonly num: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ConsumeMoney {\n\n    constructor(_json_: any) {\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n    }\n\n    /**\n     * 货币类型\n     */\n    readonly type: res.MoneyType\n    /**\n     * 货币数量\n     */\n    readonly num: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class Enemy {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.uiId === undefined) { throw new Error() }\n        this.uiId = _json_.uiId\n        if (_json_.atk === undefined) { throw new Error() }\n        this.atk = _json_.atk\n        if (_json_.hp === undefined) { throw new Error() }\n        this.hp = _json_.hp\n        if (_json_.collideLevel === undefined) { throw new Error() }\n        this.collideLevel = _json_.collideLevel\n        if (_json_.turn === undefined) { throw new Error() }\n        this.turn = _json_.turn\n        if (_json_.hpShow === undefined) { throw new Error() }\n        this.hpShow = _json_.hpShow\n        if (_json_.collideAttack === undefined) { throw new Error() }\n        this.collideAttack = _json_.collideAttack\n        if (_json_.bCollideDead === undefined) { throw new Error() }\n        this.bCollideDead = _json_.bCollideDead\n        if (_json_.bMoveAttack === undefined) { throw new Error() }\n        this.bMoveAttack = _json_.bMoveAttack\n        if (_json_.bStayAttack === undefined) { throw new Error() }\n        this.bStayAttack = _json_.bStayAttack\n        if (_json_.attackInterval === undefined) { throw new Error() }\n        this.attackInterval = _json_.attackInterval\n        if (_json_.attackNum === undefined) { throw new Error() }\n        this.attackNum = _json_.attackNum\n        if (_json_.attackData === undefined) { throw new Error() }\n        this.attackData = _json_.attackData\n        if (_json_.param === undefined) { throw new Error() }\n        this.param = _json_.param\n        if (_json_.dieShoot === undefined) { throw new Error() }\n        this.dieShoot = _json_.dieShoot\n        if (_json_.dieBullet === undefined) { throw new Error() }\n        this.dieBullet = _json_.dieBullet\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 敌机的显示id\n     */\n    readonly uiId: number\n    /**\n     * 攻击\n     */\n    readonly atk: number\n    /**\n     * 血量\n     */\n    readonly hp: number\n    /**\n     * 碰撞等级\n     */\n    readonly collideLevel: number\n    /**\n     * 是否改变方向\n     */\n    readonly turn: number\n    /**\n     * 是否显示血条\n     */\n    readonly hpShow: number\n    /**\n     * 碰撞伤害值\n     */\n    readonly collideAttack: number\n    /**\n     * 碰撞后是否死亡\n     */\n    readonly bCollideDead: number\n    /**\n     * 移动时是否攻击\n     */\n    readonly bMoveAttack: number\n    /**\n     * 静止时是否攻击\n     */\n    readonly bStayAttack: number\n    /**\n     * 攻击间隔时间\n     */\n    readonly attackInterval: number\n    /**\n     * 攻击次数\n     */\n    readonly attackNum: number\n    /**\n     * 攻击点位置数据(x,y;间隔,子弹id,子弹数量,子弹间隔,子弹攻击力百分比(100为1倍);)\n     */\n    readonly attackData: string\n    /**\n     * 自定义参数\n     */\n    readonly param: string\n    /**\n     * 死亡时发射的子弹数据\n     */\n    readonly dieShoot: string\n    /**\n     * 死亡时是否发射子弹\n     */\n    readonly dieBullet: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class EnemyUI {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.image === undefined) { throw new Error() }\n        this.image = _json_.image\n        if (_json_.isAm === undefined) { throw new Error() }\n        this.isAm = _json_.isAm\n        if (_json_.collider === undefined) { throw new Error() }\n        this.collider = _json_.collider\n        if (_json_.hpParam === undefined) { throw new Error() }\n        this.hpParam = _json_.hpParam\n        if (_json_.blastSound === undefined) { throw new Error() }\n        this.blastSound = _json_.blastSound\n        if (_json_.blp === undefined) { throw new Error() }\n        this.blp = _json_.blp\n        if (_json_.blastDurations === undefined) { throw new Error() }\n        this.blastDurations = _json_.blastDurations\n        if (_json_.blastShake === undefined) { throw new Error() }\n        this.blastShake = _json_.blastShake\n        if (_json_.damageParam === undefined) { throw new Error() }\n        this.damageParam = _json_.damageParam\n        if (_json_.extraParam0 === undefined) { throw new Error() }\n        this.extraParam0 = _json_.extraParam0\n        if (_json_.extraParam1 === undefined) { throw new Error() }\n        this.extraParam1 = _json_.extraParam1\n        if (_json_.skillResistUIDict === undefined) { throw new Error() }\n        this.skillResistUIDict = _json_.skillResistUIDict\n        if (_json_.lootParam0 === undefined) { throw new Error() }\n        this.lootParam0 = _json_.lootParam0\n        if (_json_.lootParam1 === undefined) { throw new Error() }\n        this.lootParam1 = _json_.lootParam1\n        if (_json_.showParam === undefined) { throw new Error() }\n        this.showParam = _json_.showParam\n        if (_json_.sneakAnim === undefined) { throw new Error() }\n        this.sneakAnim = _json_.sneakAnim\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 图片\n     */\n    readonly image: string\n    /**\n     * 是否动画\n     */\n    readonly isAm: number\n    /**\n     * 碰撞器数据\n     */\n    readonly collider: string\n    /**\n     * 血量参数\n     */\n    readonly hpParam: string\n    /**\n     * 爆炸音效 ID\n     */\n    readonly blastSound: number\n    /**\n     * 爆炸次数和爆炸参数\n     */\n    readonly blp: string\n    /**\n     * 爆炸持续时间\n     */\n    readonly blastDurations: string\n    /**\n     * 爆炸震动参数\n     */\n    readonly blastShake: string\n    /**\n     * 伤害参数\n     */\n    readonly damageParam: string\n    /**\n     * 额外参数\n     */\n    readonly extraParam0: string\n    /**\n     * 额外参数 1\n     */\n    readonly extraParam1: string\n    /**\n     * 技能抗性字典\n     */\n    readonly skillResistUIDict: string\n    /**\n     * 掉落参数 0\n     */\n    readonly lootParam0: string\n    /**\n     * 掉落参数 1\n     */\n    readonly lootParam1: string\n    /**\n     * 显示参数\n     */\n    readonly showParam: string\n    /**\n     * 潜行动画\n     */\n    readonly sneakAnim: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 装备属性\n */\nexport class EquipProp {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.value === undefined) { throw new Error() }\n        this.value = _json_.value\n    }\n\n    readonly id: res.PropName\n    readonly value: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class GameMap {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.level === undefined) { throw new Error() }\n        this.level = _json_.level\n        if (_json_.floor_res === undefined) { throw new Error() }\n        { this.floorRes = []; for(let _ele0 of _json_.floor_res) { let _e0; _e0 = _ele0; this.floorRes.push(_e0);}}\n        if (_json_.hide_img === undefined) { throw new Error() }\n        { this.hideImg = []; for(let _ele0 of _json_.hide_img) { let _e0; _e0 = _ele0; this.hideImg.push(_e0);}}\n        if (_json_.sky_res === undefined) { throw new Error() }\n        { this.skyRes = []; for(let _ele0 of _json_.sky_res) { let _e0; _e0 = _ele0; this.skyRes.push(_e0);}}\n        if (_json_.imageSque_res === undefined) { throw new Error() }\n        { this.imageSqueRes = []; for(let _ele0 of _json_.imageSque_res) { let _e0; _e0 = _ele0; this.imageSqueRes.push(_e0);}}\n        if (_json_.floor_speed === undefined) { throw new Error() }\n        { this.floorSpeed = []; for(let _ele0 of _json_.floor_speed) { let _e0; _e0 = _ele0; this.floorSpeed.push(_e0);}}\n        if (_json_.sky_speed === undefined) { throw new Error() }\n        { this.skySpeed = []; for(let _ele0 of _json_.sky_speed) { let _e0; _e0 = _ele0; this.skySpeed.push(_e0);}}\n        if (_json_.imageSque_speed === undefined) { throw new Error() }\n        { this.imageSqueSpeed = []; for(let _ele0 of _json_.imageSque_speed) { let _e0; _e0 = _ele0; this.imageSqueSpeed.push(_e0);}}\n        if (_json_.floor_layer === undefined) { throw new Error() }\n        { this.floorLayer = []; for(let _ele0 of _json_.floor_layer) { let _e0; _e0 = _ele0; this.floorLayer.push(_e0);}}\n        if (_json_.sky_layer === undefined) { throw new Error() }\n        { this.skyLayer = []; for(let _ele0 of _json_.sky_layer) { let _e0; _e0 = _ele0; this.skyLayer.push(_e0);}}\n        if (_json_.imageSque_layer === undefined) { throw new Error() }\n        { this.imageSqueLayer = []; for(let _ele0 of _json_.imageSque_layer) { let _e0; _e0 = _ele0; this.imageSqueLayer.push(_e0);}}\n        if (_json_.imageSqueNode_move === undefined) { throw new Error() }\n        { this.imageSqueNodeMove = []; for(let _ele0 of _json_.imageSqueNode_move) { let _e0; _e0 = _ele0; this.imageSqueNodeMove.push(_e0);}}\n        if (_json_.imageSque_pos === undefined) { throw new Error() }\n        { this.imageSquePos = []; for(let _ele0 of _json_.imageSque_pos) { let _e0; _e0 = _ele0; this.imageSquePos.push(_e0);}}\n        if (_json_.skyNode_move === undefined) { throw new Error() }\n        { this.skyNodeMove = []; for(let _ele0 of _json_.skyNode_move) { let _e0; _e0 = _ele0; this.skyNodeMove.push(_e0);}}\n        if (_json_.link_y_distance === undefined) { throw new Error() }\n        { this.linkYDistance = []; for(let _ele0 of _json_.link_y_distance) { let _e0; _e0 = _ele0; this.linkYDistance.push(_e0);}}\n        if (_json_.sky_angle === undefined) { throw new Error() }\n        { this.skyAngle = []; for(let _ele0 of _json_.sky_angle) { let _e0; _e0 = _ele0; this.skyAngle.push(_e0);}}\n        if (_json_.sky_layout === undefined) { throw new Error() }\n        { this.skyLayout = []; for(let _ele0 of _json_.sky_layout) { let _e0; _e0 = _ele0; this.skyLayout.push(_e0);}}\n        if (_json_.in_map_item === undefined) { throw new Error() }\n        { this.inMapItem = []; for(let _ele0 of _json_.in_map_item) { let _e0; _e0 = _ele0; this.inMapItem.push(_e0);}}\n        if (_json_.start_y === undefined) { throw new Error() }\n        this.startY = _json_.start_y\n        if (_json_.total_rules === undefined) { throw new Error() }\n        this.totalRules = _json_.total_rules\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * level\n     */\n    readonly level: number\n    /**\n     * floor_res\n     */\n    readonly floorRes: string[]\n    /**\n     * hide_img\n     */\n    readonly hideImg: string[]\n    /**\n     * sky_res\n     */\n    readonly skyRes: string[]\n    /**\n     * imageSque_res\n     */\n    readonly imageSqueRes: string[]\n    /**\n     * floor_speed\n     */\n    readonly floorSpeed: number[]\n    /**\n     * sky_speed\n     */\n    readonly skySpeed: number[]\n    /**\n     * imageSque_speed\n     */\n    readonly imageSqueSpeed: number[]\n    /**\n     * floor_layer\n     */\n    readonly floorLayer: number[]\n    /**\n     * sky_layer\n     */\n    readonly skyLayer: number[]\n    /**\n     * imageSque_layer\n     */\n    readonly imageSqueLayer: number[]\n    /**\n     * imageSqueNode_move\n     */\n    readonly imageSqueNodeMove: number[]\n    /**\n     * imageSque_pos\n     */\n    readonly imageSquePos: number[]\n    /**\n     * skyNode_move\n     */\n    readonly skyNodeMove: number[]\n    /**\n     * link_y_distance\n     */\n    readonly linkYDistance: string[]\n    /**\n     * sky_angle\n     */\n    readonly skyAngle: number[]\n    /**\n     * sky_layout\n     */\n    readonly skyLayout: number[]\n    /**\n     * in_map_item\n     */\n    readonly inMapItem: number[]\n    /**\n     * start_y\n     */\n    readonly startY: number\n    /**\n     * total_rules\n     */\n    readonly totalRules: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class GameMode {\n\n    constructor(_json_: any) {\n        if (_json_.ID === undefined) { throw new Error() }\n        this.ID = _json_.ID\n        if (_json_.modeType === undefined) { throw new Error() }\n        this.modeType = _json_.modeType\n        if (_json_.chapterID === undefined) { throw new Error() }\n        this.chapterID = _json_.chapterID\n        if (_json_.order === undefined) { throw new Error() }\n        this.order = _json_.order\n        if (_json_.resourceID === undefined) { throw new Error() }\n        this.resourceID = _json_.resourceID\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.conList === undefined) { throw new Error() }\n        { this.conList = []; for(let _ele0 of _json_.conList) { let _e0; _e0 = new builtin.ConParam(_ele0); this.conList.push(_e0);}}\n        if (_json_.cycle === undefined) { throw new Error() }\n        this.cycle = _json_.cycle\n        if (_json_.times === undefined) { throw new Error() }\n        this.times = _json_.times\n        if (_json_.monType === undefined) { throw new Error() }\n        this.monType = _json_.monType\n        if (_json_.costParam1 === undefined) { throw new Error() }\n        this.costParam1 = _json_.costParam1\n        if (_json_.costParam2 === undefined) { throw new Error() }\n        this.costParam2 = _json_.costParam2\n        if (_json_.rebirthTimes === undefined) { throw new Error() }\n        this.rebirthTimes = _json_.rebirthTimes\n        if (_json_.rebirthCost === undefined) { throw new Error() }\n        this.rebirthCost = _json_.rebirthCost\n        if (_json_.power === undefined) { throw new Error() }\n        this.power = _json_.power\n        if (_json_.rogueID === undefined) { throw new Error() }\n        this.rogueID = _json_.rogueID\n        if (_json_.LevelLimit === undefined) { throw new Error() }\n        this.LevelLimit = _json_.LevelLimit\n        if (_json_.rogueFirst === undefined) { throw new Error() }\n        this.rogueFirst = _json_.rogueFirst\n        if (_json_.sweepLimit === undefined) { throw new Error() }\n        this.sweepLimit = _json_.sweepLimit\n        if (_json_.rewardID1 === undefined) { throw new Error() }\n        this.rewardID1 = _json_.rewardID1\n        if (_json_.rewardID2 === undefined) { throw new Error() }\n        this.rewardID2 = _json_.rewardID2\n        if (_json_.ratingList === undefined) { throw new Error() }\n        { this.ratingList = []; for(let _ele0 of _json_.ratingList) { let _e0; _e0 = new builtin.RatingParam(_ele0); this.ratingList.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly ID: number\n    /**\n     * 模式类型\n     */\n    readonly modeType: res.ModeType\n    /**\n     * 章节ID\n     */\n    readonly chapterID: number\n    /**\n     * 排序\n     */\n    readonly order: number\n    /**\n     * 入口资源\n     */\n    readonly resourceID: number\n    /**\n     * 文本介绍\n     */\n    readonly description: string\n    readonly conList: builtin.ConParam[]\n    /**\n     * 进入周期\n     */\n    readonly cycle: res.PlayCycle\n    /**\n     * 进入次数\n     */\n    readonly times: number\n    /**\n     * 消耗类型\n     */\n    readonly monType: res.MoneyType\n    /**\n     * 消耗参数1\n     */\n    readonly costParam1: number\n    /**\n     * 消耗参数2\n     */\n    readonly costParam2: number\n    /**\n     * 复活次数\n     */\n    readonly rebirthTimes: number\n    /**\n     * 复活消耗\n     */\n    readonly rebirthCost: number\n    /**\n     * 战力评估\n     */\n    readonly power: number\n    /**\n     * 肉鸽组\n     */\n    readonly rogueID: number\n    /**\n     * 局内等级上限\n     */\n    readonly LevelLimit: number\n    /**\n     * 初始肉鸽选择\n     */\n    readonly rogueFirst: number\n    /**\n     * 扫荡次数\n     */\n    readonly sweepLimit: number\n    /**\n     * 奖励ID1\n     */\n    readonly rewardID1: number\n    /**\n     * 奖励ID2\n     */\n    readonly rewardID2: number\n    readonly ratingList: builtin.RatingParam[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class GlobalAttr {\n\n    constructor(_json_: any) {\n        if (_json_.GoldProducion === undefined) { throw new Error() }\n        this.GoldProducion = _json_.GoldProducion\n        if (_json_.MaxEnergy === undefined) { throw new Error() }\n        this.MaxEnergy = _json_.MaxEnergy\n        if (_json_.EnergyRecoverInterval === undefined) { throw new Error() }\n        this.EnergyRecoverInterval = _json_.EnergyRecoverInterval\n        if (_json_.EnergyRecoverValue === undefined) { throw new Error() }\n        this.EnergyRecoverValue = _json_.EnergyRecoverValue\n        if (_json_.ItemPickUpRadius === undefined) { throw new Error() }\n        this.ItemPickUpRadius = _json_.ItemPickUpRadius\n        if (_json_.PostHitProtection === undefined) { throw new Error() }\n        this.PostHitProtection = _json_.PostHitProtection\n    }\n\n    /**\n     * 每回合发放的金币\n     */\n    readonly GoldProducion: number\n    /**\n     * 体力上限值\n     */\n    readonly MaxEnergy: number\n    /**\n     * 体力恢复的间隔时间\n     */\n    readonly EnergyRecoverInterval: number\n    /**\n     * 体力恢复的值\n     */\n    readonly EnergyRecoverValue: number\n    /**\n     * 局内道具拾取距离\n     */\n    readonly ItemPickUpRadius: number\n    /**\n     * 受击保护\n     */\n    readonly PostHitProtection: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Level {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.prefab === undefined) { throw new Error() }\n        this.prefab = _json_.prefab\n        if (_json_.forbidFire === undefined) { throw new Error() }\n        this.forbidFire = _json_.forbidFire\n        if (_json_.forbidNBomb === undefined) { throw new Error() }\n        this.forbidNBomb = _json_.forbidNBomb\n        if (_json_.forbidActSkill === undefined) { throw new Error() }\n        this.forbidActSkill = _json_.forbidActSkill\n        if (_json_.planeCollisionScaling === undefined) { throw new Error() }\n        this.planeCollisionScaling = _json_.planeCollisionScaling\n        if (_json_.levelType === undefined) { throw new Error() }\n        this.levelType = _json_.levelType\n    }\n\n    /**\n     * 关卡id\n     */\n    readonly id: number\n    /**\n     * 关卡prefab\n     */\n    readonly prefab: string\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidFire: boolean\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidNBomb: boolean\n    /**\n     * 是、否（默认值）\n     */\n    readonly forbidActSkill: boolean\n    /**\n     * 0到1（1表示正常碰撞）\n     */\n    readonly planeCollisionScaling: number\n    /**\n     * 1=常规关卡<br/>2=机关关卡<br/>3=金币关卡<br/>4=火箭关卡<br/>99=Boss关\n     */\n    readonly levelType: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class LevelGroup {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.normLevelCount === undefined) { throw new Error() }\n        this.normLevelCount = _json_.normLevelCount\n        if (_json_.normLevelST === undefined) { throw new Error() }\n        this.normLevelST = _json_.normLevelST\n        if (_json_.normSTList === undefined) { throw new Error() }\n        { this.normSTList = []; for(let _ele0 of _json_.normSTList) { let _e0; _e0 = new builtin.randStrategy(_ele0); this.normSTList.push(_e0);}}\n        if (_json_.bossLevelCount === undefined) { throw new Error() }\n        this.bossLevelCount = _json_.bossLevelCount\n        if (_json_.bossLevelST === undefined) { throw new Error() }\n        this.bossLevelST = _json_.bossLevelST\n        if (_json_.bossSTList === undefined) { throw new Error() }\n        { this.bossSTList = []; for(let _ele0 of _json_.bossSTList) { let _e0; _e0 = new builtin.randStrategy(_ele0); this.bossSTList.push(_e0);}}\n    }\n\n    /**\n     * 关卡组ID\n     */\n    readonly id: number\n    /**\n     * 常规关卡数量\n     */\n    readonly normLevelCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly normLevelST: number\n    readonly normSTList: builtin.randStrategy[]\n    /**\n     * BOSS关卡数量\n     */\n    readonly bossLevelCount: number\n    /**\n     * 1=随机<br/>2=随机不重复<br/>3=顺序重复\n     */\n    readonly bossLevelST: number\n    readonly bossSTList: builtin.randStrategy[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.normSTList) { _e?.resolve(tables); }\n        \n        \n        for (let _e of this.bossSTList) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class MainPlane {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.body === undefined) { throw new Error() }\n        { this.body = []; for(let _ele0 of _json_.body) { let _e0; _e0 = _ele0; this.body.push(_e0);}}\n        if (_json_.transSrc === undefined) { throw new Error() }\n        { this.transSrc = []; for(let _ele0 of _json_.transSrc) { let _e0; _e0 = _ele0; this.transSrc.push(_e0);}}\n        if (_json_.transExt === undefined) { throw new Error() }\n        { this.transExt = []; for(let _ele0 of _json_.transExt) { let _e0; _e0 = _ele0; this.transExt.push(_e0);}}\n        if (_json_.zjdmtxzb === undefined) { throw new Error() }\n        { this.zjdmtxzb = []; for(let _ele0 of _json_.zjdmtxzb) { let _e0; _e0 = _ele0; this.zjdmtxzb.push(_e0);}}\n        if (_json_.transatk1 === undefined) { throw new Error() }\n        { this.transatk1 = []; for(let _ele0 of _json_.transatk1) { let _e0; _e0 = _ele0; this.transatk1.push(_e0);}}\n        if (_json_.shiftingatk1 === undefined) { throw new Error() }\n        { this.shiftingatk1 = []; for(let _ele0 of _json_.shiftingatk1) { let _e0; _e0 = _ele0; this.shiftingatk1.push(_e0);}}\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * type\n     */\n    readonly type: number\n    /**\n     * 碰撞范围\n     */\n    readonly body: number[]\n    /**\n     * 变现\n     */\n    readonly transSrc: string[]\n    /**\n     * 变形参数\n     */\n    readonly transExt: string[]\n    /**\n     * 火焰位置\n     */\n    readonly zjdmtxzb: number[]\n    /**\n     * transatk1\n     */\n    readonly transatk1: number[]\n    /**\n     * shiftingatk1\n     */\n    readonly shiftingatk1: number[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class MainPlaneLv {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.hp === undefined) { throw new Error() }\n        this.hp = _json_.hp\n        if (_json_.atk === undefined) { throw new Error() }\n        this.atk = _json_.atk\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 血量\n     */\n    readonly hp: number\n    /**\n     * 攻击\n     */\n    readonly atk: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机效果\n */\nexport class PlaneEffect {\n\n    constructor(_json_: any) {\n        if (_json_.effect_id === undefined) { throw new Error() }\n        this.effectId = _json_.effect_id\n    }\n\n    readonly effectId: number\n\n    resolve(tables:Tables) {\n        \n    }\n}\n\n\n\n\n\n/**\n * 升星材料\n */\nexport class PlaneMaterial {\n\n    constructor(_json_: any) {\n        if (_json_.material_id === undefined) { throw new Error() }\n        this.materialId = _json_.material_id\n        if (_json_.material_count === undefined) { throw new Error() }\n        this.materialCount = _json_.material_count\n    }\n\n    readonly materialId: number\n    readonly materialCount: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机属性\n */\nexport class PlaneProperty {\n\n    constructor(_json_: any) {\n        if (_json_.prop_type === undefined) { throw new Error() }\n        this.propType = _json_.prop_type\n        if (_json_.prop_value === undefined) { throw new Error() }\n        this.propValue = _json_.prop_value\n    }\n\n    readonly propType: res.PropName\n    readonly propValue: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 属性增幅\n */\nexport class PropInc {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.inc === undefined) { throw new Error() }\n        this.inc = _json_.inc\n    }\n\n    readonly id: res.PropName\n    /**\n     * 万分比\n     */\n    readonly inc: number\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机效果配置\n */\nexport class ResEffect {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.effect_type === undefined) { throw new Error() }\n        this.effectType = _json_.effect_type\n        if (_json_.effect_value === undefined) { throw new Error() }\n        this.effectValue = _json_.effect_value\n        if (_json_.effect_params === undefined) { throw new Error() }\n        this.effectParams = _json_.effect_params\n    }\n\n    readonly id: number\n    readonly name: string\n    readonly description: string\n    readonly effectType: number\n    readonly effectValue: number\n    readonly effectParams: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 装备\n */\nexport class ResEquip {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.quality_sub === undefined) { throw new Error() }\n        this.qualitySub = _json_.quality_sub\n        if (_json_.equip_class === undefined) { throw new Error() }\n        this.equipClass = _json_.equip_class\n        if (_json_.props === undefined) { throw new Error() }\n        { this.props = []; for(let _ele0 of _json_.props) { let _e0; _e0 = new EquipProp(_ele0); this.props.push(_e0);}}\n        if (_json_.consume_items === undefined) { throw new Error() }\n        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}\n    }\n\n    readonly id: number\n    readonly name: string\n    readonly quality: res.QualityType\n    readonly qualitySub: number\n    readonly equipClass: res.EquipClass\n    readonly props: EquipProp[]\n    readonly consumeItems: ConsumeItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        for (let _e of this.props) { _e?.resolve(tables); }\n        for (let _e of this.consumeItems) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\n/**\n * 装备位升级\n */\nexport class ResEquipUpgrade {\n\n    constructor(_json_: any) {\n        if (_json_.equip_class === undefined) { throw new Error() }\n        this.equipClass = _json_.equip_class\n        if (_json_.level_from === undefined) { throw new Error() }\n        this.levelFrom = _json_.level_from\n        if (_json_.level_to === undefined) { throw new Error() }\n        this.levelTo = _json_.level_to\n        if (_json_.prop_inc === undefined) { throw new Error() }\n        { this.propInc = []; for(let _ele0 of _json_.prop_inc) { let _e0; _e0 = new PropInc(_ele0); this.propInc.push(_e0);}}\n        if (_json_.consume_money === undefined) { throw new Error() }\n        this.consumeMoney = new ConsumeMoney(_json_.consume_money)\n        if (_json_.consume_items === undefined) { throw new Error() }\n        { this.consumeItems = []; for(let _ele0 of _json_.consume_items) { let _e0; _e0 = new ConsumeItem(_ele0); this.consumeItems.push(_e0);}}\n    }\n\n    /**\n     * 装备槽位的类型\n     */\n    readonly equipClass: res.EquipClass\n    /**\n     * 等级下限\n     */\n    readonly levelFrom: number\n    /**\n     * 等级上限\n     */\n    readonly levelTo: number\n    readonly propInc: PropInc[]\n    readonly consumeMoney: ConsumeMoney\n    readonly consumeItems: ConsumeItem[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        for (let _e of this.propInc) { _e?.resolve(tables); }\n        this.consumeMoney?.resolve(tables);\n        for (let _e of this.consumeItems) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\nexport class ResGM {\n\n    constructor(_json_: any) {\n        if (_json_.tabID === undefined) { throw new Error() }\n        this.tabID = _json_.tabID\n        if (_json_.tabName === undefined) { throw new Error() }\n        this.tabName = _json_.tabName\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.cmd === undefined) { throw new Error() }\n        this.cmd = _json_.cmd\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n    }\n\n    readonly tabID: res.GMTabID\n    readonly tabName: string\n    readonly name: string\n    readonly cmd: string\n    readonly desc: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 道具\n */\nexport class ResItem {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.quality_sub === undefined) { throw new Error() }\n        this.qualitySub = _json_.quality_sub\n        if (_json_.use_type === undefined) { throw new Error() }\n        this.useType = _json_.use_type\n        if (_json_.effect_id === undefined) { throw new Error() }\n        this.effectId = _json_.effect_id\n        if (_json_.effect_param1 === undefined) { throw new Error() }\n        this.effectParam1 = _json_.effect_param1\n        if (_json_.effect_param2 === undefined) { throw new Error() }\n        this.effectParam2 = _json_.effect_param2\n        if (_json_.max_stack_num === undefined) { throw new Error() }\n        this.maxStackNum = _json_.max_stack_num\n    }\n\n    readonly id: number\n    readonly name: string\n    readonly quality: res.QualityType\n    readonly qualitySub: number\n    readonly useType: res.ItemUseType\n    readonly effectId: res.ItemEffectType\n    readonly effectParam1: number\n    readonly effectParam2: number\n    readonly maxStackNum: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n/**\n * 战机\n */\nexport class ResPlane {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.star_level === undefined) { throw new Error() }\n        this.starLevel = _json_.star_level\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.description === undefined) { throw new Error() }\n        this.description = _json_.description\n        if (_json_.quality === undefined) { throw new Error() }\n        this.quality = _json_.quality\n        if (_json_.properties === undefined) { throw new Error() }\n        { this.properties = []; for(let _ele0 of _json_.properties) { let _e0; _e0 = new PlaneProperty(_ele0); this.properties.push(_e0);}}\n        if (_json_.effects === undefined) { throw new Error() }\n        { this.effects = []; for(let _ele0 of _json_.effects) { let _e0; _e0 = new PlaneEffect(_ele0); this.effects.push(_e0);}}\n        if (_json_.materials === undefined) { throw new Error() }\n        { this.materials = []; for(let _ele0 of _json_.materials) { let _e0; _e0 = new PlaneMaterial(_ele0); this.materials.push(_e0);}}\n    }\n\n    readonly id: number\n    readonly starLevel: number\n    readonly name: string\n    readonly description: string\n    readonly quality: res.QualityType\n    readonly properties: PlaneProperty[]\n    readonly effects: PlaneEffect[]\n    readonly materials: PlaneMaterial[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        for (let _e of this.properties) { _e?.resolve(tables); }\n        for (let _e of this.effects) { _e?.resolve(tables); }\n        for (let _e of this.materials) { _e?.resolve(tables); }\n    }\n}\n\n\n\n\n\n/**\n * 卡牌\n */\nexport class ResWeapon {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n    }\n\n    readonly id: number\n    readonly name: string\n\n    resolve(tables:Tables) {\n        \n        \n    }\n}\n\n\n\n\n\nexport class ResWhiteList {\n\n    constructor(_json_: any) {\n        if (_json_.openid === undefined) { throw new Error() }\n        this.openid = _json_.openid\n        if (_json_.password === undefined) { throw new Error() }\n        this.password = _json_.password\n        if (_json_.status === undefined) { throw new Error() }\n        this.status = _json_.status\n        if (_json_.privilege === undefined) { throw new Error() }\n        this.privilege = _json_.privilege\n    }\n\n    /**\n     * 第一行默认是主键\n     */\n    readonly openid: string\n    /**\n     * 密码的MD5\n     */\n    readonly password: string\n    /**\n     * account  status: normal/disable\n     */\n    readonly status: number\n    /**\n     * 可以访问的内容\n     */\n    readonly privilege: number\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class skill {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.name === undefined) { throw new Error() }\n        this.name = _json_.name\n        if (_json_.desc === undefined) { throw new Error() }\n        this.desc = _json_.desc\n        if (_json_.icon === undefined) { throw new Error() }\n        this.icon = _json_.icon\n        if (_json_.cd === undefined) { throw new Error() }\n        this.cd = _json_.cd\n        if (_json_.CostID === undefined) { throw new Error() }\n        this.CostID = _json_.CostID\n        if (_json_.CostNum === undefined) { throw new Error() }\n        this.CostNum = _json_.CostNum\n        if (_json_.conditionID === undefined) { throw new Error() }\n        this.conditionID = _json_.conditionID\n        if (_json_.ApplyBuffs === undefined) { throw new Error() }\n        { this.ApplyBuffs = []; for(let _ele0 of _json_.ApplyBuffs) { let _e0; _e0 = new builtin.ApplyBuff(_ele0); this.ApplyBuffs.push(_e0);}}\n    }\n\n    /**\n     * ID\n     */\n    readonly id: number\n    /**\n     * 技能名称\n     */\n    readonly name: string\n    /**\n     * 描述\n     */\n    readonly desc: string\n    /**\n     * 技能图标prefab\n     */\n    readonly icon: string\n    /**\n     * 冷却时间\n     */\n    readonly cd: number\n    /**\n     * 费用ID\n     */\n    readonly CostID: number\n    /**\n     * 费用消耗值\n     */\n    readonly CostNum: number\n    /**\n     * 条件\n     */\n    readonly conditionID: number\n    readonly ApplyBuffs: builtin.ApplyBuff[]\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Stage {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.mainStage === undefined) { throw new Error() }\n        this.mainStage = _json_.mainStage\n        if (_json_.subStage === undefined) { throw new Error() }\n        this.subStage = _json_.subStage\n        if (_json_.type === undefined) { throw new Error() }\n        this.type = _json_.type\n        if (_json_.enemyGroupID === undefined) { throw new Error() }\n        this.enemyGroupID = _json_.enemyGroupID\n        if (_json_.delay === undefined) { throw new Error() }\n        this.delay = _json_.delay\n        if (_json_.enemyNorRate === undefined) { throw new Error() }\n        this.enemyNorRate = _json_.enemyNorRate\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 关卡\n     */\n    readonly mainStage: number\n    /**\n     * 阶段\n     */\n    readonly subStage: number\n    /**\n     * 类型0:普通敌机 100：boss\n     */\n    readonly type: number\n    /**\n     * 波次id\n     */\n    readonly enemyGroupID: string\n    /**\n     * 延迟时间\n     */\n    readonly delay: number\n    /**\n     * 属性倍率（血量，攻击，碰撞攻击）\n     */\n    readonly enemyNorRate: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Task {\n\n    constructor(_json_: any) {\n        if (_json_.task_id === undefined) { throw new Error() }\n        this.taskId = _json_.task_id\n        if (_json_.group_id === undefined) { throw new Error() }\n        this.groupId = _json_.group_id\n        if (_json_.task_class === undefined) { throw new Error() }\n        this.taskClass = _json_.task_class\n        if (_json_.prev_id === undefined) { throw new Error() }\n        this.prevId = _json_.prev_id\n        if (_json_.period_type === undefined) { throw new Error() }\n        this.periodType = _json_.period_type\n        if (_json_.open_type === undefined) { throw new Error() }\n        this.openType = _json_.open_type\n        if (_json_.open_value === undefined) { throw new Error() }\n        this.openValue = _json_.open_value\n        if (_json_.goal_type === undefined) { throw new Error() }\n        this.goalType = _json_.goal_type\n        if (_json_.goal_params === undefined) { throw new Error() }\n        { this.goalParams = []; for(let _ele0 of _json_.goal_params) { let _e0; _e0 = _ele0; this.goalParams.push(_e0);}}\n        if (_json_.accumulate === undefined) { throw new Error() }\n        this.accumulate = _json_.accumulate\n        if (_json_.award_id === undefined) { throw new Error() }\n        this.awardId = _json_.award_id\n        if (_json_.orbit_id === undefined) { throw new Error() }\n        this.orbitId = _json_.orbit_id\n        if (_json_.orbit_value === undefined) { throw new Error() }\n        this.orbitValue = _json_.orbit_value\n        if (_json_.open_date === undefined) { throw new Error() }\n        this.openDate = _json_.open_date\n        if (_json_.open_time === undefined) { throw new Error() }\n        this.openTime = _json_.open_time\n        if (_json_.close_date === undefined) { throw new Error() }\n        this.closeDate = _json_.close_date\n        if (_json_.close_time === undefined) { throw new Error() }\n        this.closeTime = _json_.close_time\n    }\n\n    /**\n     * 任务 ID\n     */\n    readonly taskId: number\n    /**\n     * 任务集 ID\n     */\n    readonly groupId: number\n    /**\n     * 任务类型\n     */\n    readonly taskClass: TaskClass\n    /**\n     * 前置任务 ID\n     */\n    readonly prevId: number\n    /**\n     * 重置周期\n     */\n    readonly periodType: TaskPeriodType\n    /**\n     * 任务接取条件\n     */\n    readonly openType: TaskCondType\n    /**\n     * 条件参数\n     */\n    readonly openValue: number\n    /**\n     * 目标类型\n     */\n    readonly goalType: TaskObjectType\n    /**\n     * 目标参数1 \n     */\n    readonly goalParams: number[]\n    /**\n     * 是否累积\n     */\n    readonly accumulate: boolean\n    /**\n     * 奖励ID\n     */\n    readonly awardId: number\n    /**\n     * 奖励轨道集ID\n     */\n    readonly orbitId: number\n    /**\n     * 完成奖励值\n     */\n    readonly orbitValue: number\n    /**\n     * 开放日期(yyyymmdd)\n     */\n    readonly openDate: string\n    /**\n     * 开放时间(HHMMSS)\n     */\n    readonly openTime: string\n    /**\n     * 结束日期(yyyymmdd)\n     */\n    readonly closeDate: string\n    /**\n     * 结束时间(HHMMSS)\n     */\n    readonly closeTime: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Track {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.tpe === undefined) { throw new Error() }\n        this.tpe = _json_.tpe\n        if (_json_.value === undefined) { throw new Error() }\n        this.value = _json_.value\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 类型\n     */\n    readonly tpe: number\n    /**\n     * 值(不同的轨迹类型，数据代表的信息不一样)\n     */\n    readonly value: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Unit {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.uId === undefined) { throw new Error() }\n        this.uId = _json_.uId\n        if (_json_.im === undefined) { throw new Error() }\n        this.im = _json_.im\n        if (_json_.imp === undefined) { throw new Error() }\n        this.imp = _json_.imp\n        if (_json_.am === undefined) { throw new Error() }\n        this.am = _json_.am\n        if (_json_.dam === undefined) { throw new Error() }\n        this.dam = _json_.dam\n        if (_json_.hp === undefined) { throw new Error() }\n        this.hp = _json_.hp\n        if (_json_.pos === undefined) { throw new Error() }\n        this.pos = _json_.pos\n        if (_json_.hpp === undefined) { throw new Error() }\n        this.hpp = _json_.hpp\n        if (_json_.col === undefined) { throw new Error() }\n        this.col = _json_.col\n        if (_json_.sco === undefined) { throw new Error() }\n        this.sco = _json_.sco\n        if (_json_.hc === undefined) { throw new Error() }\n        this.hc = _json_.hc\n        if (_json_.hs === undefined) { throw new Error() }\n        this.hs = _json_.hs\n        if (_json_.bla === undefined) { throw new Error() }\n        this.bla = _json_.bla\n        if (_json_.so === undefined) { throw new Error() }\n        this.so = _json_.so\n        if (_json_.sk === undefined) { throw new Error() }\n        this.sk = _json_.sk\n        if (_json_.act === undefined) { throw new Error() }\n        this.act = _json_.act\n        if (_json_.mix === undefined) { throw new Error() }\n        this.mix = _json_.mix\n        if (_json_.turn === undefined) { throw new Error() }\n        this.turn = _json_.turn\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * uId\n     */\n    readonly uId: number\n    /**\n     * im\n     */\n    readonly im: string\n    /**\n     * imp\n     */\n    readonly imp: string\n    /**\n     * am\n     */\n    readonly am: string\n    /**\n     * dam\n     */\n    readonly dam: string\n    /**\n     * hp\n     */\n    readonly hp: number\n    /**\n     * pos\n     */\n    readonly pos: string\n    /**\n     * hpp\n     */\n    readonly hpp: string\n    /**\n     * col\n     */\n    readonly col: string\n    /**\n     * sco\n     */\n    readonly sco: number\n    /**\n     * hc\n     */\n    readonly hc: string\n    /**\n     * hs\n     */\n    readonly hs: string\n    /**\n     * bla\n     */\n    readonly bla: string\n    /**\n     * so\n     */\n    readonly so: string\n    /**\n     * sk\n     */\n    readonly sk: string\n    /**\n     * act\n     */\n    readonly act: string\n    /**\n     * mix\n     */\n    readonly mix: string\n    /**\n     * turn\n     */\n    readonly turn: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\nexport class Wave {\n\n    constructor(_json_: any) {\n        if (_json_.id === undefined) { throw new Error() }\n        this.id = _json_.id\n        if (_json_.enemyGroupID === undefined) { throw new Error() }\n        this.enemyGroupID = _json_.enemyGroupID\n        if (_json_.delay === undefined) { throw new Error() }\n        this.delay = _json_.delay\n        if (_json_.planeType === undefined) { throw new Error() }\n        this.planeType = _json_.planeType\n        if (_json_.planeId === undefined) { throw new Error() }\n        this.planeId = _json_.planeId\n        if (_json_.interval === undefined) { throw new Error() }\n        this.interval = _json_.interval\n        if (_json_.offsetPos === undefined) { throw new Error() }\n        this.offsetPos = _json_.offsetPos\n        if (_json_.num === undefined) { throw new Error() }\n        this.num = _json_.num\n        if (_json_.pos === undefined) { throw new Error() }\n        this.pos = _json_.pos\n        if (_json_.track === undefined) { throw new Error() }\n        this.track = _json_.track\n        if (_json_.trackParams === undefined) { throw new Error() }\n        this.trackParams = _json_.trackParams\n        if (_json_.rotatioSpeed === undefined) { throw new Error() }\n        this.rotatioSpeed = _json_.rotatioSpeed\n        if (_json_.FirstShootDelay === undefined) { throw new Error() }\n        this.FirstShootDelay = _json_.FirstShootDelay\n    }\n\n    /**\n     * id\n     */\n    readonly id: number\n    /**\n     * 波次 ID\n     */\n    readonly enemyGroupID: number\n    /**\n     * 延迟时间\n     */\n    readonly delay: number\n    /**\n     * 0 表示普通敌机\n     */\n    readonly planeType: number\n    /**\n     * 敌机id\n     */\n    readonly planeId: number\n    /**\n     * 生成间隔时间\n     */\n    readonly interval: number\n    /**\n     * 根据敌机数量设置偏移位置\n     */\n    readonly offsetPos: string\n    /**\n     * 生成的敌机数量\n     */\n    readonly num: number\n    /**\n     * 初始位置\n     */\n    readonly pos: string\n    /**\n     * 轨迹路径(次数,轨迹索引,;id,速度,间隔；)\n     */\n    readonly track: string\n    /**\n     * 轨迹参数\n     */\n    readonly trackParams: string\n    /**\n     * 旋转速度\n     */\n    readonly rotatioSpeed: number\n    /**\n     * 首次射击延迟\n     */\n    readonly FirstShootDelay: string\n\n    resolve(tables:Tables) {\n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n        \n    }\n}\n\n\n\n\n\n\n/**\n * GM命令表\n */\nexport class TbGM {\n    private _dataList: ResGM[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResGM\n            _v = new ResGM(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): ResGM[] { return this._dataList }\n\n    get(index: number): ResGM | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\n/**\n * 战机表\n */\nexport class TbPlane {\n    private _dataList: ResPlane[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResPlane\n            _v = new ResPlane(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): ResPlane[] { return this._dataList }\n\n    get(index: number): ResPlane | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\n/**\n * 效果表\n */\nexport class TbEffect {\n    private _dataMap: Map<number, ResEffect>\n    private _dataList: ResEffect[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEffect>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEffect\n            _v = new ResEffect(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEffect> { return this._dataMap; }\n    getDataList(): ResEffect[] { return this._dataList; }\n\n    get(key: number): ResEffect | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\n/**\n * 武器\n */\nexport class TbWeapon {\n    private _dataMap: Map<number, ResWeapon>\n    private _dataList: ResWeapon[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResWeapon>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResWeapon\n            _v = new ResWeapon(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResWeapon> { return this._dataMap; }\n    getDataList(): ResWeapon[] { return this._dataList; }\n\n    get(key: number): ResWeapon | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\n/**\n * 装备位升级\n */\nexport class TbEquipUpgrade {\n    private _dataList: ResEquipUpgrade[]\n    \n    constructor(_json_: any) {\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEquipUpgrade\n            _v = new ResEquipUpgrade(_json2_)\n            this._dataList.push(_v)\n        }\n    }\n\n    getDataList(): ResEquipUpgrade[] { return this._dataList }\n\n    get(index: number): ResEquipUpgrade | undefined { return this._dataList[index] }\n    \n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\n/**\n * 装备表\n */\nexport class TbEquip {\n    private _dataMap: Map<number, ResEquip>\n    private _dataList: ResEquip[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResEquip>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResEquip\n            _v = new ResEquip(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResEquip> { return this._dataMap; }\n    getDataList(): ResEquip[] { return this._dataList; }\n\n    get(key: number): ResEquip | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\n/**\n * 道具表\n */\nexport class TbItem {\n    private _dataMap: Map<number, ResItem>\n    private _dataList: ResItem[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, ResItem>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: ResItem\n            _v = new ResItem(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, ResItem> { return this._dataMap; }\n    getDataList(): ResItem[] { return this._dataList; }\n\n    get(key: number): ResItem | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbGlobalAttr {\n\n    private _data: GlobalAttr\n    constructor(_json_: any) {\n        if (_json_.length != 1) throw new Error('table mode=one, but size != 1')\n        this._data = new GlobalAttr(_json_[0])\n    }\n\n    getData(): GlobalAttr { return this._data; }\n\n    /**\n     * 每回合发放的金币\n     */\n    get  GoldProducion(): number { return this._data.GoldProducion; }\n    /**\n     * 体力上限值\n     */\n    get  MaxEnergy(): number { return this._data.MaxEnergy; }\n    /**\n     * 体力恢复的间隔时间\n     */\n    get  EnergyRecoverInterval(): number { return this._data.EnergyRecoverInterval; }\n    /**\n     * 体力恢复的值\n     */\n    get  EnergyRecoverValue(): number { return this._data.EnergyRecoverValue; }\n    /**\n     * 局内道具拾取距离\n     */\n    get  ItemPickUpRadius(): number { return this._data.ItemPickUpRadius; }\n    /**\n     * 受击保护\n     */\n    get  PostHitProtection(): number { return this._data.PostHitProtection; }\n\n    resolve(tables:Tables)\n    {\n        this._data.resolve(tables)\n    }\n    \n}\n\n\n\n\nexport class TbBoss {\n    private _dataMap: Map<number, Boss>\n    private _dataList: Boss[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Boss>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Boss\n            _v = new Boss(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Boss> { return this._dataMap; }\n    getDataList(): Boss[] { return this._dataList; }\n\n    get(key: number): Boss | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class Tbbuffer {\n    private _dataMap: Map<number, buffer>\n    private _dataList: buffer[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, buffer>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: buffer\n            _v = new buffer(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, buffer> { return this._dataMap; }\n    getDataList(): buffer[] { return this._dataList; }\n\n    get(key: number): buffer | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbBullet {\n    private _dataMap: Map<number, Bullet>\n    private _dataList: Bullet[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Bullet>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Bullet\n            _v = new Bullet(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Bullet> { return this._dataMap; }\n    getDataList(): Bullet[] { return this._dataList; }\n\n    get(key: number): Bullet | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbChapter {\n    private _dataMap: Map<number, Chapter>\n    private _dataList: Chapter[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Chapter>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Chapter\n            _v = new Chapter(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Chapter> { return this._dataMap; }\n    getDataList(): Chapter[] { return this._dataList; }\n\n    get(key: number): Chapter | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbEnemy {\n    private _dataMap: Map<number, Enemy>\n    private _dataList: Enemy[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Enemy>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Enemy\n            _v = new Enemy(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Enemy> { return this._dataMap; }\n    getDataList(): Enemy[] { return this._dataList; }\n\n    get(key: number): Enemy | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbEnemyUI {\n    private _dataMap: Map<number, EnemyUI>\n    private _dataList: EnemyUI[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, EnemyUI>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: EnemyUI\n            _v = new EnemyUI(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, EnemyUI> { return this._dataMap; }\n    getDataList(): EnemyUI[] { return this._dataList; }\n\n    get(key: number): EnemyUI | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbGameMap {\n    private _dataMap: Map<number, GameMap>\n    private _dataList: GameMap[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, GameMap>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: GameMap\n            _v = new GameMap(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, GameMap> { return this._dataMap; }\n    getDataList(): GameMap[] { return this._dataList; }\n\n    get(key: number): GameMap | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbGameMode {\n    private _dataMap: Map<number, GameMode>\n    private _dataList: GameMode[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, GameMode>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: GameMode\n            _v = new GameMode(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.ID, _v)\n        }\n    }\n\n    getDataMap(): Map<number, GameMode> { return this._dataMap; }\n    getDataList(): GameMode[] { return this._dataList; }\n\n    get(key: number): GameMode | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbLevel {\n    private _dataMap: Map<number, Level>\n    private _dataList: Level[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Level>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Level\n            _v = new Level(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Level> { return this._dataMap; }\n    getDataList(): Level[] { return this._dataList; }\n\n    get(key: number): Level | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbLevelGroup {\n    private _dataMap: Map<number, LevelGroup>\n    private _dataList: LevelGroup[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, LevelGroup>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: LevelGroup\n            _v = new LevelGroup(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, LevelGroup> { return this._dataMap; }\n    getDataList(): LevelGroup[] { return this._dataList; }\n\n    get(key: number): LevelGroup | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbMainPlane {\n    private _dataMap: Map<number, MainPlane>\n    private _dataList: MainPlane[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, MainPlane>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: MainPlane\n            _v = new MainPlane(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, MainPlane> { return this._dataMap; }\n    getDataList(): MainPlane[] { return this._dataList; }\n\n    get(key: number): MainPlane | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbMainPlaneLv {\n    private _dataMap: Map<number, MainPlaneLv>\n    private _dataList: MainPlaneLv[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, MainPlaneLv>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: MainPlaneLv\n            _v = new MainPlaneLv(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, MainPlaneLv> { return this._dataMap; }\n    getDataList(): MainPlaneLv[] { return this._dataList; }\n\n    get(key: number): MainPlaneLv | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class Tbskill {\n    private _dataMap: Map<number, skill>\n    private _dataList: skill[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, skill>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: skill\n            _v = new skill(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, skill> { return this._dataMap; }\n    getDataList(): skill[] { return this._dataList; }\n\n    get(key: number): skill | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbStage {\n    private _dataMap: Map<number, Stage>\n    private _dataList: Stage[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Stage>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Stage\n            _v = new Stage(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Stage> { return this._dataMap; }\n    getDataList(): Stage[] { return this._dataList; }\n\n    get(key: number): Stage | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbTask {\n    private _dataMap: Map<number, Task>\n    private _dataList: Task[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Task>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Task\n            _v = new Task(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.taskId, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Task> { return this._dataMap; }\n    getDataList(): Task[] { return this._dataList; }\n\n    get(key: number): Task | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbTrack {\n    private _dataMap: Map<number, Track>\n    private _dataList: Track[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Track>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Track\n            _v = new Track(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Track> { return this._dataMap; }\n    getDataList(): Track[] { return this._dataList; }\n\n    get(key: number): Track | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbUnit {\n    private _dataMap: Map<number, Unit>\n    private _dataList: Unit[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Unit>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Unit\n            _v = new Unit(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Unit> { return this._dataMap; }\n    getDataList(): Unit[] { return this._dataList; }\n\n    get(key: number): Unit | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\nexport class TbWave {\n    private _dataMap: Map<number, Wave>\n    private _dataList: Wave[]\n    constructor(_json_: any) {\n        this._dataMap = new Map<number, Wave>()\n        this._dataList = []\n        for(var _json2_ of _json_) {\n            let _v: Wave\n            _v = new Wave(_json2_)\n            this._dataList.push(_v)\n            this._dataMap.set(_v.id, _v)\n        }\n    }\n\n    getDataMap(): Map<number, Wave> { return this._dataMap; }\n    getDataList(): Wave[] { return this._dataList; }\n\n    get(key: number): Wave | undefined { return this._dataMap.get(key); }\n\n    resolve(tables:Tables) {\n        for(let  data of this._dataList)\n        {\n            data.resolve(tables)\n        }\n    }\n\n}\n\n\n\n\ntype JsonLoader = (file: string) => any\n\nexport class Tables {\n    private _TbGM: TbGM\n    /**\n     * GM命令表\n     */\n    get TbGM(): TbGM  { return this._TbGM;}\n    private _TbPlane: TbPlane\n    /**\n     * 战机表\n     */\n    get TbPlane(): TbPlane  { return this._TbPlane;}\n    private _TbEffect: TbEffect\n    /**\n     * 效果表\n     */\n    get TbEffect(): TbEffect  { return this._TbEffect;}\n    private _TbWeapon: TbWeapon\n    /**\n     * 武器\n     */\n    get TbWeapon(): TbWeapon  { return this._TbWeapon;}\n    private _TbEquipUpgrade: TbEquipUpgrade\n    /**\n     * 装备位升级\n     */\n    get TbEquipUpgrade(): TbEquipUpgrade  { return this._TbEquipUpgrade;}\n    private _TbEquip: TbEquip\n    /**\n     * 装备表\n     */\n    get TbEquip(): TbEquip  { return this._TbEquip;}\n    private _TbItem: TbItem\n    /**\n     * 道具表\n     */\n    get TbItem(): TbItem  { return this._TbItem;}\n    private _TbGlobalAttr: TbGlobalAttr\n    get TbGlobalAttr(): TbGlobalAttr  { return this._TbGlobalAttr;}\n    private _TbBoss: TbBoss\n    get TbBoss(): TbBoss  { return this._TbBoss;}\n    private _Tbbuffer: Tbbuffer\n    get Tbbuffer(): Tbbuffer  { return this._Tbbuffer;}\n    private _TbBullet: TbBullet\n    get TbBullet(): TbBullet  { return this._TbBullet;}\n    private _TbChapter: TbChapter\n    get TbChapter(): TbChapter  { return this._TbChapter;}\n    private _TbEnemy: TbEnemy\n    get TbEnemy(): TbEnemy  { return this._TbEnemy;}\n    private _TbEnemyUI: TbEnemyUI\n    get TbEnemyUI(): TbEnemyUI  { return this._TbEnemyUI;}\n    private _TbGameMap: TbGameMap\n    get TbGameMap(): TbGameMap  { return this._TbGameMap;}\n    private _TbGameMode: TbGameMode\n    get TbGameMode(): TbGameMode  { return this._TbGameMode;}\n    private _TbLevel: TbLevel\n    get TbLevel(): TbLevel  { return this._TbLevel;}\n    private _TbLevelGroup: TbLevelGroup\n    get TbLevelGroup(): TbLevelGroup  { return this._TbLevelGroup;}\n    private _TbMainPlane: TbMainPlane\n    get TbMainPlane(): TbMainPlane  { return this._TbMainPlane;}\n    private _TbMainPlaneLv: TbMainPlaneLv\n    get TbMainPlaneLv(): TbMainPlaneLv  { return this._TbMainPlaneLv;}\n    private _Tbskill: Tbskill\n    get Tbskill(): Tbskill  { return this._Tbskill;}\n    private _TbStage: TbStage\n    get TbStage(): TbStage  { return this._TbStage;}\n    private _TbTask: TbTask\n    get TbTask(): TbTask  { return this._TbTask;}\n    private _TbTrack: TbTrack\n    get TbTrack(): TbTrack  { return this._TbTrack;}\n    private _TbUnit: TbUnit\n    get TbUnit(): TbUnit  { return this._TbUnit;}\n    private _TbWave: TbWave\n    get TbWave(): TbWave  { return this._TbWave;}\n\n    constructor(loader: JsonLoader) {\n        this._TbGM = new TbGM(loader('tbgm'))\n        this._TbPlane = new TbPlane(loader('tbplane'))\n        this._TbEffect = new TbEffect(loader('tbeffect'))\n        this._TbWeapon = new TbWeapon(loader('tbweapon'))\n        this._TbEquipUpgrade = new TbEquipUpgrade(loader('tbequipupgrade'))\n        this._TbEquip = new TbEquip(loader('tbequip'))\n        this._TbItem = new TbItem(loader('tbitem'))\n        this._TbGlobalAttr = new TbGlobalAttr(loader('tbglobalattr'))\n        this._TbBoss = new TbBoss(loader('tbboss'))\n        this._Tbbuffer = new Tbbuffer(loader('tbbuffer'))\n        this._TbBullet = new TbBullet(loader('tbbullet'))\n        this._TbChapter = new TbChapter(loader('tbchapter'))\n        this._TbEnemy = new TbEnemy(loader('tbenemy'))\n        this._TbEnemyUI = new TbEnemyUI(loader('tbenemyui'))\n        this._TbGameMap = new TbGameMap(loader('tbgamemap'))\n        this._TbGameMode = new TbGameMode(loader('tbgamemode'))\n        this._TbLevel = new TbLevel(loader('tblevel'))\n        this._TbLevelGroup = new TbLevelGroup(loader('tblevelgroup'))\n        this._TbMainPlane = new TbMainPlane(loader('tbmainplane'))\n        this._TbMainPlaneLv = new TbMainPlaneLv(loader('tbmainplanelv'))\n        this._Tbskill = new Tbskill(loader('tbskill'))\n        this._TbStage = new TbStage(loader('tbstage'))\n        this._TbTask = new TbTask(loader('tbtask'))\n        this._TbTrack = new TbTrack(loader('tbtrack'))\n        this._TbUnit = new TbUnit(loader('tbunit'))\n        this._TbWave = new TbWave(loader('tbwave'))\n\n        this._TbGM.resolve(this)\n        this._TbPlane.resolve(this)\n        this._TbEffect.resolve(this)\n        this._TbWeapon.resolve(this)\n        this._TbEquipUpgrade.resolve(this)\n        this._TbEquip.resolve(this)\n        this._TbItem.resolve(this)\n        this._TbGlobalAttr.resolve(this)\n        this._TbBoss.resolve(this)\n        this._Tbbuffer.resolve(this)\n        this._TbBullet.resolve(this)\n        this._TbChapter.resolve(this)\n        this._TbEnemy.resolve(this)\n        this._TbEnemyUI.resolve(this)\n        this._TbGameMap.resolve(this)\n        this._TbGameMode.resolve(this)\n        this._TbLevel.resolve(this)\n        this._TbLevelGroup.resolve(this)\n        this._TbMainPlane.resolve(this)\n        this._TbMainPlaneLv.resolve(this)\n        this._Tbskill.resolve(this)\n        this._TbStage.resolve(this)\n        this._TbTask.resolve(this)\n        this._TbTrack.resolve(this)\n        this._TbUnit.resolve(this)\n        this._TbWave.resolve(this)\n    }\n}\n\n"]}