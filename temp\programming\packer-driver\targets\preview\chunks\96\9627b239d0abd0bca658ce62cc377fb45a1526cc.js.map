{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "Animation", "Label", "Node", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "ccclass", "property", "ToastUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "getUIOption", "isClickBgCloseUI", "onLoad", "finish", "console", "log", "onShow", "message", "label", "string", "info", "getComponent", "play", "on", "EventType", "FINISHED", "onAnimationFinished", "type", "state", "closeUI", "onHide", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAC9BC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;yBAGjBU,O,WADZF,OAAO,CAAC,SAAD,C,UAGHC,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACN,IAAD,C,2BANb,MACaO,OADb;AAAA;AAAA,4BACoC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAQZ,eAANC,MAAM,GAAW;AAAE,iBAAO,mBAAP;AAA6B;;AACxC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACvC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AAAEC,YAAAA,gBAAgB,EAAE;AAApB,WAAP;AACH;;AAESC,QAAAA,MAAM,GAAS,CAExB;;AAEDC,QAAAA,MAAM,GAAG;AACLC,UAAAA,OAAO,CAACC,GAAR,CAAY,QAAZ;AACH;;AAEKC,QAAAA,MAAM,CAACC,OAAD,EAAiC;AAAA;;AAAA;AACzC,YAAA,KAAI,CAACC,KAAL,CAAYC,MAAZ,GAAqBF,OAArB;;AACA,YAAA,KAAI,CAACG,IAAL,CAAWC,YAAX,CAAwB1B,SAAxB,EAAoC2B,IAApC,CAAyC,cAAzC;;AACA,YAAA,KAAI,CAACF,IAAL,CAAWC,YAAX,CAAwB1B,SAAxB,EAAoC4B,EAApC,CAAuC5B,SAAS,CAAC6B,SAAV,CAAoBC,QAA3D,EAAqE,KAAI,CAACC,mBAA1E,EAA+F,KAA/F;AAHyC;AAI5C;;AACOA,QAAAA,mBAAmB,CAACC,IAAD,EAA4BC,KAA5B,EAAuD;AAC9E;AAAA;AAAA,8BAAMC,OAAN,CAAczB,OAAd;AACH;;AACK0B,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAE9B;;AAnC+B,O;;;;;iBAGV,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Animation, Label, Node } from 'cc';\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/ui/UIMgr';\nimport { BundleName } from '../../../../Bundle';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('ToastUI')\nexport class ToastUI extends BaseUI {\n\n    @property(Label)\n    label: Label | null = null;\n\n    @property(Node)\n    info: Node | null = null;\n\n    public static getUrl(): string { return \"prefab/ui/ToastUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top }\n    public static getBundleName(): string { return BundleName.Home }\n    public static getUIOption(): UIOpt {\n        return { isClickBgCloseUI: false }\n    }\n\n    protected onLoad(): void {\n\n    }\n\n    finish() {\n        console.log(\"finish\");\n    }\n\n    async onShow(message: string): Promise<void> {\n        this.label!.string = message;\n        this.info!.getComponent(Animation)!.play('ToastUIClose');\n        this.info!.getComponent(Animation)!.on(Animation.EventType.FINISHED, this.onAnimationFinished, this);\n    }\n    private onAnimationFinished(type: Animation.EventType, state: AnimationPlayState) {\n        UIMgr.closeUI(ToastUI);\n    }\n    async onHide(): Promise<void> {\n    }\n    async onClose(): Promise<void> {\n\n    }\n\n}\n"]}