{"__type__": "cc.Json<PERSON>set", "_name": "tbbuffer", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "json": [{"id": 1, "buffType": 0, "duration": 0, "durationBonus": 0, "maxStack": 0, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 40, "target": 5, "param": []}], "conditionID": 0}, {"id": 2, "buffType": 0, "duration": 60000, "durationBonus": 0, "maxStack": 0, "refreshType": false, "cycle": 500, "cycleTimes": 0, "effects": [{"type": 41, "target": 3, "param": [10]}], "conditionID": 0}, {"id": 3, "buffType": 0, "duration": 0, "durationBonus": 0, "maxStack": 0, "refreshType": false, "cycle": 0, "cycleTimes": 0, "effects": [{"type": 40, "target": 5, "param": []}], "conditionID": 0}, {"id": 4, "buffType": 0, "duration": 60000, "durationBonus": 0, "maxStack": 0, "refreshType": false, "cycle": 500, "cycleTimes": 0, "effects": [{"type": 41, "target": 3, "param": [10]}], "conditionID": 0}]}