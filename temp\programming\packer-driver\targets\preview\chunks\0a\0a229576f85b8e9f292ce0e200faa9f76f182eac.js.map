{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/DialogueUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "Label", "Node", "Sprite", "BundleName", "EventMgr", "HomeUI", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ccclass", "property", "DialogueUI", "dialogueID", "dialogueContentList", "dialogueIndex", "currentIndex", "doing", "doingFast", "text", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "Home", "onLoad", "randomDialogues", "i", "length", "push", "startTypewriter", "btnClick", "node", "on", "onClick", "nodeRight", "active", "startTypewriterFast", "nextDialogue", "closeUI", "openUI", "nodeLeft", "dialogueContent", "string", "unschedule", "schedule", "char<PERSON>t", "stopTypewriter", "stopTypewriterFast", "onShow", "onHide", "onClose", "onDestroy", "targetOff"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;AAAMC,MAAAA,M,OAAAA,M;;AACjCC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;;;;;;;;OACpB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBZ,U;;4BAGjBa,U,WADZF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACT,IAAD,C,UAERS,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACT,IAAD,C,UAERS,QAAQ,CAACR,MAAD,C,UAERQ,QAAQ,CAACV,KAAD,C,UAERU,QAAQ,CAACV,KAAD,C,UAGRU,QAAQ,CAACX,MAAD,C,2BAnBb,MACaY,UADb;AAAA;AAAA,4BACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAqBnC;AArBmC,eAsBnCC,UAtBmC,GAsBd,CAtBc;AAAA,eAuBnCC,mBAvBmC,GAuBH,EAvBG;AAAA,eAwBnCC,aAxBmC,GAwBX,CAxBW;AAAA,eAyBnCC,YAzBmC,GAyBZ,CAzBY;AAAA,eA0BnCC,KA1BmC,GA0BlB,KA1BkB;AAAA,eA2BnCC,SA3BmC,GA2Bd,KA3Bc;AAAA,eA4BnCC,IA5BmC;AAAA;;AA8Bf,eAANC,MAAM,GAAW;AAAE,iBAAO,+BAAP;AAAyC;;AACpD,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AACtDC,QAAAA,MAAM,GAAS;AACrB,cAAMC,eAAe,GAAG,CACpB,mDADoB,EAEpB,qCAFoB,EAGpB,yCAHoB,EAIpB,+CAJoB,CAAxB;;AAMA,eAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGD,eAAe,CAACE,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;AAC7C,iBAAKb,mBAAL,CAAyBe,IAAzB,CAA8BH,eAAe,CAACC,CAAD,CAA7C;AACH;;AACD,eAAKR,IAAL,GAAY,KAAKL,mBAAL,CAAyB,KAAKC,aAA9B,CAAZ;AACA,eAAKe,eAAL;AACA,eAAKC,QAAL,CAAeC,IAAf,CAAoBC,EAApB,CAAuB,OAAvB,EAAgC,KAAKC,OAArC,EAA8C,IAA9C;AACA,eAAKC,SAAL,CAAgBC,MAAhB,GAAyB,KAAzB;AACH;;AAEKF,QAAAA,OAAO,GAAG;AAAA;;AAAA;AACZ,gBAAI,KAAI,CAAChB,SAAT,EAAoB;AAChB;AACH;;AACD,gBAAI,KAAI,CAACD,KAAT,EAAgB;AACZ,cAAA,KAAI,CAACoB,mBAAL;;AACA;AACH;;AACD,YAAA,KAAI,CAACC,YAAL;AARY;AASf;;AAEKA,QAAAA,YAAY,GAAG;AAAA;;AAAA;AACjB,YAAA,MAAI,CAACvB,aAAL;;AACA,gBAAI,MAAI,CAACA,aAAL,IAAsB,MAAI,CAACD,mBAAL,CAAyBc,MAAnD,EAA2D;AACvD,cAAA,MAAI,CAACb,aAAL,GAAqB,CAArB;AACA,cAAA,MAAI,CAACgB,QAAL,CAAeC,IAAf,CAAoBI,MAApB,GAA6B,KAA7B;AACA;AAAA;AAAA,kCAAMG,OAAN,CAAc3B,UAAd;AACA,oBAAM;AAAA;AAAA,kCAAM4B,MAAN;AAAA;AAAA,mCAAN;AACA;AACH;;AACD,gBAAI,MAAI,CAACzB,aAAL,GAAqB,CAArB,KAA2B,CAA/B,EAAkC;AAC9B,cAAA,MAAI,CAACoB,SAAL,CAAgBC,MAAhB,GAAyB,KAAzB;AACA,cAAA,MAAI,CAACK,QAAL,CAAeL,MAAf,GAAwB,IAAxB;AACH,aAHD,MAGO;AACH,cAAA,MAAI,CAACK,QAAL,CAAeL,MAAf,GAAwB,KAAxB;AACA,cAAA,MAAI,CAACD,SAAL,CAAgBC,MAAhB,GAAyB,IAAzB;AACH;;AACD,YAAA,MAAI,CAACjB,IAAL,GAAY,MAAI,CAACL,mBAAL,CAAyB,MAAI,CAACC,aAA9B,CAAZ;;AACA,YAAA,MAAI,CAACe,eAAL;AAjBiB;AAkBpB;;AAEDA,QAAAA,eAAe,GAAG;AACd,cAAI,KAAKX,IAAL,IAAa,EAAjB,EAAqB;AACrB,eAAKH,YAAL,GAAoB,CAApB;AACA,eAAK0B,eAAL,CAAsBC,MAAtB,GAA+B,EAA/B;AACA,eAAK1B,KAAL,GAAa,IAAb;AACA,eAAK2B,UAAL,CAAgB,KAAKd,eAArB;AACA,eAAKc,UAAL,CAAgB,KAAKP,mBAArB;AACA,eAAKQ,QAAL,CAAc,MAAM;AAChB,gBAAI,KAAK7B,YAAL,GAAoB,KAAKG,IAAL,CAAWS,MAAnC,EAA2C;AACvC,mBAAKc,eAAL,CAAsBC,MAAtB,IAAgC,KAAKxB,IAAL,CAAW2B,MAAX,CAAkB,KAAK9B,YAAvB,CAAhC;AACA,mBAAKA,YAAL;AACH,aAHD,MAGO;AACH,mBAAK+B,cAAL;AACA,mBAAK5B,IAAL,GAAa,EAAb;AACH;AACJ,WARD,EAQG,IARH;AASH;;AAEDkB,QAAAA,mBAAmB,GAAG;AAClB,eAAKnB,SAAL,GAAiB,IAAjB;AACA,eAAK0B,UAAL,CAAgB,KAAKd,eAArB;AACA,eAAKc,UAAL,CAAgB,KAAKP,mBAArB;AACA,eAAKQ,QAAL,CAAc,MAAM;AAChB,gBAAI,KAAK7B,YAAL,GAAoB,KAAKG,IAAL,CAAWS,MAAnC,EAA2C;AACvC,mBAAKc,eAAL,CAAsBC,MAAtB,IAAgC,KAAKxB,IAAL,CAAW2B,MAAX,CAAkB,KAAK9B,YAAvB,CAAhC;AACA,mBAAKA,YAAL;AACH,aAHD,MAGO;AACH,mBAAKgC,kBAAL;AACA,mBAAK7B,IAAL,GAAa,EAAb;AACH;AACJ,WARD,EAQG,IARH;AASH;;AAED4B,QAAAA,cAAc,GAAG;AACb,eAAKH,UAAL,CAAgB,KAAKd,eAArB;AACA,eAAKb,KAAL,GAAa,KAAb;AACH;;AAED+B,QAAAA,kBAAkB,GAAG;AACjB,eAAKJ,UAAL,CAAgB,KAAKP,mBAArB;AACA,eAAKnB,SAAL,GAAiB,KAAjB;AACH;;AAEK+B,QAAAA,MAAM,CAACpC,UAAD,EAAoC;AAAA;;AAAA;AAC5C,YAAA,MAAI,CAACA,UAAL,GAAkBA,UAAlB;AAD4C;AAE/C;;AACKqC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AACSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AApIkC,O;;;;;iBAGX,I;;;;;;;iBAEY,I;;;;;;;iBAEF,I;;;;;;;iBAGT,I;;;;;;;iBAEY,I;;;;;;;iBAEF,I;;;;;;;iBAEH,I;;;;;;;iBAGN,I", "sourcesContent": ["import { _decorator, Button, Label, Node, Sprite } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/Bundle';\r\nimport { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';\r\nimport { HomeUI } from 'db://assets/bundles/common/script/ui/home/<USER>';\r\nimport { BaseUI, UILayer, UIMgr } from 'db://assets/scripts/ui/UIMgr';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('DialogueUI')\r\nexport class DialogueUI extends BaseUI {\r\n\r\n    @property(Node)\r\n    nodeLeft: Node | null = null;\r\n    @property(Sprite)\r\n    characterImageLeft: Sprite | null = null;\r\n    @property(Label)\r\n    characterNameLeft: Label | null = null;\r\n\r\n    @property(Node)\r\n    nodeRight: Node | null = null;\r\n    @property(Sprite)\r\n    characterImageRight: Sprite | null = null;\r\n    @property(Label)\r\n    characterNameRight: Label | null = null;\r\n    @property(Label)\r\n    dialogueContent: Label | null = null;\r\n\r\n    @property(Button)\r\n    btnClick: Button | null = null;\r\n\r\n    //data\r\n    dialogueID: number = 0;\r\n    dialogueContentList: string[] = [];\r\n    dialogueIndex: number = 0;\r\n    currentIndex: number = 0;\r\n    doing: boolean = false;\r\n    doingFast: boolean = false;\r\n    text: string | undefined;\r\n\r\n    public static getUrl(): string { return \"prefab/ui/dialogue/DialogueUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.Home }\r\n    protected onLoad(): void {\r\n        const randomDialogues = [\r\n            \"这组坐标指向的星球不该存在大气层。工程师敲着全息屏，眉头紧锁，但探测器却传回了植物光合作用的光谱。\",\r\n            \"会不会是仪器故障？船长调出航行日志，毕竟上次校准已经是三个月前的事了。\",\r\n            \"不，数据太规律了。科学家放大图像，那些绿色斑块每天固定时间收缩扩张，像在呼吸。\",\r\n            \"立刻准备小型登陆舱。船长眼神锐利起来，如果真有生命，我们可能是第一批发现者——或者入侵者。\"\r\n        ];\r\n        for (let i = 0; i < randomDialogues.length; i++) {\r\n            this.dialogueContentList.push(randomDialogues[i]);\r\n        }\r\n        this.text = this.dialogueContentList[this.dialogueIndex];\r\n        this.startTypewriter();\r\n        this.btnClick!.node.on('click', this.onClick, this);\r\n        this.nodeRight!.active = false;\r\n    }\r\n\r\n    async onClick() {\r\n        if (this.doingFast) {\r\n            return\r\n        }\r\n        if (this.doing) {\r\n            this.startTypewriterFast();\r\n            return;\r\n        }\r\n        this.nextDialogue();\r\n    }\r\n\r\n    async nextDialogue() {\r\n        this.dialogueIndex++;\r\n        if (this.dialogueIndex >= this.dialogueContentList.length) {\r\n            this.dialogueIndex = 0;\r\n            this.btnClick!.node.active = false;\r\n            UIMgr.closeUI(DialogueUI);\r\n            await UIMgr.openUI(HomeUI);\r\n            return;\r\n        }\r\n        if (this.dialogueIndex % 2 === 0) {\r\n            this.nodeRight!.active = false;\r\n            this.nodeLeft!.active = true;\r\n        } else {\r\n            this.nodeLeft!.active = false;\r\n            this.nodeRight!.active = true;\r\n        }\r\n        this.text = this.dialogueContentList[this.dialogueIndex];\r\n        this.startTypewriter();\r\n    }\r\n\r\n    startTypewriter() {\r\n        if (this.text == \"\") return;\r\n        this.currentIndex = 0;\r\n        this.dialogueContent!.string = \"\";\r\n        this.doing = true;\r\n        this.unschedule(this.startTypewriter);\r\n        this.unschedule(this.startTypewriterFast);\r\n        this.schedule(() => {\r\n            if (this.currentIndex < this.text!.length) {\r\n                this.dialogueContent!.string += this.text!.charAt(this.currentIndex);\r\n                this.currentIndex++;\r\n            } else {\r\n                this.stopTypewriter();\r\n                this.text! = \"\"\r\n            }\r\n        }, 0.05);\r\n    }\r\n\r\n    startTypewriterFast() {\r\n        this.doingFast = true;\r\n        this.unschedule(this.startTypewriter);\r\n        this.unschedule(this.startTypewriterFast);\r\n        this.schedule(() => {\r\n            if (this.currentIndex < this.text!.length) {\r\n                this.dialogueContent!.string += this.text!.charAt(this.currentIndex);\r\n                this.currentIndex++;\r\n            } else {\r\n                this.stopTypewriterFast();\r\n                this.text! = \"\"\r\n            }\r\n        }, 0.01);\r\n    }\r\n\r\n    stopTypewriter() {\r\n        this.unschedule(this.startTypewriter);\r\n        this.doing = false;\r\n    }\r\n\r\n    stopTypewriterFast() {\r\n        this.unschedule(this.startTypewriterFast);\r\n        this.doingFast = false;\r\n    }\r\n\r\n    async onShow(dialogueID: number): Promise<void> {\r\n        this.dialogueID = dialogueID;\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n        EventMgr.targetOff(this);\r\n    }\r\n}"]}