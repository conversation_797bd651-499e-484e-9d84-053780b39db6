{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/Bundle.ts"], "names": ["IBundleEntry", "initBundle", "bundleName", "Promise", "resolve", "reject", "bundleBaseUrl", "assetManager", "loadBundle", "err", "bundle", "load", "entry", "node", "instantiate", "entryScript", "getComponent", "initEntry", "then", "catch", "Component", "BundleName"], "mappings": ";;;wGAiBsBA,Y;;AAIf,WAASC,UAAT,CAAoBC,UAApB,EAAuD;AAC1D,WAAO,IAAIC,OAAJ,CAAY,CAACC,OAAD,EAAUC,MAAV,KAAqB;AACpC,UAAMC,aAAa,GAAG,aAAaJ,UAAnC;AACAK,MAAAA,YAAY,CAACC,UAAb,CAAwBF,aAAxB,EAAuC,CAACG,GAAD,EAAMC,MAAN,KAAsC;AACzE,YAAID,GAAJ,EAAS;AACLJ,UAAAA,MAAM,CAACI,GAAD,CAAN;AACA;AACH;;AACDC,QAAAA,MAAM,CAACC,IAAP,CAAY,cAAZ,EAA4B,CAACF,GAAD,EAAMG,KAAN,KAAwB;AAChD,cAAIH,GAAJ,EAAS;AACLJ,YAAAA,MAAM,CAACI,GAAD,CAAN;AACA;AACH;;AACD,cAAMI,IAAI,GAAGC,WAAW,CAACF,KAAD,CAAxB;AACA,cAAIG,WAAW,GAAGF,IAAI,CAACG,YAAL,CAAkBhB,YAAlB,CAAlB;AACAe,UAAAA,WAAW,CAACE,SAAZ,GAAwBC,IAAxB,CAA6B,MAAM;AAC/B;AACAd,YAAAA,OAAO;AACV,WAHD,EAGGe,KAHH,CAGSV,GAAG,IAAI;AACZJ,YAAAA,MAAM,CAACI,GAAD,CAAN;AACH,WALD;AAMH,SAbD;AAcH,OAnBD;AAoBH,KAtBM,CAAP;AAuBH;;;;gBAxBeR;;;;;;;;AArBPM,MAAAA,Y,OAAAA,Y;AAA4Ba,MAAAA,S,OAAAA,S;AAAWN,MAAAA,W,OAAAA,W;;;;;;;;;4BAEpCO,U,0BAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;AAAAA,QAAAA,U;eAAAA,U;;;8BAeUrB,Y,GAAf,MAAeA,YAAf,SAAoCoB,SAApC,CAA8C,E", "sourcesContent": ["import { asset<PERSON>anager, AssetManager, Component, instantiate, Prefab } from \"cc\";\n\nexport enum BundleName {\n    //一级包\n    Common = \"common\",\n    Gm = \"gm\",\n    Home = \"home\",\n\n    //二级包\n    HomePlane = \"home_plane\",\n    HomeTalent = \"home_talent\",\n    HomeShop = \"home_shop\",\n    HomeSkyIsland = \"home_skyisland\",\n    HomeStory = \"home_story\",\n    HomeTask = \"home_task\",\n}\n\nexport abstract class IBundleEntry extends Component {\n    public abstract initEntry(...args: any[]): Promise<void>;\n}\n\nexport function initBundle(bundleName: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n        const bundleBaseUrl = \"bundles/\" + bundleName;\n        assetManager.loadBundle(bundleBaseUrl, (err, bundle: AssetManager.Bundle) => {\n            if (err) {\n                reject(err);\n                return;\n            }\n            bundle.load(\"prefab/Entry\", (err, entry: Prefab) => {\n                if (err) {\n                    reject(err);\n                    return;\n                }\n                const node = instantiate(entry)\n                let entryScript = node.getComponent(IBundleEntry) as IBundleEntry\n                entryScript.initEntry().then(() => {\n                    //node.destroy();\n                    resolve()\n                }).catch(err => {\n                    reject(err)\n                })\n            })\n        });\n    })\n}"]}