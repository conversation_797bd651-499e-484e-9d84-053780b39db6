import { _decorator, Component, EventTouch, Node, Sprite, SpriteFrame } from 'cc';
import { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';

import { PlaneUIEvent } from 'db://assets/bundles/common/script/event/PlaneUIEvent';
import { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';
import { TabStatus } from '../../PlaneTypes';
const { ccclass, property } = _decorator;

@ccclass('Tabs')
export class Tabs extends Component {
    @property(ButtonPlus)
    tabBagBtn: ButtonPlus = null!;
    @property(ButtonPlus)
    tabMergeBtn: ButtonPlus = null!;

    @property(SpriteFrame)
    pressSpriteFrame: SpriteFrame = null!;
    @property(SpriteFrame)
    releaseSpriteFrame: SpriteFrame = null!;

    private _tabStatus: TabStatus = TabStatus.None;

    protected onLoad(): void {
        this.tabBagBtn.addClick(this.onClick, this);
        this.tabMergeBtn.addClick(this.onClick, this);
    }

    init() {
        this.setTabStatus(this.tabBagBtn.node);
    }

    private onClick(event: EventTouch): void {
        const btnNode = event.target as Node;
        this.setTabStatus(btnNode);
    }

    private setTabStatus(btnNode: Node) {
        switch (btnNode) {
            case this.tabBagBtn.node:
                this._tabStatus = TabStatus.Bag;
                this.tabBagBtn.getComponent(Sprite)!.spriteFrame = this.pressSpriteFrame;
                this.tabMergeBtn.getComponent(Sprite)!.spriteFrame = this.releaseSpriteFrame;
                break;
            case this.tabMergeBtn.node:
                this._tabStatus = TabStatus.Merge;
                this.tabMergeBtn.getComponent(Sprite)!.spriteFrame = this.pressSpriteFrame;
                this.tabBagBtn.getComponent(Sprite)!.spriteFrame = this.releaseSpriteFrame;
                break;
            default:
                //logError("PlaneUI", `Tabs setTabStatus error ${btnNode.name}`)
                return;
        }
        EventMgr.emit(PlaneUIEvent.TabChange, this._tabStatus);
    }
}