import { _decorator, misc, Component, Node, Sprite, Color, CCString } from 'cc';
import { EDITOR } from 'cc/env';
import { BulletData } from '../data/bullet/BulletData';
import { ObjectPool } from './ObjectPool';
import { Movable, eSpriteDefaultFacing } from '../move/Movable';
import { BulletSystem } from './BulletSystem';
import { EventGroup, EventGroupContext } from './EventGroup';
import { Property, PropertyContainer } from './PropertyContainer';
import { Emitter } from './Emitter';
import { Bullet as BulletConfig} from 'db://assets/scripts/AutoGen/Luban/schema';
import { MyApp } from 'db://assets/scripts/MyApp';
import FCollider, { ColliderGroupType } from 'db://assets/scripts/Game/collider-system/FCollider';
import FBoxCollider from 'db://assets/scripts/Game/collider-system/FBoxCollider';
import Entity from 'db://assets/scripts/Game/ui/base/Entity';
import { eEntityTag } from 'db://assets/scripts/Game/ui/base/Entity';

const { ccclass, property, executeInEditMode } = _decorator;

export class BulletProperty extends PropertyContainer<number> {
    public duration!: Property<number>;                // 子弹持续时间(超出后销毁回收)
    public delayDestroy!: Property<number>;            // 延迟销毁时间

    public attack!: Property<number>;                  // 子弹伤害
    public speed!: Property<number>;                   // 子弹速度
    public speedAngle!: Property<number>;              // 子弹速度角度
    public acceleration!: Property<number>;            // 子弹加速度
    public accelerationAngle!: Property<number>;       // 子弹加速度角度
    public scale!: Property<number>;                   // 子弹缩放
    public color!: Property<Color>;                    // 子弹颜色
    public defaultFacing!: Property<eSpriteDefaultFacing>;          // 子弹初始朝向

    public isDestroyOutScreen!: Property<boolean>;     // 是否超出屏幕销毁
    public isDestructive!: Property<boolean>;          // 是否可被破坏
    public isDestructiveOnHit!: Property<boolean>;     // 命中时是否被销毁
    public isFacingMoveDir!: Property<boolean>;        // 是否面向移动方向
    public isTrackingTarget!: Property<boolean>;       // 是否追踪目标

    constructor() {
        super();
        this.duration = this.addProperty(0, 6000);
        this.delayDestroy = this.addProperty(1, 0);
        this.attack = this.addProperty(2, 1);
        this.speed = this.addProperty(3, 600);
        this.speedAngle = this.addProperty(4, 0);
        this.acceleration = this.addProperty(5, 0);
        this.accelerationAngle = this.addProperty(6, 0);
        this.scale = this.addProperty(7, 1);
        this.color = this.addProperty(8, Color.WHITE);
        this.defaultFacing = this.addProperty<eSpriteDefaultFacing>(9, eSpriteDefaultFacing.Up);
        this.isDestroyOutScreen = this.addProperty(10, true);
        this.isDestructive = this.addProperty(11, false);
        this.isDestructiveOnHit = this.addProperty(12, false);
        this.isFacingMoveDir = this.addProperty(13, false);
        this.isTrackingTarget = this.addProperty(14, false);
    }

    public resetFromData(data: BulletData) {
        this.duration.value = data.duration.eval(); 
        this.delayDestroy.value = data.delayDestroy.eval(); 
        this.speed.value = data.speed.eval(); 
        // this.speedAngle.value = data.speedAngle.eval(); 
        this.acceleration.value = data.acceleration.eval(); 
        this.accelerationAngle.value = data.accelerationAngle.eval(); 
        this.scale.value = data.scale.eval(); 
        // this.color.value = data.color.eval(); 
        this.isDestroyOutScreen.value = data.isDestroyOutScreen; 
        this.isDestructive.value = data.isDestructive; 
        this.isDestructiveOnHit.value = data.isDestructiveOnHit; 
        this.isFacingMoveDir.value = data.isFacingMoveDir; 
        this.isTrackingTarget.value = data.isTrackingTarget;
    }

    public copyFrom(other: BulletProperty) {
        this.forEachProperty((k, prop) => {
            prop.value = other.getPropertyValue(k)!;
        });
    }

    public clear() {
        // Clear all listeners
        this.forEachProperty((k, prop) => prop.clear());
    }
}

// 子弹 Bullet
// 如何集成到项目里? 
@ccclass('Bullet')
@executeInEditMode(true)
export class Bullet extends Entity {
    @property({type: Movable, displayName: "移动组件"})
    public mover!: Movable;

    @property({type: Sprite, displayName: "子弹精灵"})
    public bulletSprite: Sprite|null = null;

    @property({type: FCollider, displayName: '碰撞组件'})
    public collider: FCollider | null = null;
    
    public isAlive: boolean = false;
    public elapsedTime: number = 0;         // 子弹存活时间
    public emitter!: Emitter;
    public bulletData!: BulletData;
    // 以下属性重新定义一遍, 作为可修改的属性, 部分定义在movable里
    public prop: BulletProperty = new BulletProperty();
    public eventGroups: EventGroup[] = [];

    protected m_config: BulletConfig | null = null;

    onLoad(): void {
        if (!this.mover) {
            this.mover = this.getComponent(Movable)?.addComponent(Movable)!;
        }
        this.mover.onBecomeInvisibleCallback = () => {
            if (this.prop.isDestroyOutScreen.value) {
                BulletSystem.onDestroyBullet(this);
            }
        };
        if (!this.collider) {
            let boxCollider = this.addComponent(FBoxCollider);
            this.collider = boxCollider;
        }
 
        this.prop.defaultFacing.value = this.mover.defaultFacing;
        // listen to property changes
        this.prop.isFacingMoveDir.on((value) => {
            this.mover.isFacingMoveDir = value;
        });
        this.prop.isTrackingTarget.on((value) => {
            this.mover.isTrackingTarget = value;
        });
        this.prop.speed.on((value) => {
            this.mover.speed = value;
        });
        this.prop.speedAngle.on((value) => {
            this.mover.speedAngle = value;
        });
        this.prop.acceleration.on((value) => {
            this.mover.acceleration = value;
        });
        this.prop.accelerationAngle.on((value) => {
            this.mover.accelerationAngle = value;
        });
        this.prop.scale.on((value) => {
            this.node.setScale(value, value, value);
        });
        this.prop.color.on((value) => {
            if (this.bulletSprite) {
                this.bulletSprite.color = value;
            }
        });
    }

    public onCreate(emitter: Emitter, bulletID: number): void {
        this.isAlive = true;
        this.elapsedTime = 0;
        this.emitter = emitter;
        this.bulletData = emitter.bulletData!;

        // TODO: 创建entity的时候，设置正确的tag.
        const ent = emitter.getEntity();
        if (ent)
        {
            const isShootFromEnemy = ent.hasTag(eEntityTag.Enemy) ? true : false;
            this.collider!.initBaseData(ent);
            this.collider!.groupType = isShootFromEnemy ? ColliderGroupType.BULLET_ENEMY : ColliderGroupType.BULLET_SELF;
            this.collider!.isEnable = true;
            this.addTag(isShootFromEnemy ? eEntityTag.EnemyBullet : eEntityTag.PlayerBullet);
        }

        this.resetProperties();
    }

    protected destroySelf() {
        if (!this.node || !this.node.isValid) return;
        
        this.prop.clear();

        if (EDITOR) {
            this.node.destroy();
        } else {
            ObjectPool.returnNode(this.node);
        }
    }

    public resetProperties(): void {
        if (!this.emitter) return;

        this.prop.copyFrom(this.emitter.bulletProp);
        this.prop.notifyAll(true);
    }

    public resetEventGroups(): void {
        // create event groups here
        if (this.bulletData && this.bulletData.eventGroupData.length > 0) {
            let ctx = new EventGroupContext();
            ctx.bullet = this;
            ctx.emitter = this.emitter;
            for (const eventName of this.bulletData.eventGroupData) {
                BulletSystem.createBulletEventGroup(ctx, eventName);
            }
        }
    }

    public tick(dt:number) : void {
        if (!this.isAlive) return;

        this.elapsedTime += dt;
        if (this.elapsedTime > this.prop.duration.value) {
            BulletSystem.onDestroyBullet(this);
            return;
        }
        
        // 毫秒 -> 秒
        this.mover?.tick(dt / 1000);
        this.prop.notifyAll();
    }

    public willDestroy(): void {
        this.isAlive = false;
        if (this.eventGroups && this.eventGroups.length > 0)
        {
            this.eventGroups.forEach(group => group.stop()); // stop all event groups before destroying the bullet itself.
            this.eventGroups = []; // clear the event groups array
        }

        this.collider!.isEnable = false;
        this.removeAllComp();
        this.clearTags();
        if (this.prop.delayDestroy && this.prop.delayDestroy.value > 0) {
            this.scheduleOnce(() => {
                this.destroySelf();
            }, this.prop.delayDestroy.value);
        } else {
            this.destroySelf();
        }
    }

    onCollide(collider: FCollider) {
        this.remove();
    }

    public remove() {
        BulletSystem.onDestroyBullet(this);
    }
}
