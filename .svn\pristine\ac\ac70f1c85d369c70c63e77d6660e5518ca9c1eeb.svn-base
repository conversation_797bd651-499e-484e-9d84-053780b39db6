import { _decorator, misc, size, Component, Enum, Vec2, Vec3, Node, UITransform } from 'cc';
const { degreesToRadians, radiansToDegrees } = misc;
const { ccclass, property, executeInEditMode } = _decorator;
import { IMovable } from './IMovable';
import { BulletSystem } from '../bullet/BulletSystem';
import FBoxCollider from 'db://assets/scripts/Game/collider-system/FBoxCollider';
import FCollider, { ColliderGroupType } from 'db://assets/scripts/Game/collider-system/FCollider';
import Entity from 'db://assets/scripts/Game/ui/base/Entity';

export enum eSpriteDefaultFacing {
    Right = 0,    // →
    Up = -90,     // ↑
    Down = 90,    // ↓
    Left = 180    // ←
}

@ccclass('Movable')
@executeInEditMode
export class Movable extends Component implements IMovable {

    @property({type: Enum(eSpriteDefaultFacing), displayName: '图片默认朝向'})
    public defaultFacing: eSpriteDefaultFacing = eSpriteDefaultFacing.Up;
    @property({type: FCollider, displayName: '碰撞组件'})
    public collider: FCollider | null = null;

    public isFacingMoveDir: boolean = false;      // 是否朝向行进方向
    public isTrackingTarget: boolean = false;     // 是否正在追踪目标
    public speed: number = 1;                     // 速度
    public speedAngle: number = 0;                // 速度方向 (用角度表示)
    public turnSpeed: number = 60;                // 转向速度（仅用在追踪目标时）
    public acceleration: number = 0;              // 加速度
    public accelerationAngle: number = 0;         // 加速度方向 (用角度表示)

    // TODO: 
    public tiltSpeed: number = 0;                 // 偏移速度
    public tiltOffset: number = 0;                // 偏移距离

    public target: Node | null = null;            // 追踪的目标节点
    public arrivalDistance: number = 10;          // 到达目标的距离

    private _selfSize: Vec2 = new Vec2();
    private _position: Vec3 = new Vec3();

    private _isVisible: boolean = true;           // 是否可见
    public get isVisible() { return this._isVisible; }

    // Callbacks:
    public onBecomeVisibleCallback: Function | null = null;
    public onBecomeInvisibleCallback: Function | null = null;
    public onCollideCallback: Function | null = null;

    onLoad() {
        if (!this.collider) {
            let boxCollider = this.addComponent(FBoxCollider);
            this.collider = boxCollider;
        }
        const uiTransform = this.node.getComponent(UITransform);
        const self_size = uiTransform ? uiTransform.contentSize : {width: 0, height: 0};
        this._selfSize.set(self_size.width / 2, self_size.height / 2);
    }

    init(ent: Entity, isEnemy: boolean) {
        this.collider = this.getComponent(FBoxCollider);
        
        this.collider!.initBaseData(ent);
        this.collider!.groupType = isEnemy ? ColliderGroupType.BULLET_ENEMY : ColliderGroupType.BULLET_SELF;
        this.collider!.groupType = ColliderGroupType.BULLET_SELF;
        this.collider!.isEnable = true;
    }

    onCollide(collider: FCollider) {
        if (this.onCollideCallback) {
            this.onCollideCallback(collider);
        }
    }

    public tick(dt: number): void {
        // 根据移动属性更新位置
        this.node.getPosition(this._position);
        
        // Convert speed and angle to velocity vector
        let velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));
        let velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));

        if (this.isTrackingTarget && this.target) {
            const targetPos = this.target.getPosition();
            const currentPos = this.node.getPosition();
            
            // Calculate direction to target
            const directionX = targetPos.x - currentPos.x;
            const directionY = targetPos.y - currentPos.y;
            const distance = Math.sqrt(directionX * directionX + directionY * directionY);
            
            if (distance > 0) {
                // Calculate desired angle to target
                const desiredAngle = radiansToDegrees(Math.atan2(directionY, directionX));
                
                // Smoothly adjust speedAngle toward target
                const angleDiff = desiredAngle - this.speedAngle;
                // Normalize angle difference to [-180, 180] range
                const normalizedAngleDiff = ((angleDiff + 180) % 360) - 180;
                
                // Apply tracking adjustment (you can add a trackingStrength property to control this)
                const trackingStrength = 1.0; // Can be made configurable
                const maxTurnRate = this.turnSpeed; // degrees per second - can be made configurable
                const turnAmount = Math.min(Math.abs(normalizedAngleDiff), maxTurnRate * dt) * Math.sign(normalizedAngleDiff);
                
                this.speedAngle += turnAmount * trackingStrength;
                
                // Recalculate velocity with new angle
                velocityX = this.speed * Math.cos(degreesToRadians(this.speedAngle));
                velocityY = this.speed * Math.sin(degreesToRadians(this.speedAngle));
            }
        }

        // Convert acceleration and angle to acceleration vector
        const accelerationX = this.acceleration * Math.cos(degreesToRadians(this.accelerationAngle));
        const accelerationY = this.acceleration * Math.sin(degreesToRadians(this.accelerationAngle));

        // Update velocity vector: v = v + a * dt
        const newVelocityX = velocityX + accelerationX * dt;
        const newVelocityY = velocityY + accelerationY * dt;

        // Convert back to speed and angle
        this.speed = Math.sqrt(newVelocityX * newVelocityX + newVelocityY * newVelocityY);
        this.speedAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));

        // Update position: p = p + v * dt
        if (newVelocityX !== 0 || newVelocityY !== 0) {
            this._position.x += newVelocityX * dt;
            this._position.y += newVelocityY * dt;
            this.node.setPosition(this._position);    
            // this.checkVisibility();
        }
        
        if (this.isFacingMoveDir && this.speed > 0) {
            const movementAngle = radiansToDegrees(Math.atan2(newVelocityY, newVelocityX));
            const finalAngle = movementAngle + this.defaultFacing;
            this.node.setRotationFromEuler(0, 0, finalAngle);
        }
    }

    public checkVisibility(): void {
        // 这里目前的检查逻辑没有考虑旋转和缩放
        // 正常来说需要判定world corners，如果四个角有一个在屏幕内，就认为是可见的
        const visibleSize = BulletSystem.worldBounds;
        const isVisible = (this._position.x + this._selfSize.x) >= visibleSize.xMin &&
                          (this._position.x - this._selfSize.x) <= visibleSize.xMax &&
                          (this._position.y - this._selfSize.y) >= visibleSize.yMin && 
                          (this._position.y + this._selfSize.y) <= visibleSize.yMax;

        this.setVisible(isVisible);
    }

    public setVisible(visible: boolean) {
        if (this._isVisible === visible) return;

        this._isVisible = visible;
        if (visible && this.onBecomeVisibleCallback) {
            this.onBecomeVisibleCallback();
        } else if (!visible && this.onBecomeInvisibleCallback) {
            this.onBecomeInvisibleCallback();
        }
    }

    /**
     * Set the target to track
     */
    public setTarget(target: Node | null): void {
        this.target = target;
        this.isTrackingTarget = target !== null;
    }
}