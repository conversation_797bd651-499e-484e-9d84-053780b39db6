{"version": 3, "sources": ["cce:/internal/x/prerequisite-imports"], "names": ["requests", "request", "_err"], "mappings": ";;;;;;AACA;AAEA,YAAM,CAAC,YAAY;AACf,cAAMA,QAAQ,GAAG,CAAC,uCAAD,EAA4J,uCAA5J,EAA4T,uCAA5T,EAAge,uCAAhe,EAAioB,uCAAjoB,EAA4xB,uCAA5xB,EAAg7B,uCAAh7B,EAAm/B,uCAAn/B,EAAykC,uCAAzkC,EAAwqC,uCAAxqC,EAAmwC,wCAAnwC,EAA01C,wCAA11C,EAA47C,wCAA57C,EAAyhD,wCAAzhD,EAAonD,wCAApnD,EAAstD,wCAAttD,EAAszD,wCAAtzD,EAAi5D,wCAAj5D,EAAq/D,wCAAr/D,EAA0kE,wCAA1kE,EAAmqE,wCAAnqE,EAAwvE,wCAAxvE,EAA41E,wCAA51E,EAA27E,wCAA37E,EAAqhF,wCAArhF,EAAknF,wCAAlnF,EAA8sF,wCAA9sF,EAA2yF,wCAA3yF,EAA83F,wCAA93F,EAAk+F,wCAAl+F,EAAqlG,wCAArlG,EAA4sG,wCAA5sG,EAA6zG,wCAA7zG,EAA86G,wCAA96G,EAA+hH,wCAA/hH,EAAwoH,wCAAxoH,EAAqvH,wCAArvH,EAAi1H,wCAAj1H,EAA46H,wCAA56H,EAAqgI,wCAArgI,EAAmmI,wCAAnmI,EAA6rI,wCAA7rI,EAAuxI,wCAAvxI,EAA+2I,wCAA/2I,EAAg9I,wCAAh9I,EAAsjJ,wCAAtjJ,EAA8pJ,wCAA9pJ,EAA8vJ,wCAA9vJ,EAAm2J,wCAAn2J,EAAy8J,wCAAz8J,EAA+iK,wCAA/iK,EAAypK,wCAAzpK,EAA2vK,wCAA3vK,EAA61K,wCAA71K,EAA27K,wCAA37K,EAAgiL,wCAAhiL,EAAioL,wCAAjoL,EAAmuL,wCAAnuL,EAA6zL,wCAA7zL,EAAq6L,wCAAr6L,EAAygM,wCAAzgM,EAAumM,wCAAvmM,EAAksM,wCAAlsM,EAAkzM,wCAAlzM,EAAk6M,wCAAl6M,EAA2hN,wCAA3hN,EAAwoN,wCAAxoN,EAA6vN,wCAA7vN,EAAg3N,wCAAh3N,EAAy8N,wCAAz8N,EAA4iO,wCAA5iO,EAAyoO,wCAAzoO,EAA2uO,wCAA3uO,EAAs0O,wCAAt0O,EAAm6O,wCAAn6O,EAA4/O,wCAA5/O,EAAumP,wCAAvmP,EAAqrP,wCAArrP,EAAywP,wCAAzwP,EAAu1P,wCAAv1P,EAAw6P,wCAAx6P,EAAu/P,wCAAv/P,EAAqkQ,wCAArkQ,EAAopQ,wCAAppQ,EAAiuQ,wCAAjuQ,EAAozQ,wCAApzQ,EAA04Q,wCAA14Q,EAA69Q,wCAA79Q,EAAijR,wCAAjjR,EAAqoR,wCAAroR,EAAotR,wCAAptR,EAA0yR,wCAA1yR,EAA63R,wCAA73R,EAAo8R,wCAAp8R,EAAmhS,wCAAnhS,EAAimS,wCAAjmS,EAAkrS,wCAAlrS,EAAkwS,wCAAlwS,EAA20S,wCAA30S,EAAq5S,wCAAr5S,EAA89S,yCAA99S,EAA6iT,yCAA7iT,EAAkoT,yCAAloT,EAAitT,yCAAjtT,EAAiyT,yCAAjyT,EAAo3T,yCAAp3T,EAAw8T,yCAAx8T,EAA2hU,yCAA3hU,EAAqnU,yCAArnU,EAA0sU,yCAA1sU,EAA6yU,yCAA7yU,EAAi5U,yCAAj5U,EAA8+U,yCAA9+U,EAAulV,yCAAvlV,EAAisV,yCAAjsV,EAAoyV,yCAApyV,EAAk4V,yCAAl4V,EAAm+V,yCAAn+V,EAA8jW,yCAA9jW,EAAgqW,yCAAhqW,EAAkwW,yCAAlwW,EAAg2W,yCAAh2W,EAA07W,yCAA17W,EAA2gX,yCAA3gX,EAA2lX,yCAA3lX,EAAmrX,yCAAnrX,EAAkwX,yCAAlwX,EAAw1X,yCAAx1X,EAAw6X,yCAAx6X,EAAw/X,yCAAx/X,EAA0kY,yCAA1kY,EAAmqY,yCAAnqY,EAAqvY,yCAArvY,EAAq0Y,yCAAr0Y,EAAq5Y,yCAAr5Y,EAAo+Y,yCAAp+Y,EAA4jZ,yCAA5jZ,EAAqpZ,yCAArpZ,EAAkvZ,yCAAlvZ,EAAk1Z,yCAAl1Z,EAA86Z,yCAA96Z,EAA2ga,yCAA3ga,EAA4la,yCAA5la,EAAkra,yCAAlra,EAAywa,yCAAzwa,EAA81a,yCAA91a,EAAq7a,yCAAr7a,EAA2gb,yCAA3gb,EAAomb,yCAApmb,EAA8rb,yCAA9rb,EAAsxb,yCAAtxb,EAA+2b,yCAA/2b,EAA08b,yCAA18b,EAAqic,yCAAric,EAA+nc,yCAA/nc,EAAqtc,yCAArtc,EAA2yc,yCAA3yc,EAAg4c,yCAAh4c,EAA+8c,yCAA/8c,EAA6hd,yCAA7hd,EAAsnd,yCAAtnd,EAAusd,yCAAvsd,EAAyxd,yCAAzxd,EAA62d,yCAA72d,EAA67d,yCAA77d,EAAohe,yCAAphe,EAAsme,yCAAtme,EAA2re,yCAA3re,EAAmxe,yCAAnxe,EAA02e,yCAA12e,EAA47e,yCAA57e,EAAkhf,yCAAlhf,EAAwmf,yCAAxmf,EAAmsf,yCAAnsf,EAAsxf,yCAAtxf,EAA02f,yCAA12f,EAAi8f,yCAAj8f,EAAqhgB,yCAArhgB,EAA0mgB,yCAA1mgB,EAA+rgB,yCAA/rgB,EAAmxgB,yCAAnxgB,EAAu2gB,yCAAv2gB,EAAg8gB,yCAAh8gB,EAA2hhB,yCAA3hhB,EAA0nhB,yCAA1nhB,EAA8thB,yCAA9thB,EAA4zhB,yCAA5zhB,EAAq5hB,yCAAr5hB,EAA++hB,yCAA/+hB,EAAukiB,yCAAvkiB,EAAqpiB,yCAArpiB,EAAguiB,yCAAhuiB,EAA6yiB,yCAA7yiB,EAAw3iB,yCAAx3iB,EAAi9iB,yCAAj9iB,EAA+ijB,yCAA/ijB,EAA4ojB,yCAA5ojB,EAA0ujB,yCAA1ujB,EAAs0jB,yCAAt0jB,EAAu4jB,yCAAv4jB,EAAk9jB,yCAAl9jB,EAAqhkB,yCAArhkB,EAAulkB,yCAAvlkB,EAAkqkB,yCAAlqkB,EAAmvkB,yCAAnvkB,EAAw0kB,yCAAx0kB,EAA65kB,yCAA75kB,EAA6+kB,yCAA7+kB,EAA6jlB,yCAA7jlB,EAA+olB,yCAA/olB,EAAkulB,yCAAlulB,EAA2ylB,yCAA3ylB,EAA83lB,yCAA93lB,EAA+8lB,yCAA/8lB,EAAmimB,yCAAnimB,EAA0omB,yCAA1omB,EAA8vmB,yCAA9vmB,EAA82mB,yCAA92mB,EAAy9mB,yCAAz9mB,EAAsjnB,yCAAtjnB,EAAsonB,yCAAtonB,EAA0unB,yCAA1unB,EAAm1nB,yCAAn1nB,EAA07nB,yCAA17nB,EAAkioB,yCAAlioB,EAA2noB,yCAA3noB,EAAqsoB,yCAArsoB,EAA8woB,yCAA9woB,EAAm1oB,yCAAn1oB,EAA+5oB,yCAA/5oB,CAAjB;;AACA,aAAK,MAAMC,OAAX,IAAsBD,QAAtB,EAAgC;AAC5B,cAAI;AACA,kBAAMC,OAAO,EAAb;AACH,WAFD,CAEE,OAAOC,IAAP,EAAa,CACX;AACH;AACJ;AACJ,OATK,GAAN", "sourcesContent": ["\n// Auto generated represents the prerequisite imports of project modules.\n\nawait (async () => {\n    const requests = [() => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-dof-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-pass.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-settings.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline-types.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/default_renderpipeline/builtin-pipeline.ts\"), () => import(\"file:///C:/ProgramData/cocos/editors/Creator/3.8.6/resources/resources/3d/engine/editor/assets/tools/debug-view-runtime-control.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/Bundle.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/CommonEntry.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/DataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/bag/Bag.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/base/AttributeData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/base/BaseInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/Equip.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipCombine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/equip/EquipSlots.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/fight/Rogue.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/game_level/GameLevel.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/gm/GM.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/mail/Mail.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/pk/PK.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneCacheInfo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/data/plane/PlaneData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/DataEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/EventManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/HomeUIEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/event/PlaneUIEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/Plane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/TopBlockInputUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelect.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/SelectList/uiSelectItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/ButtonPlus.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/button/DragButton.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/dropdown/DropDown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/List.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/common/components/list/ListItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/DialogueUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueSelectIcon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/RogueUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/FriendAddUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/FriendCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/FriendListUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/FriendStrangerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/FriendUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/MailCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/MailUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/PKHistoryCellUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/PKHistoryUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/PKRewardIcon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/PKUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneCombineResultUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneEquipInfoUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneTypes.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/PlaneUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagGrid.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/BagItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/SortTypeDropdown.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/Tabs.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/CombineDisplay.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/display/EquipDisplay.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/shop/ShopUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/skyisland/SkyIslandUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuidingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuildingInfoUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/StoryUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/talent/TalentUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/components/ProgressPanel.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/GmEntry.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmButtonUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/emitter/EmitterEditor.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/EmitterGizmo.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoDrawer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/gizmos/GizmoUtils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorElemUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorEventUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveParam.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/LevelEditorWaveUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/level/utils.ts\"), () => import(\"file:///E:/M2Game/Client/assets/editor/planeview/PlaneView.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AAA/init_cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/Luban/schema.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/AutoGen/PB/cs_proto.js\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ColliderTest.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameFunc.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/GameIns.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/BulletSystem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/Easing.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/EventGroup.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/EventRunner.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/ObjectPool.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/PropertyContainer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/StateMachine.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/actions/BulletEventActions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/actions/EmitterEventActions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/actions/IEventAction.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/conditions/BulletEventConditions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/conditions/EmitterEventConditions.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/bullet/conditions/IEventCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FBoxCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FCircleCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FColliderManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/FPolygonCollider.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/Intersection.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/collider-system/QuadTree.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameConst.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameEnum.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/const/GameResourceList.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/BossData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/BulletEventData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EnemyData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/EnemyWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/GameMapData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/MainPlaneFightData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/MapItemData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/StageData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/TrackData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/WaveData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/BulletData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EmitterData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EventActionType.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EventConditionType.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/EventGroupData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/data/bullet/ExpressionValue.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/LevelItem.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/level/LevelItemEvent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BossManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/BulletManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/EnemyManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GamePlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameResManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GameRuleManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/GlobalDataManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/HurtEffectManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/MainPlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/SceneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/StageManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/manager/WaveManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/move/IMovable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/move/Movable.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/randTerrain/RandTerrain.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/scenes/GameMain.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/BaseComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Controller.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/Entity.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/ImageSequence.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/NodeMove.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/PfFrameAnim.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/TrackComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/base/UIAnimMethods.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/Bullet.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/BattleLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/layer/EnemyEffectLayer.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/GameMapRun.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelBaseUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelElemUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelEventUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelLayerUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/map/LevelWaveUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/PlaneBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/boss/BossPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyPlaneRole.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/enemy/EnemyShootComponent.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/mainPlane/MainPlane.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/skill/BuffComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/skill/SkillComp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/ui/plane/weapon/Weapon.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/utils/Helper.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/utils/RPN.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/utils/Tools.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/wave/Wave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/Baker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/LevelBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PathBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Baker/PointBaker.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Game/world/level/Data/PathPoint.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/IMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Luban/LubanMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MainUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/MyApp.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Network/NetMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/DevLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/DevLoginData.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/IPlatformSDK.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/PlatformSDK/WXLogin.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/ResUpdate.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/RootPersist.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ResUpdate/audioManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/Utils/Logger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/PlaneManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/ResManager.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/core/base/SingletonBase.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtion.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayDistance.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionDelayTime.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/LevelDataEventCondtionWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/condition/newCondition.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/leveldata.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTrigger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerAudio.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerLog.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/LevelDataEventTriggerWave.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/leveldata/trigger/newTrigger.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/DevLoginUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/LoadingUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/UIMgr.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/game/MBoomUI.ts\"), () => import(\"file:///E:/M2Game/Client/assets/scripts/ui/res/PlaneRes.ts\")];\n    for (const request of requests) {\n        try {\n            await request();\n        } catch (_err) {\n            // The error should have been caught by executor.\n        }\n    }\n})();\n    "]}