{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/FriendStrangerUI.ts"], "names": ["_decorator", "Component", "List", "FriendCellUI", "ccclass", "property", "FriendStrangerUI", "start", "list", "node", "active", "numItems", "update", "deltaTime", "onList<PERSON>ender", "listItem", "row", "cell", "getComponent", "setType", "txtName", "string"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACdC,MAAAA,I;;AACEC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;kCAGjBM,gB,WADZF,OAAO,CAAC,kBAAD,C,UAEHC,QAAQ;AAAA;AAAA,uB,2BAFb,MACaC,gBADb,SACsCL,SADtC,CACgD;AAAA;AAAA;;AAAA;AAAA;;AAG5CM,QAAAA,KAAK,GAAG;AACJ,eAAKC,IAAL,CAAWC,IAAX,CAAgBC,MAAhB,GAAyB,IAAzB;AACA,eAAKF,IAAL,CAAWG,QAAX,GAAsB,EAAtB;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AACDC,QAAAA,YAAY,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AACtC,cAAMC,IAAI,GAAGF,QAAQ,CAACG,YAAT;AAAA;AAAA,2CAAb;;AACA,cAAID,IAAI,KAAK,IAAb,EAAmB;AACfA,YAAAA,IAAI,CAACE,OAAL,CAAa,CAAb;AACAF,YAAAA,IAAI,CAACG,OAAL,CAAcC,MAAd,GAAuB,UAAUL,GAAG,GAAG,EAAhB,CAAvB;AACH;AACJ;;AAjB2C,O;;;;;iBAExB,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport List from '../../common/components/list/List';\r\nimport { FriendCellUI } from './FriendCellUI';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('FriendStrangerUI')\r\nexport class FriendStrangerUI extends Component {\r\n    @property(List)\r\n    list: List | null = null;\r\n    start() {\r\n        this.list!.node.active = true;\r\n        this.list!.numItems = 10;\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n    onListRender(listItem: Node, row: number) {\r\n        const cell = listItem.getComponent(FriendCellUI);\r\n        if (cell !== null) {\r\n            cell.setType(3);\r\n            cell.txtName!.string = \"小师妹：\" + (row + 21);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}