import { MyApp } from "db://assets/scripts/MyApp";
import { GameIns } from "../../../GameIns";
import BaseComp from "../../base/BaseComp";
import { logInfo, logWarn } from "db://assets/scripts/Utils/Logger";
import Entity from "../../base/Entity";
import { res } from "db://assets/scripts/AutoGen/Luban/schema";
import Plane from "../PlaneBase";

export default class SkillComp extends BaseComp {
    Cast(caster:Plane, skillID:number) {
        logInfo("Skill", `cast skill ${skillID}`);
        let skillData = MyApp.lubanTables.Tbskill.get(skillID);
        if (!skillData) {
            logWarn("Skill", `cast skill ${skillID} but config not found`)
            return;
        }
        skillData.ApplyBuffs.forEach((applyBuff) => {
            SkillComp.forEachByTargetType(caster, applyBuff.target, (entity) => {
                (entity as Plane).buffComp.ApplyBuff(applyBuff.buffID);
            })
        })
    }
    
    static forEachByTargetType(caster:Plane, targetType: res.TargetType, callback: (entity: Plane) => void) {
        switch (targetType) {
            case res.TargetType.Self:
                callback(caster as Plane);
                break;
            case res.TargetType.Main:
                callback(GameIns.mainPlaneManager.mainPlane!);
                break;
            case res.TargetType.MainFriendly:
                callback(GameIns.mainPlaneManager.mainPlane!);
                break;
            case res.TargetType.Enemy:
                GameIns.enemyManager.enemies.forEach((plane) => {
                    callback(plane);
                });
                GameIns.bossManager.bosses.forEach((boss) => {
                    // boss.getUnits().forEach((unit) => {
                    //     callback(unit);
                    // });
                    callback(boss)
                });
                break;
            case res.TargetType.BossEnemy:
                GameIns.bossManager.bosses.forEach((boss) => {
                    // boss.getUnits().forEach((unit) => {
                    //     callback(unit);
                    // });
                    callback(boss)
                });
                break;
            case res.TargetType.NormalEnemy:
                GameIns.enemyManager.enemies.forEach((plane) => {
                    callback(plane);
                });
                break;
            default:
                break;
        }
    }
}