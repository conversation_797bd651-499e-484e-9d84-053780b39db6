{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/editor/level/LevelEditorLayerUI.ts"], "names": ["_decorator", "Component", "Node", "assetManager", "instantiate", "Vec2", "UITransform", "v2", "LevelDataEvent", "LevelDataWave", "LevelEditorUtils", "LevelScrollLayerUI", "LevelEditorWaveUI", "LevelEditorEventUI", "ccclass", "property", "executeInEditMode", "TerrainsNodeName", "ScrollsNodeName", "DynamicNodeName", "WaveNodeName", "EventNodeName", "LevelEditorLayerUI", "terrainsNode", "scrollsNode", "dynamicNode", "wavesNode", "eventsNode", "onLoad", "getOrAddNode", "node", "initByLevelData", "data", "console", "log", "terrains", "for<PERSON>ach", "terrain", "loadAny", "uuid", "err", "prefab", "error", "terrainNode", "setPosition", "position", "x", "y", "setScale", "scale", "setRotationFromEuler", "rotation", "<PERSON><PERSON><PERSON><PERSON>", "dynamics", "dynamic", "index", "dynaNode", "waves", "wave", "waveUIComp", "addComponent", "events", "event", "eventUIComp", "initScorllsByLevelData", "layerData", "scrolls", "scroll", "scroll<PERSON>ayer", "scrollPrefab", "scrollLayers", "push", "totalHeight", "speed", "totalTime", "childCount", "posOffsetY", "i", "child", "offY", "getComponent", "contentSize", "height", "fillLevelData", "children", "_prefab", "asset", "_uuid", "z", "scrollNode", "waveNode", "eventNode", "tick", "progress"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAqBC,MAAAA,S,OAAAA,S;AAAsBC,MAAAA,I,OAAAA,I;AAA8EC,MAAAA,Y,OAAAA,Y;AAAoEC,MAAAA,W,OAAAA,W;AAAaC,MAAAA,I,OAAAA,I;AAAiBC,MAAAA,W,OAAAA,W;AAAmBC,MAAAA,E,OAAAA,E;;AAG9OC,MAAAA,c,iBAAAA,c;AAAgCC,MAAAA,a,iBAAAA,a;;AAEhCC,MAAAA,gB,iBAAAA,gB;AAA8BC,MAAAA,kB,iBAAAA,kB;;AAC9BC,MAAAA,iB,iBAAAA,iB;;AACAC,MAAAA,kB,iBAAAA,kB;;;;;;;;;OANH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,QAAX;AAAqBC,QAAAA;AAArB,O,GAA2ChB,U;AAQ3CiB,MAAAA,gB,GAAmB,U;AACnBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,e,GAAkB,S;AAClBC,MAAAA,Y,GAAe,O;AACfC,MAAAA,a,GAAgB,Q;;oCAITC,kB,WAFZR,OAAO,CAAC,oBAAD,C,UACPE,iBAAiB,E,+BADlB,MAEaM,kBAFb,SAEwCrB,SAFxC,CAEkD;AAAA;AAAA;AAAA,eACvCsB,YADuC,GACb,IADa;AAAA,eAEvCC,WAFuC,GAEd,IAFc;AAAA,eAGvCC,WAHuC,GAGd,IAHc;AAAA,eAIvCC,SAJuC,GAIhB,IAJgB;AAAA,eAKvCC,UALuC,GAKf,IALe;AAAA;;AAO9CC,QAAAA,MAAM,GAAS;AACX,eAAKL,YAAL,GAAoB;AAAA;AAAA,oDAAiBM,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCb,gBAAzC,CAApB;AACA,eAAKO,WAAL,GAAmB;AAAA;AAAA,oDAAiBK,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCZ,eAAzC,CAAnB;AACA,eAAKO,WAAL,GAAmB;AAAA;AAAA,oDAAiBI,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCX,eAAzC,CAAnB;AACA,eAAKO,SAAL,GAAiB;AAAA;AAAA,oDAAiBG,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCV,YAAzC,CAAjB;AACA,eAAKO,UAAL,GAAkB;AAAA;AAAA,oDAAiBE,YAAjB,CAA8B,KAAKC,IAAnC,EAAyCT,aAAzC,CAAlB;AACH;;AAEMU,QAAAA,eAAe,CAACC,IAAD,EAA4B;AAAA;;AAC9CC,UAAAA,OAAO,CAACC,GAAR,CAAY,oCAAZ;;AACA,cAAI,CAACF,IAAL,EAAW;AACP;AACH;;AAED,cAAI,KAAKT,YAAL,KAAsB,IAA1B,EAAgC;AAC5B;AACH;;AACD,4BAAAS,IAAI,CAACG,QAAL,4BAAeC,OAAf,CAAwBC,OAAD,IAAa;AAChClC,YAAAA,YAAY,CAACmC,OAAb,CAAqB;AAACC,cAAAA,IAAI,EAACF,OAAO,CAACE;AAAd,aAArB,EAA0C,CAACC,GAAD,EAAMC,MAAN,KAAwB;AAC9D,kBAAID,GAAJ,EAAS;AACLP,gBAAAA,OAAO,CAACS,KAAR,CAAc,4DAAd,EAA4EF,GAA5E;AACA;AACH;;AACD,kBAAIG,WAAW,GAAGvC,WAAW,CAACqC,MAAD,CAA7B;AACAE,cAAAA,WAAW,CAACC,WAAZ,CAAwBP,OAAO,CAACQ,QAAR,CAAiBC,CAAzC,EAA4CT,OAAO,CAACQ,QAAR,CAAiBE,CAA7D,EAAgE,CAAhE;AACAJ,cAAAA,WAAW,CAACK,QAAZ,CAAqBX,OAAO,CAACY,KAAR,CAAcH,CAAnC,EAAsCT,OAAO,CAACY,KAAR,CAAcF,CAApD,EAAuD,CAAvD;AACAJ,cAAAA,WAAW,CAACO,oBAAZ,CAAiC,CAAjC,EAAoC,CAApC,EAAuCb,OAAO,CAACc,QAA/C;AACA,mBAAK5B,YAAL,CAAmB6B,QAAnB,CAA4BT,WAA5B;AACH,aAVD;AAWH,WAZD;AAcA,4BAAAX,IAAI,CAACqB,QAAL,4BAAejB,OAAf,CAAuB,CAACkB,OAAD,EAAUC,KAAV,KAAoB;AACvC,gBAAIC,QAAQ,GAAG;AAAA;AAAA,sDAAiB3B,YAAjB,CAA8B,KAAKJ,WAAnC,EAAiD,UAAQ8B,KAAzD,CAAf;AACAC,YAAAA,QAAQ,CAACZ,WAAT,CAAqBU,OAAO,CAACT,QAAR,CAAiBC,CAAtC,EAAyCQ,OAAO,CAACT,QAAR,CAAiBE,CAA1D,EAA6D,CAA7D;AACAS,YAAAA,QAAQ,CAACR,QAAT,CAAkBM,OAAO,CAACL,KAAR,CAAcH,CAAhC,EAAmCQ,OAAO,CAACL,KAAR,CAAcF,CAAjD,EAAoD,CAApD;AACAS,YAAAA,QAAQ,CAACN,oBAAT,CAA8B,CAA9B,EAAiC,CAAjC,EAAoCI,OAAO,CAACH,QAA5C;AAEAG,YAAAA,OAAO,CAACnB,QAAR,CAAiBC,OAAjB,CAA0BC,OAAD,IAAW;AAChClC,cAAAA,YAAY,CAACmC,OAAb,CAAqB;AAACC,gBAAAA,IAAI,EAACF,OAAO,CAACE;AAAd,eAArB,EAA0C,CAACC,GAAD,EAAMC,MAAN,KAAwB;AAC9D,oBAAID,GAAJ,EAAS;AACLP,kBAAAA,OAAO,CAACS,KAAR,CAAc,4DAAd,EAA4EF,GAA5E;AACA;AACH;;AACD,oBAAIf,WAAW,GAAGrB,WAAW,CAACqC,MAAD,CAA7B;AACA,qBAAKhB,WAAL,CAAkB2B,QAAlB,CAA2B3B,WAA3B;AACH,eAPD;AAQH,aATD;AAUH,WAhBD;AAkBA,yBAAAO,IAAI,CAACyB,KAAL,yBAAYrB,OAAZ,CAAqBsB,IAAD,IAAQ;AACxB,gBAAI5B,IAAI,GAAG,IAAI5B,IAAJ,EAAX;AACA,gBAAIyD,UAAU,GAAG7B,IAAI,CAAC8B,YAAL;AAAA;AAAA,uDAAjB;AACAD,YAAAA,UAAU,CAAC5B,eAAX,CAA2B2B,IAA3B;AACA,iBAAKhC,SAAL,CAAgB0B,QAAhB,CAAyBtB,IAAzB;AACH,WALD;AAMA,0BAAAE,IAAI,CAAC6B,MAAL,0BAAazB,OAAb,CAAsB0B,KAAD,IAAS;AAC1B,gBAAIhC,IAAI,GAAG,IAAI5B,IAAJ,EAAX;AACA,gBAAI6D,WAAW,GAAGjC,IAAI,CAAC8B,YAAL;AAAA;AAAA,yDAAlB;AACAG,YAAAA,WAAW,CAAChC,eAAZ,CAA4B+B,KAA5B;AACA,iBAAKnC,UAAL,CAAiByB,QAAjB,CAA0BtB,IAA1B;AACH,WALD;AAMH;;AAEMkC,QAAAA,sBAAsB,CAACC,SAAD,EAAwBjC,IAAxB,EAAmD;AAAA;;AAC5E,cAAI,CAACA,IAAL,EAAW;AACP;AACH;;AAED,2BAAAA,IAAI,CAACkC,OAAL,2BAAc9B,OAAd,CAAsB,CAAC+B,MAAD,EAASZ,KAAT,KAAmB;AACrCpD,YAAAA,YAAY,CAACmC,OAAb,CAAqB;AAACC,cAAAA,IAAI,EAAC4B,MAAM,CAAC5B;AAAb,aAArB,EAAyC,CAACC,GAAD,EAAMC,MAAN,KAAwB;AAC7D,kBAAID,GAAJ,EAAS;AACLP,gBAAAA,OAAO,CAACS,KAAR,CAAc,4DAAd,EAA4EF,GAA5E;AACA;AACH;;AAED,oBAAM4B,WAAW,GAAG;AAAA;AAAA,6DAApB;AACAA,cAAAA,WAAW,CAACC,YAAZ,GAA2B5B,MAA3B;AACAwB,cAAAA,SAAS,CAACK,YAAV,CAAuBC,IAAvB,CAA4BH,WAA5B;AAEA,oBAAM5C,WAAW,GAAG;AAAA;AAAA,wDAAiBK,YAAjB,CAA8B,KAAKL,WAAnC,EAAkD,UAAS+B,KAAM,EAAjE,CAApB;AAEA,kBAAIiB,WAAW,GAAGxC,IAAI,CAACyC,KAAL,GAAazC,IAAI,CAAC0C,SAApC;AACA,kBAAIC,UAAU,GAAGH,WAAW,GAAGxC,IAAI,CAACyC,KAAnB,GAA2B,CAA5C;AACA,kBAAIG,UAAU,GAAG,CAAjB;;AACA,mBAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,UAApB,EAAgCE,CAAC,EAAjC,EAAqC;AACjC,sBAAMC,KAAK,GAAG1E,WAAW,CAACqC,MAAD,CAAzB;AACAqC,gBAAAA,KAAK,CAAClC,WAAN,CAAkB,CAAlB,EAAqBgC,UAArB,EAAiC,CAAjC;AACA,oBAAIG,IAAI,GAAGD,KAAK,CAACE,YAAN,CAAmB1E,WAAnB,EAAiC2E,WAAjC,CAA6CC,MAAxD;AACA1D,gBAAAA,WAAW,CAAC4B,QAAZ,CAAqB0B,KAArB;AACAF,gBAAAA,UAAU,IAAIG,IAAd;AACH;AACJ,aAtBD;AAuBH,WAxBD;AAyBH;;AAEMI,QAAAA,aAAa,CAACnD,IAAD,EAA4B;AAC5CA,UAAAA,IAAI,CAACG,QAAL,GAAgB,EAAhB;AACA,eAAKZ,YAAL,CAAmB6D,QAAnB,CAA4BhD,OAA5B,CAAqCO,WAAD,IAAiB;AACjDX,YAAAA,IAAI,CAACG,QAAL,CAAcoC,IAAd,CAAmB;AACf;AACAhC,cAAAA,IAAI,EAAEI,WAAW,CAAC0C,OAAZ,CAAoBC,KAApB,CAA0BC,KAFjB;AAGf1C,cAAAA,QAAQ,EAAE,IAAIxC,IAAJ,CAASsC,WAAW,CAACE,QAAZ,CAAqBC,CAA9B,EAAiCH,WAAW,CAACE,QAAZ,CAAqBE,CAAtD,CAHK;AAIfE,cAAAA,KAAK,EAAE,IAAI5C,IAAJ,CAASsC,WAAW,CAACM,KAAZ,CAAkBH,CAA3B,EAA8BH,WAAW,CAACM,KAAZ,CAAkBF,CAAhD,CAJQ;AAKfI,cAAAA,QAAQ,EAAER,WAAW,CAACQ,QAAZ,CAAqBqC;AALhB,aAAnB;AAOH,WARD,EAF4C,CAY5C;;AACAvD,UAAAA,OAAO,CAACC,GAAR,CAAY,0CAAZ,EAAwDF,IAAI,CAACkC,OAA7D;AACA,eAAK1C,WAAL,CAAkB4D,QAAlB,CAA2BhD,OAA3B,CAAmC,CAACqD,UAAD,EAAalC,KAAb,KAAuB;AACtD;AACAvB,YAAAA,IAAI,CAACkC,OAAL,CAAaX,KAAb,EAAoBhB,IAApB,GAA2BkD,UAAU,CAACJ,OAAX,CAAmBC,KAAnB,CAAyBC,KAApD;AACAvD,YAAAA,IAAI,CAACkC,OAAL,CAAaX,KAAb,EAAoBV,QAApB,GAA+BtC,EAAE,CAACkF,UAAU,CAAC5C,QAAX,CAAoBC,CAArB,EAAwB2C,UAAU,CAAC5C,QAAX,CAAoBE,CAA5C,CAAjC;AACAf,YAAAA,IAAI,CAACkC,OAAL,CAAaX,KAAb,EAAoBN,KAApB,GAA4B1C,EAAE,CAACkF,UAAU,CAACxC,KAAX,CAAiBH,CAAlB,EAAqB2C,UAAU,CAACxC,KAAX,CAAiBF,CAAtC,CAA9B;AACAf,YAAAA,IAAI,CAACkC,OAAL,CAAaX,KAAb,EAAoBJ,QAApB,GAA+BsC,UAAU,CAACtC,QAAX,CAAoBqC,CAAnD;AACH,WAND,EAd4C,CAsB5C;;AACA,eAAK/D,WAAL,CAAkB2D,QAAlB,CAA2BhD,OAA3B,CAAmC,CAACX,WAAD,EAAc8B,KAAd,KAAwB;AACvDvB,YAAAA,IAAI,CAACqB,QAAL,CAAcE,KAAd,EAAqBV,QAArB,GAAgCtC,EAAE,CAACkB,WAAW,CAACoB,QAAZ,CAAqBC,CAAtB,EAAyBrB,WAAW,CAACoB,QAAZ,CAAqBE,CAA9C,CAAlC;AACAf,YAAAA,IAAI,CAACqB,QAAL,CAAcE,KAAd,EAAqBN,KAArB,GAA6B1C,EAAE,CAACkB,WAAW,CAACwB,KAAZ,CAAkBH,CAAnB,EAAsBrB,WAAW,CAACwB,KAAZ,CAAkBF,CAAxC,CAA/B;AACAf,YAAAA,IAAI,CAACqB,QAAL,CAAcE,KAAd,EAAqBJ,QAArB,GAAgC1B,WAAW,CAAC0B,QAAZ,CAAqBqC,CAArD;AACH,WAJD;AAMAxD,UAAAA,IAAI,CAACyB,KAAL,GAAa,EAAb;AACA,eAAK/B,SAAL,CAAgB0D,QAAhB,CAAyBhD,OAAzB,CAAkCsD,QAAD,IAAc;AAC3C,gBAAIhC,IAAI,GAAG;AAAA;AAAA,iDAAX;AACA,gBAAIC,UAAU,GAAG+B,QAAQ,CAACV,YAAT;AAAA;AAAA,uDAAjB;AACArB,YAAAA,UAAU,CAAEwB,aAAZ,CAA0BzB,IAA1B;AACA1B,YAAAA,IAAI,CAACyB,KAAL,CAAWc,IAAX,CAAgBb,IAAhB;AACH,WALD;AAMA1B,UAAAA,IAAI,CAAC6B,MAAL,GAAc,EAAd;AACA,eAAKlC,UAAL,CAAiByD,QAAjB,CAA0BhD,OAA1B,CAAmCuD,SAAD,IAAe;AAC7C,gBAAI7B,KAAK,GAAG;AAAA;AAAA,mDAAZ;AACA,gBAAIC,WAAW,GAAG4B,SAAS,CAACX,YAAV;AAAA;AAAA,yDAAlB;AACAjB,YAAAA,WAAW,CAAEoB,aAAb,CAA2BrB,KAA3B;AACA9B,YAAAA,IAAI,CAAC6B,MAAL,CAAYU,IAAZ,CAAiBT,KAAjB;AACH,WALD;AAMH;;AAEM8B,QAAAA,IAAI,CAACC,QAAD,EAAmBnB,SAAnB,EAAqCD,KAArC,EAAwD;AAC/D,eAAK3C,IAAL,CAAUc,WAAV,CAAsB,CAAtB,EAAyB,CAAEiD,QAAF,GAAanB,SAAb,GAAyBD,KAAlD,EAAyD,CAAzD;AACH;;AArJ6C,O", "sourcesContent": ["import { _decorator, CCFloat, Component, JsonAsset, Node, Prefab, Slider, Vec3, ValueType, CCBoolean, CCString, Asset, resources, assetManager, AssetManager, Sprite, SpriteFrame, SpriteAtlas, math, instantiate, Vec2, CCInteger, UITransform, view, v2 } from 'cc';\r\nconst { ccclass, property, executeInEditMode } = _decorator;\r\n\r\nimport { LevelDataEvent, LevelDataLayer, LevelDataWave } from 'db://assets/scripts/leveldata/leveldata';\r\n\r\nimport { LevelEditorUtils, LevelLayer, LevelScrollLayerUI } from './utils';\r\nimport { LevelEditorWaveUI } from './LevelEditorWaveUI';\r\nimport { LevelEditorEventUI } from './LevelEditorEventUI';\r\n\r\nconst TerrainsNodeName = \"terrains\";\r\nconst ScrollsNodeName = \"scrolls\";\r\nconst DynamicNodeName = \"dynamic\";\r\nconst WaveNodeName = \"waves\";\r\nconst EventNodeName = \"events\"\r\n\r\n@ccclass('LevelEditorLayerUI')\r\n@executeInEditMode()\r\nexport class LevelEditorLayerUI extends Component {\r\n    public terrainsNode: Node|null = null;\r\n    public scrollsNode: Node|null = null;\r\n    public dynamicNode: Node|null = null;\r\n    public wavesNode: Node|null = null;\r\n    public eventsNode: Node|null = null;\r\n\r\n    onLoad(): void {\r\n        this.terrainsNode = LevelEditorUtils.getOrAddNode(this.node, TerrainsNodeName);\r\n        this.scrollsNode = LevelEditorUtils.getOrAddNode(this.node, ScrollsNodeName);\r\n        this.dynamicNode = LevelEditorUtils.getOrAddNode(this.node, DynamicNodeName);\r\n        this.wavesNode = LevelEditorUtils.getOrAddNode(this.node, WaveNodeName);\r\n        this.eventsNode = LevelEditorUtils.getOrAddNode(this.node, EventNodeName);\r\n    }\r\n\r\n    public initByLevelData(data: LevelDataLayer):void {\r\n        console.log(\"LevelEditorLayerUI initByLevelData\");\r\n        if (!data) {\r\n            return;\r\n        }\r\n\r\n        if (this.terrainsNode === null) {\r\n            return;\r\n        }\r\n        data.terrains?.forEach((terrain) => {\r\n            assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"LevelEditorLayerUI initByLevelData load terrain prefab err\", err);\r\n                    return\r\n                } \r\n                var terrainNode = instantiate(prefab);\r\n                terrainNode.setPosition(terrain.position.x, terrain.position.y, 0);\r\n                terrainNode.setScale(terrain.scale.x, terrain.scale.y, 1);\r\n                terrainNode.setRotationFromEuler(0, 0, terrain.rotation);\r\n                this.terrainsNode!.addChild(terrainNode);                \r\n            });\r\n        });\r\n\r\n        data.dynamics?.forEach((dynamic, index) => {\r\n            var dynaNode = LevelEditorUtils.getOrAddNode(this.dynamicNode!, 'dyna_'+index);\r\n            dynaNode.setPosition(dynamic.position.x, dynamic.position.y, 0);\r\n            dynaNode.setScale(dynamic.scale.x, dynamic.scale.y, 1);\r\n            dynaNode.setRotationFromEuler(0, 0, dynamic.rotation);\r\n\r\n            dynamic.terrains.forEach((terrain)=>{\r\n                assetManager.loadAny({uuid:terrain.uuid}, (err, prefab:Prefab) => {\r\n                    if (err) {\r\n                        console.error(\"LevelEditorLayerUI initByLevelData load dynamic prefab err\", err);\r\n                        return\r\n                    } \r\n                    var dynamicNode = instantiate(prefab);\r\n                    this.dynamicNode!.addChild(dynamicNode);                \r\n                });\r\n            });\r\n        });\r\n\r\n        data.waves?.forEach((wave)=>{\r\n            var node = new Node();\r\n            var waveUIComp = node.addComponent(LevelEditorWaveUI);\r\n            waveUIComp.initByLevelData(wave);\r\n            this.wavesNode!.addChild(node);\r\n        })\r\n        data.events?.forEach((event)=>{\r\n            var node = new Node();\r\n            var eventUIComp = node.addComponent(LevelEditorEventUI);\r\n            eventUIComp.initByLevelData(event);\r\n            this.eventsNode!.addChild(node);\r\n        })\r\n    }\r\n\r\n    public initScorllsByLevelData(layerData: LevelLayer, data: LevelDataLayer):void {\r\n        if (!data) {\r\n            return;\r\n        } \r\n\r\n        data.scrolls?.forEach((scroll, index) => {\r\n            assetManager.loadAny({uuid:scroll.uuid}, (err, prefab:Prefab) => {\r\n                if (err) {\r\n                    console.error(\"LevelEditorLayerUI initByLevelData load scrolls prefab err\", err);\r\n                    return\r\n                }\r\n                \r\n                const scrollLayer = new LevelScrollLayerUI();\r\n                scrollLayer.scrollPrefab = prefab;\r\n                layerData.scrollLayers.push(scrollLayer);\r\n\r\n                const scrollsNode = LevelEditorUtils.getOrAddNode(this.scrollsNode!, `scroll_${index}`);\r\n\r\n                var totalHeight = data.speed * data.totalTime;\r\n                var childCount = totalHeight / data.speed - 1;\r\n                var posOffsetY = 0;\r\n                for (let i = 0; i < childCount; i++) { \r\n                    const child = instantiate(prefab);\r\n                    child.setPosition(0, posOffsetY, 0);\r\n                    var offY = child.getComponent(UITransform)!.contentSize.height;\r\n                    scrollsNode.addChild(child);\r\n                    posOffsetY += offY;\r\n                }      \r\n            });\r\n        });\r\n    }\r\n\r\n    public fillLevelData(data: LevelDataLayer):void {\r\n        data.terrains = []\r\n        this.terrainsNode!.children.forEach((terrainNode) => {\r\n            data.terrains.push({\r\n                // @ts-ignore\r\n                uuid: terrainNode._prefab.asset._uuid,\r\n                position: new Vec2(terrainNode.position.x, terrainNode.position.y),\r\n                scale: new Vec2(terrainNode.scale.x, terrainNode.scale.y),\r\n                rotation: terrainNode.rotation.z\r\n            })\r\n        })\r\n\r\n        //data.scrolls = [] 在上层有保存其它信息，所以这里不清空\r\n        console.log(\"LevelEditorLayerUI fillLevelData scrolls\", data.scrolls);\r\n        this.scrollsNode!.children.forEach((scrollNode, index) => {\r\n            // @ts-ignore\r\n            data.scrolls[index].uuid = scrollNode._prefab.asset._uuid;\r\n            data.scrolls[index].position = v2(scrollNode.position.x, scrollNode.position.y);\r\n            data.scrolls[index].scale = v2(scrollNode.scale.x, scrollNode.scale.y);\r\n            data.scrolls[index].rotation = scrollNode.rotation.z;\r\n        });\r\n\r\n        //data.dynamics = [] 在上层有保存其它信息，所以这里不清空\r\n        this.dynamicNode!.children.forEach((dynamicNode, index) => {\r\n            data.dynamics[index].position = v2(dynamicNode.position.x, dynamicNode.position.y);\r\n            data.dynamics[index].scale = v2(dynamicNode.scale.x, dynamicNode.scale.y);\r\n            data.dynamics[index].rotation = dynamicNode.rotation.z;\r\n        });\r\n\r\n        data.waves = []\r\n        this.wavesNode!.children.forEach((waveNode) => {\r\n            var wave = new LevelDataWave()\r\n            var waveUIComp = waveNode.getComponent(LevelEditorWaveUI);\r\n            waveUIComp!.fillLevelData(wave)\r\n            data.waves.push(wave)\r\n        })\r\n        data.events = []\r\n        this.eventsNode!.children.forEach((eventNode) => {\r\n            var event = new LevelDataEvent()\r\n            var eventUIComp = eventNode.getComponent(LevelEditorEventUI);\r\n            eventUIComp!.fillLevelData(event)\r\n            data.events.push(event)\r\n        })\r\n    }\r\n\r\n    public tick(progress: number, totalTime:number, speed:number):void {\r\n        this.node.setPosition(0, - progress * totalTime * speed, 0);\r\n    }\r\n}"]}