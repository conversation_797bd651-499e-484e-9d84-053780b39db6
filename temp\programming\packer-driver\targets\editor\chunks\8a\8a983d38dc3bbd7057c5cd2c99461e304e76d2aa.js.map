{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/game/MBoomUI.ts"], "names": ["_decorator", "v3", "Drag<PERSON><PERSON><PERSON>", "GameIns", "logDebug", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "ccclass", "property", "MBoomUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "start", "node", "position", "getComponent", "addClick", "onClick", "mainPlaneManager", "mainPlane", "CastSkill", "onShow", "onHide", "args", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,E,OAAAA,E;;AAEZC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;;;;;;;;OACX;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBR,U;;yBAGjBS,O,WADZF,OAAO,CAAC,SAAD,C,gBAAR,MACaE,OADb;AAAA;AAAA,4BACoC;AACZ,eAANC,MAAM,GAAW;AAAE,iBAAO,iBAAP;AAA2B;;AACtC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AAElDC,QAAAA,KAAK,GAAS;AACpB,eAAKC,IAAL,CAAUC,QAAV,GAAqBd,EAAE,CAAC,CAAC,GAAF,EAAO,CAAC,GAAR,EAAa,CAAb,CAAvB;AACA,eAAKe,YAAL;AAAA;AAAA,wCAA+BC,QAA/B,CAAwC,KAAKC,OAA7C,EAAsD,IAAtD;AACH;;AAEY,cAAPA,OAAO,GAAG;AAAA;;AACZ;AAAA;AAAA,oCAAS,SAAT,EAAoB,SAApB,EAA+B,QAA/B;AACA;AAAA;AAAA,kCAAQC,gBAAR,CAAyBC,SAAzB,mCAAoCC,SAApC,CAA8C,CAA9C;AACH;;AAEW,cAANC,MAAM,GAAkB,CAC7B;;AAEW,cAANC,MAAM,CAAC,GAAGC,IAAJ,EAAgC,CAC3C;;AAEY,cAAPC,OAAO,CAAC,GAAGD,IAAJ,EAAgC,CAC5C;;AArB+B,O", "sourcesContent": ["import { _decorator, v3 } from 'cc';\n\nimport { Drag<PERSON><PERSON>on } from '../../../bundles/common/script/ui/common/components/button/DragButton';\nimport { GameIns } from '../../Game/GameIns';\nimport { logDebug } from '../../Utils/Logger';\nimport { BaseUI, UILayer } from '../UIMgr';\nconst { ccclass, property } = _decorator;\n\n@ccclass('MBoomUI')\nexport class MBoomUI extends BaseUI {\n    public static getUrl(): string { return \"ui/game/MBoomUI\"; }\n    public static getLayer(): UILayer { return UILayer.Default }\n\n    protected start(): void {\n        this.node.position = v3(-230, -400, 0)\n        this.getComponent(DragButton)!.addClick(this.onClick, this)\n    }\n\n    async onClick() {\n        logDebug(\"MBoomUI\", \"onClick\", \"aaaaaa\")\n        GameIns.mainPlaneManager.mainPlane?.CastSkill(1)\n    }\n\n    async onShow(): Promise<void> {\n    }\n\n    async onHide(...args: any[]): Promise<void> {\n    }\n\n    async onClose(...args: any[]): Promise<void> {\n    }\n}"]}