{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/const/AttributeConst.ts"], "names": ["AttributeComeConst", "EQUIP", "SKILL", "BUFF"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAsBaA,kB,GAAqB;AAC9BC,QAAAA,KAAK,EAAE,OADuB;AACd;AAChBC,QAAAA,KAAK,EAAE,OAFuB;AAEd;AAChBC,QAAAA,IAAI,EAAE,MAHwB,CAGhB;;AAHgB,O", "sourcesContent": ["export const enum AttributeConst {\r\n    //基础属性\r\n    MaxHP = 1, //最大生命\r\n    Attack = 2, //攻击\r\n    AttackBoss = 3, //攻击Boss攻击力\r\n    AttackNormal = 4,// 攻击普通敌机攻击力\r\n    BulletHurtResistance = 5, // 子弹伤害抗性\r\n    CollisionHurtResistance = 6, // 撞击伤害抗性\r\n    Fortunate = 7,//幸运值\r\n    MissRate = 8,//闪避\r\n    KillScoreRate = 9, // 击杀得分\r\n    FinalScoreRate = 10, // 结算得分\r\n    EnergyRecovery = 11, // 能量恢复\r\n    EnergyRecoveryRate = 12, // 能量恢复\r\n    HPRecovery = 13,//气血恢复\r\n    HPRecoveryRate = 14,//气血恢复\r\n    PickRadius = 15, // 拾取范围\r\n    BombMax = 16, // 核弹携带上限\r\n    BombHurt = 17,\r\n}\r\n\r\n\r\nexport const AttributeComeConst = {\r\n    EQUIP: \"Equip\", //装备\r\n    SKILL: \"Skill\", //技能\r\n    BUFF: \"Buff\", //buff\r\n}"]}