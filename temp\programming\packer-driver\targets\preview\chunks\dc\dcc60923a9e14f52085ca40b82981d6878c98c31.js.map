{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/PKHistoryUI.ts"], "names": ["_decorator", "BundleName", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ButtonPlus", "List", "PKUI", "ccclass", "property", "PKHistoryUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "getBundleName", "Home", "onLoad", "btnClose", "addClick", "closeUI", "list", "numItems", "openUI", "onShow", "onHide", "onClose", "onDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACFC,MAAAA,I;;AACEC,MAAAA,I,iBAAAA,I;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;6BAGjBU,W,WADZF,OAAO,CAAC,aAAD,C,UAEHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,uB,2BAJb,MACaC,WADb;AAAA;AAAA,4BACwC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AAKhB,eAANC,MAAM,GAAW;AAAE,iBAAO,0BAAP;AAAoC;;AAC/C,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,OAAf;AAAwB;;AACjC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAyB;;AACvDC,QAAAA,MAAM,GAAS;AACrB,eAAKC,QAAL,CAAeC,QAAf,CAAwB,KAAKC,OAA7B,EAAsC,IAAtC;AACA,eAAKC,IAAL,CAAWC,QAAX,GAAsB,EAAtB;AACH;;AACKF,QAAAA,OAAO,GAAG;AAAA;AACZ;AAAA;AAAA,gCAAMA,OAAN,CAAcT,WAAd;AACA,kBAAM;AAAA;AAAA,gCAAMY,MAAN;AAAA;AAAA,6BAAN;AAFY;AAGf;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAE7B;;AACKC,QAAAA,MAAM,GAAkB;AAAA;AAC7B;;AACKC,QAAAA,OAAO,GAAkB;AAAA;AAC9B;;AACSC,QAAAA,SAAS,GAAS,CAE3B;;AAzBmC,O;;;;;iBAEN,I;;;;;;;iBAEV,I", "sourcesContent": ["import { _decorator } from 'cc';\r\nimport { BundleName } from 'db://assets/bundles/Bundle';\r\nimport { BaseUI, UILayer, UIMgr } from '../../../../../../scripts/ui/UIMgr';\r\nimport { ButtonPlus } from '../../common/components/button/ButtonPlus';\r\nimport List from '../../common/components/list/List';\r\nimport { PKUI } from './PKUI';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PKHistoryUI')\r\nexport class PKHistoryUI extends BaseUI {\r\n    @property(ButtonPlus)\r\n    btnClose: ButtonPlus | null = null;\r\n    @property(List)\r\n    list: List | null = null;\r\n    public static getUrl(): string { return \"prefab/ui/pk/PKHistoryUI\"; }\r\n    public static getLayer(): UILayer { return UILayer.Default }\r\n    public static getBundleName(): string { return BundleName.Home; }\r\n    protected onLoad(): void {\r\n        this.btnClose!.addClick(this.closeUI, this);\r\n        this.list!.numItems = 20;\r\n    }\r\n    async closeUI() {\r\n        UIMgr.closeUI(PKHistoryUI);\r\n        await UIMgr.openUI(PKUI)\r\n    }\r\n    async onShow(): Promise<void> {\r\n\r\n    }\r\n    async onHide(): Promise<void> {\r\n    }\r\n    async onClose(): Promise<void> {\r\n    }\r\n    protected onDestroy(): void {\r\n\r\n    }\r\n}\r\n\r\n\r\n"]}