{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/PKHistoryCellUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "Component", "Sprite", "csproto", "MyApp", "DataEvent", "EventMgr", "ccclass", "property", "PKHistoryCellUI", "guid", "onButtonClick", "netMgr", "sendMessage", "cs", "CS_CMD", "CS_CMD_GAME_PVP_GET_AWARD", "game_pvp_get_award", "start", "on", "GamePvpGetAward", "getAward", "update", "deltaTime", "onDestroy"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,M,OAAAA,M;;AACjCC,MAAAA,O;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;iCAGjBU,e,WADZF,OAAO,CAAC,iBAAD,C,UAGHC,QAAQ,CAACN,MAAD,C,UAGRM,QAAQ,CAACR,MAAD,C,2BANb,MACaS,eADb,SACqCR,SADrC,CAC+C;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAQpCS,IARoC;AAAA;;AAU3CC,QAAAA,aAAa,GAAG;AACZ,cAAI,CAAC,KAAKD,IAAV,EAAgB;AACZ;AACH;;AACD;AAAA;AAAA,8BAAME,MAAN,CAAaC,WAAb,CAAyB;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,yBAA3C,EAAsE;AAAEC,YAAAA,kBAAkB,EAAE;AAAEP,cAAAA,IAAI,EAAE,KAAKA;AAAb;AAAtB,WAAtE;AACH;;AAEDQ,QAAAA,KAAK,GAAG;AACJ;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,sCAAUC,eAAtB,EAAuC,KAAKC,QAA5C,EAAsD,IAAtD;AACH;;AACOA,QAAAA,QAAQ,GAAG,CAClB;;AACDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AAESC,QAAAA,SAAS,GAAS,CAE3B;;AA5B0C,O;;;;;iBAGrB,I;;;;;;;iBAGI,I", "sourcesContent": ["import { _decorator, Button, Component, Sprite } from 'cc';\r\nimport csproto from '../../../../../../scripts/AutoGen/PB/cs_proto.js';\r\nimport { MyApp } from '../../../../../../scripts/MyApp';\r\nimport { DataEvent } from '../../../event/DataEvent';\r\nimport { EventMgr } from '../../../event/EventManager';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PKHistoryCellUI')\r\nexport class PKHistoryCellUI extends Component {\r\n\r\n    @property(Sprite)\r\n    icon: Sprite | null = null;\r\n\r\n    @property(Button)\r\n    btnClick: Button | null = null;\r\n\r\n    public guid: Long | undefined;\r\n\r\n    onButtonClick() {\r\n        if (!this.guid) {\r\n            return;\r\n        }\r\n        MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GAME_PVP_GET_AWARD, { game_pvp_get_award: { guid: this.guid } });\r\n    }\r\n\r\n    start() {\r\n        EventMgr.on(DataEvent.GamePvpGetAward, this.getAward, this)\r\n    }\r\n    private getAward() {\r\n    }\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n\r\n    }\r\n\r\n}\r\n\r\n\r\n"]}