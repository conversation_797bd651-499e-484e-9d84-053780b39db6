System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, assetManager, Component, instantiate, IBundleEntry, _crd, BundleName;

  function initBundle(bundleName) {
    return new Promise((resolve, reject) => {
      var bundleBaseUrl = "bundles/" + bundleName;
      assetManager.loadBundle(bundleBaseUrl, (err, bundle) => {
        if (err) {
          reject(err);
          return;
        }

        bundle.load("prefab/Entry", (err, entry) => {
          if (err) {
            reject(err);
            return;
          }

          var node = instantiate(entry);
          var entryScript = node.getComponent(IBundleEntry);
          entryScript.initEntry().then(() => {
            //node.destroy();
            resolve();
          }).catch(err => {
            reject(err);
          });
        });
      });
    });
  }

  _export({
    IBundleEntry: void 0,
    initBundle: initBundle
  });

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      assetManager = _cc.assetManager;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "31020YjYEBDxYGEzDici6Vu", "Bundle", undefined);

      __checkObsolete__(['assetManager', 'AssetManager', 'Component', 'instantiate', 'Prefab']);

      _export("BundleName", BundleName = /*#__PURE__*/function (BundleName) {
        BundleName["Common"] = "common";
        BundleName["Gm"] = "gm";
        BundleName["Home"] = "home";
        BundleName["HomePlane"] = "home_plane";
        BundleName["HomeTalent"] = "home_talent";
        BundleName["HomeShop"] = "home_shop";
        BundleName["HomeSkyIsland"] = "home_skyisland";
        BundleName["HomeStory"] = "home_story";
        BundleName["HomeTask"] = "home_task";
        return BundleName;
      }({}));

      _export("IBundleEntry", IBundleEntry = class IBundleEntry extends Component {});

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=4dc11ae5e71a03202791bfc45da9406d60055a26.js.map