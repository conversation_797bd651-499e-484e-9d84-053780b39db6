import { _decorator, Component, Node, Rect, UITransform, Vec3, view } from 'cc';
import { MainPlane } from '../../AutoGen/Luban/schema';
import { MyApp } from '../../MyApp';

const { ccclass, property } = _decorator;

@ccclass('PlaneRes')
export class PlaneRes extends Component {


    @property(Node)
    bulletLayer: Node | null = null;

    m_screenDatas: number[][] = []; // 屏幕数据
    m_config: MainPlane | null = null;
    worldPos = new Vec3();

    // start() {
    //     this.initPlaneById(70100);
    //     this.bulletLayer!.getWorldPosition(this.worldPos);
    // }

    // protected onDisable(): void {
    //     this.setFireEnable(false);
    //     this.bulletLayer?.removeAllChildren();
    // }

    // update(deltaTime: number) {
    //     this.checkRemoveBullet();
    // }

    // initPlaneById(planeId: number) {
    //     this.m_config = MyApp.lubanTables.TbMainPlane.get(planeId)!;
    //     this.changeScreenLv(1);
    //     this.setFireEnable(true);
    // }

    // /**
    //  * 启用或禁用火力
    //  * @param {boolean} enable 是否启用火力
    //  */
    // setFireEnable(enable: boolean) {
    //     for (let i = 0; i < this.m_fires.length; i++) {
    //         const fire = this.m_fires[i];
    //         fire.isEnabled = enable;
    //     }
    // }

    // /**
    // * 改变屏幕等级
    // * @param {number} level 屏幕等级
    // */
    // changeScreenLv(level: number) {
    //     if (!this.m_config) return;

    //     this.m_screenDatas = [];
    //     const attackData = this.m_config.shiftingatk1;

    //     for (let i = 0; i < attackData.length; i += 8) {
    //         const screenData = attackData.slice(i, i + 8);
    //         this.m_screenDatas.push(screenData);
    //     }


    //     this.m_screenDatas.forEach((data, index) => {
    //         if (this.m_fires[index] == null) {
    //             this.createAttackPoint(data);
    //         } else {
    //             this.changeScreen(index, data);
    //         }
    //     });

    //     for (let i = this.m_screenDatas.length; i < this.m_fires.length; i++) {
    //         this.changeScreen(i, []);
    //     }
    // }

    // /**
    //  * 移除所有火力点
    //  */
    // removeAllFire() {
    //     this.m_fires.forEach((fire) => {
    //         fire.setData([]);
    //     });
    // }


    // createAttackPoint(data: any[]) {
    //     const fireNode = new Node("fire");
    //     fireNode.parent = this.node;

    //     const fire = fireNode.addComponent(FireShells);
    //     fire.setData(data, null, this.bulletLayer);

    //     this.m_fires.push(fire);
    //     return fire;
    // }


    // /**
    //  * 改变屏幕上的火力点
    //  * @param {number} index 火力点索引
    //  * @param {Array|null} data 火力点数据
    //  */
    // changeScreen(index: number, data: any[]) {
    //     if (data == null) {
    //         if (index < this.m_fires.length) {
    //             const fire = this.m_fires[index];
    //             fire.setData([]);
    //             fire.node.active = false;
    //         }
    //     } else {
    //         if (index < this.m_fires.length) {
    //             const fire = this.m_fires[index];
    //             fire.node!.active = true;
    //             fire.setData(data, null, this.bulletLayer);
    //         } else {
    //             this.createAttackPoint(data);
    //         }
    //     }
    // }


    // checkRemoveBullet() {
    //     if (!this.bulletLayer) return;
    //     const bullets = this.bulletLayer.children;
    //     for (let i = bullets.length - 1; i >= 0; i--) {
    //         const bullet = bullets[i];
    //         const uiTransform = bullet.getComponent(UITransform);
    //         if (uiTransform) {
    //             const aabb = uiTransform.getBoundingBoxToWorld();
    //             if (this.isOutOfScreen(aabb)) {
    //                 bullet.destroy();
    //             }
    //         }
    //     }
    // }

    // isOutOfScreen(aabb: Rect): boolean {
    //     const visibleSize = view.getVisibleSize();
    //     const screenLeft = -200;
    //     const screenRight = visibleSize.width + 200;
    //     const screenTop = Math.min(this.worldPos.y + 100, visibleSize.height + 200);
    //     const screenBottom = -200;
    
    //     // 判断是否超出屏幕边界
    //     return (
    //         aabb.x + aabb.width < screenLeft || // 超出屏幕左边
    //         aabb.x > screenRight ||            // 超出屏幕右边
    //         aabb.y + aabb.height < screenBottom || // 超出屏幕下边
    //         aabb.y > screenTop                 // 超出屏幕上边
    //     );
    // }
}


