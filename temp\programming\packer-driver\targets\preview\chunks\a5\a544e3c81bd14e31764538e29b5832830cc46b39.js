System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, instantiate, Layout, ScrollView, UITransform, Vec3, ButtonPlus, List, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, DropDown;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../list/List", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      instantiate = _cc.instantiate;
      Layout = _cc.Layout;
      ScrollView = _cc.ScrollView;
      UITransform = _cc.UITransform;
      Vec3 = _cc.Vec3;
    }, function (_unresolved_2) {
      ButtonPlus = _unresolved_2.ButtonPlus;
    }, function (_unresolved_3) {
      List = _unresolved_3.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "0af159+QQpGq5jUg0sN8HBU", "DropDown", undefined);

      __checkObsolete__(['_decorator', 'Component', 'EventTouch', 'instantiate', 'Layout', 'Node', 'ScrollView', 'UITransform', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("DropDown", DropDown = (_dec = ccclass('DropDown'), _dec2 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec(_class = (_class2 = class DropDown extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "optionList", _descriptor, this);

          _initializerDefineProperty(this, "statusMarkNode", _descriptor2, this);

          this._currentOptKeyList = [];
          this._originOptKeyList = [];
          this._renderFunc = null;
          this._onClick = null;
          this._selectedKey = null;
          this._expandBtn = null;
        }

        get selectedKey() {
          return this._selectedKey;
        }

        onLoad() {
          this.statusMarkNode.addClick(this.onClick, this); //this.expandBtn.addClick(this.unExpand, this)
        }

        init(optKeyList, renderFunc, onClick) {
          this._originOptKeyList = [...optKeyList];
          this._renderFunc = renderFunc;
          this._onClick = onClick;
          this._selectedKey = this._originOptKeyList[0];
          this._currentOptKeyList = [...optKeyList.filter(v => v != this._selectedKey)];
          this.optionList.numItems = 1;
          this.statusMarkNode.node.angle = 0;
        }

        unExpand(isUpdate) {
          if (this._currentOptKeyList.length == 0) {
            return;
          }

          this.optionList.node.active = false;
          this.statusMarkNode.node.angle = 0;

          if (isUpdate) {
            this._renderFunc(this._expandBtn.node, this._selectedKey);
          }
        }

        expand() {
          if (this._currentOptKeyList.length == 0) {
            return;
          }

          this.optionList.node.active = true;
          this.statusMarkNode.node.angle = 90;

          this._renderFunc(this._expandBtn.node, this._selectedKey);

          this.optionList.numItems = this._currentOptKeyList.length;
        }

        onListRender(item, idx) {
          if (!item.name.startsWith("dwItem")) {
            var btn = item.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
              error: Error()
            }), ButtonPlus) : ButtonPlus);
            btn.addClick(this.onClick, this);
          }

          var optKey = this.selectedKey;

          if (this._currentOptKeyList.length > 0) {
            optKey = this._currentOptKeyList[idx];
          }

          var listPos = this.optionList.node.getPosition();

          this._renderFunc(item, optKey);

          if (!this._expandBtn) {
            this._expandBtn = instantiate(item).getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
              error: Error()
            }), ButtonPlus) : ButtonPlus);

            this._expandBtn.addClick(this.onClick, this);

            this._expandBtn.node.parent = this.optionList.node.parent;

            this._expandBtn.node.setWorldPosition(item.getWorldPosition());

            this._renderFunc(this._expandBtn.node, this._selectedKey);

            var itemSize = item.getComponent(UITransform).contentSize;
            var layout = this.optionList.getComponent(ScrollView).content.getComponent(Layout);
            var newPos = new Vec3(listPos.x, listPos.y - itemSize.height - layout.paddingTop, listPos.z);
            this.optionList.node.active = false;
            this.optionList.node.setPosition(newPos);
          }

          item.name = "dwItem_" + idx;
        }

        onClick(event) {
          var clickKey = "";
          var btn = event.target.getComponent(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
            error: Error()
          }), ButtonPlus) : ButtonPlus);

          if (btn.node.name.startsWith("dwItem") && this._currentOptKeyList.length > 0) {
            var idx = Number(btn.node.name.split("_")[1]);
            clickKey = this._currentOptKeyList[idx];
          } else {
            clickKey = this.selectedKey;
          }

          var isSameKey = clickKey == this._selectedKey;
          this._selectedKey = clickKey;

          var filtered = this._originOptKeyList.filter(item => item !== this._selectedKey);

          this._currentOptKeyList = [...filtered];

          if (this.optionList.node.active) {
            this.unExpand(!isSameKey);
          } else {
            this.expand();
          }

          if (!isSameKey) {
            this._onClick(this._selectedKey);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "optionList", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "statusMarkNode", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=a544e3c81bd14e31764538e29b5832830cc46b39.js.map