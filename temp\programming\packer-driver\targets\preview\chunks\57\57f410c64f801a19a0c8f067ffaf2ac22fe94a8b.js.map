{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/res/PlaneRes.ts"], "names": ["_decorator", "Component", "Node", "Vec3", "ccclass", "property", "PlaneRes", "m_screenDatas", "m_config", "worldPos"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,I,OAAAA,I;AAAyBC,MAAAA,I,OAAAA,I;;;;;;;;;OAInD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;0BAGjBM,Q,WADZF,OAAO,CAAC,UAAD,C,UAIHC,QAAQ,CAACH,IAAD,C,2BAJb,MACaI,QADb,SAC8BL,SAD9B,CACwC;AAAA;AAAA;;AAAA;;AAAA,eAMpCM,aANoC,GAMR,EANQ;AAMJ;AANI,eAOpCC,QAPoC,GAOP,IAPO;AAAA,eAQpCC,QARoC,GAQzB,IAAIN,IAAJ,EARyB;AAAA,UAUpC;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAjJoC,O;;;;;iBAIT,I", "sourcesContent": ["import { _decorator, Component, Node, Rect, UITransform, Vec3, view } from 'cc';\r\nimport { MainPlane } from '../../AutoGen/Luban/schema';\r\nimport { MyApp } from '../../MyApp';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('PlaneRes')\r\nexport class PlaneRes extends Component {\r\n\r\n\r\n    @property(Node)\r\n    bulletLayer: Node | null = null;\r\n\r\n    m_screenDatas: number[][] = []; // 屏幕数据\r\n    m_config: MainPlane | null = null;\r\n    worldPos = new Vec3();\r\n\r\n    // start() {\r\n    //     this.initPlaneById(70100);\r\n    //     this.bulletLayer!.getWorldPosition(this.worldPos);\r\n    // }\r\n\r\n    // protected onDisable(): void {\r\n    //     this.setFireEnable(false);\r\n    //     this.bulletLayer?.removeAllChildren();\r\n    // }\r\n\r\n    // update(deltaTime: number) {\r\n    //     this.checkRemoveBullet();\r\n    // }\r\n\r\n    // initPlaneById(planeId: number) {\r\n    //     this.m_config = MyApp.lubanTables.TbMainPlane.get(planeId)!;\r\n    //     this.changeScreenLv(1);\r\n    //     this.setFireEnable(true);\r\n    // }\r\n\r\n    // /**\r\n    //  * 启用或禁用火力\r\n    //  * @param {boolean} enable 是否启用火力\r\n    //  */\r\n    // setFireEnable(enable: boolean) {\r\n    //     for (let i = 0; i < this.m_fires.length; i++) {\r\n    //         const fire = this.m_fires[i];\r\n    //         fire.isEnabled = enable;\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    // * 改变屏幕等级\r\n    // * @param {number} level 屏幕等级\r\n    // */\r\n    // changeScreenLv(level: number) {\r\n    //     if (!this.m_config) return;\r\n\r\n    //     this.m_screenDatas = [];\r\n    //     const attackData = this.m_config.shiftingatk1;\r\n\r\n    //     for (let i = 0; i < attackData.length; i += 8) {\r\n    //         const screenData = attackData.slice(i, i + 8);\r\n    //         this.m_screenDatas.push(screenData);\r\n    //     }\r\n\r\n\r\n    //     this.m_screenDatas.forEach((data, index) => {\r\n    //         if (this.m_fires[index] == null) {\r\n    //             this.createAttackPoint(data);\r\n    //         } else {\r\n    //             this.changeScreen(index, data);\r\n    //         }\r\n    //     });\r\n\r\n    //     for (let i = this.m_screenDatas.length; i < this.m_fires.length; i++) {\r\n    //         this.changeScreen(i, []);\r\n    //     }\r\n    // }\r\n\r\n    // /**\r\n    //  * 移除所有火力点\r\n    //  */\r\n    // removeAllFire() {\r\n    //     this.m_fires.forEach((fire) => {\r\n    //         fire.setData([]);\r\n    //     });\r\n    // }\r\n\r\n\r\n    // createAttackPoint(data: any[]) {\r\n    //     const fireNode = new Node(\"fire\");\r\n    //     fireNode.parent = this.node;\r\n\r\n    //     const fire = fireNode.addComponent(FireShells);\r\n    //     fire.setData(data, null, this.bulletLayer);\r\n\r\n    //     this.m_fires.push(fire);\r\n    //     return fire;\r\n    // }\r\n\r\n\r\n    // /**\r\n    //  * 改变屏幕上的火力点\r\n    //  * @param {number} index 火力点索引\r\n    //  * @param {Array|null} data 火力点数据\r\n    //  */\r\n    // changeScreen(index: number, data: any[]) {\r\n    //     if (data == null) {\r\n    //         if (index < this.m_fires.length) {\r\n    //             const fire = this.m_fires[index];\r\n    //             fire.setData([]);\r\n    //             fire.node.active = false;\r\n    //         }\r\n    //     } else {\r\n    //         if (index < this.m_fires.length) {\r\n    //             const fire = this.m_fires[index];\r\n    //             fire.node!.active = true;\r\n    //             fire.setData(data, null, this.bulletLayer);\r\n    //         } else {\r\n    //             this.createAttackPoint(data);\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n\r\n    // checkRemoveBullet() {\r\n    //     if (!this.bulletLayer) return;\r\n    //     const bullets = this.bulletLayer.children;\r\n    //     for (let i = bullets.length - 1; i >= 0; i--) {\r\n    //         const bullet = bullets[i];\r\n    //         const uiTransform = bullet.getComponent(UITransform);\r\n    //         if (uiTransform) {\r\n    //             const aabb = uiTransform.getBoundingBoxToWorld();\r\n    //             if (this.isOutOfScreen(aabb)) {\r\n    //                 bullet.destroy();\r\n    //             }\r\n    //         }\r\n    //     }\r\n    // }\r\n\r\n    // isOutOfScreen(aabb: Rect): boolean {\r\n    //     const visibleSize = view.getVisibleSize();\r\n    //     const screenLeft = -200;\r\n    //     const screenRight = visibleSize.width + 200;\r\n    //     const screenTop = Math.min(this.worldPos.y + 100, visibleSize.height + 200);\r\n    //     const screenBottom = -200;\r\n    \r\n    //     // 判断是否超出屏幕边界\r\n    //     return (\r\n    //         aabb.x + aabb.width < screenLeft || // 超出屏幕左边\r\n    //         aabb.x > screenRight ||            // 超出屏幕右边\r\n    //         aabb.y + aabb.height < screenBottom || // 超出屏幕下边\r\n    //         aabb.y > screenTop                 // 超出屏幕上边\r\n    //     );\r\n    // }\r\n}\r\n\r\n\r\n"]}