System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, List, FriendCellUI, _dec, _dec2, _class, _class2, _descriptor, _crd, ccclass, property, FriendAddUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../../common/components/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFriendCellUI(extras) {
    _reporterNs.report("FriendCellUI", "./FriendCellUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
    }, function (_unresolved_2) {
      List = _unresolved_2.default;
    }, function (_unresolved_3) {
      FriendCellUI = _unresolved_3.FriendCellUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "03b39MjN15F/bGCB8dfipPB", "FriendAddUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("FriendAddUI", FriendAddUI = (_dec = ccclass('FriendAddUI'), _dec2 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec(_class = (_class2 = class FriendAddUI extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "list", _descriptor, this);
        }

        start() {
          this.list.node.active = true;
          this.list.numItems = 10;
        }

        update(deltaTime) {}

        onListRender(listItem, row) {
          var cell = listItem.getComponent(_crd && FriendCellUI === void 0 ? (_reportPossibleCrUseOfFriendCellUI({
            error: Error()
          }), FriendCellUI) : FriendCellUI);

          if (cell !== null) {
            cell.setType(2);
            cell.txtName.string = "小师妹：" + (row + 11);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "list", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=7e8a501d506b5bc70c4ad24a4365c9105171c8a5.js.map