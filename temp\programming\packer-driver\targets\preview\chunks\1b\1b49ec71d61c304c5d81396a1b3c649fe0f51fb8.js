System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, easing, Label, Node, tween, ButtonPlus, _dec, _dec2, _dec3, _dec4, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, WheelSpinnerUI;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "../common/components/button/ButtonPlus", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      easing = _cc.easing;
      Label = _cc.Label;
      Node = _cc.Node;
      tween = _cc.tween;
    }, function (_unresolved_2) {
      ButtonPlus = _unresolved_2.ButtonPlus;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "60985kNDC1CJbTv5Vdbto5y", "WheelSpinnerUI", undefined);

      __checkObsolete__(['_decorator', 'Component', 'easing', 'Label', 'Node', 'tween']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("WheelSpinnerUI", WheelSpinnerUI = (_dec = ccclass('WheelSpinnerUI'), _dec2 = property({
        type: Node
      }), _dec3 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec4 = property(Label), _dec(_class = (_class2 = class WheelSpinnerUI extends Component {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "wheel", _descriptor, this);

          // 转盘节点
          _initializerDefineProperty(this, "segments", _descriptor2, this);

          // 转盘分块数量
          _initializerDefineProperty(this, "button", _descriptor3, this);

          _initializerDefineProperty(this, "angleLabel", _descriptor4, this);

          this.isSpinning = false;
        }

        // 是否正在转动
        onLoad() {
          this.button.addClick(this.onButtonClick, this);
        } // 开始转动（顺时针）


        spin2(targetIndex) {
          if (this.isSpinning) return;
          this.isSpinning = true; // 重置角度为 0

          this.wheel.angle = 0; // 计算目标角度（顺时针为负方向）

          var anglePerSegment = 360 / this.segments;
          var targetAngle = -360 * 15 - anglePerSegment * targetIndex; // 多转几圈（顺时针）

          this.angleLabel.string = targetAngle.toFixed(2) + "\xB0"; // 使用 Tween 实现缓动动画

          tween(this.wheel).to(3.5, {
            angle: targetAngle
          }, {
            easing: easing.cubicOut
          }).call(() => {
            this.isSpinning = false;
            console.log("\u505C\u6B62\u5728\u7B2C " + (targetIndex + 1) + " \u683C");
          }).start();
        } // 测试用：点击按钮触发转动


        onButtonClick() {
          var randomIndex = Math.floor(Math.random() * this.segments); // 随机选择一个格子

          this.spin2(randomIndex);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "wheel", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "segments", [property], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return 7;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "button", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "angleLabel", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=1b49ec71d61c304c5d81396a1b3c649fe0f51fb8.js.map