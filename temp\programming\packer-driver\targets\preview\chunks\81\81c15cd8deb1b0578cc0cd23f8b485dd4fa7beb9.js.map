{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/bullet/Emitter.ts"], "names": ["_decorator", "misc", "Prefab", "assetManager", "EDITOR", "BulletProperty", "Bullet", "EmitterData", "BulletData", "ObjectPool", "BulletSystem", "EventGroupContext", "PropertyContainerComponent", "ccclass", "executeInEditMode", "property", "disallowMultiple", "menu", "degreesToRadians", "radiansToDegrees", "eEmitterStatus", "eEmitterProp", "Emitter", "displayName", "type", "onBulletCreatedCallback", "onEmitterStatusChangedCallback", "isActive", "isOnlyInScreen", "isPreWarm", "isLoop", "initialDelay", "preWarmDuration", "emitBulletID", "emitDuration", "emitInterval", "emitPower", "loopInterval", "perEmitCount", "perEmitInterval", "perEmitOffsetX", "angle", "count", "arc", "radius", "totalElapsedTime", "bulletProp", "eventGroups", "_status", "None", "_statusElapsedTime", "_isEmitting", "_nextEmitTime", "_bulletPrefab", "_prewarmEffectPrefab", "_emitEffectPrefab", "_entity", "_perEmitBulletQueue", "isEmitting", "status", "statusElapsedTime", "onLoad", "createProperties", "createEventGroups", "resetProperties", "onLostFocusInEditor", "updatePropertiesInEditor", "resetInEditor", "changeStatus", "value", "emitterData", "bulletID", "eval", "notifyAll", "setActive", "active", "notify", "setEntity", "entity", "getEntity", "clear", "addProperty", "IsActive", "TotalElapsedTime", "EmitBulletID", "IsOnlyInScreen", "IsPreWarm", "IsLoop", "InitialDelay", "PreWarmDuration", "EmitDuration", "EmitInterval", "EmitPower", "LoopInterval", "PerEmitCount", "PerEmitInterval", "PerEmitOffsetX", "<PERSON><PERSON>", "Count", "Arc", "<PERSON><PERSON>", "on", "onCreateEmitter", "onDestroyEmitter", "eventGroupData", "length", "ctx", "emitter", "eventGroup", "createEmitterEventGroup", "resetFromData", "bulletData", "oldStatus", "Emitting", "Prewarm", "for<PERSON>ach", "group", "start", "stop", "scheduleNextEmit", "startEmitting", "stopEmitting", "unscheduleAllCallbacks", "canEmit", "emit", "j", "targetTime", "i", "push", "index", "perEmitIndex", "emitSingle", "processPerEmitQueue", "nextBullet", "shift", "tryEmit", "direction", "getSpawnDirection", "position", "getSpawnPosition", "createBullet", "angleOffset", "radian", "x", "Math", "cos", "y", "sin", "getEmitOffsetX", "interval", "stepsFromMiddle", "floor", "ceil", "bulletPrefab", "createBulletInEditor", "console", "warn", "bullet", "instantiateBullet", "onCreateBullet", "emitterPos", "node", "getWorldPosition", "setWorldPosition", "z", "prop", "speedAngle", "atan2", "speed", "resetEventGroups", "prefabPath", "Editor", "Message", "request", "then", "uuid", "loadAny", "err", "prefab", "error", "bulletNode", "getNode", "bulletParent", "getComponent", "destroy", "name", "kBulletNameInEditor", "playEffect", "rotation", "duration", "effectNode", "setWorldRotation", "scheduleOnce", "returnNode", "isInScreen", "tick", "deltaTime", "updateStatusNone", "updateStatusPrewarm", "updateStatusEmitting", "Loop<PERSON>ndReached", "updateStatusLoopEndReached", "Completed", "updateStatusCompleted", "wasEmitting"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,I,OAAAA,I;AAAoCC,MAAAA,M,OAAAA,M;AAA2BC,MAAAA,Y,OAAAA,Y;;AAC3EC,MAAAA,M,UAAAA,M;;AACAC,MAAAA,c,iBAAAA,c;AAAgBC,MAAAA,M,iBAAAA,M;;AAChBC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Y,iBAAAA,Y;;AACYC,MAAAA,iB,iBAAAA,iB;;AACFC,MAAAA,0B,iBAAAA,0B;;;;;;;;;OAGb;AAAEC,QAAAA,OAAF;AAAWC,QAAAA,iBAAX;AAA8BC,QAAAA,QAA9B;AAAwCC,QAAAA,gBAAxC;AAA0DC,QAAAA;AAA1D,O,GAAoEjB,U;OACpE;AAAEkB,QAAAA,gBAAF;AAAoBC,QAAAA;AAApB,O,GAAyClB,I;;gCAEnCmB,c,0BAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;AAAAA,QAAAA,c,CAAAA,c;eAAAA,c;cAIZ;;;8BACYC,Y,0BAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;AAAAA,QAAAA,Y,CAAAA,Y;eAAAA,Y;;;yBAgBCC,O,WALZT,OAAO,CAAC,SAAD,C,UAEPI,IAAI,CAAC,UAAD,C,UACJH,iBAAiB,CAAC,IAAD,C,UACjBE,gBAAgB,CAAC,IAAD,C,UAKZD,QAAQ,CAAC;AAACQ,QAAAA,WAAW,EAAE;AAAd,OAAD,C,UAGRR,QAAQ,CAAC;AAACS,QAAAA,IAAI,EAACtB,MAAN;AAAcqB,QAAAA,WAAW,EAAE;AAA3B,OAAD,C,UAGRR,QAAQ,CAAC;AAACS,QAAAA,IAAI;AAAA;AAAA,sCAAL;AAAoBD,QAAAA,WAAW,EAAE;AAAjC,OAAD,C,UAGRR,QAAQ,CAAC;AAACS,QAAAA,IAAI;AAAA;AAAA,oCAAL;AAAmBD,QAAAA,WAAW,EAAE;AAAhC,OAAD,C,mFAlBb,MAKaD,OALb;AAAA;AAAA,oEAKsE;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAgBlE;AAhBkE,eAiBlEG,uBAjBkE,GAiBR,IAjBQ;AAAA,eAkBlEC,8BAlBkE,GAkBM,IAlBN;AAoBlE;AApBkE,eAqB3DC,QArB2D;AAAA,eAsB3DC,cAtB2D;AAAA,eAuB3DC,SAvB2D;AAAA,eAwB3DC,MAxB2D;AAAA,eAyB3DC,YAzB2D;AAAA,eA0B3DC,eA1B2D;AAAA,eA2B3DC,YA3B2D;AAAA,eA4B3DC,YA5B2D;AAAA,eA6B3DC,YA7B2D;AAAA,eA8B3DC,SA9B2D;AAAA,eA+B3DC,YA/B2D;AAAA,eAgC3DC,YAhC2D;AAAA,eAiC3DC,eAjC2D;AAAA,eAkC3DC,cAlC2D;AAAA,eAmC3DC,KAnC2D;AAAA,eAoC3DC,KApC2D;AAAA,eAqC3DC,GArC2D;AAAA,eAsC3DC,MAtC2D;AAAA,eAuC3DC,gBAvC2D;AAwClE;AAxCkE,eAyC3DC,UAzC2D;AA2ClE;AA3CkE,eA4C3DC,WA5C2D,GA4C/B,EA5C+B;AA8ClE;AA9CkE,eA+CxDC,OA/CwD,GA+C9B5B,cAAc,CAAC6B,IA/Ce;AAAA,eAgDxDC,kBAhDwD,GAgD3B,CAhD2B;AAAA,eAiDxDC,WAjDwD,GAiDjC,KAjDiC;AAAA,eAkDxDC,aAlDwD,GAkDhC,CAlDgC;AAAA,eAmDxDC,aAnDwD,GAmD3B,IAnD2B;AAAA,eAoDxDC,oBApDwD,GAoDpB,IApDoB;AAAA,eAqDxDC,iBArDwD,GAqDvB,IArDuB;AAAA,eAsDxDC,OAtDwD,GAsDjC,IAtDiC;AAwDlE;AAxDkE,eAyDxDC,mBAzDwD,GAyDgC,EAzDhC;AAAA;;AA2DpD,YAAVC,UAAU,GAAY;AAAE,iBAAO,KAAKP,WAAZ;AAA0B;;AAC5C,YAANQ,MAAM,GAAmB;AAAE,iBAAO,KAAKX,OAAZ;AAAsB;;AAChC,YAAjBY,iBAAiB,GAAW;AAAE,iBAAO,KAAKV,kBAAZ;AAAiC;;AAEzDW,QAAAA,MAAM,GAAU;AACtB,eAAKC,gBAAL;AACA,eAAKC,iBAAL,GAFsB,CAItB;;AACA,eAAKC,eAAL;AACH,SArEiE,CAuElE;;;AACOC,QAAAA,mBAAmB,GAAS;AAC/B,eAAKC,wBAAL;AACA,eAAKH,iBAAL;AACH;;AAEMI,QAAAA,aAAa,GAAG;AACnB,eAAKH,eAAL;AACA,eAAKI,YAAL,CAAkBhD,cAAc,CAAC6B,IAAjC;AACA,eAAKE,WAAL,GAAmB,KAAnB;AACA,eAAKN,gBAAL,CAAsBwB,KAAtB,GAA8B,CAA9B;AACH;;AAEMH,QAAAA,wBAAwB,GAAG;AAC9B,cAAI,CAAC,KAAKI,WAAV,EAAuB;AAEvB,eAAK3C,QAAL,CAAc0C,KAAd,GAAsB,IAAtB;AACA,eAAKpC,YAAL,CAAkBoC,KAAlB,GAA0B,KAAKE,QAA/B;AACA,eAAK3C,cAAL,CAAoByC,KAApB,GAA4B,KAAKC,WAAL,CAAiB1C,cAA7C;AACA,eAAKC,SAAL,CAAewC,KAAf,GAAuB,KAAKC,WAAL,CAAiBzC,SAAxC;AACA,eAAKC,MAAL,CAAYuC,KAAZ,GAAoB,KAAKC,WAAL,CAAiBxC,MAArC;AAEA,eAAKC,YAAL,CAAkBsC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBvC,YAAjB,CAA8ByC,IAA9B,EAA1B;AACA,eAAKxC,eAAL,CAAqBqC,KAArB,GAA6B,KAAKC,WAAL,CAAiBtC,eAAjB,CAAiCwC,IAAjC,EAA7B;AACA,eAAKtC,YAAL,CAAkBmC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBpC,YAAjB,CAA8BsC,IAA9B,EAA1B;AACA,eAAKrC,YAAL,CAAkBkC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBnC,YAAjB,CAA8BqC,IAA9B,EAA1B;AACA,eAAKpC,SAAL,CAAeiC,KAAf,GAAuB,KAAKC,WAAL,CAAiBlC,SAAjB,CAA2BoC,IAA3B,EAAvB;AACA,eAAKnC,YAAL,CAAkBgC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBjC,YAAjB,CAA8BmC,IAA9B,EAA1B;AACA,eAAKlC,YAAL,CAAkB+B,KAAlB,GAA0B,KAAKC,WAAL,CAAiBhC,YAAjB,CAA8BkC,IAA9B,EAA1B;AACA,eAAKjC,eAAL,CAAqB8B,KAArB,GAA6B,KAAKC,WAAL,CAAiB/B,eAAjB,CAAiCiC,IAAjC,EAA7B;AACA,eAAKhC,cAAL,CAAoB6B,KAApB,GAA4B,KAAKC,WAAL,CAAiB9B,cAAjB,CAAgCgC,IAAhC,EAA5B;AACA,eAAK/B,KAAL,CAAW4B,KAAX,GAAmB,KAAKC,WAAL,CAAiB7B,KAAjB,CAAuB+B,IAAvB,EAAnB;AACA,eAAK9B,KAAL,CAAW2B,KAAX,GAAmB,KAAKC,WAAL,CAAiB5B,KAAjB,CAAuB8B,IAAvB,EAAnB;AACA,eAAK7B,GAAL,CAAS0B,KAAT,GAAiB,KAAKC,WAAL,CAAiB3B,GAAjB,CAAqB6B,IAArB,EAAjB;AACA,eAAK5B,MAAL,CAAYyB,KAAZ,GAAoB,KAAKC,WAAL,CAAiB1B,MAAjB,CAAwB4B,IAAxB,EAApB;AAEA,eAAKC,SAAL,CAAe,IAAf;AACH,SA5GiE,CA6GlE;AAEA;;;AACOC,QAAAA,SAAS,CAACC,MAAD,EAAkB;AAC9B,eAAKhD,QAAL,CAAc0C,KAAd,GAAsBM,MAAtB;AACA,eAAKhD,QAAL,CAAciD,MAAd;AACH;;AAEMC,QAAAA,SAAS,CAACC,MAAD,EAAiB;AAC7B,eAAKtB,OAAL,GAAesB,MAAf;AACH;;AAEMC,QAAAA,SAAS,GAAkB;AAC9B,iBAAO,KAAKvB,OAAZ;AACH;;AAESM,QAAAA,gBAAgB,GAAG;AACzB,eAAKkB,KAAL;AAEA,eAAKrD,QAAL,GAAgB,KAAKsD,WAAL,CAAiB5D,YAAY,CAAC6D,QAA9B,EAAwC,KAAxC,CAAhB;AACA,eAAKrC,gBAAL,GAAwB,KAAKoC,WAAL,CAAiB5D,YAAY,CAAC8D,gBAA9B,EAAgD,CAAhD,CAAxB;AACA,eAAKlD,YAAL,GAAoB,KAAKgD,WAAL,CAAiB5D,YAAY,CAAC+D,YAA9B,EAA4C,KAAKb,QAAjD,CAApB;AACA,eAAK3C,cAAL,GAAsB,KAAKqD,WAAL,CAAiB5D,YAAY,CAACgE,cAA9B,EAA8C,IAA9C,CAAtB;AACA,eAAKxD,SAAL,GAAiB,KAAKoD,WAAL,CAAiB5D,YAAY,CAACiE,SAA9B,EAAyC,IAAzC,CAAjB;AACA,eAAKxD,MAAL,GAAc,KAAKmD,WAAL,CAAiB5D,YAAY,CAACkE,MAA9B,EAAsC,IAAtC,CAAd;AAEA,eAAKxD,YAAL,GAAoB,KAAKkD,WAAL,CAAiB5D,YAAY,CAACmE,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKxD,eAAL,GAAuB,KAAKiD,WAAL,CAAiB5D,YAAY,CAACoE,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAKvD,YAAL,GAAoB,KAAK+C,WAAL,CAAiB5D,YAAY,CAACqE,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKvD,YAAL,GAAoB,KAAK8C,WAAL,CAAiB5D,YAAY,CAACsE,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKvD,SAAL,GAAiB,KAAK6C,WAAL,CAAiB5D,YAAY,CAACuE,SAA9B,EAAyC,CAAzC,CAAjB;AACA,eAAKvD,YAAL,GAAoB,KAAK4C,WAAL,CAAiB5D,YAAY,CAACwE,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKvD,YAAL,GAAoB,KAAK2C,WAAL,CAAiB5D,YAAY,CAACyE,YAA9B,EAA4C,CAA5C,CAApB;AACA,eAAKvD,eAAL,GAAuB,KAAK0C,WAAL,CAAiB5D,YAAY,CAAC0E,eAA9B,EAA+C,CAA/C,CAAvB;AACA,eAAKvD,cAAL,GAAsB,KAAKyC,WAAL,CAAiB5D,YAAY,CAAC2E,cAA9B,EAA8C,CAA9C,CAAtB;AACA,eAAKvD,KAAL,GAAa,KAAKwC,WAAL,CAAiB5D,YAAY,CAAC4E,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAKvD,KAAL,GAAa,KAAKuC,WAAL,CAAiB5D,YAAY,CAAC6E,KAA9B,EAAqC,CAArC,CAAb;AACA,eAAKvD,GAAL,GAAW,KAAKsC,WAAL,CAAiB5D,YAAY,CAAC8E,GAA9B,EAAmC,CAAnC,CAAX;AACA,eAAKvD,MAAL,GAAc,KAAKqC,WAAL,CAAiB5D,YAAY,CAAC+E,MAA9B,EAAsC,CAAtC,CAAd,CAtByB,CAwBzB;;AACA,eAAKtD,UAAL,GAAkB;AAAA;AAAA,iDAAlB,CAzByB,CA2BzB;;AACA,eAAKb,YAAL,CAAkBoE,EAAlB,CAAsBhC,KAAD,IAAW;AAC5B;AACA,iBAAKhB,aAAL,GAAqB,IAArB;AACH,WAHD;AAIA,eAAK1B,QAAL,CAAc0E,EAAd,CAAkBhC,KAAD,IAAW;AACxB,gBAAIA,KAAJ,EAAW;AACP;AAAA;AAAA,gDAAaiC,eAAb,CAA6B,IAA7B;AACH,aAFD,MAEO;AACH;AAAA;AAAA,gDAAaC,gBAAb,CAA8B,IAA9B;AACH;AACJ,WAND;AAOH;;AAESxC,QAAAA,iBAAiB,GAAG;AAC1B,cAAI,CAAC,KAAKO,WAAN,IAAqB,KAAKA,WAAL,CAAiBkC,cAAjB,CAAgCC,MAAhC,IAA0C,CAAnE,EAAsE;AAEtE,eAAK1D,WAAL,GAAmB,EAAnB;AACA,cAAI2D,GAAG,GAAG;AAAA;AAAA,uDAAV;AACAA,UAAAA,GAAG,CAACC,OAAJ,GAAc,IAAd;;AACA,eAAK,IAAMC,UAAX,IAAyB,KAAKtC,WAAL,CAAiBkC,cAA1C,EAA0D;AACtD;AAAA;AAAA,8CAAaK,uBAAb,CAAqCH,GAArC,EAA0CE,UAA1C;AACH;AACJ,SA/KiE,CAiLlE;;;AACU5C,QAAAA,eAAe,GAAG;AACxB,cAAI,CAAC,KAAKM,WAAV,EAAuB;AAEvB,eAAKzB,gBAAL,CAAsBwB,KAAtB,GAA8B,CAA9B;AACA,eAAK1C,QAAL,CAAc0C,KAAd,GAAsB,KAAtB;AACA,eAAKpC,YAAL,CAAkBoC,KAAlB,GAA0B,KAAKE,QAA/B;AACA,eAAK3C,cAAL,CAAoByC,KAApB,GAA4B,KAAKC,WAAL,CAAiB1C,cAA7C;AACA,eAAKC,SAAL,CAAewC,KAAf,GAAuB,KAAKC,WAAL,CAAiBzC,SAAxC;AACA,eAAKC,MAAL,CAAYuC,KAAZ,GAAoB,KAAKC,WAAL,CAAiBxC,MAArC;AAEA,eAAKC,YAAL,CAAkBsC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBvC,YAAjB,CAA8ByC,IAA9B,EAA1B;AACA,eAAKxC,eAAL,CAAqBqC,KAArB,GAA6B,KAAKC,WAAL,CAAiBtC,eAAjB,CAAiCwC,IAAjC,EAA7B;AACA,eAAKtC,YAAL,CAAkBmC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBpC,YAAjB,CAA8BsC,IAA9B,EAA1B;AACA,eAAKrC,YAAL,CAAkBkC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBnC,YAAjB,CAA8BqC,IAA9B,EAA1B;AACA,eAAKpC,SAAL,CAAeiC,KAAf,GAAuB,KAAKC,WAAL,CAAiBlC,SAAjB,CAA2BoC,IAA3B,EAAvB;AACA,eAAKnC,YAAL,CAAkBgC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBjC,YAAjB,CAA8BmC,IAA9B,EAA1B;AACA,eAAKlC,YAAL,CAAkB+B,KAAlB,GAA0B,KAAKC,WAAL,CAAiBhC,YAAjB,CAA8BkC,IAA9B,EAA1B;AACA,eAAKjC,eAAL,CAAqB8B,KAArB,GAA6B,KAAKC,WAAL,CAAiB/B,eAAjB,CAAiCiC,IAAjC,EAA7B;AACA,eAAKhC,cAAL,CAAoB6B,KAApB,GAA4B,KAAKC,WAAL,CAAiB9B,cAAjB,CAAgCgC,IAAhC,EAA5B;AACA,eAAK/B,KAAL,CAAW4B,KAAX,GAAmB,KAAKC,WAAL,CAAiB7B,KAAjB,CAAuB+B,IAAvB,EAAnB;AACA,eAAK9B,KAAL,CAAW2B,KAAX,GAAmB,KAAKC,WAAL,CAAiB5B,KAAjB,CAAuB8B,IAAvB,EAAnB;AACA,eAAK7B,GAAL,CAAS0B,KAAT,GAAiB,KAAKC,WAAL,CAAiB3B,GAAjB,CAAqB6B,IAArB,EAAjB;AACA,eAAK5B,MAAL,CAAYyB,KAAZ,GAAoB,KAAKC,WAAL,CAAiB1B,MAAjB,CAAwB4B,IAAxB,EAApB;AAEA,eAAK1B,UAAL,CAAgBgE,aAAhB,CAA8B,KAAKC,UAAnC;AAEA,eAAKtC,SAAL,CAAe,IAAf;AACH;AAED;AACJ;AACA;;;AACIL,QAAAA,YAAY,CAACT,MAAD,EAAyB;AACjC,cAAI,KAAKX,OAAL,KAAiBW,MAArB,EAA6B;AAE7B,cAAMqD,SAAS,GAAG,KAAKhE,OAAvB;AACA,eAAKA,OAAL,GAAeW,MAAf;AACA,eAAKT,kBAAL,GAA0B,CAA1B;AACA,eAAKE,aAAL,GAAqB,CAArB,CANiC,CAOjC;;AACA,eAAKK,mBAAL,GAA2B,EAA3B;;AAEA,cAAIE,MAAM,KAAKvC,cAAc,CAAC6F,QAA1B,IAAsCtD,MAAM,KAAKvC,cAAc,CAAC8F,OAApE,EAA6E;AACzE,gBAAI,KAAKnE,WAAL,CAAiB0D,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,mBAAK1D,WAAL,CAAiBoE,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACC,KAAN,EAAlC;AACH;AACJ,WAJD,MAKK;AACD,gBAAI,KAAKtE,WAAL,CAAiB0D,MAAjB,GAA0B,CAA9B,EAAiC;AAC7B,mBAAK1D,WAAL,CAAiBoE,OAAjB,CAAyBC,KAAK,IAAIA,KAAK,CAACE,IAAN,EAAlC;AACH;AACJ;;AAED,cAAI,KAAK5F,8BAAL,IAAuC,IAA3C,EACA;AACI,iBAAKA,8BAAL,CAAoC,IAApC,EAA0CsF,SAA1C,EAAqDrD,MAArD;AACH;AACJ;;AAES4D,QAAAA,gBAAgB,GAAG;AACzB;AACA,eAAKpF,YAAL,CAAkBkC,KAAlB,GAA0B,KAAKC,WAAL,CAAiBnC,YAAjB,CAA8BqC,IAA9B,EAA1B,CAFyB,CAIzB;;AACA,eAAKpB,aAAL,GAAqB,KAAKF,kBAAL,GAA0B,KAAKf,YAAL,CAAkBkC,KAAjE;AACH;;AAESmD,QAAAA,aAAa,GAAG;AACtB,eAAKrE,WAAL,GAAmB,IAAnB,CADsB,CAEtB;AACA;AACH;;AAESsE,QAAAA,YAAY,GAAG;AACrB,eAAKtE,WAAL,GAAmB,KAAnB,CADqB,CAErB;;AACA,eAAKM,mBAAL,GAA2B,EAA3B;AACA,eAAKiE,sBAAL;AACH;;AAESC,QAAAA,OAAO,GAAY;AACzB;AACA;AACA,iBAAO,IAAP;AACH;;AAESC,QAAAA,IAAI,GAAS;AACnB;AACA,eAAKtF,YAAL,CAAkB+B,KAAlB,GAA0B,KAAKC,WAAL,CAAiBhC,YAAjB,CAA8BkC,IAA9B,EAA1B;;AAEA,cAAI,KAAKjC,eAAL,CAAqB8B,KAArB,GAA6B,CAAjC,EAAoC;AAChC;AACA,iBAAK,IAAIwD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKvF,YAAL,CAAkB+B,KAAtC,EAA6CwD,CAAC,EAA9C,EAAkD;AAC9C,mBAAKtF,eAAL,CAAqB8B,KAArB,GAA6B,KAAKC,WAAL,CAAiB/B,eAAjB,CAAiCiC,IAAjC,EAA7B;AACA,kBAAMsD,UAAU,GAAG,KAAK5E,kBAAL,GAA2B,KAAKX,eAAL,CAAqB8B,KAArB,GAA6BwD,CAA3E;;AACA,mBAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKrF,KAAL,CAAW2B,KAA/B,EAAsC0D,CAAC,EAAvC,EAA2C;AACvC,qBAAKtE,mBAAL,CAAyBuE,IAAzB,CAA8B;AAC1BC,kBAAAA,KAAK,EAAEF,CADmB;AAE1BG,kBAAAA,YAAY,EAAEL,CAFY;AAG1BC,kBAAAA,UAAU,EAAEA;AAHc,iBAA9B;AAKH;AACJ;AACJ,WAbD,MAcK;AACD;AACA,iBAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKrF,KAAL,CAAW2B,KAA/B,EAAsC0D,EAAC,EAAvC,EAA2C;AACvC,mBAAK,IAAIF,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAG,KAAKvF,YAAL,CAAkB+B,KAAtC,EAA6CwD,EAAC,EAA9C,EAAkD;AAC9C,qBAAKM,UAAL,CAAgBJ,EAAhB,EAAmBF,EAAnB;AACH;AACJ;AACJ;AACJ;;AAESO,QAAAA,mBAAmB,GAAS;AAClC;AACA,iBAAO,KAAK3E,mBAAL,CAAyBgD,MAAzB,GAAkC,CAAzC,EAA4C;AACxC,gBAAM4B,UAAU,GAAG,KAAK5E,mBAAL,CAAyB,CAAzB,CAAnB,CADwC,CAGxC;;AACA,gBAAI,KAAKP,kBAAL,IAA2BmF,UAAU,CAACP,UAA1C,EAAsD;AAClD;AACA,mBAAKrE,mBAAL,CAAyB6E,KAAzB;;AACA,mBAAKH,UAAL,CAAgBE,UAAU,CAACJ,KAA3B,EAAkCI,UAAU,CAACH,YAA7C;AACH,aAJD,MAIO;AACH;AACA;AACH;AACJ;AACJ;;AAESK,QAAAA,OAAO,GAAY;AACzB,cAAI,KAAKZ,OAAL,EAAJ,EAAoB;AAChB,iBAAKC,IAAL;AACA,mBAAO,IAAP;AACH;;AACD,iBAAO,KAAP;AACH;;AAESO,QAAAA,UAAU,CAACF,KAAD,EAAeC,YAAf,EAAqC;AACrD,cAAMM,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB;AACA,cAAMS,QAAQ,GAAG,KAAKC,gBAAL,CAAsBV,KAAtB,EAA6BC,YAA7B,CAAjB;AACA,eAAKU,YAAL,CAAkBJ,SAAlB,EAA6BE,QAA7B;AACH;AAED;AACJ;AACA;AACA;AACA;;;AACID,QAAAA,iBAAiB,CAACR,KAAD,EAA0C;AACvD;AACA,cAAMY,WAAW,GAAG,KAAKnG,KAAL,CAAW2B,KAAX,GAAmB,CAAnB,GAAwB,KAAK1B,GAAL,CAAS0B,KAAT,IAAkB,KAAK3B,KAAL,CAAW2B,KAAX,GAAmB,CAArC,CAAD,GAA4C4D,KAA5C,GAAoD,KAAKtF,GAAL,CAAS0B,KAAT,GAAiB,CAA5F,GAAgG,CAApH;AACA,cAAMyE,MAAM,GAAG5H,gBAAgB,CAAC,KAAKuB,KAAL,CAAW4B,KAAX,GAAmBwE,WAApB,CAA/B;AAEA,iBAAO;AACHE,YAAAA,CAAC,EAAEC,IAAI,CAACC,GAAL,CAASH,MAAT,CADA;AAEHI,YAAAA,CAAC,EAAEF,IAAI,CAACG,GAAL,CAASL,MAAT;AAFA,WAAP;AAIH;AAED;AACJ;AACA;AACA;AACA;AACA;;;AACIH,QAAAA,gBAAgB,CAACV,KAAD,EAAgBC,YAAhB,EAAgE;AAC5E;AACA;AACA,cAAMkB,cAAc,GAAG,CAAClB,YAAD,EAAuB5F,YAAvB,EAA6CE,cAA7C,KAAwE;AAC3F,gBAAIF,YAAY,IAAI,CAApB,EAAuB,OAAO,CAAP;AACvB,gBAAM+G,QAAQ,GAAG7G,cAAc,IAAIF,YAAY,GAAG,CAAnB,CAA/B,CAF2F,CAG3F;;AAEA,gBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,kBAAI4F,YAAY,KAAK,CAArB,EAAwB,OAAO,CAAP;;AACxB,kBAAIA,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,oBAAMoB,eAAe,GAAIN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAzB;AACA,uBAAO,CAACoB,eAAD,GAAmBD,QAA1B;AACH,eAJD,MAKK;AACD;AACA,oBAAMC,gBAAe,GAAIN,IAAI,CAACQ,IAAL,CAAUtB,YAAY,GAAG,CAAzB,CAAzB;;AACA,uBAAOoB,gBAAe,GAAGD,QAAzB;AACH;AACJ,aAbD,MAaO;AACH;AACA,kBAAInB,YAAY,KAAK,CAArB,EAAwB,OAAO,CAACmB,QAAD,GAAY,CAAnB;;AACxB,kBAAInB,YAAY,GAAG,CAAf,KAAqB,CAAzB,EAA4B;AACxB;AACA,oBAAMoB,iBAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;;AACA,uBAAO,CAACmB,QAAD,GAAY,CAAZ,GAAgBC,iBAAe,GAAGD,QAAzC;AACH,eAJD,MAKK;AACD;AACA,oBAAMC,iBAAe,GAAGN,IAAI,CAACO,KAAL,CAAWrB,YAAY,GAAG,CAA1B,CAAxB;;AACA,uBAAOmB,QAAQ,GAAG,CAAX,GAAeC,iBAAe,GAAGD,QAAxC;AACH;AACJ;AACJ,WAhCD;;AAkCA,eAAK7G,cAAL,CAAoB6B,KAApB,GAA4B,KAAKC,WAAL,CAAiB9B,cAAjB,CAAgCgC,IAAhC,EAA5B;AACA,cAAMhC,cAAc,GAAG4G,cAAc,CAAClB,YAAD,EAAe,KAAK5F,YAAL,CAAkB+B,KAAjC,EAAwC,KAAK7B,cAAL,CAAoB6B,KAA5D,CAArC;;AAEA,cAAI,KAAKzB,MAAL,CAAYyB,KAAZ,IAAqB,CAAzB,EAA4B;AACxB,mBAAO;AAAE0E,cAAAA,CAAC,EAAEvG,cAAL;AAAqB0G,cAAAA,CAAC,EAAE;AAAxB,aAAP;AACH;;AAED,cAAMV,SAAS,GAAG,KAAKC,iBAAL,CAAuBR,KAAvB,CAAlB;AACA,iBAAO;AACHc,YAAAA,CAAC,EAAEP,SAAS,CAACO,CAAV,GAAc,KAAKnG,MAAL,CAAYyB,KAA1B,GAAkC7B,cADlC;AAEH0G,YAAAA,CAAC,EAAEV,SAAS,CAACU,CAAV,GAAc,KAAKtG,MAAL,CAAYyB;AAF1B,WAAP;AAIH;;AAEDuE,QAAAA,YAAY,CAACJ,SAAD,EAAsCE,QAAtC,EAAgF;AACxF,cAAI,CAAC,KAAKrF,aAAV,EAAyB;AACrB,gBAAI,KAAKoG,YAAT,EAAuB;AACnB,mBAAKpG,aAAL,GAAqB,KAAKoG,YAA1B;AACH,aAFD,MAGK;AACD,kBAAIrJ,MAAJ,EAAY;AACR,qBAAKsJ,oBAAL,CAA0BlB,SAA1B,EAAqCE,QAArC;AACH,eAFD,MAGK;AACDiB,gBAAAA,OAAO,CAACC,IAAR,CAAa,oCAAb;AACH;;AACD;AACH;AACJ;;AAED,cAAMC,MAAM,GAAG,KAAKC,iBAAL,EAAf;AACA,cAAI,CAACD,MAAL,EAAa;AAEb;AAAA;AAAA,4CAAaE,cAAb,CAA4B,IAA5B,EAAkCF,MAAlC,EAnBwF,CAoBxF;;AACA,cAAMG,UAAU,GAAG,KAAKC,IAAL,CAAUC,gBAAV,EAAnB;AACAL,UAAAA,MAAM,CAACI,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAACjB,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIiB,UAAU,CAACd,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIc,UAAU,CAACI,CAHf;AAKAP,UAAAA,MAAM,CAACQ,IAAP,CAAYC,UAAZ,CAAuBjG,KAAvB,GAA+BlD,gBAAgB,CAAC6H,IAAI,CAACuB,KAAL,CAAW/B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAc,UAAAA,MAAM,CAACQ,IAAP,CAAYG,KAAZ,CAAkBnG,KAAlB,IAA2B,KAAKjC,SAAL,CAAeiC,KAA1C;AACAwF,UAAAA,MAAM,CAACQ,IAAP,CAAY5F,SAAZ,GA7BwF,CA8BxF;AACA;;AACAoF,UAAAA,MAAM,CAACY,gBAAP;;AAEA,cAAI,KAAKhJ,uBAAL,IAAgC,IAApC,EACA;AACI,iBAAKA,uBAAL,CAA6BoI,MAA7B;AACH;AACJ;;AAEeH,QAAAA,oBAAoB,CAAClB,SAAD,EAAsCE,QAAtC,EAA0E;AAAA;;AAAA;AAC1G;AACA,gBAAMgC,UAAU,GAAG,sDAAnB,CAF0G,CAG1G;;AACAC,YAAAA,MAAM,CAACC,OAAP,CAAeC,OAAf,CAAuB,UAAvB,EAAmC,YAAnC,EAAiDH,UAAjD,EACKI,IADL,CACWC,IAAD,IAAkB;AACpB5K,cAAAA,YAAY,CAAC6K,OAAb,CAAqB;AAACD,gBAAAA,IAAI,EAAEA;AAAP,eAArB,EAAmC,CAACE,GAAD,EAAMC,MAAN,KAAiB;AAChD,oBAAID,GAAJ,EAAS;AACLtB,kBAAAA,OAAO,CAACwB,KAAR,CAAcF,GAAd;AACA;AACH;;AACD,gBAAA,KAAI,CAAC5H,aAAL,GAAqB6H,MAArB;;AACA,oBAAMrB,MAAM,GAAG,KAAI,CAACC,iBAAL,EAAf;;AACA,oBAAI,CAACD,MAAL,EAAa;AAEb;AAAA;AAAA,kDAAaE,cAAb,CAA4B,KAA5B,EAAkCF,MAAlC,EATgD,CAUhD;;AACA,oBAAMG,UAAU,GAAG,KAAI,CAACC,IAAL,CAAUC,gBAAV,EAAnB;;AACAL,gBAAAA,MAAM,CAACI,IAAP,CAAYE,gBAAZ,CACIH,UAAU,CAACjB,CAAX,GAAeL,QAAQ,CAACK,CAD5B,EAEIiB,UAAU,CAACd,CAAX,GAAeR,QAAQ,CAACQ,CAF5B,EAGIc,UAAU,CAACI,CAHf;AAKAP,gBAAAA,MAAM,CAACQ,IAAP,CAAYC,UAAZ,CAAuBjG,KAAvB,GAA+BlD,gBAAgB,CAAC6H,IAAI,CAACuB,KAAL,CAAW/B,SAAS,CAACU,CAArB,EAAwBV,SAAS,CAACO,CAAlC,CAAD,CAA/C;AACAc,gBAAAA,MAAM,CAACQ,IAAP,CAAYG,KAAZ,CAAkBnG,KAAlB,IAA2B,KAAI,CAACjC,SAAL,CAAeiC,KAA1C;AACAwF,gBAAAA,MAAM,CAACQ,IAAP,CAAY5F,SAAZ;AAEAoF,gBAAAA,MAAM,CAACY,gBAAP;AACH,eAtBD;AAuBH,aAzBL;AAJ0G;AA8B7G;;AAESX,QAAAA,iBAAiB,GAAkB;AACzC,cAAMsB,UAAU,GAAG;AAAA;AAAA,wCAAWC,OAAX,CAAmB;AAAA;AAAA,4CAAaC,YAAhC,EAA8C,KAAKjI,aAAnD,CAAnB;;AACA,cAAI,CAAC+H,UAAL,EAAiB;AACbzB,YAAAA,OAAO,CAACwB,KAAR,CAAc,8CAAd;AACA,mBAAO,IAAP;AACH,WALwC,CAOzC;;;AACA,cAAMtB,MAAM,GAAGuB,UAAU,CAACG,YAAX;AAAA;AAAA,+BAAf;;AACA,cAAI,CAAC1B,MAAL,EAAa;AACTF,YAAAA,OAAO,CAACwB,KAAR,CAAc,uDAAd;AACAC,YAAAA,UAAU,CAACI,OAAX;AACA,mBAAO,IAAP;AACH;;AAED,cAAIpL,MAAJ,EAAY;AACRgL,YAAAA,UAAU,CAACK,IAAX,GAAkBnK,OAAO,CAACoK,mBAA1B;AACH;;AAED,iBAAO7B,MAAP;AACH;;AAED8B,QAAAA,UAAU,CAACT,MAAD,EAAiBxC,QAAjB,EAAiCkD,QAAjC,EAAiDC,QAAjD,EAAmE;AACzE,cAAI,CAACX,MAAL,EAAa;AAEb,cAAMY,UAAU,GAAG;AAAA;AAAA,wCAAWT,OAAX,CAAmB,KAAKpB,IAAxB,EAA8BiB,MAA9B,CAAnB;AACA,cAAI,CAACY,UAAL,EAAiB;AAEjBA,UAAAA,UAAU,CAAC3B,gBAAX,CAA4BzB,QAA5B;AACAoD,UAAAA,UAAU,CAACC,gBAAX,CAA4BH,QAA5B,EAPyE,CAQzE;AACA;;AACA,eAAKI,YAAL,CAAkB,MAAM;AACpB;AAAA;AAAA,0CAAWC,UAAX,CAAsBH,UAAtB;AACH,WAFD,EAEGD,QAFH;AAGH;AAED;AACJ;AACA;;;AACcK,QAAAA,UAAU,GAAa;AAC7B;AACA,iBAAO,IAAP;AACH;;AAEMC,QAAAA,IAAI,CAACC,SAAD,EAA0B;AACjC,cAAI,CAAC,KAAKzK,QAAN,IAAkB,CAAC,KAAKA,QAAL,CAAc0C,KAArC,EAA4C;AACxC;AACH;;AAED,kBAAQ,KAAKrB,OAAb;AAEI,iBAAK5B,cAAc,CAAC6B,IAApB;AACI,mBAAKoJ,gBAAL;AACA;;AACJ,iBAAKjL,cAAc,CAAC8F,OAApB;AACI,mBAAKoF,mBAAL;AACA;;AACJ,iBAAKlL,cAAc,CAAC6F,QAApB;AACI,mBAAKsF,oBAAL;AACA;;AACJ,iBAAKnL,cAAc,CAACoL,cAApB;AACI,mBAAKC,0BAAL;AACA;;AACJ,iBAAKrL,cAAc,CAACsL,SAApB;AACI,mBAAKC,qBAAL;AACA;;AACJ;AACI;AAlBR;;AAqBA,eAAKzJ,kBAAL,IAA2BkJ,SAA3B;AACA,eAAKvJ,gBAAL,CAAsBwB,KAAtB,IAA+B+H,SAA/B;AAEA,eAAK3H,SAAL;AACH;;AAES4H,QAAAA,gBAAgB,GAAG;AACzB,cAAI,KAAKnJ,kBAAL,IAA2B,KAAKnB,YAAL,CAAkBsC,KAAjD,EAAwD;AACpD,iBAAKD,YAAL,CAAkBhD,cAAc,CAAC8F,OAAjC;AACH;AACJ;;AAESoF,QAAAA,mBAAmB,GAAG;AAC5B,cAAI,CAAC,KAAKzK,SAAL,CAAewC,KAApB,EACI,KAAKD,YAAL,CAAkBhD,cAAc,CAAC6F,QAAjC,EADJ,KAEK;AACD,gBAAI,KAAK/D,kBAAL,IAA2B,KAAKlB,eAAL,CAAqBqC,KAApD,EAA2D;AACvD,mBAAKD,YAAL,CAAkBhD,cAAc,CAAC6F,QAAjC;AACH;AACJ;AACJ;;AAESsF,QAAAA,oBAAoB,GAAG;AAC7B,cAAI,KAAKrJ,kBAAL,GAA0B,KAAKhB,YAAL,CAAkBmC,KAAhD,EAAuD;AACnD,iBAAKoD,YAAL;AACA,gBAAI,KAAK3F,MAAT,EACI,KAAKsC,YAAL,CAAkBhD,cAAc,CAACoL,cAAjC,EADJ,KAGI,KAAKpI,YAAL,CAAkBhD,cAAc,CAACsL,SAAjC;AACJ;AACH,WAR4B,CAU7B;;;AACA,cAAI,CAAC,KAAKvJ,WAAV,EAAuB;AACnB,iBAAKqE,aAAL;AACH,WAFD,MAGK,IAAI,KAAKtE,kBAAL,IAA2B,KAAKE,aAApC,EAAmD;AACpD,iBAAKmF,OAAL;;AACA,gBAAI,KAAKhG,eAAL,CAAqB8B,KAArB,IAA8B,CAAlC,EAAqC;AACjC,mBAAKkD,gBAAL;AACH,aAFD,MAGK;AACD;AACA,mBAAKnE,aAAL,GAAqB,KAAKF,kBAAL,GAA0B,QAA/C;AACH;AACJ;;AAED,cAAI0J,WAAW,GAAG,KAAKnJ,mBAAL,CAAyBgD,MAAzB,GAAkC,CAApD,CAzB6B,CA0B7B;;AACA,eAAK2B,mBAAL;;AACA,cAAIwE,WAAW,IAAI,KAAKnJ,mBAAL,CAAyBgD,MAAzB,IAAmC,CAAtD,EAAyD;AACrD,iBAAKc,gBAAL;AACH;AACJ;;AAESkF,QAAAA,0BAA0B,GAAG;AACnC,cAAI,KAAKvJ,kBAAL,IAA2B,KAAKb,YAAL,CAAkBgC,KAAjD,EAAwD;AACpD,iBAAKD,YAAL,CAAkBhD,cAAc,CAAC8F,OAAjC;AACH;AACJ;;AAESyF,QAAAA,qBAAqB,GAAG;AAC9B;AACA,eAAKhL,QAAL,CAAc0C,KAAd,GAAsB,KAAtB;AACA,eAAK1C,QAAL,CAAciD,MAAd;AACH;;AA5lBiE,O,UAE3D8G,mB,GAA6B,U;;;;;iBAGR,C;;;;;;;;;;;;iBAMQ;AAAA;AAAA,2C;;;;;;;iBAGF;AAAA;AAAA,yC", "sourcesContent": ["import { _decorator, misc, instantiate, Node, Component, Prefab, Color, Vec3, Quat, assetManager } from 'cc';\r\nimport { EDITOR } from 'cc/env';\r\nimport { BulletProperty, Bullet } from './Bullet';\r\nimport { EmitterData } from '../data/bullet/EmitterData';\r\nimport { BulletData } from '../data/bullet/BulletData';\r\nimport { ObjectPool } from './ObjectPool';\r\nimport { BulletSystem } from './BulletSystem';\r\nimport { EventGroup, EventGroupContext } from \"./EventGroup\";\r\nimport { Property, PropertyContainerComponent } from './PropertyContainer';\r\nimport Entity from 'db://assets/scripts/Game/ui/base/Entity';\r\n\r\nconst { ccclass, executeInEditMode, property, disallowMultiple, menu  } = _decorator;\r\nconst { degreesToRadians, radiansToDegrees } = misc;\r\n\r\nexport enum eEmitterStatus {\r\n    None, Prewarm, Emitting, LoopEndReached, Completed\r\n}\r\n\r\n// 用枚举定义属性\r\nexport enum eEmitterProp {\r\n    IsActive = 1, IsOnlyInScreen, IsPreWarm, IsLoop, \r\n    InitialDelay, PreWarmDuration, EmitBulletID, EmitDuration, EmitInterval, EmitPower, LoopInterval,\r\n    PerEmitCount, PerEmitInterval, PerEmitOffsetX, \r\n    Angle, Count, Arc, Radius,\r\n    TotalElapsedTime, \r\n}\r\n\r\nexport type onBulletCreatedDelegate = (bullet: Bullet) => void;\r\nexport type onEmitterStatusChangedDelegate = (emitter: Emitter, oldStatus: eEmitterStatus, newStatus: eEmitterStatus) => void;\r\n\r\n@ccclass('Emitter')\r\n// @inspector('editor/inspector/components/emitter')\r\n@menu('子弹系统/发射器')\r\n@executeInEditMode(true)\r\n@disallowMultiple(true)\r\nexport class Emitter extends PropertyContainerComponent<eEmitterProp> {\r\n\r\n    static kBulletNameInEditor:string = \"_bullet_\";\r\n\r\n    @property({displayName: \"子弹ID\"})\r\n    readonly bulletID: number = 0;\r\n\r\n    @property({type:Prefab, displayName: \"子弹Prefab(临时)\"})\r\n    readonly bulletPrefab!: Prefab;\r\n\r\n    @property({type: EmitterData, displayName: \"发射器属性\"})\r\n    readonly emitterData: EmitterData = new EmitterData();\r\n\r\n    @property({type: BulletData, displayName: \"子弹属性\"})\r\n    readonly bulletData: BulletData = new BulletData();\r\n\r\n    // callbacks\r\n    onBulletCreatedCallback: onBulletCreatedDelegate | null = null;\r\n    onEmitterStatusChangedCallback: onEmitterStatusChangedDelegate | null = null;\r\n\r\n    // 以下属性缓存为了性能优化(减少this.getProperty<T>的调用)\r\n    public isActive!: Property<boolean>;\r\n    public isOnlyInScreen!: Property<boolean>;\r\n    public isPreWarm!: Property<boolean>;\r\n    public isLoop!: Property<boolean>;\r\n    public initialDelay!: Property<number>;\r\n    public preWarmDuration!: Property<number>;\r\n    public emitBulletID!: Property<number>;\r\n    public emitDuration!: Property<number>;\r\n    public emitInterval!: Property<number>;\r\n    public emitPower!: Property<number>;\r\n    public loopInterval!: Property<number>;\r\n    public perEmitCount!: Property<number>;\r\n    public perEmitInterval!: Property<number>;\r\n    public perEmitOffsetX!: Property<number>;\r\n    public angle!: Property<number>;\r\n    public count!: Property<number>;\r\n    public arc!: Property<number>;\r\n    public radius!: Property<number>;\r\n    public totalElapsedTime!: Property<number>;\r\n    // 以下用于事件组修改子弹的属性，（不直接修改bulletData)\r\n    public bulletProp!: BulletProperty;\r\n\r\n    // 发射器自己的事件组\r\n    public eventGroups: EventGroup[] = [];\r\n\r\n    // 私有变量\r\n    protected _status: eEmitterStatus = eEmitterStatus.None;\r\n    protected _statusElapsedTime: number = 0;\r\n    protected _isEmitting: boolean = false;\r\n    protected _nextEmitTime: number = 0;\r\n    protected _bulletPrefab: Prefab|null = null;\r\n    protected _prewarmEffectPrefab: Prefab|null = null;\r\n    protected _emitEffectPrefab: Prefab|null = null;\r\n    protected _entity: Entity|null = null;\r\n\r\n    // Per-emit timing tracking\r\n    protected _perEmitBulletQueue: Array<{index: number, perEmitIndex: number, targetTime: number}> = [];\r\n\r\n    get isEmitting(): boolean { return this._isEmitting; }\r\n    get status(): eEmitterStatus { return this._status; }\r\n    get statusElapsedTime(): number { return this._statusElapsedTime; }\r\n\r\n    protected onLoad() : void {\r\n        this.createProperties();\r\n        this.createEventGroups();\r\n\r\n        // 更新属性\r\n        this.resetProperties();\r\n    }\r\n\r\n    //#region \"Editor Region\"\r\n    public onLostFocusInEditor(): void {\r\n        this.updatePropertiesInEditor();\r\n        this.createEventGroups();\r\n    }\r\n\r\n    public resetInEditor() {\r\n        this.resetProperties();\r\n        this.changeStatus(eEmitterStatus.None);\r\n        this._isEmitting = false;\r\n        this.totalElapsedTime.value = 0;\r\n    }\r\n\r\n    public updatePropertiesInEditor() {\r\n        if (!this.emitterData) return;\r\n        \r\n        this.isActive.value = true;\r\n        this.emitBulletID.value = this.bulletID;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval();\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval();\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        this.emitPower.value = this.emitterData.emitPower.eval();\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval();\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        this.angle.value = this.emitterData.angle.eval();\r\n        this.count.value = this.emitterData.count.eval();\r\n        this.arc.value = this.emitterData.arc.eval();\r\n        this.radius.value = this.emitterData.radius.eval();\r\n\r\n        this.notifyAll(true);\r\n    }\r\n    //#endregion \"Editor Region\"\r\n\r\n    // 通过这个接口来启用和禁用发射器\r\n    public setActive(active: boolean) {\r\n        this.isActive.value = active;\r\n        this.isActive.notify();\r\n    }\r\n\r\n    public setEntity(entity: Entity) {\r\n        this._entity = entity;\r\n    }\r\n\r\n    public getEntity(): Entity | null {\r\n        return this._entity;\r\n    }\r\n\r\n    protected createProperties() {\r\n        this.clear();\r\n        \r\n        this.isActive = this.addProperty(eEmitterProp.IsActive, false);\r\n        this.totalElapsedTime = this.addProperty(eEmitterProp.TotalElapsedTime, 0);\r\n        this.emitBulletID = this.addProperty(eEmitterProp.EmitBulletID, this.bulletID);\r\n        this.isOnlyInScreen = this.addProperty(eEmitterProp.IsOnlyInScreen, true);\r\n        this.isPreWarm = this.addProperty(eEmitterProp.IsPreWarm, true);\r\n        this.isLoop = this.addProperty(eEmitterProp.IsLoop, true);\r\n\r\n        this.initialDelay = this.addProperty(eEmitterProp.InitialDelay, 0);\r\n        this.preWarmDuration = this.addProperty(eEmitterProp.PreWarmDuration, 0);\r\n        this.emitDuration = this.addProperty(eEmitterProp.EmitDuration, 0);\r\n        this.emitInterval = this.addProperty(eEmitterProp.EmitInterval, 0);\r\n        this.emitPower = this.addProperty(eEmitterProp.EmitPower, 1);\r\n        this.loopInterval = this.addProperty(eEmitterProp.LoopInterval, 0);\r\n        this.perEmitCount = this.addProperty(eEmitterProp.PerEmitCount, 1);\r\n        this.perEmitInterval = this.addProperty(eEmitterProp.PerEmitInterval, 0);\r\n        this.perEmitOffsetX = this.addProperty(eEmitterProp.PerEmitOffsetX, 0);\r\n        this.angle = this.addProperty(eEmitterProp.Angle, 0);\r\n        this.count = this.addProperty(eEmitterProp.Count, 1);\r\n        this.arc = this.addProperty(eEmitterProp.Arc, 0);\r\n        this.radius = this.addProperty(eEmitterProp.Radius, 0);\r\n\r\n        // 子弹相关属性\r\n        this.bulletProp = new BulletProperty();\r\n\r\n        // 子弹表->Prefab路径\r\n        this.emitBulletID.on((value) => {\r\n            // TODO: reload bullet prefab\r\n            this._bulletPrefab = null;\r\n        });\r\n        this.isActive.on((value) => {\r\n            if (value) {\r\n                BulletSystem.onCreateEmitter(this);\r\n            } else {\r\n                BulletSystem.onDestroyEmitter(this);\r\n            }\r\n        });\r\n    }\r\n\r\n    protected createEventGroups() {\r\n        if (!this.emitterData || this.emitterData.eventGroupData.length <= 0) return;\r\n\r\n        this.eventGroups = [];\r\n        let ctx = new EventGroupContext();\r\n        ctx.emitter = this;\r\n        for (const eventGroup of this.emitterData.eventGroupData) {\r\n            BulletSystem.createEmitterEventGroup(ctx, eventGroup);\r\n        }\r\n    }\r\n\r\n    // reset properties from emitterData\r\n    protected resetProperties() {\r\n        if (!this.emitterData) return;\r\n        \r\n        this.totalElapsedTime.value = 0;\r\n        this.isActive.value = false;\r\n        this.emitBulletID.value = this.bulletID;\r\n        this.isOnlyInScreen.value = this.emitterData.isOnlyInScreen;\r\n        this.isPreWarm.value = this.emitterData.isPreWarm;\r\n        this.isLoop.value = this.emitterData.isLoop;\r\n\r\n        this.initialDelay.value = this.emitterData.initialDelay.eval();\r\n        this.preWarmDuration.value = this.emitterData.preWarmDuration.eval();\r\n        this.emitDuration.value = this.emitterData.emitDuration.eval();\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        this.emitPower.value = this.emitterData.emitPower.eval();\r\n        this.loopInterval.value = this.emitterData.loopInterval.eval();\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n        this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        this.angle.value = this.emitterData.angle.eval();\r\n        this.count.value = this.emitterData.count.eval();\r\n        this.arc.value = this.emitterData.arc.eval();\r\n        this.radius.value = this.emitterData.radius.eval();\r\n\r\n        this.bulletProp.resetFromData(this.bulletData);\r\n\r\n        this.notifyAll(true);\r\n    }\r\n\r\n    /**\r\n     * public apis\r\n     */\r\n    changeStatus(status: eEmitterStatus) {\r\n        if (this._status === status) return;\r\n\r\n        const oldStatus = this._status;\r\n        this._status = status;\r\n        this._statusElapsedTime = 0;\r\n        this._nextEmitTime = 0;\r\n        // Clear per-emit queue when changing status\r\n        this._perEmitBulletQueue = [];\r\n\r\n        if (status === eEmitterStatus.Emitting || status === eEmitterStatus.Prewarm) {\r\n            if (this.eventGroups.length > 0) {\r\n                this.eventGroups.forEach(group => group.start());\r\n            }\r\n        }\r\n        else {\r\n            if (this.eventGroups.length > 0) {\r\n                this.eventGroups.forEach(group => group.stop());\r\n            }\r\n        }\r\n\r\n        if (this.onEmitterStatusChangedCallback != null)\r\n        {\r\n            this.onEmitterStatusChangedCallback(this, oldStatus, status);\r\n        }\r\n    }\r\n\r\n    protected scheduleNextEmit() {\r\n        // re-eval\r\n        this.emitInterval.value = this.emitterData.emitInterval.eval();\r\n        \r\n        // Schedule the next emit after emitInterval\r\n        this._nextEmitTime = this._statusElapsedTime + this.emitInterval.value;\r\n    }\r\n\r\n    protected startEmitting() {\r\n        this._isEmitting = true;\r\n        // 下一次update时触发发射\r\n        // 或者在这里调用 this.tryEmit() && this.scheduleNextEmit(); 立即触发发射\r\n    }\r\n    \r\n    protected stopEmitting() {\r\n        this._isEmitting = false;\r\n        // Clear the per-emit bullet queue\r\n        this._perEmitBulletQueue = [];\r\n        this.unscheduleAllCallbacks();\r\n    }\r\n\r\n    protected canEmit(): boolean {\r\n        // 检查是否可以触发发射\r\n        // Override this method in subclasses to add custom trigger conditions\r\n        return true;\r\n    }\r\n\r\n    protected emit(): void {\r\n        // re-eval\r\n        this.perEmitCount.value = this.emitterData.perEmitCount.eval();\r\n\r\n        if (this.perEmitInterval.value > 0) {\r\n            // Generate bullets in time-sorted order directly\r\n            for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                this.perEmitInterval.value = this.emitterData.perEmitInterval.eval();\r\n                const targetTime = this._statusElapsedTime + (this.perEmitInterval.value * j);\r\n                for (let i = 0; i < this.count.value; i++) {\r\n                    this._perEmitBulletQueue.push({\r\n                        index: i,\r\n                        perEmitIndex: j,\r\n                        targetTime: targetTime\r\n                    });\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // Immediate emission - no timing needed\r\n            for (let i = 0; i < this.count.value; i++) {\r\n                for (let j = 0; j < this.perEmitCount.value; j++) {\r\n                    this.emitSingle(i, j);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    protected processPerEmitQueue(): void {\r\n        // Process bullets that should be emitted based on current time\r\n        while (this._perEmitBulletQueue.length > 0) {\r\n            const nextBullet = this._perEmitBulletQueue[0];\r\n\r\n            // Check if it's time to emit this bullet\r\n            if (this._statusElapsedTime >= nextBullet.targetTime) {\r\n                // Remove from queue and emit\r\n                this._perEmitBulletQueue.shift();\r\n                this.emitSingle(nextBullet.index, nextBullet.perEmitIndex);\r\n            } else {\r\n                // No more bullets ready to emit yet\r\n                break;\r\n            }\r\n        }\r\n    }\r\n\r\n    protected tryEmit(): boolean {\r\n        if (this.canEmit()) {\r\n            this.emit();\r\n            return true;\r\n        }\r\n        return false;\r\n    }\r\n\r\n    protected emitSingle(index:number, perEmitIndex: number) {\r\n        const direction = this.getSpawnDirection(index);\r\n        const position = this.getSpawnPosition(index, perEmitIndex);\r\n        this.createBullet(direction, position);\r\n    }\r\n\r\n    /**\r\n     * Calculate the direction for a bullet at the given index\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Direction vector {x, y}\r\n     */\r\n    getSpawnDirection(index: number): { x: number, y: number } {\r\n        // 计算发射方向\r\n        const angleOffset = this.count.value > 1 ? (this.arc.value / (this.count.value - 1)) * index - this.arc.value / 2 : 0;\r\n        const radian = degreesToRadians(this.angle.value + angleOffset);\r\n\r\n        return {\r\n            x: Math.cos(radian),\r\n            y: Math.sin(radian)\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Get the spawn position for a bullet at the given index\r\n     * odd number to the right, even number to the left\r\n     * @param index The index of the bullet (0 to count-1)\r\n     * @returns Position offset from emitter center\r\n     */\r\n    getSpawnPosition(index: number, perEmitIndex: number): { x: number, y: number } {\r\n        // add perEmitOffsetX by perEmitIndex, with the rules:\r\n        // by the following order:0, 1; 2, 0, 1; 2, 0, 1, 3;\r\n        const getEmitOffsetX = (perEmitIndex: number, perEmitCount: number, perEmitOffsetX: number) => {\r\n            if (perEmitCount <= 1) return 0;\r\n            const interval = perEmitOffsetX / (perEmitCount - 1);\r\n            //const middle = 0;\r\n\r\n            if (perEmitCount % 2 === 1) {\r\n                // 奇数情况\r\n                if (perEmitIndex === 0) return 0;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle =  Math.floor(perEmitIndex / 2);\r\n                    return -stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle =  Math.ceil(perEmitIndex / 2);\r\n                    return stepsFromMiddle * interval;\r\n                }\r\n            } else {\r\n                // 偶数情况\r\n                if (perEmitIndex === 0) return -interval / 2;\r\n                if (perEmitIndex % 2 === 0) {\r\n                    // 偶数索引在左边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return -interval / 2 - stepsFromMiddle * interval;\r\n                }\r\n                else {\r\n                    // 奇数索引在右边\r\n                    const stepsFromMiddle = Math.floor(perEmitIndex / 2);\r\n                    return interval / 2 + stepsFromMiddle * interval;\r\n                }\r\n            }\r\n        }\r\n\r\n        this.perEmitOffsetX.value = this.emitterData.perEmitOffsetX.eval();\r\n        const perEmitOffsetX = getEmitOffsetX(perEmitIndex, this.perEmitCount.value, this.perEmitOffsetX.value);\r\n\r\n        if (this.radius.value <= 0) {\r\n            return { x: perEmitOffsetX, y: 0 };\r\n        }\r\n        \r\n        const direction = this.getSpawnDirection(index);\r\n        return {\r\n            x: direction.x * this.radius.value + perEmitOffsetX,\r\n            y: direction.y * this.radius.value\r\n        };\r\n    }\r\n\r\n    createBullet(direction: { x: number, y: number }, position: { x: number, y: number }): void {\r\n        if (!this._bulletPrefab) {\r\n            if (this.bulletPrefab) {\r\n                this._bulletPrefab = this.bulletPrefab;\r\n            }\r\n            else {\r\n                if (EDITOR) {\r\n                    this.createBulletInEditor(direction, position);\r\n                }\r\n                else {\r\n                    console.warn(\"Emitter: No bullet prefab assigned\");\r\n                }\r\n                return;\r\n            }\r\n        }\r\n        \r\n        const bullet = this.instantiateBullet();\r\n        if (!bullet) return;\r\n\r\n        BulletSystem.onCreateBullet(this, bullet);\r\n        // Set bullet position relative to emitter\r\n        const emitterPos = this.node.getWorldPosition();\r\n        bullet.node.setWorldPosition(\r\n            emitterPos.x + position.x,\r\n            emitterPos.y + position.y,\r\n            emitterPos.z\r\n        );\r\n        bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n        bullet.prop.speed.value *= this.emitPower.value;\r\n        bullet.prop.notifyAll();\r\n        // 为什么需要在这里resetEventGroups?\r\n        // 因为EventGroups的条件初始化依赖上面先初始化子弹的属性\r\n        bullet.resetEventGroups();\r\n\r\n        if (this.onBulletCreatedCallback != null)\r\n        {\r\n            this.onBulletCreatedCallback(bullet);\r\n        }\r\n    }\r\n\r\n    protected async createBulletInEditor(direction: { x: number, y: number }, position: { x: number, y: number }) {\r\n        // use a default bullet prefab\r\n        const prefabPath = 'db://assets/resources/Game/prefabs/Bullet_New.prefab';\r\n        // @ts-ignore\r\n        Editor.Message.request('asset-db', 'query-uuid', prefabPath)\r\n            .then((uuid: string) => {\r\n                assetManager.loadAny({uuid: uuid}, (err, prefab) => {\r\n                    if (err) {\r\n                        console.error(err);\r\n                        return;\r\n                    }\r\n                    this._bulletPrefab = prefab;\r\n                    const bullet = this.instantiateBullet();\r\n                    if (!bullet) return;\r\n\r\n                    BulletSystem.onCreateBullet(this, bullet);\r\n                    // Set bullet position relative to emitter\r\n                    const emitterPos = this.node.getWorldPosition();\r\n                    bullet.node.setWorldPosition(\r\n                        emitterPos.x + position.x,\r\n                        emitterPos.y + position.y,\r\n                        emitterPos.z\r\n                    );\r\n                    bullet.prop.speedAngle.value = radiansToDegrees(Math.atan2(direction.y, direction.x));\r\n                    bullet.prop.speed.value *= this.emitPower.value;\r\n                    bullet.prop.notifyAll();\r\n\r\n                    bullet.resetEventGroups();\r\n                });\r\n            });\r\n    }\r\n\r\n    protected instantiateBullet(): Bullet | null {\r\n        const bulletNode = ObjectPool.getNode(BulletSystem.bulletParent, this._bulletPrefab!);\r\n        if (!bulletNode) {\r\n            console.error(\"Emitter: Failed to instantiate bullet prefab\");\r\n            return null;\r\n        }\r\n\r\n        // Get the bullet component\r\n        const bullet = bulletNode.getComponent(Bullet);\r\n        if (!bullet) {\r\n            console.error(\"Emitter: Bullet prefab does not have Bullet component\");\r\n            bulletNode.destroy();\r\n            return null;\r\n        }\r\n\r\n        if (EDITOR) {\r\n            bulletNode.name = Emitter.kBulletNameInEditor;\r\n        }\r\n\r\n        return bullet;\r\n    }\r\n\r\n    playEffect(prefab: Prefab, position: Vec3, rotation: Quat, duration: number) {\r\n        if (!prefab) return;\r\n\r\n        const effectNode = ObjectPool.getNode(this.node, prefab);\r\n        if (!effectNode) return;\r\n\r\n        effectNode.setWorldPosition(position);\r\n        effectNode.setWorldRotation(rotation);\r\n        // Play the effect and destroy it after duration\r\n        // effectNode.getComponent(ParticleSystem)?.play();\r\n        this.scheduleOnce(() => {\r\n            ObjectPool.returnNode(effectNode);\r\n        }, duration);\r\n    }\r\n\r\n    /**\r\n     * Return true if this.node is in screen\r\n     */\r\n    protected isInScreen() : boolean {\r\n        // TODO: Get mainCamera.containsNode(this.node)\r\n        return true;\r\n    }\r\n\r\n    public tick(deltaTime: number): void {\r\n        if (!this.isActive || !this.isActive.value) {\r\n            return;\r\n        }\r\n\r\n        switch (this._status)\r\n        {\r\n            case eEmitterStatus.None:\r\n                this.updateStatusNone();\r\n                break;\r\n            case eEmitterStatus.Prewarm:\r\n                this.updateStatusPrewarm();\r\n                break;\r\n            case eEmitterStatus.Emitting:\r\n                this.updateStatusEmitting();\r\n                break;\r\n            case eEmitterStatus.LoopEndReached:\r\n                this.updateStatusLoopEndReached();\r\n                break;\r\n            case eEmitterStatus.Completed:\r\n                this.updateStatusCompleted();\r\n                break;\r\n            default:\r\n                break;\r\n        }\r\n\r\n        this._statusElapsedTime += deltaTime;\r\n        this.totalElapsedTime.value += deltaTime;\r\n\r\n        this.notifyAll();\r\n    }\r\n\r\n    protected updateStatusNone() {\r\n        if (this._statusElapsedTime >= this.initialDelay.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusPrewarm() {\r\n        if (!this.isPreWarm.value)\r\n            this.changeStatus(eEmitterStatus.Emitting);\r\n        else {\r\n            if (this._statusElapsedTime >= this.preWarmDuration.value) {\r\n                this.changeStatus(eEmitterStatus.Emitting);\r\n            }\r\n        }\r\n    }\r\n\r\n    protected updateStatusEmitting() {\r\n        if (this._statusElapsedTime > this.emitDuration.value) {\r\n            this.stopEmitting();\r\n            if (this.isLoop)\r\n                this.changeStatus(eEmitterStatus.LoopEndReached);\r\n            else\r\n                this.changeStatus(eEmitterStatus.Completed);\r\n            return;\r\n        }\r\n        \r\n        // Start emitting if not already started\r\n        if (!this._isEmitting) {\r\n            this.startEmitting();\r\n        }\r\n        else if (this._statusElapsedTime >= this._nextEmitTime) {\r\n            this.tryEmit();\r\n            if (this.perEmitInterval.value <= 0) {\r\n                this.scheduleNextEmit();\r\n            }\r\n            else {\r\n                // 开始这一波\r\n                this._nextEmitTime = this._statusElapsedTime + 10000000;\r\n            }\r\n        }\r\n        \r\n        let wasEmitting = this._perEmitBulletQueue.length > 0;\r\n        // Process per-emit bullet queue based on precise timing\r\n        this.processPerEmitQueue();\r\n        if (wasEmitting && this._perEmitBulletQueue.length <= 0) {\r\n            this.scheduleNextEmit();\r\n        }\r\n    }\r\n\r\n    protected updateStatusLoopEndReached() {\r\n        if (this._statusElapsedTime >= this.loopInterval.value) {\r\n            this.changeStatus(eEmitterStatus.Prewarm);\r\n        }\r\n    }\r\n\r\n    protected updateStatusCompleted() {\r\n        // Do nothing or cleanup if needed\r\n        this.isActive.value = false;\r\n        this.isActive.notify();\r\n    }\r\n}\r\n"]}