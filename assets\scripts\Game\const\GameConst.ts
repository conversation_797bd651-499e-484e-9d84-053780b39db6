
import {Size, v2, Vec2, view } from "cc";

class _GameConst {

    readonly Cache: boolean = false;
    readonly ColliderDraw: boolean = false;
    readonly ActionFrameTime: number = 0.0333;

    // 敌人相关
    readonly EnemyPos: Vec2 = Vec2.ZERO;
    readonly battleConfigUrl: string = "Game/jsons/normal/chapter_";

    get ViewHeight(){
        return view.getVisibleSize().height
    }
    get ViewSize(){
        return view.getVisibleSize()
    }
    get ViewWidth(){
        return view.getVisibleSize().width
    }
    get ViewCenter(){
        return v2(this.ViewWidth/2, this.ViewHeight/2)
    }
}

export const GameConst = new _GameConst();

export const MainPlaneAnimationName = {
    Entry: "Entry",
    Moveleft: "MoveLeft",
    MovelRight: "MoveRight",
    Dodge: "Dodge",
    Super: "Super",
    Hurt: "Hurt",
    Idle: "Idle",
}