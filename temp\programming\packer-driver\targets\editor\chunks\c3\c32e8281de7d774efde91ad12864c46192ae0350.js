System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8", "__unresolved_9", "__unresolved_10"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, size, Sprite, tween, TrackComponent, GameEnum, Tools, GameIns, TrackGroup, PlaneBase, EnemyPlaneRole, FBoxCollider, ColliderGroupType, Bullet, _dec, _dec2, _dec3, _dec4, _dec5, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _crd, ccclass, property, BossPlane;

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfTrackComponent(extras) {
    _reporterNs.report("TrackComponent", "../../base/TrackComponent", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameEnum(extras) {
    _reporterNs.report("GameEnum", "../../../const/GameEnum", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  function _reportPossibleCrUseOfGameIns(extras) {
    _reporterNs.report("GameIns", "../../../GameIns", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTrackGroup(extras) {
    _reporterNs.report("TrackGroup", "../../../data/EnemyWave", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBossData(extras) {
    _reporterNs.report("BossData", "../../../data/BossData", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "../PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEnemyPlaneRole(extras) {
    _reporterNs.report("EnemyPlaneRole", "../enemy/EnemyPlaneRole", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFBoxCollider(extras) {
    _reporterNs.report("FBoxCollider", "../../../collider-system/FBoxCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfFCollider(extras) {
    _reporterNs.report("FCollider", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfColliderGroupType(extras) {
    _reporterNs.report("ColliderGroupType", "../../../collider-system/FCollider", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBullet(extras) {
    _reporterNs.report("Bullet", "../../bullet/Bullet", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      size = _cc.size;
      Sprite = _cc.Sprite;
      tween = _cc.tween;
    }, function (_unresolved_2) {
      TrackComponent = _unresolved_2.default;
    }, function (_unresolved_3) {
      GameEnum = _unresolved_3.GameEnum;
    }, function (_unresolved_4) {
      Tools = _unresolved_4.Tools;
    }, function (_unresolved_5) {
      GameIns = _unresolved_5.GameIns;
    }, function (_unresolved_6) {
      TrackGroup = _unresolved_6.TrackGroup;
    }, function (_unresolved_7) {
      PlaneBase = _unresolved_7.default;
    }, function (_unresolved_8) {
      EnemyPlaneRole = _unresolved_8.default;
    }, function (_unresolved_9) {
      FBoxCollider = _unresolved_9.default;
    }, function (_unresolved_10) {
      ColliderGroupType = _unresolved_10.ColliderGroupType;
    }, function (_unresolved_11) {
      Bullet = _unresolved_11.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "b08277TuKlEIY3rWaTkBUdv", "BossPlane", undefined);

      __checkObsolete__(['_decorator', 'Component', 'instantiate', 'Node', 'size', 'Sprite', 'Tween', 'tween', 'UITransform', 'v3', 'Vec2', 'Vec3']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", BossPlane = (_dec = ccclass("BossPlane"), _dec2 = property(_crd && EnemyPlaneRole === void 0 ? (_reportPossibleCrUseOfEnemyPlaneRole({
        error: Error()
      }), EnemyPlaneRole) : EnemyPlaneRole), _dec3 = property(Sprite), _dec4 = property(Sprite), _dec5 = property(Sprite), _dec(_class = (_class2 = class BossPlane extends (_crd && PlaneBase === void 0 ? (_reportPossibleCrUseOfPlaneBase({
        error: Error()
      }), PlaneBase) : PlaneBase) {
        constructor(...args) {
          super(...args);

          _initializerDefineProperty(this, "role", _descriptor, this);

          _initializerDefineProperty(this, "hpBg", _descriptor2, this);

          _initializerDefineProperty(this, "hpSpr", _descriptor3, this);

          _initializerDefineProperty(this, "hpWhite", _descriptor4, this);

          this._datas = [];
          this._data = null;
          this._trackCom = null;
          this._idleName = "idle1";
          this._formIndex = -1;
          //形态索引
          this._formNum = 0;
          //形态数量
          this._posX = 0;
          this._posY = 0;
          this._moveToX = 0;
          this._moveToY = 0;
          this._moveSpeed = 0;
          this._bArriveDes = false;
          //是否达到目标点
          this._transFormMove = false;
          //下一个航点
          this._nextWayPointTime = 0;
          this._nextWayPointX = 0;
          this._nextWayPointY = 0;
          this._nextWayPointInterval = 0;
          this._nextWaySpeed = 0;
          this._shootAble = true;
          this._atkActions = [];
          this._bOrderAttack = false;
          this._orderIndex = 0;
          this._orderAtkArr = [];
          this._atkPointDatas = [];
          this._action = -1;
          this._bDamageable = false;
          this._bAttackMove = false;
          this._bFirstWayPoint = false;
          this.transformBattle = true;
          this._bRemoveable = false;
          // _shadow: any = null;
          // wingmanPlanes: any[] = [];
          // _cloakeAnim: PfFrameAnim | null = null;
          this._nextAttackInterval = 0;
          this._nextAttackTime = 0;
          this._attackID = 0;
          this.tip = "";
          this._hpWhiteTween = null;
          this.bullets = [];
        }

        /**
         * 初始化 Boss 数据
         * @param datas Boss 数据数组
         */
        initBoss(datas) {
          super.init();
          this._datas = datas;
          this._formNum = this._datas.length;
          this._bFirstWayPoint = true;

          this._initUI();

          this._initProperty();

          this._initTrack();

          this._initCollide();

          this.setFormIndex(0);
        }
        /**
        * 初始化 UI
        */


        _initUI() {}

        _initProperty() {
          //暂时写死，后续读取新配表
          this.curHp = 4500;
          this.maxHp = this.curHp;
          this.attack = 60;
        }

        _initTrack() {
          this._trackCom = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
            error: Error()
          }), Tools) : Tools).addScript(this.node, _crd && TrackComponent === void 0 ? (_reportPossibleCrUseOfTrackComponent({
            error: Error()
          }), TrackComponent) : TrackComponent);

          this._trackCom.setTrackGroupStartCall(() => {});

          this._trackCom.setTrackGroupOverCall(() => {
            if (this._action === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).BossAction.Appear) {
              this._trackCom.setTrackAble(false);

              this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).BossAction.Transform);
            }
          });

          this._trackCom.setTrackOverCall(() => {});

          this._trackCom.setTrackLeaveCall(() => {});

          this._trackCom.setTrackStartCall(track => {});
        }

        _initCollide() {
          this.collideComp = this.getComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider) || this.addComponent(_crd && FBoxCollider === void 0 ? (_reportPossibleCrUseOfFBoxCollider({
            error: Error()
          }), FBoxCollider) : FBoxCollider);
          this.collideComp.init(this, size(100, 100)); // 初始化碰撞组件

          this.collideComp.groupType = (_crd && ColliderGroupType === void 0 ? (_reportPossibleCrUseOfColliderGroupType({
            error: Error()
          }), ColliderGroupType) : ColliderGroupType).ENEMY_NORMAL;
          this.colliderEnabled = false;
        }

        setCollideAble(isEnabled) {
          this.collideComp.isEnable = isEnabled;
        }

        setAction(action) {
          if (this._action !== action) {
            this._action = action;
            let BossAction = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).BossAction;

            switch (this._action) {
              case BossAction.Normal:
                this._playSkel(this._idleName, true, () => {});

                this.setDamangeable(true);
                break;

              case BossAction.Appear:
                this._playSkel(`enter${this._formIndex + 1}`, true, () => {});

                this.setDamangeable(false);

                this._startAppearTrack();

                break;

              case BossAction.Transform:
                this._playSkel(`ready${this._formIndex + 1}`, false, () => {
                  this.transformBattle && this.transformEnd();
                });

                this.setDamangeable(false);
                break;

              case BossAction.AttackPrepare:
                this.scheduleOnce(() => {
                  this.setAction(BossAction.AttackIng);
                });
                this.setDamangeable(true);
                break;

              case BossAction.AttackIng:
              case BossAction.AttackOver:
                this.setDamangeable(true);
                break;

              case BossAction.Blast:
                this.setDamangeable(false);
                break;

              default:
                this.setDamangeable(true);
            }
          }
        }
        /**
        * 设置是否可被攻击
        * @param damageable 是否可被攻击
        */


        setDamangeable(damageable) {
          this._bDamageable = damageable;
        }

        _playSkel(animName, loop, callback) {
          this.role.playAnim(animName, loop, callback);
        }
        /**
         * 设置提示信息
         * @param tip 提示信息
         */


        setTip(tip) {
          this.tip = tip;
        }
        /**
        * 变形结束
        */


        transformEnd() {
          if (this.tip !== "") {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.bossChangeFinish(this.tip);
          } else {
            (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
              error: Error()
            }), GameIns) : GameIns).battleManager.bossFightStart();
          }
        }
        /**
         * 设置形态索引
         * @param index 形态索引
         */


        setFormIndex(index) {
          if (this._formIndex !== index) {
            this._formIndex = index;
            this._bOrderAttack = true;
            this._orderIndex = 0;
            this._data = this._datas[this._formIndex];
            this._idleName = `idle${this._formIndex + 1}`;

            if (index === 0) {
              this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
                error: Error()
              }), GameEnum) : GameEnum).BossAction.Appear);
            }

            this._orderAtkArr = [];

            for (let i = 0; i < this._data.attackActions.length; i++) {
              this._orderAtkArr.push(i);
            }

            this._atkPointDatas = [];

            for (const point of this._data.attackPoints) {
              const data = [point.bAvailable, point];

              this._atkPointDatas.push(data);
            }

            this._atkActions = [...this._data.attackActions];
          }
        }
        /**
         * 进入下一形态
         */


        enterNextForm() {
          if (this._formIndex < this._datas.length - 1) {
            this._formIndex++;
            this.setFormIndex(this._formIndex);
          }
        }
        /**
         * 开始战斗
         */


        startBattle() {
          this._startNormalTrack();

          this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction.Normal);
          this.colliderEnabled = true;
        }
        /**
         * 更新游戏逻辑
         * @param deltaTime 每帧时间
         */


        updateGameLogic(deltaTime) {
          if (!this.isDead) {
            let BossAction = (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).BossAction;

            switch (this._action) {
              case BossAction.Normal:
                this._processNextWayPoint(deltaTime);

                this._updateMove(deltaTime);

                this._processNextAttack(deltaTime);

                break;

              case BossAction.Appear:
                this._updateMove(deltaTime);

                if (this._bArriveDes) {
                  this.setAction(BossAction.Transform);
                }

                break;

              case BossAction.Transform:
                if (this._transFormMove) {
                  this._updateMove(deltaTime);
                }

                break;

              case BossAction.AttackPrepare:
                this._processNextWayPoint(deltaTime);

                if (this._bAttackMove) {
                  this._updateMove(deltaTime);
                }

                break;

              case BossAction.AttackIng:
                this._processNextWayPoint(deltaTime);

                if (this._bAttackMove) {
                  this._updateMove(deltaTime);
                }

                this._udpateShoot(deltaTime);

                break;

              case BossAction.AttackOver:
                this._processNextWayPoint(deltaTime);

                if (this._bAttackMove) {
                  this._updateMove(deltaTime);
                }

                this.setAction(BossAction.Normal);
                break;

              case BossAction.Blast:
                break;
            }
          }
        }
        /**
         * 是否可被攻击
         */


        isDamageable() {
          return this._bDamageable;
        }
        /**
         * 是否可移除
         */


        get removeAble() {
          return this._bRemoveable;
        }

        set removeAble(value) {
          this._bRemoveable = value;
        }
        /**
         * 开始出现轨迹
         */


        _startAppearTrack() {
          const trackGroup = new (_crd && TrackGroup === void 0 ? (_reportPossibleCrUseOfTrackGroup({
            error: Error()
          }), TrackGroup) : TrackGroup)();
          trackGroup.loopNum = 1;
          trackGroup.trackIDs = [this._data.appearParam[2]];
          trackGroup.speeds = [this._data.appearParam[3]];
          trackGroup.trackIntervals = [0];

          this._trackCom.init(this, [trackGroup], [], this._data.appearParam[0], this._data.appearParam[1]);

          this._trackCom.setTrackAble(true);

          this._trackCom.startTrack();
        }
        /**
         * 开始正常轨迹
         */


        _startNormalTrack() {
          this._trackCom.init(this, this._data.trackGroups, [], this.node.x, this.node.y);

          this._trackCom.setTrackAble(true);

          this._trackCom.startTrack();

          this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction.Normal);
        }
        /**
         * 移动到指定位置
         * @param x X 坐标
         * @param y Y 坐标
         * @param speed 移动速度
         * @param transformMove 是否为变形移动
         */


        moveToPos(x, y, speed, transformMove = false) {
          this._moveToX = x;
          this._moveToY = y;
          this._moveSpeed = speed;
          this._bArriveDes = false;
          this._transFormMove = transformMove;
        }

        setPos(x, y, update = true) {
          this.node.setPosition(x, y);
          this._posX = x;
          this._posY = y;
        }
        /**
         * 处理下一个路径点
         * @param deltaTime 每帧时间
         */


        _processNextWayPoint(deltaTime) {
          if (this._bArriveDes && this._data.trackGroups.length === 0) {
            this._nextWayPointTime += deltaTime;

            if (this._nextWayPointTime > this._nextWayPointInterval) {
              this._nextWayPointInterval = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                error: Error()
              }), Tools) : Tools).getRandomInArray(this._data.wayPointIntervals);
              this._nextWayPointTime = 0;

              if (this._bFirstWayPoint) {
                this._bFirstWayPoint = false;
              } else {
                const index = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).random_int(0, this._data.wayPointXs.length - 1);
                this._nextWayPointX = this._data.wayPointXs[index];
                this._nextWayPointY = this._data.wayPointYs[index];
                this._nextWaySpeed = (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
                  error: Error()
                }), Tools) : Tools).getRandomInArray(this._data.speeds);
                this.moveToPos(this._nextWayPointX, this._nextWayPointY, this._nextWaySpeed);
              }
            }
          }
        }

        _updateMove(deltaTime) {
          if (this._action === (_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction.Appear || this._data.trackGroups.length > 0) {
            // 如果 Boss 在出现阶段或有轨迹组，则更新轨迹逻辑
            this._trackCom.updateGameLogic(deltaTime);
          } else if (!this._bArriveDes) {
            // 如果未到达目标位置，则更新移动逻辑
            // this._prePosX = this._posX;
            // this._prePosY = this._posY;
            const deltaX = this._moveToX - this._posX;
            const deltaY = this._moveToY - this._posY;
            const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
            let moveX = 0;
            let moveY = 0; // 如果距离小于等于移动速度，则直接到达目标点

            if (distance <= this._moveSpeed) {
              moveX = deltaX;
              moveY = deltaY;
            } // 否则按比例移动
            else {
              moveX = this._moveSpeed * deltaX / distance;
              moveY = this._moveSpeed * deltaY / distance;
            } // 更新位置


            this._posX += moveX;
            this._posY += moveY;
            this.setPos(this._posX, this._posY); // 检查是否到达目的地（当移动量很小时认为已到达）

            this._bArriveDes = Math.abs(moveX) < 0.5 && Math.abs(moveY) < 0.5;
          }
        }
        /**
         * 处理下一次攻击
         * @param deltaTime 每帧时间
         */


        _processNextAttack(deltaTime) {// if (this._shootAble && this._action === GameEnum.BossAction.Normal) {
          //     this._nextAttackTime += deltaTime;
          //     if (this._nextAttackTime > this._nextAttackInterval) {
          //         this._nextAttackInterval = Tools.getRandomInArray(this._data!.attackIntervals)!;
          //         this._nextAttackTime = 0;
          //         let attackAction = null;
          //         if (this._bOrderAttack) {
          //             const randomIndex = Tools.getRandomInArray(this._orderAtkArr)!;
          //             Tools.arrRemove(this._orderAtkArr, randomIndex);
          //             attackAction = this._atkActions[randomIndex];
          //             this._orderIndex++;
          //             if (this._orderIndex > this._atkActions.length - 1) {
          //                 this._bOrderAttack = false;
          //             }
          //         } else {
          //             attackAction = Tools.getRandomInArray(this._atkActions);
          //         }
          //         if (attackAction) {
          //             this._bAttackMove = attackAction.bAtkMove;
          //             this._attackID = attackAction.atkActId;
          //             this._attackPoints.splice(0);
          //             for (const pointId of attackAction.atkPointId) {
          //                 const pointData = this._atkPointDatas[pointId];
          //                 if (pointData[0]) {
          //                     let attackPoint = this._atkPointsPool[pointId]
          //                     if (!attackPoint) {
          //                         const pointNode = new Node();
          //                         this.node.addChild(pointNode);
          //                         attackPoint = pointNode.addComponent(AttackPoint);
          //                         this._atkPointsPool.push(attackPoint);
          //                     }
          //                     attackPoint.initForBoss(pointData[1], this);
          //                     this._attackPoints.push(attackPoint);
          //                 }
          //             }
          //             if (this._attackPoints.length > 0) {
          //                 this.setAction(GameEnum.BossAction.AttackPrepare);
          //             }
          //         }
          //     }
          // }
        }
        /**
         * 更新射击逻辑
         * @param deltaTime 每帧时间
         */


        async _udpateShoot(deltaTime) {// if (this._shootAble) {
          //     let allAttacksOver = true;
          //     for (const attackPoint of this._attackPoints) {
          //         await attackPoint.updateGameLogic(deltaTime);
          //         if (!attackPoint.isAttackOver()) {
          //             allAttacksOver = false;
          //         }
          //     }
          //     if (allAttacksOver) {
          //         this.setAction(GameEnum.BossAction.AttackOver);
          //     }
          // }
        }

        onCollide(collider) {
          if (!this.isDead && this.isDamageable()) {
            if (collider.entity instanceof (_crd && Bullet === void 0 ? (_reportPossibleCrUseOfBullet({
              error: Error()
            }), Bullet) : Bullet)) {
              let damage = collider.entity.getAttack();

              try {
                (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
                  error: Error()
                }), GameIns) : GameIns).hurtEffectManager.createHurtNumByType(collider.entity.node.getPosition(), damage);
              } catch (error) {
                console.error(error);
              }

              this.hurt(-damage);
            }
          }
        }

        hurt(damage) {
          if (this.isDead || !this.isDamageable()) {
            return false;
          }

          this.curHp += damage;

          if (this.curHp < 0) {
            this.curHp = 0;
          } else if (this.curHp > this.maxHp) {
            this.curHp = this.maxHp;
          }

          this._refreshHpBar();

          this.checkHp();

          if (!this.isDead) {// this.role.winkWhite();
          }

          return true;
        }

        checkHp() {
          if (this.curHp <= 0) {
            this._toDie();

            return true;
          }

          return false;
        }

        _toDie() {
          if (!super.toDie()) {
            return false;
          }

          this.colliderEnabled = false;

          if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();

            this._hpWhiteTween = null;
          }

          this.onDie();
          this.setAction((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
            error: Error()
          }), GameEnum) : GameEnum).BossAction.Blast);

          this._playDieAnim();
        }

        onDie() {
          for (const plane of (_crd && GameIns === void 0 ? (_reportPossibleCrUseOfGameIns({
            error: Error()
          }), GameIns) : GameIns).enemyManager.planes) {
            plane.die((_crd && GameEnum === void 0 ? (_reportPossibleCrUseOfGameEnum({
              error: Error()
            }), GameEnum) : GameEnum).EnemyDestroyType.Die);
          }

          this.removeBullets();
        }

        _playDieAnim() {
          // this._skel.setAnimation(0, `shake${this._bossPlane.formIndex + 1}`, false);
          this._bRemoveable = true;
        }

        _refreshHpBar() {
          const hpRatio = this.curHp / this.maxHp;
          const isDecreasing = hpRatio < this.hpSpr.fillRange; // 更新血条显示

          this.hpSpr.fillRange = hpRatio; // 停止之前的血条动画

          if (this._hpWhiteTween) {
            this._hpWhiteTween.stop();

            this._hpWhiteTween = null;
          } // 如果血量减少，播放白色血条的动画


          if (isDecreasing) {
            const duration = Math.abs(this.hpWhite.fillRange - this.hpSpr.fillRange);
            this._hpWhiteTween = tween(this.hpWhite).to(duration, {
              fillRange: this.hpSpr.fillRange
            }).call(() => {
              this._hpWhiteTween = null;
            }).start();
          } else {
            this.hpWhite.fillRange = hpRatio;
          }
        }
        /**
         * 添加子弹
         * @param bullet 子弹对象
         */


        addBullet(bullet) {
          if (this.bullets) {
            this.bullets.push(bullet);
          }
        }
        /**
         * 移除子弹
         * @param bullet 子弹对象
         */


        removeBullet(bullet) {
          if (this.bullets) {
            const index = this.bullets.indexOf(bullet);

            if (index >= 0) {
              this.bullets.splice(index, 1);
            }
          }
        }
        /**
         * 移除子弹
         */


        removeBullets() {
          for (const bullet of this.bullets) {
            bullet.dieRemove();
          }

          this.bullets.splice(0);
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "role", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "hpBg", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "hpSpr", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "hpWhite", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function () {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=c32e8281de7d774efde91ad12864c46192ae0350.js.map