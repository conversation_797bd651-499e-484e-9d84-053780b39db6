System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BundleName, ButtonPlus, BaseUI, UILayer, UIMgr, MyApp, List, HomeUI, MailCellUI, _dec, _dec2, _dec3, _class, _class2, _descriptor, _descriptor2, _crd, ccclass, property, MailUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfButtonPlus(extras) {
    _reporterNs.report("ButtonPlus", "db://assets/bundles/common/script/ui/common/components/button/ButtonPlus", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfcsproto(extras) {
    _reporterNs.report("csproto", "../../../../../../scripts/AutoGen/PB/cs_proto.js", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "../../../../../../scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfList(extras) {
    _reporterNs.report("List", "../../common/components/list/List", _context.meta, extras);
  }

  function _reportPossibleCrUseOfHomeUI(extras) {
    _reporterNs.report("HomeUI", "../HomeUI", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMailCellUI(extras) {
    _reporterNs.report("MailCellUI", "./MailCellUI", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      ButtonPlus = _unresolved_3.ButtonPlus;
    }, function (_unresolved_4) {
      BaseUI = _unresolved_4.BaseUI;
      UILayer = _unresolved_4.UILayer;
      UIMgr = _unresolved_4.UIMgr;
    }, function (_unresolved_5) {
      MyApp = _unresolved_5.MyApp;
    }, function (_unresolved_6) {
      List = _unresolved_6.default;
    }, function (_unresolved_7) {
      HomeUI = _unresolved_7.HomeUI;
    }, function (_unresolved_8) {
      MailCellUI = _unresolved_8.MailCellUI;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "df54eIR+M9E8Z45UuDhut+i", "MailUI", undefined);

      __checkObsolete__(['_decorator', 'Node']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("MailUI", MailUI = (_dec = ccclass('MailUI'), _dec2 = property(_crd && ButtonPlus === void 0 ? (_reportPossibleCrUseOfButtonPlus({
        error: Error()
      }), ButtonPlus) : ButtonPlus), _dec3 = property(_crd && List === void 0 ? (_reportPossibleCrUseOfList({
        error: Error()
      }), List) : List), _dec(_class = (_class2 = class MailUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          _initializerDefineProperty(this, "btnClose", _descriptor, this);

          _initializerDefineProperty(this, "list", _descriptor2, this);

          this.keys = [];
          this.items = [];
        }

        static getUrl() {
          return "prefab/ui/mail/MailUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Default;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).Home;
        }

        onLoad() {
          this.btnClose.addClick(this.closeUI, this);
          this.keys = Array.from((_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.TbItem.getDataMap().keys());
          this.list.node.active = true;
          this.list.numItems = Math.min(this.keys.length, 20);
        }

        closeUI() {
          return _asyncToGenerator(function* () {
            (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).closeUI(MailUI);
            yield (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
              error: Error()
            }), UIMgr) : UIMgr).openUI(_crd && HomeUI === void 0 ? (_reportPossibleCrUseOfHomeUI({
              error: Error()
            }), HomeUI) : HomeUI);
          })();
        }

        onShow() {
          return _asyncToGenerator(function* () {})();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

        onDestroy() {}

        start() {}

        onListRender(listItem, row) {
          // 有数据要在 this.list.numItems 之前设置
          var key = row < this.keys.length ? this.keys[row] : this.keys[0];
          var cell = listItem.getComponent(_crd && MailCellUI === void 0 ? (_reportPossibleCrUseOfMailCellUI({
            error: Error()
          }), MailCellUI) : MailCellUI);

          if (cell !== null) {
            cell.setData(key);
          }
        }

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "btnClose", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "list", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=2ae24e664f65cb9c80bb436dbfe37fceba938b7f.js.map