{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/MailCellUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "Component", "Label", "Sprite", "MyApp", "UIMgr", "PopupUI", "ccclass", "property", "MailCellUI", "itemID", "start", "update", "deltaTime", "onButtonClick", "openUI", "setData", "item", "lubanTables", "TbItem", "get", "mailTitle", "string", "name", "mailContent", "resMgr", "loadCoin", "mailIcon"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAEtCC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,O,iBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U;;4BAGjBU,U,WADZF,OAAO,CAAC,YAAD,C,UAGHC,QAAQ,CAACL,MAAD,C,UAGRK,QAAQ,CAACN,KAAD,C,UAGRM,QAAQ,CAACN,KAAD,C,UAGRM,QAAQ,CAACR,MAAD,C,2BAZb,MACaS,UADb,SACgCR,SADhC,CAC0C;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eActCS,MAdsC,GAcd,IAdc;AAAA;;AAgBtCC,QAAAA,KAAK,GAAG,CAEP;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AACDC,QAAAA,aAAa,GAAG;AACZ;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA,kCAAsB,UAAU,KAAKL,MAArC;AACH;;AACMM,QAAAA,OAAO,CAACN,MAAD,EAAuB;AACjC,eAAKA,MAAL,GAAcA,MAAd;AACA,cAAIO,IAAyB,GAAG;AAAA;AAAA,8BAAMC,WAAN,CAAkBC,MAAlB,CAAyBC,GAAzB,CAA6BV,MAA7B,CAAhC;AACA,eAAKW,SAAL,CAAgBC,MAAhB,GAAyB,CAAAL,IAAI,QAAJ,YAAAA,IAAI,CAAEM,IAAN,KAAc,EAAvC;AACA,eAAKC,WAAL,CAAkBF,MAAlB,GAA2B,CAAAL,IAAI,QAAJ,YAAAA,IAAI,CAAEM,IAAN,KAAc,EAAzC;AACA;AAAA;AAAA,8BAAME,MAAN,CAAaC,QAAb,CAAsB,KAAKC,QAA3B;AACH;;AAhCqC,O;;;;;iBAGZ,I;;;;;;;iBAGA,I;;;;;;;iBAGE,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, Button, Component, Label, Sprite } from 'cc';\r\nimport { ResItem } from '../../../../../../scripts/AutoGen/Luban/schema';\r\nimport { MyApp } from '../../../../../../scripts/MyApp';\r\nimport { UIMgr } from '../../../../../../scripts/ui/UIMgr';\r\nimport { PopupUI } from '../PopupUI';\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('MailCellUI')\r\nexport class MailCellUI extends Component {\r\n\r\n    @property(Sprite)\r\n    mailIcon: Sprite | null = null;\r\n\r\n    @property(Label)\r\n    mailTitle: Label | null = null;\r\n\r\n    @property(Label)\r\n    mailContent: Label | null = null;\r\n\r\n    @property(Button)\r\n    btnClick: Button | null = null;\r\n\r\n    itemID: number | null = null;\r\n\r\n    start() {\r\n\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n    onButtonClick() {\r\n        UIMgr.openUI(PopupUI, '物品ID：' + this.itemID);\r\n    }\r\n    public setData(itemID: number): void {\r\n        this.itemID = itemID;\r\n        let item: ResItem | undefined = MyApp.lubanTables.TbItem.get(itemID);\r\n        this.mailTitle!.string = item?.name || \"\";\r\n        this.mailContent!.string = item?.name || \"\";\r\n        MyApp.resMgr.loadCoin(this.mailIcon!);\r\n    }\r\n}\r\n\r\n\r\n"]}