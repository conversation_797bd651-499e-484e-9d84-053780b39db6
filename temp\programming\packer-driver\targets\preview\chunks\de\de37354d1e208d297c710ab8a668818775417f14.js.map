{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/plane/components/back_pack/SortTypeDropdown.ts"], "names": ["_decorator", "Component", "Label", "DataMgr", "DataEvent", "EventMgr", "MyApp", "logDebug", "PlaneUIEvent", "ButtonPlus", "DropDown", "BagSortType", "TabStatus", "ccclass", "property", "SortTypeDropdown", "_tabStatus", "None", "_sortType", "_bagTabOptions", "key", "Quality", "label", "Part", "_mergeTabOptions", "<PERSON><PERSON>", "onLoad", "getComponent", "addClick", "onDropDownOptionClick", "on", "TabChange", "onTabChangeEvent", "UpdateBagGrids", "sortBag", "ItemsRefresh", "tabStatus", "optionKeyList", "Bag", "map", "v", "dropDown", "init", "onOptionRender", "bind", "optNode", "optKey", "getComponentInChildren", "opt", "find", "string", "items", "sortEquipsInBagTabStatus", "sortItems", "sortEquipsInCombineTabStatus", "emit", "SortTypeChange", "sorted", "bag", "for<PERSON>ach", "cfg", "lubanMgr", "table", "TbItem", "get", "item_id", "push", "sort", "a", "b", "add_time", "tbEquip", "TbEquip", "emptySlots", "equip", "eqSlots", "getEmptySlots", "length", "join", "emptySlotEquips", "unequippedEquips", "item", "isEmptyEquipClass", "some", "e", "equip_class", "equipClass", "sortFn", "aCfg", "bCfg", "quality", "sortedEmptySlotEquips", "sortedUnequippedEquips", "index", "equipType", "includes", "count", "getEquippedSlots", "equip_id", "guid", "combineConfigs", "eqCombine", "calculateAllCombinePossible", "materialInfos", "combineMainMaterial", "getByPos", "info", "isMainMaterial", "isSubMaterial", "canSynthesize", "combineQuality", "isRelatedToSlotMain", "materialQuality", "itemQuality", "lubanTables", "eqQuality", "mainMaterial", "consumeItems", "subMaterials", "slice", "id", "m", "isSameMatType", "material", "type", "status", "related"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;;AACvBC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AAEAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,kBAAAA,W;AAAaC,MAAAA,S,kBAAAA,S;;;;;;;;;OAChB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBd,U;;kCAcjBe,gB,WADZF,OAAO,CAAC,kBAAD,C,UAEHC,QAAQ;AAAA;AAAA,+B,2BAFb,MACaC,gBADb,SACsCd,SADtC,CACgD;AAAA;AAAA;;AAAA;;AAAA,eAIpCe,UAJoC,GAIZ;AAAA;AAAA,sCAAUC,IAJE;AAAA,eAKpCC,SALoC,GAKX;AAAA;AAAA,0CAAYD,IALD;AAAA,eAMpCE,cANoC,GAMoB,CAC5D;AAAEC,YAAAA,GAAG,EAAE;AAAA;AAAA,4CAAYC,OAAnB;AAA4BC,YAAAA,KAAK,EAAE;AAAnC,WAD4D,EAE5D;AAAEF,YAAAA,GAAG,EAAE;AAAA;AAAA,4CAAYG,IAAnB;AAAyBD,YAAAA,KAAK,EAAE;AAAhC,WAF4D,CANpB;AAAA,eAUpCE,gBAVoC,GAUsB,CAC9D;AAAEJ,YAAAA,GAAG,EAAE;AAAA;AAAA,4CAAYK,KAAnB;AAA0BH,YAAAA,KAAK,EAAE;AAAjC,WAD8D,CAVtB;AAAA;;AAc5CI,QAAAA,MAAM,GAAG;AACL,eAAKC,YAAL;AAAA;AAAA,wCAA+BC,QAA/B,CAAwC,KAAKC,qBAA7C,EAAoE,IAApE;AACA;AAAA;AAAA,oCAASC,EAAT,CAAY;AAAA;AAAA,4CAAaC,SAAzB,EAAoC,KAAKC,gBAAzC,EAA2D,IAA3D;AACA;AAAA;AAAA,oCAASF,EAAT,CAAY;AAAA;AAAA,4CAAaG,cAAzB,EAAyC,KAAKC,OAA9C,EAAuD,IAAvD;AACA;AAAA;AAAA,oCAASJ,EAAT,CAAY;AAAA;AAAA,sCAAUK,YAAtB,EAAoC,KAAKD,OAAzC,EAAkD,IAAlD;AACH;;AAEDF,QAAAA,gBAAgB,CAACI,SAAD,EAAuB;AACnC,cAAIA,SAAS,KAAK,KAAKpB,UAAvB,EAAmC;AAC/B;AACH;;AACD,eAAKA,UAAL,GAAkBoB,SAAlB;AACA,cAAIC,aAAJ;;AACA,cAAID,SAAS,KAAK;AAAA;AAAA,sCAAUE,GAA5B,EAAiC;AAC7BD,YAAAA,aAAa,GAAG,KAAKlB,cAAL,CAAoBoB,GAApB,CAAwBC,CAAC,IAAIA,CAAC,CAACpB,GAA/B,CAAhB;AACH,WAFD,MAEO;AACHiB,YAAAA,aAAa,GAAG,KAAKb,gBAAL,CAAsBe,GAAtB,CAA0BC,CAAC,IAAIA,CAAC,CAACpB,GAAjC,CAAhB;AACH;;AACD,eAAKqB,QAAL,CAAeC,IAAf,CAAoBL,aAApB,EAAmC,KAAKM,cAAL,CAAoBC,IAApB,CAAyB,IAAzB,CAAnC,EAAmE,KAAKf,qBAAL,CAA2Be,IAA3B,CAAgC,IAAhC,CAAnE;AACA,eAAKV,OAAL;AACH;;AAEDS,QAAAA,cAAc,CAACE,OAAD,EAAgBC,MAAhB,EAAgC;AAC1C,cAAMxB,KAAK,GAAGuB,OAAO,CAACE,sBAAR,CAA+B7C,KAA/B,CAAd;AACA,cAAI8C,GAAJ;;AACA,kBAAQ,KAAKhC,UAAb;AACI,iBAAK;AAAA;AAAA,wCAAUsB,GAAf;AACIU,cAAAA,GAAG,GAAG,KAAK7B,cAAL,CAAoB8B,IAApB,CAAyBT,CAAC,IAAIA,CAAC,CAACpB,GAAF,IAAS0B,MAAvC,CAAN;AACAxB,cAAAA,KAAK,CAAC4B,MAAN,GAAeF,GAAG,CAAC1B,KAAnB;AACA;;AACJ,iBAAK;AAAA;AAAA,wCAAUG,KAAf;AACIuB,cAAAA,GAAG,GAAG,KAAKxB,gBAAL,CAAsByB,IAAtB,CAA2BT,CAAC,IAAIA,CAAC,CAACpB,GAAF,IAAS0B,MAAzC,CAAN;AACAxB,cAAAA,KAAK,CAAC4B,MAAN,GAAeF,GAAG,CAAC1B,KAAnB;AACA;;AACJ;AACI;AACA;AAXR;AAaH;;AAEDO,QAAAA,qBAAqB,CAACiB,MAAD,EAAiB;AAClC,cAAIE,GAAJ;;AACA,kBAAQ,KAAKhC,UAAb;AACI,iBAAK;AAAA;AAAA,wCAAUsB,GAAf;AACIU,cAAAA,GAAG,GAAG,KAAK7B,cAAL,CAAoB8B,IAApB,CAAyBT,CAAC,IAAIA,CAAC,CAACpB,GAAF,IAAS0B,MAAvC,CAAN;AACA;;AACJ,iBAAK;AAAA;AAAA,wCAAUrB,KAAf;AACIuB,cAAAA,GAAG,GAAG,KAAKxB,gBAAL,CAAsByB,IAAtB,CAA2BT,CAAC,IAAIA,CAAC,CAACpB,GAAF,IAAS0B,MAAzC,CAAN;AACA;;AACJ;AACI;AACA;AATR;;AAWA,eAAKZ,OAAL;AACH;;AAEOA,QAAAA,OAAO,GAAG;AACd,cAAIiB,KAAK,GAAG,EAAZ;;AACA,kBAAQ,KAAKnC,UAAb;AACI,iBAAK;AAAA;AAAA,wCAAUsB,GAAf;AACIa,cAAAA,KAAK,GAAG,CAAC,GAAG,KAAKC,wBAAL,EAAJ,EAAqC,GAAG,KAAKC,SAAL,EAAxC,CAAR;AACA;;AACJ,iBAAK;AAAA;AAAA,wCAAU5B,KAAf;AACI0B,cAAAA,KAAK,GAAG,KAAKG,4BAAL,EAAR;AACA;;AACJ;AACI;AACA;AATR;;AAWA;AAAA;AAAA,oCAASC,IAAT,CAAc;AAAA;AAAA,4CAAaC,cAA3B,EAA2C,KAAKxC,UAAhD,EAA4DmC,KAA5D;AACH;;AAEOE,QAAAA,SAAS,GAAG;AAChB,cAAII,MAA4B,GAAG,EAAnC;AACA;AAAA;AAAA,kCAAQC,GAAR,CAAYP,KAAZ,CAAkBQ,OAAlB,CAA0BnB,CAAC,IAAI;AAC3B,gBAAMoB,GAAG,GAAG;AAAA;AAAA,gCAAMC,QAAN,CAAeC,KAAf,CAAqBC,MAArB,CAA4BC,GAA5B,CAAgCxB,CAAC,CAACyB,OAAlC,CAAZ;;AACA,gBAAIL,GAAJ,EAAS;AACLH,cAAAA,MAAM,CAACS,IAAP,CAAY1B,CAAZ;AACH;AACJ,WALD;AAMAiB,UAAAA,MAAM,CAACU,IAAP,CAAY,CAACC,CAAD,EAAwBC,CAAxB,KAAkD;AAC1D,mBAAOA,CAAC,CAACC,QAAF,GAAcF,CAAC,CAACE,QAAvB;AACH,WAFD;AAGA,iBAAOb,MAAP;AACH;;AAEOL,QAAAA,wBAAwB,GAAG;AAC/B,cAAMmB,OAAO,GAAG;AAAA;AAAA,8BAAMV,QAAN,CAAeC,KAAf,CAAqBU,OAArC,CAD+B,CAE/B;;AACA,cAAMC,UAAyC,GAAG;AAAA;AAAA,kCAAQC,KAAR,CAAcC,OAAd,CAAsBC,aAAtB,EAAlD;AAEA;AAAA;AAAA,oCAAS,SAAT,2CAA2D;AAAA;AAAA,kCAAQlB,GAAR,CAAYP,KAAZ,CAAkB0B,MAA7E,sBAAoGJ,UAAU,CAACK,IAAX,CAAgB,GAAhB,CAApG,EAL+B,CAO/B;AACA;AACA;;AACA,cAAMC,eAAqC,GAAG,EAA9C;AACA,cAAMC,gBAAsC,GAAG,EAA/C;;AAX+B,uCAaO;AAClC,gBAAMpB,GAAG,GAAGW,OAAO,CAACP,GAAR,CAAYiB,IAAI,CAAChB,OAAjB,CAAZ;AACA,gBAAI,CAACL,GAAL;AAEA;;AACA,gBAAMsB,iBAAiB,GAAGT,UAAU,CAACU,IAAX,CAAgBC,CAAC,IAAIA,CAAC,CAACC,WAAF,KAAkBzB,GAAG,CAAC0B,UAA3C,CAA1B;;AACA,gBAAIJ,iBAAJ,EAAuB;AACnBH,cAAAA,eAAe,CAACb,IAAhB,CAAqBe,IAArB;AACH,aAFD,MAEO;AACHD,cAAAA,gBAAgB,CAACd,IAAjB,CAAsBe,IAAtB;AACH;AACJ,WAxB8B;;AAa/B,eAAK,IAAMA,IAAX,IAAmB;AAAA;AAAA,kCAAQvB,GAAR,CAAYP,KAA/B;AAAA,yBAEc;AAFd,WAb+B,CA0B/B;;;AACA,cAAMoC,MAAM,GAAG,CAACnB,CAAD,EAAwBC,CAAxB,KAAkD;AAC7D,gBAAMmB,IAAI,GAAGjB,OAAO,CAACP,GAAR,CAAYI,CAAC,CAACH,OAAd,CAAb;AACA,gBAAMwB,IAAI,GAAGlB,OAAO,CAACP,GAAR,CAAYK,CAAC,CAACJ,OAAd,CAAb;;AAEA,gBAAI,KAAK/C,SAAL,KAAmB;AAAA;AAAA,4CAAYK,IAAnC,EAAyC;AACrC;AACA,qBAAOiE,IAAI,CAACF,UAAL,GAAmBG,IAAI,CAACH,UAAxB,IAAuCG,IAAI,CAACC,OAAL,GAAgBF,IAAI,CAACE,OAAnE;AACH,aAHD,MAGO;AACH;AACA,qBAAOD,IAAI,CAACC,OAAL,GAAgBF,IAAI,CAACE,OAArB,IAAiCF,IAAI,CAACF,UAAL,GAAkBG,IAAI,CAACH,UAA/D;AACH;AACJ,WAXD,CA3B+B,CAwC/B;;;AACA,cAAMK,qBAAqB,GAAGZ,eAAe,CAACZ,IAAhB,CAAqBoB,MAArB,CAA9B;AACA,cAAMK,sBAAsB,GAAGZ,gBAAgB,CAACb,IAAjB,CAAsBoB,MAAtB,CAA/B,CA1C+B,CA6C/B;;AACA,cAAM9B,MAAM,GAAG,CAAC,GAAGkC,qBAAJ,EAA2B,GAAGC,sBAA9B,CAAf,CA9C+B,CAgD/B;;AACAnC,UAAAA,MAAM,CAACE,OAAP,CAAe,CAACyB,CAAD,EAAIS,KAAJ,KAAc;AACzB,gBAAMjC,GAAG,GAAGW,OAAO,CAACP,GAAR,CAAYoB,CAAC,CAACnB,OAAd,CAAZ;AACA,gBAAM6B,SAAS,GAAGH,qBAAqB,CAACI,QAAtB,CAA+BX,CAA/B,IAAoC,OAApC,GAA8C,SAAhE;AACA;AAAA;AAAA,sCAAS,SAAT,EAAuBS,KAAK,GAAG,CAA/B,UAAqCC,SAArC,YAAqDV,CAAC,CAACnB,OAAvD,sBAAqEL,GAAG,CAAC0B,UAAzE,sBAA0F1B,GAAG,CAAC8B,OAA9F,sBAA4GN,CAAC,CAACY,KAA9G;AACH,WAJD;AAMA,iBAAOvC,MAAP;AACH;AAGD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;;;AACIH,QAAAA,4BAA4B,GAAU;AAAA;;AAClC,cAAMH,KAAK,GAAG,CAAC,GAAG;AAAA;AAAA,kCAAQO,GAAR,CAAYP,KAAhB,EAAuB,GAAG;AAAA;AAAA,kCAAQuB,KAAR,CAAcC,OAAd,CAAsBsB,gBAAtB,GAAyC1D,GAAzC,CAA6CC,CAAC,KAAgB;AAClGyB,YAAAA,OAAO,EAAEzB,CAAC,CAAC0D,QADuF;AAElGF,YAAAA,KAAK,EAAE,CAF2F;AAGlGG,YAAAA,IAAI,EAAE3D,CAAC,CAAC2D;AAH0F,WAAhB,CAA9C,CAA1B,CAAd;AAKA,cAAMC,cAAc,GAAG;AAAA;AAAA,kCAAQ1B,KAAR,CAAc2B,SAAd,CAAwBC,2BAAxB,CAAoDnD,KAApD,CAAvB;AACA,cAAIoD,aAAiC,GAAG,EAAxC,CAPkC,CAQlC;;AACA,cAAMC,mBAAmB,4BAAG;AAAA;AAAA,kCAAQ9B,KAAR,CAAc2B,SAAd,CAAwBI,QAAxB,CAAiC,CAAjC,CAAH,qBAAG,sBAAqCxB,IAAjE;AAEA9B,UAAAA,KAAK,CAACQ,OAAN,CAAcnB,CAAC,IAAI;AAAA;;AACf,gBAAMkE,IAAsB,GAAG;AAC3BzB,cAAAA,IAAI,EAAEzC,CADqB;AAE3BmE,cAAAA,cAAc,EAAE,KAFW;AAG3BC,cAAAA,aAAa,EAAE,KAHY;AAI3BC,cAAAA,aAAa,EAAE,KAJY;AAK3BC,cAAAA,cAAc,EAAE,CALW;AAM3BC,cAAAA,mBAAmB,EAAE,KANM;AAO3BC,cAAAA,eAAe,EAAE;AAPU,aAA/B;AASA,gBAAMC,WAAW,4BAAG;AAAA;AAAA,gCAAMC,WAAN,CAAkBnD,MAAlB,CAAyBC,GAAzB,CAA6BxB,CAAC,CAACyB,OAA/B,CAAH,qBAAG,sBAA0CyB,OAA9D;;AACA,gBAAI,CAACuB,WAAL,EAAkB;AAAA;;AACd,kBAAME,SAAS,4BAAG;AAAA;AAAA,kCAAMD,WAAN,CAAkB1C,OAAlB,CAA0BR,GAA1B,CAA8BxB,CAAC,CAACyB,OAAhC,CAAH,qBAAG,sBAA2CyB,OAA7D;;AACA,kBAAI,CAACyB,SAAL,EAAgB;AACZ;AACH;;AACDT,cAAAA,IAAI,CAACM,eAAL,GAAuBG,SAAvB;AACH,aAND,MAMO;AACHT,cAAAA,IAAI,CAACM,eAAL,GAAuBC,WAAvB;AACH,aAnBc,CAoBf;;;AACAV,YAAAA,aAAa,CAACrC,IAAd,CAAmBwC,IAAnB;AACH,WAtBD;AAwBAN,UAAAA,cAAc,CAACzC,OAAf,CAAuBC,GAAG,IAAI;AAC1B,gBAAMwD,YAAY,GAAGxD,GAAG,CAACyD,YAAJ,CAAiB,CAAjB,CAArB;AACA,gBAAMC,YAAY,GAAG1D,GAAG,CAACyD,YAAJ,CAAiBE,KAAjB,CAAuB,CAAvB,CAArB;AACAhB,YAAAA,aAAa,CAAC5C,OAAd,CAAsBnB,CAAC,IAAI;AACvB,kBAAI4E,YAAY,CAACI,EAAb,IAAmBhF,CAAC,CAACyC,IAAF,CAAOhB,OAA9B,EAAuC;AACnCzB,gBAAAA,CAAC,CAACmE,cAAF,GAAmB,IAAnB;AACAnE,gBAAAA,CAAC,CAACqE,aAAF,GAAkB,IAAlB;;AACA,oBAAI,CAAAL,mBAAmB,QAAnB,YAAAA,mBAAmB,CAAEvC,OAArB,KAAgCzB,CAAC,CAACyC,IAAF,CAAOhB,OAA3C,EAAoD;AAChDzB,kBAAAA,CAAC,CAACuE,mBAAF,GAAwB,IAAxB;AACH;AACJ;AACJ,aARD;AASAO,YAAAA,YAAY,CAAC3D,OAAb,CAAqBnB,CAAC,IAAI;AACtB+D,cAAAA,aAAa,CAAC5C,OAAd,CAAsB8D,CAAC,IAAI;AACvB,oBAAI;AAAA;AAAA,wCAAQ/C,KAAR,CAAc2B,SAAd,CAAwBqB,aAAxB,CAAsClF,CAAC,CAACgF,EAAxC,EAA4CC,CAAC,CAACxC,IAAF,CAAOhB,OAAnD,CAAJ,EAAkE;AAC9DwD,kBAAAA,CAAC,CAACb,aAAF,GAAkB,IAAlB;AACAa,kBAAAA,CAAC,CAACZ,aAAF,GAAkB,IAAlB;AACAY,kBAAAA,CAAC,CAACX,cAAF,GAAmBlD,GAAG,CAAC8B,OAAJ,IAAe,CAAlC;;AACA,sBAAI,CAAAc,mBAAmB,QAAnB,YAAAA,mBAAmB,CAAEvC,OAArB,KAAgCmD,YAAY,CAACI,EAAjD,EAAqD;AACjDC,oBAAAA,CAAC,CAACV,mBAAF,GAAwB,IAAxB;AACH;AACJ;AACJ,eATD;AAUH,aAXD;AAYH,WAxBD;AA0BAR,UAAAA,aAAa,CAACpC,IAAd,CAAmB,CAACC,CAAD,EAAIC,CAAJ,KAAU;AACzB;AACA,gBAAImC,mBAAJ,EAAyB;AACrB;AACA,kBAAIpC,CAAC,CAAC2C,mBAAF,IAAyB,CAAC1C,CAAC,CAAC0C,mBAAhC,EAAqD,OAAO,CAAC,CAAR;AACrD,kBAAI,CAAC3C,CAAC,CAAC2C,mBAAH,IAA0B1C,CAAC,CAAC0C,mBAAhC,EAAqD,OAAO,CAAP,CAHhC,CAKrB;;AACA,kBAAI3C,CAAC,CAAC2C,mBAAF,IAAyB1C,CAAC,CAAC0C,mBAA/B,EAAoD;AAChD,oBAAI3C,CAAC,CAACuC,cAAF,IAAoB,CAACtC,CAAC,CAACsC,cAA3B,EAA2C,OAAO,CAAC,CAAR;AAC3C,oBAAI,CAACvC,CAAC,CAACuC,cAAH,IAAqBtC,CAAC,CAACsC,cAA3B,EAA2C,OAAO,CAAP,CAFK,CAIhD;;AACA,oBAAItC,CAAC,CAACyC,cAAF,KAAqB1C,CAAC,CAAC0C,cAA3B,EAA2C;AACvC,yBAAOzC,CAAC,CAACyC,cAAF,GAAmB1C,CAAC,CAAC0C,cAA5B;AACH;AACJ,eAdoB,CAgBrB;;;AACA,kBAAIzC,CAAC,CAAC2C,eAAF,KAAsB5C,CAAC,CAAC4C,eAA5B,EAA6C;AACzC,uBAAO3C,CAAC,CAAC2C,eAAF,GAAoB5C,CAAC,CAAC4C,eAA7B;AACH;AACJ,aApBD,CAqBA;AArBA,iBAsBK;AACD;AACA,kBAAI5C,CAAC,CAACyC,aAAF,IAAmB,CAACxC,CAAC,CAACwC,aAA1B,EAAyC,OAAO,CAAC,CAAR;AACzC,kBAAI,CAACzC,CAAC,CAACyC,aAAH,IAAoBxC,CAAC,CAACwC,aAA1B,EAAyC,OAAO,CAAP,CAHxC,CAKD;;AACA,kBAAIzC,CAAC,CAACyC,aAAF,IAAmBxC,CAAC,CAACwC,aAAzB,EAAwC;AACpC,oBAAIxC,CAAC,CAACyC,cAAF,KAAqB1C,CAAC,CAAC0C,cAA3B,EAA2C;AACvC,yBAAOzC,CAAC,CAACyC,cAAF,GAAmB1C,CAAC,CAAC0C,cAA5B;AACH,iBAHmC,CAKpC;;;AACA,oBAAI1C,CAAC,CAACuC,cAAF,IAAoB,CAACtC,CAAC,CAACsC,cAA3B,EAA2C,OAAO,CAAC,CAAR;AAC3C,oBAAI,CAACvC,CAAC,CAACuC,cAAH,IAAqBtC,CAAC,CAACsC,cAA3B,EAA2C,OAAO,CAAP;AAC9C,eAdA,CAgBD;;;AACA,kBAAItC,CAAC,CAAC2C,eAAF,KAAsB5C,CAAC,CAAC4C,eAA5B,EAA6C;AACzC,uBAAO3C,CAAC,CAAC2C,eAAF,GAAoB5C,CAAC,CAAC4C,eAA7B;AACH;AACJ,aA5CwB,CA8CzB;;;AACA,mBAAO5C,CAAC,CAACa,IAAF,CAAQhB,OAAR,GAAmBI,CAAC,CAACY,IAAF,CAAQhB,OAAlC;AACH,WAhDD,EA7DkC,CA+GlC;;AACA;AAAA;AAAA,oCAAS,SAAT,+EAAuCuC,mBAAmB,WAASA,mBAAmB,CAACvC,OAA7B,GAAyC,GAAnG;AACAsC,UAAAA,aAAa,CAAC5C,OAAd,CAAsB,CAACgE,QAAD,EAAW9B,KAAX,KAAqB;AAAA;;AACvC,gBAAM+B,IAAI,GAAGD,QAAQ,CAAChB,cAAT,GAA0B,KAA1B,GAAkC,KAA/C;AACA,gBAAMkB,MAAM,GAAGF,QAAQ,CAACd,aAAT,uCAAkCc,QAAQ,CAACb,cAA3C,SAA+D,MAA9E;AACA,gBAAMgB,OAAO,GAAGH,QAAQ,CAACZ,mBAAT,GAA+B,QAA/B,GAA0C,EAA1D;AACA,gBAAMrB,OAAO,oBAAQiC,QAAQ,CAACX,eAA9B;AACA;AAAA;AAAA,sCAAS,SAAT,EAAuBnB,KAAK,GAAG,CAA/B,gCAAwC8B,QAAQ,CAAC1C,IAAjD,qBAAwC,eAAehB,OAAvD,uBAAqE0D,QAAQ,CAAC1C,IAAT,CAAce,KAAnF,SAA4F4B,IAA5F,SAAoGC,MAApG,SAA8GnC,OAA9G,SAAyHoC,OAAzH;AACH,WAND;AAQA,iBAAOvB,aAAa,CAAChE,GAAd,CAAkBC,CAAC,IAAIA,CAAC,CAACyC,IAAzB,CAAP;AACH;;AAjS2C,O;;;;;iBAEhB,I", "sourcesContent": ["import { _decorator, Component, Label, Node } from 'cc';\nimport { DataMgr } from 'db://assets/bundles/common/script/data/DataManager';\nimport { DataEvent } from 'db://assets/bundles/common/script/event/DataEvent';\nimport { EventMgr } from 'db://assets/bundles/common/script/event/EventManager';\nimport csproto, { cs } from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { MyApp } from 'db://assets/scripts/MyApp';\nimport { logDebug } from 'db://assets/scripts/Utils/Logger';\nimport { PlaneUIEvent } from '../../../../event/PlaneUIEvent';\nimport { ButtonPlus } from '../../../common/components/button/ButtonPlus';\nimport { DropDown } from '../../../common/components/dropdown/DropDown';\nimport { BagSortType, TabStatus } from '../../PlaneTypes';\nconst { ccclass, property } = _decorator;\n\ninterface MaterialSortInfo {\n    item: csproto.cs.ICSItem; // 物品信息\n    isMainMaterial: boolean; // 是否为主材料\n    isSubMaterial: boolean; // 是否为子材料\n    canSynthesize: boolean; // 是否可以合成\n    isRelatedToSlotMain: boolean; // 是否与已点击的主材料相关\n    combineQuality: number; // 合成优先级（品质越高，数值越大）\n    materialQuality: number; // 材料自身的品质\n\n}\n\n@ccclass('SortTypeDropdown')\nexport class SortTypeDropdown extends Component {\n    @property(DropDown)\n    dropDown: DropDown | null = null;\n\n    private _tabStatus: TabStatus = TabStatus.None;\n    private _sortType: BagSortType = BagSortType.None;\n    private _bagTabOptions: { key: BagSortType, label: string }[] = [\n        { key: BagSortType.Quality, label: '按品质排序' },\n        { key: BagSortType.Part, label: '按部位排序' },\n    ];\n    private _mergeTabOptions: { key: BagSortType, label: string }[] = [\n        { key: BagSortType.Merge, label: '按合成排序' },\n    ];\n\n    onLoad() {\n        this.getComponent(ButtonPlus)!.addClick(this.onDropDownOptionClick, this);\n        EventMgr.on(PlaneUIEvent.TabChange, this.onTabChangeEvent, this);\n        EventMgr.on(PlaneUIEvent.UpdateBagGrids, this.sortBag, this)\n        EventMgr.on(DataEvent.ItemsRefresh, this.sortBag, this)\n    }\n\n    onTabChangeEvent(tabStatus: TabStatus) {\n        if (tabStatus === this._tabStatus) {\n            return;\n        }\n        this._tabStatus = tabStatus;\n        let optionKeyList: string[]\n        if (tabStatus === TabStatus.Bag) {\n            optionKeyList = this._bagTabOptions.map(v => v.key)\n        } else {\n            optionKeyList = this._mergeTabOptions.map(v => v.key)\n        }\n        this.dropDown!.init(optionKeyList, this.onOptionRender.bind(this), this.onDropDownOptionClick.bind(this))\n        this.sortBag();\n    }\n\n    onOptionRender(optNode: Node, optKey: string) {\n        const label = optNode.getComponentInChildren(Label)!;\n        let opt: { label: string, key: BagSortType };\n        switch (this._tabStatus) {\n            case TabStatus.Bag:\n                opt = this._bagTabOptions.find(v => v.key == optKey)!\n                label.string = opt.label;\n                break;\n            case TabStatus.Merge:\n                opt = this._mergeTabOptions.find(v => v.key == optKey)!\n                label.string = opt.label;\n                break;\n            default:\n                //logError(\"PlaneUI\", `onOptionRender error ${this._tabStatus}`)\n                break;\n        }\n    }\n\n    onDropDownOptionClick(optKey: string) {\n        let opt: { label: string, key: BagSortType };\n        switch (this._tabStatus) {\n            case TabStatus.Bag:\n                opt = this._bagTabOptions.find(v => v.key == optKey)!;\n                break;\n            case TabStatus.Merge:\n                opt = this._mergeTabOptions.find(v => v.key == optKey)!;\n                break;\n            default:\n                //logError(\"PlaneUI\", `Dropdown onClickDropDownOption error ${this._tabStatus}`)\n                return;\n        }\n        this.sortBag()\n    }\n\n    private sortBag() {\n        let items = []\n        switch (this._tabStatus) {\n            case TabStatus.Bag:\n                items = [...this.sortEquipsInBagTabStatus(), ...this.sortItems()]\n                break;\n            case TabStatus.Merge:\n                items = this.sortEquipsInCombineTabStatus();\n                break;\n            default:\n                //logError(\"PlaneUI\", `Dropdown onDisPlayRefresh error ${this._tabStatus}`)\n                return;\n        }\n        EventMgr.emit(PlaneUIEvent.SortTypeChange, this._tabStatus, items);\n    }\n\n    private sortItems() {\n        let sorted: csproto.cs.ICSItem[] = []\n        DataMgr.bag.items.forEach(v => {\n            const cfg = MyApp.lubanMgr.table.TbItem.get(v.item_id!)\n            if (cfg) {\n                sorted.push(v)\n            }\n        })\n        sorted.sort((a: csproto.cs.ICSItem, b: csproto.cs.ICSItem) => {\n            return b.add_time! - a.add_time!\n        })\n        return sorted\n    }\n\n    private sortEquipsInBagTabStatus() {\n        const tbEquip = MyApp.lubanMgr.table.TbEquip;\n        // 1. 找出所有空部位\n        const emptySlots: csproto.cs.ICSEquipSlotInfo[] = DataMgr.equip.eqSlots.getEmptySlots();\n\n        logDebug(\"PlaneUI\", `sortEquipsInBagTabStatus item_total:${DataMgr.bag.items.length}, empty_slots:${emptySlots.join(',')}`);\n\n        // 2. 将装备分为三部分：\n        //    - emptySlotEquips: 对应空部位的装备（最高优先级）\n        //    - unequippedEquips: 未装备的其他装备\n        const emptySlotEquips: csproto.cs.ICSItem[] = [];\n        const unequippedEquips: csproto.cs.ICSItem[] = [];\n\n        for (const item of DataMgr.bag.items) {\n            const cfg = tbEquip.get(item.item_id!);\n            if (!cfg) continue;\n\n            // 3. 检查是否对应空部位\n            const isEmptyEquipClass = emptySlots.some(e => e.equip_class === cfg.equipClass);\n            if (isEmptyEquipClass) {\n                emptySlotEquips.push(item);\n            } else {\n                unequippedEquips.push(item);\n            }\n        }\n\n        // 4. 排序函数\n        const sortFn = (a: csproto.cs.ICSItem, b: csproto.cs.ICSItem) => {\n            const aCfg = tbEquip.get(a.item_id!)!;\n            const bCfg = tbEquip.get(b.item_id!)!;\n\n            if (this._sortType === BagSortType.Part) {\n                // 按部位排序：先按部位类型，再按品质（从高到低）\n                return aCfg.equipClass! - bCfg.equipClass! || bCfg.quality! - aCfg.quality!;\n            } else {\n                // 按品质排序：先按品质（从高到低），再按部位类型\n                return bCfg.quality! - aCfg.quality! || aCfg.equipClass - bCfg.equipClass;\n            }\n        };\n\n        // 4. 分别排序三部分\n        const sortedEmptySlotEquips = emptySlotEquips.sort(sortFn);\n        const sortedUnequippedEquips = unequippedEquips.sort(sortFn);\n\n\n        // 5. 合并结果：空部位装备 →  其他装备\n        const sorted = [...sortedEmptySlotEquips, ...sortedUnequippedEquips];\n\n        // 6. 调试输出\n        sorted.forEach((e, index) => {\n            const cfg = tbEquip.get(e.item_id!)!;\n            const equipType = sortedEmptySlotEquips.includes(e) ? \"[空部位]\" : \"[已装备部位]\"\n            logDebug(\"PlaneUI\", `${index + 1}. ${equipType} ID:${e.item_id} 部位:${cfg.equipClass} 品质:${cfg.quality} 数量:${e.count}`);\n        });\n\n        return sorted;\n    }\n\n\n    /**\n    * 材料排序函数（支持主材料格子优先）\n    * 功能：对背包中的材料进行智能排序\n    * 排序规则：\n    * 1. 如果主材料格子有放主材料，优先排该主材料相关的副材料\n    * 2. 如果主材料格子没放主材料，按合成后品质排可合成主材料,然后也按品质排其他副材料\n    * @returns 排序后的材料列表\n    */\n    sortEquipsInCombineTabStatus(): any[] {\n        const items = [...DataMgr.bag.items, ...DataMgr.equip.eqSlots.getEquippedSlots().map(v => <cs.ICSItem>{\n            item_id: v.equip_id,\n            count: 1,\n            guid: v.guid,\n        })]\n        const combineConfigs = DataMgr.equip.eqCombine.calculateAllCombinePossible(items)\n        let materialInfos: MaterialSortInfo[] = []\n        // 获取主材料格子上的材料\n        const combineMainMaterial = DataMgr.equip.eqCombine.getByPos(0)?.item;\n\n        items.forEach(v => {\n            const info: MaterialSortInfo = {\n                item: v,\n                isMainMaterial: false,\n                isSubMaterial: false,\n                canSynthesize: false,\n                combineQuality: 0,\n                isRelatedToSlotMain: false,\n                materialQuality: 0,\n            }\n            const itemQuality = MyApp.lubanTables.TbItem.get(v.item_id!)?.quality\n            if (!itemQuality) {\n                const eqQuality = MyApp.lubanTables.TbEquip.get(v.item_id!)?.quality\n                if (!eqQuality) {\n                    return\n                }\n                info.materialQuality = eqQuality\n            } else {\n                info.materialQuality = itemQuality\n            }\n            //info.isRelatedToSlotMain = combineMainMaterial?.item_id == v.item_id\n            materialInfos.push(info)\n        })\n\n        combineConfigs.forEach(cfg => {\n            const mainMaterial = cfg.consumeItems[0];\n            const subMaterials = cfg.consumeItems.slice(1);\n            materialInfos.forEach(v => {\n                if (mainMaterial.id == v.item.item_id) {\n                    v.isMainMaterial = true;\n                    v.canSynthesize = true;\n                    if (combineMainMaterial?.item_id == v.item.item_id) {\n                        v.isRelatedToSlotMain = true;\n                    }\n                }\n            });\n            subMaterials.forEach(v => {\n                materialInfos.forEach(m => {\n                    if (DataMgr.equip.eqCombine.isSameMatType(v.id, m.item.item_id!)) {\n                        m.isSubMaterial = true;\n                        m.canSynthesize = true;\n                        m.combineQuality = cfg.quality || 0;\n                        if (combineMainMaterial?.item_id == mainMaterial.id) {\n                            m.isRelatedToSlotMain = true;\n                        }\n                    }\n                })\n            })\n        })\n\n        materialInfos.sort((a, b) => {\n            // 场景1: 主材料格子有材料\n            if (combineMainMaterial) {\n                // 1. 优先排与主材料格子相关的材料（包括主材料和副材料）\n                if (a.isRelatedToSlotMain && !b.isRelatedToSlotMain) return -1;\n                if (!a.isRelatedToSlotMain && b.isRelatedToSlotMain) return 1;\n\n                // 2. 都是相关材料时，主材料优先于副材料\n                if (a.isRelatedToSlotMain && b.isRelatedToSlotMain) {\n                    if (a.isMainMaterial && !b.isMainMaterial) return -1;\n                    if (!a.isMainMaterial && b.isMainMaterial) return 1;\n\n                    // 都是副材料时，按合成后品质排序\n                    if (b.combineQuality !== a.combineQuality) {\n                        return b.combineQuality - a.combineQuality;\n                    }\n                }\n\n                // 3. 非相关材料按自身品质排序\n                if (b.materialQuality !== a.materialQuality) {\n                    return b.materialQuality - a.materialQuality;\n                }\n            }\n            // 场景2: 主材料格子没有材料\n            else {\n                // 1. 可合成材料优先于不可合成材料\n                if (a.canSynthesize && !b.canSynthesize) return -1;\n                if (!a.canSynthesize && b.canSynthesize) return 1;\n\n                // 2. 都可合成时，按合成后品质排序（品质高的优先）\n                if (a.canSynthesize && b.canSynthesize) {\n                    if (b.combineQuality !== a.combineQuality) {\n                        return b.combineQuality - a.combineQuality;\n                    }\n\n                    // 同一合成优先级时，主材料优先于副材料\n                    if (a.isMainMaterial && !b.isMainMaterial) return -1;\n                    if (!a.isMainMaterial && b.isMainMaterial) return 1;\n                }\n\n                // 3. 都不可合成时，按材料自身品质排序\n                if (b.materialQuality !== a.materialQuality) {\n                    return b.materialQuality - a.materialQuality;\n                }\n            }\n\n            // 最后按ID排序确保稳定性\n            return a.item!.item_id! - b.item!.item_id!;\n        });\n\n        // 调试输出\n        logDebug(\"PlaneUI\", `材料排序结果 - 主材料格子: ${combineMainMaterial ? `ID ${combineMainMaterial.item_id}` : '空'}`);\n        materialInfos.forEach((material, index) => {\n            const type = material.isMainMaterial ? '主材料' : '副材料';\n            const status = material.canSynthesize ? `可合成(品质${material.combineQuality})` : '不可合成';\n            const related = material.isRelatedToSlotMain ? '[格子相关]' : '';\n            const quality = `品质${material.materialQuality}`;\n            logDebug(\"PlaneUI\", `${index + 1}. ID:${material.item?.item_id} 数量:${material.item.count} ${type} ${status} ${quality} ${related}`);\n        });\n\n        return materialInfos.map(v => v.item);\n    }\n}"]}