{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "Label", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "BundleName", "EventMgr", "ccclass", "property", "TopUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Home", "onLoad", "onShow", "onHide", "onClose", "update", "dt", "onDestroy", "targetOff"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AACZC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;AACRC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;uBAGjBQ,K,WADZF,OAAO,CAAC,OAAD,C,UAGHC,QAAQ,CAACN,KAAD,C,2BAHb,MACaO,KADb;AAAA;AAAA,4BACkC;AAIV,eAANC,MAAM,GAAW;AAAE,iBAAO,iBAAP;AAA2B;;AACtC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AAEtDC,QAAAA,MAAM,GAAS,CAExB;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AACSC,QAAAA,SAAS,GAAS;AACxB;AAAA;AAAA,oCAASC,SAAT,CAAmB,IAAnB;AACH;;AAtB6B,O", "sourcesContent": ["import { _decorator, Label } from 'cc';\nimport { BaseUI, UILayer } from '../../../../../scripts/ui/UIMgr';\nimport { BundleName } from '../../../../Bundle';\nimport { EventMgr } from '../../event/EventManager';\nconst { ccclass, property } = _decorator;\n\n@ccclass('TopUI')\nexport class TopUI extends BaseUI {\n\n    @property(Label)\n\n    public static getUrl(): string { return \"prefab/ui/TopUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top }\n    public static getBundleName(): string { return BundleName.Home }\n\n    protected onLoad(): void {\n\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n    protected onDestroy(): void {\n        EventMgr.targetOff(this)\n    }\n}\n\n"]}