{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/task/TaskUI.ts"], "names": ["_decorator", "Label", "BundleName", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "List", "ccclass", "property", "TaskUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getBundleName", "HomeTask", "onLoad", "onShow", "onHide", "onClose", "update", "dt"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,K,OAAAA,K;;AACZC,MAAAA,U,iBAAAA,U;;AAEAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;;AACVC,MAAAA,I;;;;;;;;;OACD;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBP,U;;wBAGjBQ,M,WADZF,OAAO,CAAC,QAAD,C,UAKHC,QAAQ,CAACN,KAAD,C,UAERM,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ;AAAA;AAAA,uB,2BATb,MACaC,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AACX,eAANC,MAAM,GAAW;AAAE,iBAAO,kBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AACpC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,QAAlB;AAA4B;;AAQ1DC,QAAAA,MAAM,GAAS,CAExB;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;AAC5C;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAtB8B,O;;;;;iBAKT,I;;;;;;;iBAEE,I;;;;;;;iBAEO,I", "sourcesContent": ["import { _decorator, Label } from 'cc';\nimport { BundleName } from 'db://assets/bundles/Bundle';\n\nimport { BaseUI, UILayer } from 'db://assets/scripts/ui/UIMgr';\nimport List from '../common/components/list/List';\nconst { ccclass, property } = _decorator;\n\n@ccclass('TaskUI')\nexport class TaskUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/TaskUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    public static getBundleName(): string { return BundleName.HomeTask }\n    @property(Label)\n    title: Label | null = null;\n    @property(List)\n    TaskList: List | null = null;\n    @property(List)\n    achievementList: List | null = null;\n\n    protected onLoad(): void {\n\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n    }\n    protected update(dt: number): void {\n    }\n\n}\n\n"]}