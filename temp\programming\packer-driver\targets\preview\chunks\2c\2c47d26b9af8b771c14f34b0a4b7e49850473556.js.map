{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/gm/script/ui/GmUI.ts"], "names": ["_decorator", "EditBox", "Label", "Node", "DataMgr", "ButtonPlus", "DropDown", "List", "csproto", "MyApp", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "logDebug", "BundleName", "GmButtonUI", "ccclass", "property", "GmUI", "_inputNodeList", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "getBundleName", "Gm", "getUIOption", "isClickBgHideUI", "onLoad", "inputParentNode", "children", "for<PERSON>ach", "v", "push", "getComponentInChildren", "sendBtn", "addClick", "onSendBtnClick", "clearBtn", "onClearBtnClick", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GM", "onGmMsg", "logLabel", "string", "res", "log", "body", "gm", "text", "onCmdBtnRender", "item", "idx", "cmdInfo", "getCmdBtnListByTabID", "tabDropDown", "<PERSON><PERSON><PERSON>", "label", "cfg", "name", "onCmdBtnClick", "filter", "cmd", "placeholder", "desc", "onDropDownOptionRender", "nd", "optKey", "tabID", "Number", "cmdList", "tabName", "onDropDownOptionClick", "cmdBtnList", "numItems", "length", "msg", "Date", "toLocaleString", "args", "target", "onSendClick", "gmStr", "sendMessage", "gm_str", "onShow", "tabIDList", "init", "bind", "Promise", "resolve", "onHide", "openUI", "onClose"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,O,OAAAA,O;AAASC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,I,OAAAA,I;;AAC5BC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,Q,iBAAAA,Q;;AACFC,MAAAA,I;;AACAC,MAAAA,O;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,U,kBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBjB,U;;sBAEjBkB,I,WADZF,OAAO,CAAC,MAAD,C,UAUHC,QAAQ;AAAA;AAAA,+B,UAERA,QAAQ;AAAA;AAAA,uB,UAERA,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ,CAACd,IAAD,C,UAERc,QAAQ,CAACf,KAAD,C,UAERe,QAAQ;AAAA;AAAA,mC,2BApBb,MACaC,IADb;AAAA;AAAA,4BACiC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;;AAAA,eAsBrBC,cAtBqB,GAsBO,EAtBP;AAAA;;AACT,eAANC,MAAM,GAAW;AAAE,iBAAO,gBAAP;AAA0B;;AACrC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAC7B,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,EAAlB;AAAsB;;AACrC,eAAXC,WAAW,GAAU;AAC/B,iBAAO;AACHC,YAAAA,eAAe,EAAE;AADd,WAAP;AAGH;;AAgBSC,QAAAA,MAAM,GAAS;AACrB,eAAKC,eAAL,CAAsBC,QAAtB,CAA+BC,OAA/B,CAAuCC,CAAC,IAAI;AACxC,iBAAKZ,cAAL,CAAoBa,IAApB,CAAyBD,CAAC,CAACE,sBAAF,CAAyBhC,OAAzB,CAAzB;AACH,WAFD;AAGA,eAAKiC,OAAL,CAAcC,QAAd,CAAuB,KAAKC,cAA5B,EAA4C,IAA5C;AACA,eAAKC,QAAL,CAAeF,QAAf,CAAwB,KAAKG,eAA7B,EAA8C,IAA9C;AACA;AAAA;AAAA,8BAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,kCAAQC,EAAR,CAAWC,MAAX,CAAkBC,SAA/C,EAA0D,KAAKC,OAA/D,EAAwE,IAAxE;AACH;;AAEON,QAAAA,eAAe,GAAG;AACtB,eAAKO,QAAL,CAAeC,MAAf,GAAwB,EAAxB;AACH;;AAEOF,QAAAA,OAAO,CAACG,GAAD,EAA0B;AACrC,eAAKC,GAAL,sBAAkBD,GAAG,CAACE,IAAJ,CAAUC,EAAV,CAAcC,IAAhC;AACH;;AAEOC,QAAAA,cAAc,CAACC,IAAD,EAAaC,GAAb,EAA0B;AAC5C,cAAMC,OAAO,GAAG;AAAA;AAAA,kCAAQL,EAAR,CAAWM,oBAAX,CAAgC,KAAKC,WAAL,CAAkBC,WAAlD,EAA+DJ,GAA/D,CAAhB;AACA,cAAMK,KAAK,GAAGN,IAAI,CAACpB,sBAAL,CAA4B/B,KAA5B,CAAd;AACAyD,UAAAA,KAAK,CAACb,MAAN,GAAeS,OAAO,CAAEK,GAAT,CAAcC,IAA7B;AACA;AAAA;AAAA,oCAAS,MAAT,sBAAmCN,OAAO,CAAEK,GAAT,CAAcC,IAAjD;AACH;;AAEOC,QAAAA,aAAa,CAACT,IAAD,EAAaC,GAAb,EAA0B;AAC3C,cAAMC,OAAO,GAAG;AAAA;AAAA,kCAAQL,EAAR,CAAWM,oBAAX,CAAgC,KAAKC,WAAL,CAAkBC,WAAlD,EAA+DK,MAA/D,CAAsEhC,CAAC,IAAIA,CAAC,CAAC6B,GAAF,CAAOC,IAAP,KAAgB,EAA3F,EAA+FP,GAA/F,CAAhB;AACA,eAAKnC,cAAL,CAAoB,CAApB,EAAuB2B,MAAvB,GAAgCS,OAAO,CAACK,GAAR,CAAaI,GAA7C;AACA,eAAK7C,cAAL,CAAoB,CAApB,EAAuB8C,WAAvB,GAAqCV,OAAO,CAACK,GAAR,CAAaM,IAAlD;AACH;;AAEOC,QAAAA,sBAAsB,CAACC,EAAD,EAAWC,MAAX,EAA2B;AACrD,cAAMC,KAAK,GAAGC,MAAM,CAACF,MAAD,CAApB;AACA,cAAMG,OAAO,GAAG;AAAA;AAAA,kCAAQtB,EAAR,CAAWM,oBAAX,CAAgCc,KAAhC,CAAhB;AACAF,UAAAA,EAAE,CAACnC,sBAAH,CAA0B/B,KAA1B,EAAkC4C,MAAlC,GAA2C0B,OAAO,CAAC,CAAD,CAAP,CAAYZ,GAAZ,CAAiBa,OAA5D;AACH;;AAEOC,QAAAA,qBAAqB,CAACL,MAAD,EAAiB;AAC1C,cAAMC,KAAK,GAAGC,MAAM,CAACF,MAAD,CAApB;AACA,cAAMG,OAAO,GAAG;AAAA;AAAA,kCAAQtB,EAAR,CAAWM,oBAAX,CAAgCc,KAAhC,EAAuCP,MAAvC,CAA8ChC,CAAC,IAAIA,CAAC,CAAC6B,GAAF,CAAOC,IAAP,KAAgB,EAAnE,CAAhB;AACA,eAAKc,UAAL,CAAiBC,QAAjB,GAA4BJ,OAAO,CAACK,MAApC;AACA;AAAA;AAAA,oCAAS,MAAT,6BAA0CL,OAAO,CAACK,MAAlD;AACH;;AAEO7B,QAAAA,GAAG,CAAC8B,GAAD,EAAc;AACrB,eAAKjC,QAAL,CAAeC,MAAf,UAA6B,IAAIiC,IAAJ,GAAWC,cAAX,EAA7B,UAA6DF,GAA7D;AACH;;AAEO1C,QAAAA,cAAc,GAAG;AACrB,eAAKjB,cAAL,CAAoBW,OAApB,CAA4BC,CAAC,IAAI;AAC7B,gBAAIA,CAAC,CAACe,MAAF,IAAYf,CAAC,CAACkC,WAAlB,EAA+B;AAC3BlC,cAAAA,CAAC,CAACe,MAAF,GAAW,EAAX;AACH;AACJ,WAJD;;AAKA,cAAMkB,GAAG,GAAG,KAAK7C,cAAL,CAAoB,CAApB,EAAuB2B,MAAnC;AACA,cAAMmC,IAAI,GAAG,KAAK9D,cAAL,CAAoB,CAApB,EAAuB2B,MAApC;AACA,cAAIoC,MAAM,GAAG,KAAK/D,cAAL,CAAoB,CAApB,EAAuB2B,MAApC;;AACA,cAAIoC,MAAM,KAAK,EAAf,EAAmB;AACfA,YAAAA,MAAM,iBAAeA,MAAf,MAAN;AACH;;AACD,cAAM3B,OAAO,GAAG;AAAA;AAAA,kCAAQL,EAAR,CAAWM,oBAAX,CAAgC,KAAKC,WAAL,CAAkBC,WAAlD,EAA+DK,MAA/D,CAAsEhC,CAAC,IAAIA,CAAC,CAAC6B,GAAF,CAAOI,GAAP,KAAeA,GAA1F,EAA+F,CAA/F,CAAhB;;AACA,cAAIT,OAAJ,YAAIA,OAAO,CAAE4B,WAAb,EAA0B;AACtB,gBAAMpC,GAAG,GAAGQ,OAAO,CAAC4B,WAAR,CAAoBF,IAApB,CAAZ;AACA,iBAAKjC,GAAL,OAAagB,GAAb,0BAA2BjB,GAA3B;AACH,WAHD,MAGO;AACH,gBAAMqC,KAAK,GAAGpB,GAAG,GAAG,GAAN,GAAYkB,MAAZ,GAAqB,GAArB,GAA2BD,IAAzC;AACA;AAAA;AAAA,gCAAM1C,MAAN,CAAa8C,WAAb,CAAyB;AAAA;AAAA,oCAAQ5C,EAAR,CAAWC,MAAX,CAAkBC,SAA3C,EAAsD;AAClDO,cAAAA,EAAE,EAAE;AAAEoC,gBAAAA,MAAM,EAAEF;AAAV;AAD8C,aAAtD;AAGA,iBAAKpC,GAAL,OAAagB,GAAb,0BAA2BoB,KAA3B;AACH;AACJ,SA9F4B,CAgG7B;;;AACOG,QAAAA,MAAM,GAAgC;AACzC,cAAMC,SAAS,GAAG;AAAA;AAAA,kCAAQtC,EAAR,CAAWsC,SAA7B;AACA,eAAK/B,WAAL,CAAkBgC,IAAlB,CAAuBD,SAAvB,EAAkC,KAAKrB,sBAAL,CAA4BuB,IAA5B,CAAiC,IAAjC,CAAlC,EAA0E,KAAKhB,qBAAL,CAA2BgB,IAA3B,CAAgC,IAAhC,CAA1E;AACA,eAAKf,UAAL,CAAiBC,QAAjB,GAA4B;AAAA;AAAA,kCAAQ1B,EAAR,CAAWM,oBAAX,CAAgC,KAAKC,WAAL,CAAkBC,WAAlD,EAA+DmB,MAA3F;AACA;AAAA;AAAA,oCAAS,MAAT,cAA2B,KAAKF,UAAL,CAAiBC,QAA5C;AACA,eAAKtC,eAAL;AACA,iBAAOqD,OAAO,CAACC,OAAR,EAAP;AACH;;AAED;AACOC,QAAAA,MAAM,GAAgC;AACzC;AAAA;AAAA,8BAAMC,MAAN;AAAA;AAAA;AACA,iBAAOH,OAAO,CAACC,OAAR,EAAP;AACH;;AACD;AACOG,QAAAA,OAAO,GAAgC;AAC1C;AAAA;AAAA,8BAAMD,MAAN;AAAA;AAAA;AACA,iBAAOH,OAAO,CAACC,OAAR,EAAP;AACH;;AAnH4B,O;;;;;iBAUE,I;;;;;;;iBAEL,I;;;;;;;iBAEG,I;;;;;;;iBAEE,I;;;;;;;iBAEN,I;;;;;;;iBAEK,I", "sourcesContent": ["import { _decorator, EditBox, Label, Node } from 'cc';\nimport { DataMgr } from 'db://assets/bundles/common/script/data/DataManager';\nimport { ButtonPlus } from 'db://assets/bundles/common/script/ui/common/components/button/ButtonPlus';\nimport { DropDown } from 'db://assets/bundles/common/script/ui/common/components/dropdown/DropDown';\nimport List from 'db://assets/bundles/common/script/ui/common/components/list/List';\nimport csproto from 'db://assets/scripts/AutoGen/PB/cs_proto.js';\nimport { MyApp } from 'db://assets/scripts/MyApp';\nimport { BaseUI, UILayer, UIMgr, UIOpt } from 'db://assets/scripts/ui/UIMgr';\nimport { logDebug } from 'db://assets/scripts/Utils/Logger';\nimport { BundleName } from '../../../Bundle';\nimport { GmButtonUI } from './GmButtonUI';\n\nconst { ccclass, property } = _decorator;\n@ccclass('GmUI')\nexport class GmUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/GmUI\"; }\n    public static getLayer(): UILayer { return UILayer.Top }\n    public static getBundleName(): string { return BundleName.Gm }\n    public static getUIOption(): UIOpt {\n        return {\n            isClickBgHideUI: true,\n        }\n    }\n    @property(DropDown)\n    tabDropDown: DropDown | null = null;\n    @property(List)\n    cmdBtnList: List | null = null;\n    @property(ButtonPlus)\n    sendBtn: ButtonPlus | null = null;\n    @property(Node)\n    inputParentNode: Node | null = null;\n    @property(Label)\n    logLabel: Label | null = null;\n    @property(ButtonPlus)\n    clearBtn: ButtonPlus | null = null;\n\n    private _inputNodeList: EditBox[] = [];\n\n    protected onLoad(): void {\n        this.inputParentNode!.children.forEach(v => {\n            this._inputNodeList.push(v.getComponentInChildren(EditBox)!)\n        })\n        this.sendBtn!.addClick(this.onSendBtnClick, this)\n        this.clearBtn!.addClick(this.onClearBtnClick, this)\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GM, this.onGmMsg, this)\n    }\n\n    private onClearBtnClick() {\n        this.logLabel!.string = \"\"\n    }\n\n    private onGmMsg(res: csproto.cs.IS2CMsg) {\n        this.log(`接收 => ${res.body!.gm!.text}`)\n    }\n\n    private onCmdBtnRender(item: Node, idx: number) {\n        const cmdInfo = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown!.selectedKey)[idx]\n        const label = item.getComponentInChildren(Label)!\n        label.string = cmdInfo!.cfg!.name\n        logDebug(\"GmUI\", `onCmdBtnRender ${cmdInfo!.cfg!.name}`)\n    }\n\n    private onCmdBtnClick(item: Node, idx: number) {\n        const cmdInfo = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown!.selectedKey).filter(v => v.cfg!.name !== \"\")[idx]\n        this._inputNodeList[0].string = cmdInfo.cfg!.cmd\n        this._inputNodeList[1].placeholder = cmdInfo.cfg!.desc\n    }\n\n    private onDropDownOptionRender(nd: Node, optKey: string) {\n        const tabID = Number(optKey)\n        const cmdList = DataMgr.gm.getCmdBtnListByTabID(tabID)\n        nd.getComponentInChildren(Label)!.string = cmdList[0]!.cfg!.tabName\n    }\n\n    private onDropDownOptionClick(optKey: string) {\n        const tabID = Number(optKey)\n        const cmdList = DataMgr.gm.getCmdBtnListByTabID(tabID).filter(v => v.cfg!.name !== \"\")\n        this.cmdBtnList!.numItems = cmdList.length\n        logDebug(\"GmUI\", `onDropDownOptionClick ${cmdList.length}`)\n    }\n\n    private log(msg: string) {\n        this.logLabel!.string += `[${new Date().toLocaleString()}] ${msg}\\n`\n    }\n\n    private onSendBtnClick() {\n        this._inputNodeList.forEach(v => {\n            if (v.string == v.placeholder) {\n                v.string = \"\"\n            }\n        })\n        const cmd = this._inputNodeList[0].string\n        const args = this._inputNodeList[1].string\n        let target = this._inputNodeList[2].string\n        if (target !== \"\") {\n            target = `[destuin ${target}]`\n        }\n        const cmdInfo = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown!.selectedKey).filter(v => v.cfg!.cmd === cmd)[0]\n        if (cmdInfo?.onSendClick) {\n            const res = cmdInfo.onSendClick(args)\n            this.log(`[${cmd}] 发送 => ${res}`)\n        } else {\n            const gmStr = cmd + \" \" + target + \" \" + args\n            MyApp.netMgr.sendMessage(csproto.cs.CS_CMD.CS_CMD_GM, {\n                gm: { gm_str: gmStr }\n            })\n            this.log(`[${cmd}] 发送 => ${gmStr}`)\n        }\n    }\n\n    // 显示 UI 的方法，需要子类实现\n    public onShow(...args: any[]): Promise<void> {\n        const tabIDList = DataMgr.gm.tabIDList\n        this.tabDropDown!.init(tabIDList, this.onDropDownOptionRender.bind(this), this.onDropDownOptionClick.bind(this))\n        this.cmdBtnList!.numItems = DataMgr.gm.getCmdBtnListByTabID(this.tabDropDown!.selectedKey).length\n        logDebug(\"GmUI\", `onShow ${this.cmdBtnList!.numItems}`)\n        this.onClearBtnClick()\n        return Promise.resolve()\n    };\n\n    // 隐藏 UI 的方法，需要子类实现\n    public onHide(...args: any[]): Promise<void> {\n        UIMgr.openUI(GmButtonUI)\n        return Promise.resolve()\n    };\n    // 关闭 UI 的方法，需要子类实现\n    public onClose(...args: any[]): Promise<void> {\n        UIMgr.openUI(GmButtonUI)\n        return Promise.resolve()\n    };\n}\n\n"]}