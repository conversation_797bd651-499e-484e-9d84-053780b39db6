{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>"], "names": ["_decorator", "director", "GameIns", "MyApp", "LoadingUI", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "BundleName", "DataMgr", "EventMgr", "HomeUIEvent", "ButtonPlus", "StoryUI", "ccclass", "property", "HomeUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Background", "getBundleName", "Home", "onLoad", "btnBattle", "addClick", "onBattleClick", "btnStory", "onStoryClick", "getLocalIP", "interfaces", "require", "networkInterfaces", "name", "Object", "keys", "iface", "family", "internal", "address", "onShow", "onHide", "onClose", "targetOff", "onDestroy", "unscheduleAllCallbacks", "update", "dt", "globalMgr", "chapterID", "onBattle", "openUI", "emit", "Leave", "preloadScene", "planeData", "planeInfo", "getPlaneInfoById", "battleManager", "setBattleInfo", "loadScene"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,Q,OAAAA,Q;;AAEZC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;AACjBC,MAAAA,U,iBAAAA,U;;AACAC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,W,iBAAAA,W;;AACAC,MAAAA,U,kBAAAA,U;;AACAC,MAAAA,O,kBAAAA,O;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBf,U;;wBAGjBgB,M,WADZF,OAAO,CAAC,QAAD,C,UAKHC,QAAQ;AAAA;AAAA,mC,UAERA,QAAQ;AAAA;AAAA,mC,2BAPb,MACaC,MADb;AAAA;AAAA,4BACmC;AAAA;AAAA;;AAAA;;AAAA;AAAA;;AACX,eAANC,MAAM,GAAW;AAAE,iBAAO,kBAAP;AAA4B;;AACvC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,UAAf;AAA2B;;AACpC,eAAbC,aAAa,GAAW;AAAE,iBAAO;AAAA;AAAA,wCAAWC,IAAlB;AAAwB;;AAMtDC,QAAAA,MAAM,GAAS;AACrB,eAAKC,SAAL,CAAgBC,QAAhB,CAAyB,KAAKC,aAA9B,EAA6C,IAA7C;AACA,eAAKC,QAAL,CAAeF,QAAf,CAAwB,KAAKG,YAA7B,EAA2C,IAA3C;AACH;;AAEDC,QAAAA,UAAU,GAAkB;AACxB,cAAMC,UAAU,GAAGC,OAAO,CAAC,IAAD,CAAP,CAAcC,iBAAd,EAAnB;;AACA,eAAK,IAAMC,IAAX,IAAmBC,MAAM,CAACC,IAAP,CAAYL,UAAZ,CAAnB,EAA4C;AACxC,iBAAK,IAAMM,KAAX,IAAoBN,UAAU,CAACG,IAAD,CAA9B,EAAsC;AAClC,kBAAIG,KAAK,CAACC,MAAN,KAAiB,MAAjB,IAA2B,CAACD,KAAK,CAACE,QAAtC,EAAgD;AAC5C,uBAAOF,KAAK,CAACG,OAAb;AACH;AACJ;AACJ;;AACD,iBAAO,IAAP;AACH;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AACKC,QAAAA,OAAO,GAAgC;AAAA;;AAAA;AACzC;AAAA;AAAA,sCAASC,SAAT,CAAmB,KAAnB;AADyC;AAE5C;;AACSC,QAAAA,SAAS,GAAS;AACxB,eAAKC,sBAAL;AACH;;AACSC,QAAAA,MAAM,CAACC,EAAD,EAAmB,CAClC;;AAEKrB,QAAAA,aAAa,GAAG;AAAA;;AAAA;AAClB;AAAA;AAAA,gCAAMsB,SAAN,CAAgBC,SAAhB,GAA4B,CAA5B;;AACA,YAAA,MAAI,CAACC,QAAL;AAFkB;AAGrB;;AAEKA,QAAAA,QAAQ,GAAG;AAAA;AACb,kBAAM;AAAA;AAAA,gCAAMC,MAAN;AAAA;AAAA,uCAAN;AACA;AAAA;AAAA,sCAASC,IAAT,CAAc;AAAA;AAAA,4CAAYC,KAA1B;AACAnD,YAAAA,QAAQ,CAACoD,YAAT,CAAsB,MAAtB,iCAA8B,aAAY;AACtC,kBAAIC,SAAS,GAAG;AAAA;AAAA,sCAAQC,SAAR,CAAkBC,gBAAlB,EAAhB;AACA;AAAA;AAAA,sCAAQC,aAAR,CAAsBC,aAAtB,CAAoC,CAApC,EAAuC,CAAvC,EAA0CJ,SAA1C;AACArD,cAAAA,QAAQ,CAAC0D,SAAT,CAAmB,MAAnB;AACH,aAJD;AAHa;AAQhB;;AAEKhC,QAAAA,YAAY,GAAG;AAAA;AACjB,kBAAM;AAAA;AAAA,gCAAMuB,MAAN;AAAA;AAAA,mCAAN,CADiB,CAEjB;AAFiB;AAGpB;;AAzD8B,O;;;;;iBAKA,I;;;;;;;iBAED,I", "sourcesContent": ["import { _decorator, director } from 'cc';\n\nimport { GameIns } from 'db://assets/scripts/Game/GameIns';\nimport { MyApp } from '../../../../../scripts/MyApp';\nimport { LoadingUI } from '../../../../../scripts/ui/LoadingUI';\nimport { BaseUI, UILayer, UIMgr } from '../../../../../scripts/ui/UIMgr';\nimport { BundleName } from '../../../../Bundle';\nimport { DataMgr } from '../../data/DataManager';\nimport { EventMgr } from '../../event/EventManager';\nimport { HomeUIEvent } from '../../event/HomeUIEvent';\nimport { ButtonPlus } from '../common/components/button/ButtonPlus';\nimport { StoryUI } from '../story/StoryUI';\n\nconst { ccclass, property } = _decorator;\n\n@ccclass('HomeUI')\nexport class HomeUI extends BaseUI {\n    public static getUrl(): string { return \"prefab/ui/HomeUI\"; }\n    public static getLayer(): UILayer { return UILayer.Background }\n    public static getBundleName(): string { return BundleName.Home }\n    @property(ButtonPlus)\n    btnBattle: ButtonPlus | null = null;\n    @property(ButtonPlus)\n    btnStory: ButtonPlus | null = null;\n\n    protected onLoad(): void {\n        this.btnBattle!.addClick(this.onBattleClick, this);\n        this.btnStory!.addClick(this.onStoryClick, this);\n    }\n\n    getLocalIP(): string | null {\n        const interfaces = require('os').networkInterfaces();\n        for (const name of Object.keys(interfaces)) {\n            for (const iface of interfaces[name]) {\n                if (iface.family === 'IPv4' && !iface.internal) {\n                    return iface.address;\n                }\n            }\n        }\n        return null;\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n    }\n    async onHide(...args: any[]): Promise<void> {\n    }\n    async onClose(...args: any[]): Promise<void> {\n        EventMgr.targetOff(this)\n    }\n    protected onDestroy(): void {\n        this.unscheduleAllCallbacks();\n    }\n    protected update(dt: number): void {\n    }\n\n    async onBattleClick() {\n        MyApp.globalMgr.chapterID = 0;\n        this.onBattle();\n    }\n\n    async onBattle() {\n        await UIMgr.openUI(LoadingUI)\n        EventMgr.emit(HomeUIEvent.Leave)\n        director.preloadScene(\"Game\", async () => {\n            let planeData = DataMgr.planeInfo.getPlaneInfoById();\n            GameIns.battleManager.setBattleInfo(1, 1, planeData);\n            director.loadScene(\"Game\")\n        })\n    }\n\n    async onStoryClick() {\n        await UIMgr.openUI(StoryUI);\n        //EventMgr.emit(HomeUIEvent.Leave)\n    }\n}\n\n"]}