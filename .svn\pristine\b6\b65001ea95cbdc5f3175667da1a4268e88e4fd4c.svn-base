import { _decorator} from 'cc';
import { GameIns } from '../../GameIns';
import Entity from '../base/Entity';
import FCollider, { ColliderGroupType } from '../../collider-system/FCollider';

const { ccclass, property } = _decorator;

@ccclass('Bullet')
export default class Bullet extends Entity {

    @property(FCollider)
    collideComp:FCollider|  null = null;

    enemy = false;
    attack:number = 0;

    protected onLoad(): void {
        this.collideComp!.isEnable = true;
    }


    initData(isEnemy:boolean, attack:number = 0,colliderGroupType:number = ColliderGroupType.BULLET_SELF) {
        this.enemy = isEnemy;
        this.attack = attack;
        this.collideComp!.groupType = colliderGroupType;
    }

    getAttack():number {
        return this.attack;
    }
    
    /**
     * 播放子弹命中音效
     */
    playHurtAudio() {
        // if (this.m_config.hit.length > 0) {
        //     Bullet.playAudio('hit2');
        // }
    }

    onCollide(collider:<PERSON>ollider) {
        this.remove(true);
    }

    /**
     * 子弹超出屏幕处理
     */
    onOutScreen() {
        this.remove();
    }

    /**
     * 移除子弹
     * @param {boolean} force 是否强制移除
     */
    remove(force = false) {
        this.willRemove();
        GameIns.bulletManager.removeBullet(this);
    }

    /**
     * 子弹死亡移除
     */
    dieRemove() {
        this.willRemove();
    }

    /**
     * 子弹移除前的清理操作
     */
    willRemove() {
        if (this.collideComp) {
            this.collideComp.isEnable = false;
        }
    }
}