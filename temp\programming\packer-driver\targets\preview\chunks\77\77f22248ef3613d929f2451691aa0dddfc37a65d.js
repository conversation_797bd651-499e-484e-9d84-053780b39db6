System.register(["__unresolved_0", "cc", "__unresolved_1"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, Component, v2, Tools, _dec, _class, _crd, ccclass, property, EnemyShootComponent;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _reportPossibleCrUseOfTools(extras) {
    _reporterNs.report("Tools", "../../../utils/Tools", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
      Component = _cc.Component;
      v2 = _cc.v2;
    }, function (_unresolved_2) {
      Tools = _unresolved_2.Tools;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "1069etjGHlOrb/e7htWgjin", "EnemyShootComponent", undefined);

      __checkObsolete__(['_decorator', 'Component', 'Node', 'UITransform', 'v2']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("default", EnemyShootComponent = (_dec = ccclass('EnemyShootComponent'), _dec(_class = class EnemyShootComponent extends Component {
        constructor() {
          super(...arguments);
          this._target = null;
          this._atkNode = null;
          this._shootAble = false;
          this._nextAble = false;
          this.bulletTurn = false;
          this._bShooting = false;
          this._shootInterval = 0;
          this._shootTime = 0;
          this._shootCount = 0;
          this._attackNum = 0;
          this._attackArrIndex = 0;
          this._attackArrOver = true;
          this._attackPointArr = [];
          this._firstShootDelay = -1;
          this._isFirstShoot = true;
          this._atkOverCall = null;
          this._atkStartCall = null;
        }

        /**
         * 初始化射击组件
         * @param target 目标对象
         * @param atkNode 攻击节点
         * @param shootData 射击数据
         * @param bulletTurn 是否允许子弹转向
         */
        init(target, atkNode, shootData, bulletTurn) {
          if (bulletTurn === void 0) {
            bulletTurn = false;
          }

          this.reset();
          this.bulletTurn = bulletTurn;
          this._target = target;
          this._atkNode = atkNode;
          this.setShootData(shootData);

          this._initAtkPoints();
        }
        /**
         * 重置射击组件
         */


        reset() {
          this._nextAble = false;
          this._bShooting = false;
          this._shootInterval = 0;
          this._shootTime = 0;
          this._shootCount = 0;
          this._attackNum = 0;
          this._attackArrIndex = 0;
          this._attackArrOver = true;
          this._attackPointArr = [];
          this._isFirstShoot = true;
          this._firstShootDelay = -1;
          this._atkStartCall = null;
          this._atkOverCall = null;
        }
        /**
         * 设置射击数据
         * @param shootData 射击数据
         */


        setShootData(shootData) {
          if (shootData) {
            this._shootAble = true;
            this._attackArrIndex = 0;
            this._shootInterval = shootData.attackInterval;
            this._attackNum = shootData.attackNum;
            this._attackPointArr = shootData.attackPointArr;
          } else {
            this._shootAble = false;
          }
        }
        /**
         * 设置首次射击延迟
         * @param delay 延迟时间
         */


        setFirstShootDelay(delay) {
          this._firstShootDelay = delay;
        }
        /**
         * 获取首次射击延迟
         * @returns {number} 首次射击延迟
         */


        getFirstShootDelay() {
          return this._firstShootDelay;
        }
        /**
         * 设置是否正在射击
         * @param isShooting 是否射击
         */


        setIsShooting(isShooting) {
          this._bShooting = isShooting;
        }
        /**
         * 设置是否可以进行下一次射击
         * @param nextAble 是否可以进行下一次射击
         */


        setNextAble(nextAble) {
          this._nextAble = nextAble;
        }
        /**
         * 开始射击
         */


        startShoot() {
          this._bShooting = true;
        }
        /**
         * 停止射击
         */


        stopShoot() {
          this._attackArrOver = true;
          this._shootTime = 0;
          this._attackArrIndex = 0;
        }
        /**
         * 设置攻击开始的回调
         * @param callback 回调函数
         */


        setAtkStartCall(callback) {
          this._atkStartCall = callback;
        }
        /**
         * 设置攻击结束的回调
         * @param callback 回调函数
         */


        setAtkOverCall(callback) {
          this._atkOverCall = callback;
        }
        /**
         * 更新游戏逻辑
         * @param deltaTime 帧间隔时间
         */


        updateGameLogic(deltaTime) {
          if (this._shootAble) {
            if (this._target.sceneLayer < 0 && (_crd && Tools === void 0 ? (_reportPossibleCrUseOfTools({
              error: Error()
            }), Tools) : Tools).isPlaneOutScreen(v2(this.node.position.x, this.node.position.y))) {
              return;
            }

            this._updateShoot(deltaTime);

            if (this._nextAble) {
              this._updateNextShoot(deltaTime);
            }
          }
        }
        /**
         * 初始化攻击点
         */


        _initAtkPoints() {}
        /**
         * 准备射击
         */


        shootPrepare() {
          this._shootTime = 0;

          if (this._atkStartCall) {
            this._atkStartCall();
          }
        }
        /**
         * 立即进行下一次射击
         */


        setNextShootAtOnce() {
          if (!this._isFirstShoot || this._firstShootDelay < 0) {
            this._shootTime = this._shootInterval;
          }
        }
        /**
         * 更新射击逻辑
         * @param deltaTime 帧间隔时间
         */


        _updateShoot(deltaTime) {// if (this._bShooting) {
          //     let allOver = true;
          //     for (const point of this._attackPoints) {
          //         await point.updateGameLogic(deltaTime);
          //         if (!point.isAttackOver()) {
          //             allOver = false;
          //         }
          //     }
          //     if (allOver) {
          //         this._shootTime = 0;
          //         this._bShooting = false;
          //         this._attackArrOver = true;
          //         this._attackArrIndex++;
          //         if (this._attackArrIndex < this._attackPointArr.length) {
          //             this._setNextShootArr();
          //             this.shootPrepare();
          //         } else if (this._atkOverCall) {
          //             this._atkOverCall();
          //         }
          //     }
          // }

          return _asyncToGenerator(function* () {})();
        }
        /**
         * 更新下一次射击逻辑
         * @param deltaTime 帧间隔时间
         */


        _updateNextShoot(deltaTime) {
          if (!this._bShooting && this._attackArrOver) {
            this._shootTime += deltaTime;

            if (this._isShootLoop() || this._shootCount < this._getShootLoop()) {
              if (this._isFirstShoot && this._firstShootDelay >= 0) {
                if (this._shootTime > this._firstShootDelay) {
                  this.setNextShoot();
                }
              } else if (this._shootTime > this._shootInterval) {
                this.setNextShoot();
              }
            }
          }
        }
        /**
         * 设置下一次射击的攻击点数组
         */


        _setNextShootArr() {// this._attackArrOver = false;
          // this._attackPoints.splice(0);
          // const attackPoints = this._attackPointArr[this._attackArrIndex];
          // if (attackPoints && attackPoints.length > 0) {
          //     for (let i = 0; i < attackPoints.length; i++) {
          //         let atkPoint = this._atkPointsPool[i];
          //         if (!atkPoint) {
          //             const node = new Node();
          //             node.addComponent(UITransform);
          //             node.angle = -180;
          //             this._atkNode!.addChild(node);
          //             atkPoint = node.addComponent(AttackPoint);
          //             this._atkPointsPool.push(atkPoint);
          //         }
          //         atkPoint.initForEnemy(attackPoints[i], this._target);
          //         atkPoint.bulletTurn = this.bulletTurn;
          //         this._attackPoints.push(atkPoint);
          //     }
          //     this._isFirstShoot = false;
          // } else {
          //     Tools.log('shoot error');
          // }
        }
        /**
         * 立即进行下一次射击
         */


        setNextShoot() {
          this._shootCount++;
          this._attackArrIndex = 0;

          this._setNextShootArr();

          this.shootPrepare();
        }
        /**
         * 是否为循环射击
         * @returns {boolean} 是否循环
         */


        _isShootLoop() {
          return this._attackNum === -1;
        }
        /**
         * 获取射击循环次数
         * @returns {number} 循环次数
         */


        _getShootLoop() {
          return this._attackNum;
        }

      }) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=77f22248ef3613d929f2451691aa0dddfc37a65d.js.map