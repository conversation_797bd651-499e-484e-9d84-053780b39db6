import { _decorator, Label, Sprite, tween, Tween } from 'cc';
const { ccclass, property } = _decorator;

import Entity from 'db://assets/scripts/Game/ui/base/Entity';
import SkillComp from './skill/SkillComp';
import BuffComp, { Buff } from './skill/BuffComp';
import { builtin, res } from '../../../AutoGen/Luban/schema';
import { AttributeData } from 'db://assets/bundles/common/script/data/base/AttributeData';
import { AttributeConst } from 'db://assets/bundles/common/script/const/AttributeConst';
import FCircleCollider from '../../collider-system/FCircleCollider';
import FBoxCollider from '../../collider-system/FBoxCollider';
import FPolygonCollider from '../../collider-system/FPolygonCollider';


@ccclass('PlaneBase')
export default class PlaneBase extends Entity {

    @property(Sprite)
    hpBar: Sprite | null = null; // 血条
    @property(Sprite)
    hpAniSprite: Sprite | null = null; // 血条动画条
    @property(Label)
    hpfont: Label | null = null; // 血条文本

    enemy = true; // 是否为敌机
    isDead = false; // 是否死亡
    type = 0; // 敌人类型

    maxHp: number = 0;
    curHp: number = 0;
    attack: number = 0;
    collideComp: FCircleCollider | FBoxCollider | FPolygonCollider | null = null; // 碰撞组件

    private _skillComp: SkillComp | null = null;
    private _buffComp: BuffComp | null = null;

    // TODO 临时做法，后续应该挪到 PlaneBase
    private _attributeData: AttributeData = new AttributeData();


    init() {
        this._skillComp = new SkillComp();
        this.addComp("skill", this._skillComp);
        this._buffComp = new BuffComp();
        this.addComp("buff", this._buffComp)
        super.init();
    }

    get skillComp() {
        return this._skillComp!;
    }

    get buffComp() {
        return this._buffComp!;
    }

    get attribute(): AttributeData {
        return this._attributeData;
    }

    set colliderEnabled(value: boolean) {
        if (this.collideComp) {
            this.collideComp.isEnable = value;
        }
    }
    get colliderEnabled(): boolean {
        return this.collideComp ? this.collideComp.isEnable : false;
    }

    CastSkill(skillID: number) {
        this.skillComp.Cast(this, skillID);
    }

    addHp(heal: number) {
        this.curHp = Math.min(
            this.maxHp,
            this.curHp + heal
        );
        this.updateHpUI();;
    }

    hurt(damage: number) {
        if (this.isDead) {
            return;
        }
        this.cutHp(damage);
        this.playHurtAnim();
        if (this.curHp <= 0) {
            this.toDie();
        }
    }

    /**
     * 减少血量
     * @param {number} damage 受到的伤害值
     */
    cutHp(damage: number) {
        const newHp = this.curHp - damage;
        this.curHp = Math.max(0, newHp);

        this.updateHpUI();
    }

    toDie(): boolean {
        if (this.isDead) {
            return false
        }
        this.isDead = true;
        this.colliderEnabled = false;
        return true
    }
    /**
     * 更新血量显示
     */
    updateHpUI() {
        if (this.hpBar) {
            // 更新血条前景的填充范围
            this.hpBar.fillRange = this.curHp / this.maxHp;

            if (this.hpAniSprite) {
                // 计算血条动画时间
                const duration = Math.abs(this.hpAniSprite.fillRange - this.hpBar.fillRange);

                Tween.stopAllByTarget(this.hpAniSprite);
                // 血条中间部分的动画
                tween(this.hpAniSprite)
                    .to(duration, { fillRange: this.hpBar.fillRange })
                    .call(() => {

                    })
                    .start();
            }
        }

        // 更新血量文字
        this.hpfont && (this.hpfont!.string = this.curHp.toFixed(0));
    }

    playHurtAnim() {
        // 子类实现
    }

    ApplyBuffEffect(buff: Buff | null, effectData: builtin.EffectParam) {
        switch (effectData.type) {
            case res.EffectType.Kill:
                this.toDie();
                break;
            case res.EffectType.Hurt:
                if (effectData.param.length >= 1) {
                    this.hurt(effectData.param[0]);
                }
                break;
            case res.EffectType.AttrMaxHPPer:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHP, effectData, true);
                break;
            case res.EffectType.AttrMaxHPAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.MaxHP, effectData, false);
                break;
            case res.EffectType.AttrHPRecoveryPer:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecovery, effectData, true);
                break;
            case res.EffectType.AttrHPRecoveryAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecovery, effectData, false);
                break;
            case res.EffectType.AttrHPRecoveryMaxHPPerAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.HPRecoveryRate, effectData, false);
                break;
            case res.EffectType.AttrAttackPer:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.Attack, effectData, true);
                break;
            case res.EffectType.AttrAttackAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.Attack, effectData, false);
                break;
            case res.EffectType.AttrAttackBossPer:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackBoss, effectData, true);
                break;
            case res.EffectType.AttrAttackNormalPer:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.AttackNormal, effectData, true);
                break;
            case res.EffectType.AttrFortunatePer:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.Fortunate, effectData, true);
                break;
            case res.EffectType.AttrFortunateAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.Fortunate, effectData, false);
                break;
            case res.EffectType.AttrMissAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.MissRate, effectData, false);
                break;
            case res.EffectType.AttrBulletHurtResistancePer:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistance, effectData, true);
                break;
            case res.EffectType.AttrBulletHurtResistanceAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.BulletHurtResistance, effectData, false);
                break;
            case res.EffectType.AttrCollisionHurtResistancePer:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistance, effectData, true);
                break;
            case res.EffectType.AttrCollisionHurtResistanceAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.CollisionHurtResistance, effectData, false);
                break;
            case res.EffectType.AttrFinalScoreAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.FinalScoreRate, effectData, true);
                break;
            case res.EffectType.AttrKillScoreAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.KillScoreRate, effectData, true);
                break;
            case res.EffectType.AttrEnergyRecoveryPerAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecoveryRate, effectData, false);
                break;
            case res.EffectType.AttrEnergyRecoveryAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.EnergyRecovery, effectData, false);
                break;
            case res.EffectType.AttrPickRadiusPer:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadius, effectData, true);
                break;
            case res.EffectType.AttrPickRadius:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.PickRadius, effectData, false);
                break;
            case res.EffectType.AttrBombMax:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.BombMax, effectData, false);
                break;
            case res.EffectType.AttrBombHurtAdd:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.BombHurt, effectData, false);
                break;
            case res.EffectType.AttrBombHurtPer:
                this.ApplyBuffAttributeEffect(buff, AttributeConst.BombHurt, effectData, true);
                break;
            default:
                break;
        }
    }
    private ApplyBuffAttributeEffect(buff: Buff | null, key: number, effectData: builtin.EffectParam, isPer: boolean) {
        if (!buff) {
            return;
        }
        if (effectData.param.length < 1) {
            return;
        }
        this.attribute.addModify(buff.id, key, effectData.param[0], isPer);
    }
    RemoveBuffEffect(buff: Buff, effectData: builtin.EffectParam) {
        this.attribute.removeModify(buff.id);
    }
}