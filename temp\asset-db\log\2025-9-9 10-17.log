2025-9-9 10:17:07-log: Cannot access game frame or container.
2025-9-9 10:17:07-debug: asset-db:require-engine-code (401ms)
2025-9-9 10:17:07-log: meshopt wasm decoder initialized
2025-9-9 10:17:07-log: [box2d]:box2d wasm lib loaded.
2025-9-9 10:17:07-log: [bullet]:bullet wasm lib loaded.
2025-9-9 10:17:07-log: Cocos Creator v3.8.6
2025-9-9 10:17:07-log: Forward render pipeline initialized.
2025-9-9 10:17:07-debug: [Assets Memory track]: asset-db:worker-init: initEngine start:30.86MB, end 80.08MB, increase: 49.23MB
2025-9-9 10:17:07-debug: [Assets Memory track]: asset-db-plugin-register: programming start:80.99MB, end 84.04MB, increase: 3.05MB
2025-9-9 10:17:07-log: Using legacy pipeline
2025-9-9 10:17:08-debug: [Assets Memory track]: asset-db-plugin-register: project start:80.84MB, end 228.46MB, increase: 147.62MB
2025-9-9 10:17:08-debug: [Assets Memory track]: asset-db:worker-init: initPlugin start:80.11MB, end 228.49MB, increase: 148.38MB
2025-9-9 10:17:08-debug: [Assets Memory track]: asset-db-plugin-register: engine-extends start:225.12MB, end 228.26MB, increase: 3.13MB
2025-9-9 10:17:08-debug: [Assets Memory track]: asset-db-plugin-register: builder start:84.08MB, end 224.87MB, increase: 140.80MB
2025-9-9 10:17:08-debug: run package(harmonyos-next) handler(enable) start
2025-9-9 10:17:08-debug: run package(harmonyos-next) handler(enable) success!
2025-9-9 10:17:08-debug: run package(honor-mini-game) handler(enable) start
2025-9-9 10:17:08-debug: run package(huawei-agc) handler(enable) start
2025-9-9 10:17:08-debug: run package(honor-mini-game) handler(enable) success!
2025-9-9 10:17:08-debug: run package(huawei-agc) handler(enable) success!
2025-9-9 10:17:08-debug: run package(huawei-quick-game) handler(enable) success!
2025-9-9 10:17:08-debug: run package(ios) handler(enable) start
2025-9-9 10:17:08-debug: run package(huawei-quick-game) handler(enable) start
2025-9-9 10:17:08-debug: run package(linux) handler(enable) start
2025-9-9 10:17:08-debug: run package(ios) handler(enable) success!
2025-9-9 10:17:08-debug: run package(linux) handler(enable) success!
2025-9-9 10:17:08-debug: run package(mac) handler(enable) start
2025-9-9 10:17:08-debug: run package(mac) handler(enable) success!
2025-9-9 10:17:08-debug: run package(migu-mini-game) handler(enable) start
2025-9-9 10:17:08-debug: run package(migu-mini-game) handler(enable) success!
2025-9-9 10:17:08-debug: run package(ohos) handler(enable) success!
2025-9-9 10:17:08-debug: run package(native) handler(enable) start
2025-9-9 10:17:08-debug: run package(native) handler(enable) success!
2025-9-9 10:17:08-debug: run package(ohos) handler(enable) start
2025-9-9 10:17:08-debug: run package(oppo-mini-game) handler(enable) success!
2025-9-9 10:17:08-debug: run package(oppo-mini-game) handler(enable) start
2025-9-9 10:17:08-debug: run package(runtime-dev-tools) handler(enable) start
2025-9-9 10:17:08-debug: run package(runtime-dev-tools) handler(enable) success!
2025-9-9 10:17:08-debug: run package(taobao-mini-game) handler(enable) success!
2025-9-9 10:17:08-debug: run package(vivo-mini-game) handler(enable) start
2025-9-9 10:17:08-debug: run package(vivo-mini-game) handler(enable) success!
2025-9-9 10:17:08-debug: run package(web-desktop) handler(enable) success!
2025-9-9 10:17:08-debug: run package(web-mobile) handler(enable) start
2025-9-9 10:17:08-debug: run package(taobao-mini-game) handler(enable) start
2025-9-9 10:17:08-debug: run package(web-mobile) handler(enable) success!
2025-9-9 10:17:08-debug: run package(wechatgame) handler(enable) start
2025-9-9 10:17:08-debug: run package(web-desktop) handler(enable) start
2025-9-9 10:17:08-debug: run package(wechatgame) handler(enable) success!
2025-9-9 10:17:08-debug: run package(wechatprogram) handler(enable) start
2025-9-9 10:17:08-debug: run package(windows) handler(enable) start
2025-9-9 10:17:08-debug: run package(wechatprogram) handler(enable) success!
2025-9-9 10:17:08-debug: run package(xiaomi-quick-game) handler(enable) success!
2025-9-9 10:17:08-debug: run package(xiaomi-quick-game) handler(enable) start
2025-9-9 10:17:08-debug: run package(cocos-service) handler(enable) success!
2025-9-9 10:17:08-debug: run package(im-plugin) handler(enable) start
2025-9-9 10:17:08-debug: run package(windows) handler(enable) success!
2025-9-9 10:17:08-debug: run package(cocos-service) handler(enable) start
2025-9-9 10:17:08-debug: run package(emitter-editor) handler(enable) start
2025-9-9 10:17:08-debug: run package(im-plugin) handler(enable) success!
2025-9-9 10:17:08-debug: run package(emitter-editor) handler(enable) success!
2025-9-9 10:17:08-debug: run package(fix-asset-default-userdata) handler(enable) start
2025-9-9 10:17:08-debug: run package(fix-asset-default-userdata) handler(enable) success!
2025-9-9 10:17:08-debug: asset-db:worker-init: initPlugin (973ms)
2025-9-9 10:17:08-debug: [Assets Memory track]: asset-db:worker-init start:30.85MB, end 225.17MB, increase: 194.32MB
2025-9-9 10:17:08-debug: Run asset db hook programming:beforePreStart success!
2025-9-9 10:17:08-debug: Run asset db hook engine-extends:beforePreStart ...
2025-9-9 10:17:08-debug: Run asset db hook programming:beforePreStart ...
2025-9-9 10:17:08-debug: Run asset db hook engine-extends:beforePreStart success!
2025-9-9 10:17:08-debug: run package(level-editor) handler(enable) start
2025-9-9 10:17:08-debug: run package(level-editor) handler(enable) success!
2025-9-9 10:17:08-debug: Preimport db internal success
2025-9-9 10:17:08-debug: run package(localization-editor) handler(enable) start
2025-9-9 10:17:08-debug: run package(localization-editor) handler(enable) success!
2025-9-9 10:17:08-debug: asset-db:worker-init (1542ms)
2025-9-9 10:17:08-debug: asset-db-hook-engine-extends-beforePreStart (104ms)
2025-9-9 10:17:08-debug: run package(placeholder) handler(enable) start
2025-9-9 10:17:08-debug: asset-db-hook-programming-beforePreStart (104ms)
2025-9-9 10:17:08-debug: run package(placeholder) handler(enable) success!
2025-9-9 10:17:08-debug: Run asset db hook programming:afterPreStart ...
2025-9-9 10:17:08-debug: Preimport db assets success
2025-9-9 10:17:08-debug: starting packer-driver...
2025-9-9 10:17:33-debug: initialize scripting environment...
2025-9-9 10:17:33-debug: [[Executor]] prepare before lock
2025-9-9 10:17:33-debug: Set detail map pack:///resolution-detail-map.json: {
  "./chunks/6e/6eea8ad00994b16f97f6894a5666f4985f6ef6b5.js": {
    "__unresolved_3": {
      "error": "Error: 以 file:///E:/M2Game/Client/assets/scripts/Game/ui/bullet/BulletNew.ts 为起点找不到模块 \"../plane/mainPlane/MainPlane\"",
      "messages": [
        {
          "level": "warn",
          "text": "你是否遗漏了扩展名？请注意你不能在模块说明符中省略扩展名。"
        }
      ]
    }
  }
}

2025-9-9 10:17:33-debug: [[Executor]] prepare after unlock
2025-9-9 10:17:33-debug: Run asset db hook engine-extends:afterPreStart ...
2025-9-9 10:17:33-debug: Run asset db hook programming:afterPreStart success!
2025-9-9 10:17:33-debug: asset-db-hook-programming-afterPreStart (24701ms)
2025-9-9 10:17:33-debug: recompile effect.bin success
2025-9-9 10:17:33-debug: Run asset db hook engine-extends:afterPreStart success!
2025-9-9 10:17:33-debug: Start up the 'internal' database...
2025-9-9 10:17:33-debug: asset-db:worker-effect-data-processing (376ms)
2025-9-9 10:17:33-debug: asset-db-hook-engine-extends-afterPreStart (376ms)
2025-9-9 10:17:33-debug: Start up the 'assets' database...
2025-9-9 10:17:33-debug: asset-db:worker-startup-database[internal] (25300ms)
2025-9-9 10:17:33-debug: [Assets Memory track]: asset-db:worker-init: startup start:192.10MB, end 199.17MB, increase: 7.07MB
2025-9-9 10:17:33-debug: lazy register asset handler directory
2025-9-9 10:17:33-debug: lazy register asset handler text
2025-9-9 10:17:33-debug: lazy register asset handler json
2025-9-9 10:17:33-debug: lazy register asset handler *
2025-9-9 10:17:33-debug: lazy register asset handler dragonbones
2025-9-9 10:17:33-debug: lazy register asset handler dragonbones-atlas
2025-9-9 10:17:33-debug: lazy register asset handler spine-data
2025-9-9 10:17:33-debug: lazy register asset handler terrain
2025-9-9 10:17:33-debug: lazy register asset handler typescript
2025-9-9 10:17:33-debug: lazy register asset handler prefab
2025-9-9 10:17:33-debug: lazy register asset handler javascript
2025-9-9 10:17:33-debug: lazy register asset handler sprite-frame
2025-9-9 10:17:33-debug: lazy register asset handler scene
2025-9-9 10:17:33-debug: lazy register asset handler tiled-map
2025-9-9 10:17:33-debug: lazy register asset handler image
2025-9-9 10:17:33-debug: lazy register asset handler buffer
2025-9-9 10:17:33-debug: lazy register asset handler alpha-image
2025-9-9 10:17:33-debug: lazy register asset handler texture
2025-9-9 10:17:33-debug: lazy register asset handler sign-image
2025-9-9 10:17:33-debug: lazy register asset handler texture-cube
2025-9-9 10:17:33-debug: lazy register asset handler erp-texture-cube
2025-9-9 10:17:33-debug: lazy register asset handler rt-sprite-frame
2025-9-9 10:17:33-debug: lazy register asset handler render-texture
2025-9-9 10:17:33-debug: lazy register asset handler gltf-mesh
2025-9-9 10:17:33-debug: lazy register asset handler gltf
2025-9-9 10:17:33-debug: lazy register asset handler texture-cube-face
2025-9-9 10:17:33-debug: lazy register asset handler gltf-material
2025-9-9 10:17:33-debug: lazy register asset handler gltf-animation
2025-9-9 10:17:33-debug: lazy register asset handler gltf-scene
2025-9-9 10:17:33-debug: lazy register asset handler gltf-skeleton
2025-9-9 10:17:33-debug: lazy register asset handler fbx
2025-9-9 10:17:33-debug: lazy register asset handler physics-material
2025-9-9 10:17:33-debug: lazy register asset handler material
2025-9-9 10:17:33-debug: lazy register asset handler effect-header
2025-9-9 10:17:33-debug: lazy register asset handler audio-clip
2025-9-9 10:17:33-debug: lazy register asset handler effect
2025-9-9 10:17:33-debug: lazy register asset handler gltf-embeded-image
2025-9-9 10:17:33-debug: lazy register asset handler animation-graph-variant
2025-9-9 10:17:33-debug: lazy register asset handler animation-mask
2025-9-9 10:17:33-debug: lazy register asset handler ttf-font
2025-9-9 10:17:33-debug: lazy register asset handler animation-graph
2025-9-9 10:17:33-debug: lazy register asset handler animation-clip
2025-9-9 10:17:33-debug: lazy register asset handler bitmap-font
2025-9-9 10:17:33-debug: lazy register asset handler sprite-atlas
2025-9-9 10:17:33-debug: lazy register asset handler auto-atlas
2025-9-9 10:17:33-debug: lazy register asset handler label-atlas
2025-9-9 10:17:33-debug: lazy register asset handler render-pipeline
2025-9-9 10:17:33-debug: lazy register asset handler render-stage
2025-9-9 10:17:33-debug: lazy register asset handler instantiation-material
2025-9-9 10:17:33-debug: lazy register asset handler instantiation-mesh
2025-9-9 10:17:33-debug: lazy register asset handler instantiation-animation
2025-9-9 10:17:33-debug: lazy register asset handler render-flow
2025-9-9 10:17:33-debug: lazy register asset handler instantiation-skeleton
2025-9-9 10:17:33-debug: lazy register asset handler particle
2025-9-9 10:17:33-debug: lazy register asset handler video-clip
2025-9-9 10:17:33-debug: asset-db:worker-startup-database[assets] (25298ms)
2025-9-9 10:17:33-debug: asset-db:ready (28324ms)
2025-9-9 10:17:33-debug: asset-db:start-database (25376ms)
2025-9-9 10:17:33-debug: init worker message success
2025-9-9 10:17:33-debug: fix the bug of updateDefaultUserData
2025-9-9 10:17:33-debug: programming:execute-script (3ms)
2025-9-9 10:17:33-debug: [Build Memory track]: builder:worker-init start:202.86MB, end 215.28MB, increase: 12.43MB
2025-9-9 10:17:33-debug: builder:worker-init (269ms)
2025-9-9 10:19:58-debug: refresh db internal success
2025-9-9 10:19:58-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet\BulletNew.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:19:58-debug: refresh db assets success
2025-9-9 10:19:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:19:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:19:58-debug: asset-db:refresh-all-database (165ms)
2025-9-9 10:22:28-debug: Query all assets info in project
2025-9-9 10:22:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:22:28-debug: Skip compress image, progress: 0%
2025-9-9 10:22:28-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:22:28-debug: Init all bundles start..., progress: 0%
2025-9-9 10:22:28-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:22:28-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:22:28-debug:   Number of all scripts: 245
2025-9-9 10:22:28-debug:   Number of other assets: 2123
2025-9-9 10:22:28-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:22:28-debug:   Number of all scenes: 9
2025-9-9 10:22:28-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ---- (24ms)
2025-9-9 10:22:28-debug: [Build Memory track]: 查询 Asset Bundle start:207.83MB, end 207.72MB, increase: -113.28KB
2025-9-9 10:22:28-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:22:28-log: run build task 查询 Asset Bundle success in 24 ms√, progress: 5%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-9 10:22:28-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-9 10:22:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:22:28-debug: [Build Memory track]: 查询 Asset Bundle start:207.76MB, end 208.05MB, increase: 301.34KB
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 10:22:28-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-9 10:22:28-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.09MB, end 208.13MB, increase: 31.54KB
2025-9-9 10:22:28-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:22:28-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:22:28-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-9 10:22:28-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-9 10:22:28-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.16MB, end 208.19MB, increase: 32.87KB
2025-9-9 10:22:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-9 10:22:28-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-9 10:22:28-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.23MB, end 208.52MB, increase: 302.41KB
2025-9-9 10:22:28-debug: Query all assets info in project
2025-9-9 10:22:28-debug: Query all assets info in project
2025-9-9 10:22:28-debug: Query all assets info in project
2025-9-9 10:22:28-debug: Query all assets info in project
2025-9-9 10:22:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:22:28-debug: Skip compress image, progress: 0%
2025-9-9 10:22:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:22:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:22:28-debug: Skip compress image, progress: 0%
2025-9-9 10:22:28-debug: Skip compress image, progress: 0%
2025-9-9 10:22:28-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:22:28-debug: Skip compress image, progress: 0%
2025-9-9 10:22:28-debug: Init all bundles start..., progress: 0%
2025-9-9 10:22:28-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:22:28-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:22:28-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:22:28-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:22:28-debug: Init all bundles start..., progress: 0%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:22:28-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:22:28-debug: Init all bundles start..., progress: 0%
2025-9-9 10:22:28-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:22:28-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:22:28-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:22:28-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:22:28-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:22:28-debug: Init all bundles start..., progress: 0%
2025-9-9 10:22:28-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:22:28-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:22:28-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:22:28-debug:   Number of all scripts: 245
2025-9-9 10:22:28-debug:   Number of all scenes: 9
2025-9-9 10:22:28-debug:   Number of other assets: 2123
2025-9-9 10:22:28-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:22:28-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:22:28-debug:   Number of all scripts: 245
2025-9-9 10:22:28-debug:   Number of other assets: 2123
2025-9-9 10:22:28-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:22:28-debug:   Number of all scenes: 9
2025-9-9 10:22:28-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:22:28-debug:   Number of other assets: 2123
2025-9-9 10:22:28-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:22:28-debug:   Number of all scenes: 9
2025-9-9 10:22:28-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:22:28-debug:   Number of all scripts: 245
2025-9-9 10:22:28-debug:   Number of all scenes: 9
2025-9-9 10:22:28-debug:   Number of all scripts: 245
2025-9-9 10:22:28-debug:   Number of other assets: 2123
2025-9-9 10:22:28-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ---- (39ms)
2025-9-9 10:22:28-log: run build task 查询 Asset Bundle success in 39 ms√, progress: 5%
2025-9-9 10:22:28-debug: [Build Memory track]: 查询 Asset Bundle start:208.36MB, end 213.72MB, increase: 5.36MB
2025-9-9 10:22:28-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:22:28-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-9 10:22:28-debug: [Build Memory track]: 查询 Asset Bundle start:213.75MB, end 213.77MB, increase: 20.80KB
2025-9-9 10:22:28-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:22:28-debug: [Build Memory track]: 查询 Asset Bundle start:213.81MB, end 213.82MB, increase: 14.46KB
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:22:28-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-9 10:22:28-log: run build task 查询 Asset Bundle success in √, progress: 5%
2025-9-9 10:22:28-debug: [Build Memory track]: 查询 Asset Bundle start:213.87MB, end 213.88MB, increase: 14.95KB
2025-9-9 10:22:28-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:22:28-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:22:28-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-9 10:22:28-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-9 10:22:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:22:28-debug: [Build Memory track]: 查询 Asset Bundle start:213.92MB, end 214.98MB, increase: 1.06MB
2025-9-9 10:22:28-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-9 10:22:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:22:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:22:28-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-9 10:22:28-log: run build task 查询 Asset Bundle success in √, progress: 10%
2025-9-9 10:22:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 10:22:28-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-9 10:22:28-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:22:28-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.15MB, end 215.18MB, increase: 32.95KB
2025-9-9 10:22:28-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 10:22:28-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:22:28-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:22:28-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-9 10:22:28-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 10:22:28-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-9 10:22:28-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:22:28-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:22:28-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-9 10:22:28-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:22:28-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-9 10:22:28-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.41MB, end 212.44MB, increase: 34.46KB
2025-9-9 10:22:28-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-9 10:22:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:22:28-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-9 10:22:28-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-9 10:22:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:22:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:22:28-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 10:22:28-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:22:28-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:22:28-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-9 10:22:28-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-9 10:22:28-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.60MB, end 213.70MB, increase: 1.10MB
2025-9-9 10:22:28-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-9 10:22:28-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-9 10:22:28-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 15%
2025-9-9 10:24:39-debug: refresh db internal success
2025-9-9 10:24:39-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletSystem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:24:39-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\BattleManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:24:39-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:24:39-debug: refresh db assets success
2025-9-9 10:24:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:24:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:24:39-debug: asset-db:refresh-all-database (168ms)
2025-9-9 10:24:40-debug: Query all assets info in project
2025-9-9 10:24:40-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:24:40-debug: Skip compress image, progress: 0%
2025-9-9 10:24:40-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:24:40-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:24:40-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:24:40-debug: Init all bundles start..., progress: 0%
2025-9-9 10:24:40-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:24:40-debug:   Number of other assets: 2123
2025-9-9 10:24:40-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:24:40-debug:   Number of all scripts: 245
2025-9-9 10:24:40-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:24:40-debug:   Number of all scenes: 9
2025-9-9 10:24:40-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-9 10:24:40-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:24:40-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-9 10:24:40-debug: [Build Memory track]: 查询 Asset Bundle start:217.79MB, end 217.93MB, increase: 140.28KB
2025-9-9 10:24:40-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:24:40-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-9 10:24:40-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-9 10:24:40-debug: [Build Memory track]: 查询 Asset Bundle start:217.96MB, end 218.24MB, increase: 289.68KB
2025-9-9 10:24:40-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:24:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:24:40-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-9 10:24:40-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.28MB, end 218.30MB, increase: 21.05KB
2025-9-9 10:24:40-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:24:40-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:24:40-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 10:24:40-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:24:40-debug: [Build Memory track]: 填充脚本数据到 settings.json start:218.34MB, end 218.35MB, increase: 19.62KB
2025-9-9 10:24:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:24:40-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-9 10:24:40-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:218.39MB, end 218.69MB, increase: 305.40KB
2025-9-9 10:24:40-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-9 10:25:35-debug: refresh db internal success
2025-9-9 10:25:36-debug: refresh db assets success
2025-9-9 10:25:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:25:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:25:36-debug: asset-db:refresh-all-database (120ms)
2025-9-9 10:25:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 10:25:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:31:48-debug: refresh db internal success
2025-9-9 10:31:48-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:31:48-debug: %cImport%c: E:\M2Game\Client\assets\editor\gizmos\GizmoUtils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:31:48-debug: refresh db assets success
2025-9-9 10:31:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:31:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:31:48-debug: asset-db:refresh-all-database (171ms)
2025-9-9 10:31:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:31:53-debug: Query all assets info in project
2025-9-9 10:31:53-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:31:53-debug: Skip compress image, progress: 0%
2025-9-9 10:31:53-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:31:53-debug: Init all bundles start..., progress: 0%
2025-9-9 10:31:53-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:31:53-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:31:53-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:31:53-debug:   Number of all scripts: 245
2025-9-9 10:31:53-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:31:53-debug:   Number of other assets: 2123
2025-9-9 10:31:53-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:31:53-debug:   Number of all scenes: 9
2025-9-9 10:31:53-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-9 10:31:53-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-9 10:31:53-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:31:53-debug: [Build Memory track]: 查询 Asset Bundle start:206.20MB, end 206.70MB, increase: 510.10KB
2025-9-9 10:31:53-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:31:53-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-9 10:31:53-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-9 10:31:53-debug: [Build Memory track]: 查询 Asset Bundle start:206.73MB, end 207.02MB, increase: 288.91KB
2025-9-9 10:31:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:31:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:31:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.05MB, end 207.07MB, increase: 19.13KB
2025-9-9 10:31:53-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:31:53-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-9 10:31:53-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:31:53-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 10:31:53-debug: [Build Memory track]: 填充脚本数据到 settings.json start:207.11MB, end 207.13MB, increase: 19.96KB
2025-9-9 10:31:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:31:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:31:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 10:31:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:207.17MB, end 207.46MB, increase: 301.56KB
2025-9-9 10:31:53-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-9 10:31:55-debug: refresh db internal success
2025-9-9 10:31:55-debug: refresh db assets success
2025-9-9 10:31:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:31:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:31:55-debug: asset-db:refresh-all-database (113ms)
2025-9-9 10:33:07-debug: refresh db internal success
2025-9-9 10:33:07-debug: refresh db assets success
2025-9-9 10:33:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:33:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:33:07-debug: asset-db:refresh-all-database (132ms)
2025-9-9 10:33:39-debug: refresh db internal success
2025-9-9 10:33:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:33:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:33:39-debug: refresh db assets success
2025-9-9 10:33:39-debug: asset-db:refresh-all-database (110ms)
2025-9-9 10:34:29-debug: refresh db internal success
2025-9-9 10:34:29-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:34:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:34:29-debug: refresh db assets success
2025-9-9 10:34:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:34:29-debug: asset-db:refresh-all-database (167ms)
2025-9-9 10:34:29-debug: asset-db:worker-effect-data-processing (4ms)
2025-9-9 10:34:29-debug: asset-db-hook-engine-extends-afterRefresh (5ms)
2025-9-9 10:35:03-debug: refresh db internal success
2025-9-9 10:35:04-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:35:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:35:04-debug: refresh db assets success
2025-9-9 10:35:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:35:04-debug: asset-db:refresh-all-database (156ms)
2025-9-9 10:35:04-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-9 10:35:04-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 10:35:05-debug: Query all assets info in project
2025-9-9 10:35:05-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:35:05-debug: Skip compress image, progress: 0%
2025-9-9 10:35:05-debug: Init all bundles start..., progress: 0%
2025-9-9 10:35:05-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:35:05-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:35:05-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:35:05-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:35:05-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:35:05-debug:   Number of all scenes: 9
2025-9-9 10:35:05-debug:   Number of other assets: 2123
2025-9-9 10:35:05-debug:   Number of all scripts: 245
2025-9-9 10:35:05-log: run build task 查询 Asset Bundle success in 25 ms√, progress: 5%
2025-9-9 10:35:05-debug: // ---- build task 查询 Asset Bundle ---- (25ms)
2025-9-9 10:35:05-debug: [Build Memory track]: 查询 Asset Bundle start:218.88MB, end 219.58MB, increase: 712.25KB
2025-9-9 10:35:05-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:35:05-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:35:05-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:35:05-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-9 10:35:05-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-9 10:35:05-debug: [Build Memory track]: 查询 Asset Bundle start:219.61MB, end 219.89MB, increase: 286.12KB
2025-9-9 10:35:05-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:35:05-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:35:05-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 10:35:05-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-9 10:35:05-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.92MB, end 219.94MB, increase: 26.08KB
2025-9-9 10:35:05-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:35:05-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:35:05-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-9 10:35:05-debug: [Build Memory track]: 填充脚本数据到 settings.json start:219.97MB, end 220.00MB, increase: 27.16KB
2025-9-9 10:35:05-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-9 10:35:05-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:35:05-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:35:05-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-9 10:35:05-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:220.02MB, end 220.32MB, increase: 300.37KB
2025-9-9 10:35:05-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-9 10:37:04-debug: refresh db internal success
2025-9-9 10:37:04-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:37:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:37:04-debug: refresh db assets success
2025-9-9 10:37:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:37:04-debug: asset-db:refresh-all-database (155ms)
2025-9-9 10:37:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:37:08-debug: Query all assets info in project
2025-9-9 10:37:08-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:37:08-debug: Skip compress image, progress: 0%
2025-9-9 10:37:08-debug: Init all bundles start..., progress: 0%
2025-9-9 10:37:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:37:08-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:37:08-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:37:08-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:37:08-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:37:08-debug:   Number of all scenes: 9
2025-9-9 10:37:08-debug:   Number of all scripts: 245
2025-9-9 10:37:08-debug:   Number of other assets: 2123
2025-9-9 10:37:08-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:37:08-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-9 10:37:08-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-9 10:37:08-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:37:08-debug: [Build Memory track]: 查询 Asset Bundle start:224.38MB, end 224.43MB, increase: 43.04KB
2025-9-9 10:37:08-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:37:08-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-9 10:37:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:37:08-debug: [Build Memory track]: 查询 Asset Bundle start:224.46MB, end 224.89MB, increase: 440.71KB
2025-9-9 10:37:08-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-9 10:37:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:37:08-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-9 10:37:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:224.80MB, end 224.82MB, increase: 16.54KB
2025-9-9 10:37:08-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:37:08-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:37:08-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 10:37:08-debug: [Build Memory track]: 填充脚本数据到 settings.json start:224.85MB, end 224.87MB, increase: 22.05KB
2025-9-9 10:37:08-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:37:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:37:08-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-9 10:37:08-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:224.90MB, end 225.20MB, increase: 303.33KB
2025-9-9 10:37:08-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-9 10:42:54-debug: refresh db internal success
2025-9-9 10:42:55-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:42:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:42:55-debug: refresh db assets success
2025-9-9 10:42:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:42:55-debug: asset-db:refresh-all-database (158ms)
2025-9-9 10:42:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 10:42:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:44:58-debug: refresh db internal success
2025-9-9 10:44:58-debug: refresh db assets success
2025-9-9 10:44:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:44:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:44:58-debug: asset-db:refresh-all-database (146ms)
2025-9-9 10:45:46-debug: refresh db internal success
2025-9-9 10:45:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:45:46-debug: refresh db assets success
2025-9-9 10:45:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:45:46-debug: asset-db:refresh-all-database (109ms)
2025-9-9 10:45:46-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 10:45:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:47:04-debug: refresh db internal success
2025-9-9 10:47:04-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:47:04-debug: refresh db assets success
2025-9-9 10:47:04-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:47:04-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:47:04-debug: asset-db:refresh-all-database (148ms)
2025-9-9 10:47:04-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 10:47:04-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:47:33-debug: refresh db internal success
2025-9-9 10:47:33-debug: refresh db assets success
2025-9-9 10:47:33-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:47:33-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:47:33-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 10:47:33-debug: asset-db:refresh-all-database (131ms)
2025-9-9 10:47:33-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:47:46-debug: refresh db internal success
2025-9-9 10:47:46-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:47:46-debug: refresh db assets success
2025-9-9 10:47:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:47:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:47:46-debug: asset-db:refresh-all-database (111ms)
2025-9-9 10:47:46-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:48:07-debug: refresh db internal success
2025-9-9 10:48:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:48:07-debug: refresh db assets success
2025-9-9 10:48:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:48:07-debug: asset-db:refresh-all-database (137ms)
2025-9-9 10:48:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:48:40-debug: refresh db internal success
2025-9-9 10:48:40-debug: refresh db assets success
2025-9-9 10:48:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:48:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:48:40-debug: asset-db:refresh-all-database (119ms)
2025-9-9 10:48:40-debug: asset-db:worker-effect-data-processing (5ms)
2025-9-9 10:48:40-debug: asset-db-hook-engine-extends-afterRefresh (5ms)
2025-9-9 10:50:16-debug: refresh db internal success
2025-9-9 10:50:16-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\utils\RPN.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:50:16-debug: refresh db assets success
2025-9-9 10:50:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:50:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:50:16-debug: asset-db:refresh-all-database (147ms)
2025-9-9 10:51:23-debug: refresh db internal success
2025-9-9 10:51:23-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\utils\RPN.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:51:23-debug: refresh db assets success
2025-9-9 10:51:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:51:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:51:23-debug: asset-db:refresh-all-database (145ms)
2025-9-9 10:51:49-debug: refresh db internal success
2025-9-9 10:51:49-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 10:51:49-debug: refresh db assets success
2025-9-9 10:51:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:51:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:51:49-debug: asset-db:refresh-all-database (147ms)
2025-9-9 10:51:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:52:49-debug: refresh db internal success
2025-9-9 10:52:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:52:49-debug: refresh db assets success
2025-9-9 10:52:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:52:49-debug: asset-db:refresh-all-database (134ms)
2025-9-9 10:52:49-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 10:52:49-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:55:27-debug: refresh db internal success
2025-9-9 10:55:27-debug: refresh db assets success
2025-9-9 10:55:27-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:55:27-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:55:27-debug: asset-db:refresh-all-database (137ms)
2025-9-9 10:55:32-debug: Query all assets info in project
2025-9-9 10:55:32-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:55:32-debug: Skip compress image, progress: 0%
2025-9-9 10:55:32-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:55:32-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:55:32-debug: Init all bundles start..., progress: 0%
2025-9-9 10:55:32-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:55:32-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:55:32-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:55:32-debug:   Number of all scripts: 245
2025-9-9 10:55:32-debug:   Number of other assets: 2123
2025-9-9 10:55:32-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:55:32-debug:   Number of all scenes: 9
2025-9-9 10:55:32-debug: // ---- build task 查询 Asset Bundle ---- (30ms)
2025-9-9 10:55:32-log: run build task 查询 Asset Bundle success in 30 ms√, progress: 5%
2025-9-9 10:55:32-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:55:32-debug: [Build Memory track]: 查询 Asset Bundle start:218.87MB, end 219.45MB, increase: 594.75KB
2025-9-9 10:55:32-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:55:32-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-9 10:55:32-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-9 10:55:32-debug: [Build Memory track]: 查询 Asset Bundle start:219.40MB, end 219.68MB, increase: 284.82KB
2025-9-9 10:55:32-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:55:32-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:55:32-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-9 10:55:32-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:55:32-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.71MB, end 219.73MB, increase: 17.52KB
2025-9-9 10:55:32-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:55:32-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 10:55:32-debug: [Build Memory track]: 填充脚本数据到 settings.json start:219.76MB, end 219.77MB, increase: 16.06KB
2025-9-9 10:55:32-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:55:32-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:55:32-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-9 10:55:32-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-9 10:55:32-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:219.80MB, end 220.09MB, increase: 296.46KB
2025-9-9 10:55:48-debug: refresh db internal success
2025-9-9 10:55:49-debug: refresh db assets success
2025-9-9 10:55:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:55:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:55:49-debug: asset-db:refresh-all-database (131ms)
2025-9-9 10:55:50-debug: refresh db internal success
2025-9-9 10:55:50-debug: refresh db assets success
2025-9-9 10:55:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:55:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:55:50-debug: asset-db:refresh-all-database (162ms)
2025-9-9 10:55:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:55:53-debug: Query all assets info in project
2025-9-9 10:55:53-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:55:53-debug: Skip compress image, progress: 0%
2025-9-9 10:55:53-debug: Init all bundles start..., progress: 0%
2025-9-9 10:55:53-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:55:53-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:55:53-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:55:53-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:55:53-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:55:53-debug:   Number of all scenes: 9
2025-9-9 10:55:53-debug:   Number of all scripts: 245
2025-9-9 10:55:53-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:55:53-debug:   Number of other assets: 2123
2025-9-9 10:55:53-debug: // ---- build task 查询 Asset Bundle ---- (35ms)
2025-9-9 10:55:53-log: run build task 查询 Asset Bundle success in 35 ms√, progress: 5%
2025-9-9 10:55:53-debug: [Build Memory track]: 查询 Asset Bundle start:207.99MB, end 208.53MB, increase: 553.04KB
2025-9-9 10:55:53-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:55:53-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:55:53-debug: // ---- build task 查询 Asset Bundle ---- (4ms)
2025-9-9 10:55:53-debug: [Build Memory track]: 查询 Asset Bundle start:208.56MB, end 208.84MB, increase: 285.45KB
2025-9-9 10:55:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:55:53-log: run build task 查询 Asset Bundle success in 4 ms√, progress: 10%
2025-9-9 10:55:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:55:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-9 10:55:53-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 12%
2025-9-9 10:55:53-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:55:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.87MB, end 208.91MB, increase: 46.46KB
2025-9-9 10:55:53-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:55:53-debug: [Build Memory track]: 填充脚本数据到 settings.json start:208.94MB, end 208.96MB, increase: 16.91KB
2025-9-9 10:55:53-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 10:55:53-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:55:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:55:53-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (5ms)
2025-9-9 10:55:53-log: run build task 整理部分构建选项内数据到 settings.json success in 5 ms√, progress: 15%
2025-9-9 10:55:53-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:208.99MB, end 209.29MB, increase: 302.43KB
2025-9-9 10:56:29-debug: refresh db internal success
2025-9-9 10:56:30-debug: refresh db assets success
2025-9-9 10:56:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:56:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:56:30-debug: asset-db:refresh-all-database (143ms)
2025-9-9 10:56:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 10:56:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:56:36-debug: refresh db internal success
2025-9-9 10:56:36-debug: refresh db assets success
2025-9-9 10:56:36-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:56:36-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:56:36-debug: asset-db:refresh-all-database (141ms)
2025-9-9 10:56:36-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 10:56:36-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 10:56:38-debug: Query all assets info in project
2025-9-9 10:56:38-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 10:56:38-debug: Skip compress image, progress: 0%
2025-9-9 10:56:38-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 10:56:38-debug: Init all bundles start..., progress: 0%
2025-9-9 10:56:38-debug: Num of bundles: 11..., progress: 0%
2025-9-9 10:56:38-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:56:38-debug: Init bundle root assets start..., progress: 0%
2025-9-9 10:56:38-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 10:56:38-debug:   Number of all scenes: 9
2025-9-9 10:56:38-debug:   Number of all scripts: 245
2025-9-9 10:56:38-debug:   Number of other assets: 2123
2025-9-9 10:56:38-debug: Init bundle root assets success..., progress: 0%
2025-9-9 10:56:38-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-9 10:56:38-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-9 10:56:38-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 10:56:38-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 10:56:38-debug: [Build Memory track]: 查询 Asset Bundle start:212.54MB, end 212.54MB, increase: 7.45KB
2025-9-9 10:56:38-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-9 10:56:38-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-9 10:56:38-debug: [Build Memory track]: 查询 Asset Bundle start:212.57MB, end 212.85MB, increase: 284.01KB
2025-9-9 10:56:38-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 10:56:38-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:56:38-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.88MB, end 212.90MB, increase: 16.73KB
2025-9-9 10:56:38-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-9 10:56:38-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 10:56:38-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 10:56:38-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-9 10:56:38-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-9 10:56:38-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.92MB, end 212.95MB, increase: 26.25KB
2025-9-9 10:56:38-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 10:56:38-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 10:56:38-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-9 10:56:38-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-9 10:56:38-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.98MB, end 213.26MB, increase: 291.17KB
2025-9-9 10:56:57-debug: refresh db internal success
2025-9-9 10:56:57-debug: refresh db assets success
2025-9-9 10:56:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 10:56:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 10:56:57-debug: asset-db:refresh-all-database (107ms)
2025-9-9 10:56:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 10:56:57-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:06:50-debug: refresh db internal success
2025-9-9 11:06:50-debug: refresh db assets success
2025-9-9 11:06:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:06:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:06:50-debug: asset-db:refresh-all-database (137ms)
2025-9-9 11:06:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:24:40-debug: refresh db internal success
2025-9-9 11:24:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:24:40-debug: refresh db assets success
2025-9-9 11:24:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:24:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:24:40-debug: asset-db:refresh-all-database (151ms)
2025-9-9 11:24:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:25:12-debug: refresh db internal success
2025-9-9 11:25:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:25:12-debug: refresh db assets success
2025-9-9 11:25:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:25:12-debug: asset-db:refresh-all-database (177ms)
2025-9-9 11:25:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:25:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:25:44-debug: refresh db internal success
2025-9-9 11:25:44-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:25:44-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:25:44-debug: refresh db assets success
2025-9-9 11:25:44-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:25:44-debug: asset-db:refresh-all-database (142ms)
2025-9-9 11:25:44-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 11:25:44-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-9 11:25:51-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\Emitter_main_01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:25:51-debug: asset-db:reimport-asset9f706f16-d7c3-4f42-a751-be38d4c9dabd (5ms)
2025-9-9 11:25:54-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\prefabs\emitter\Emitter_main_01.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:25:54-debug: asset-db:reimport-asset9f706f16-d7c3-4f42-a751-be38d4c9dabd (3ms)
2025-9-9 11:26:08-debug: refresh db internal success
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_story\script
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\randTerrain
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\utils
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\const
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\HomeEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_story\script\HomeStoryEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\core\base\ResManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\randTerrain\RandTerrain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\utils\RPN.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\const\AttributeConst.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\fight
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\HomeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\PlaneCombineResultUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\PlaneEquipInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\bullet\Bullet.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\FightEntity.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\base\AttributeData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\list
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\SelectList
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\script\ui\fight\RogueSelectIcon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\list\ListItem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\list\List.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\SelectList\uiSelect.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:26:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:26:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:26:08-debug: refresh db assets success
2025-9-9 11:26:08-debug: asset-db:refresh-all-database (178ms)
2025-9-9 11:26:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:26:08-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 11:26:11-debug: Query all assets info in project
2025-9-9 11:26:11-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 11:26:11-debug: Skip compress image, progress: 0%
2025-9-9 11:26:11-debug: Init all bundles start..., progress: 0%
2025-9-9 11:26:11-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 11:26:11-debug: Num of bundles: 11..., progress: 0%
2025-9-9 11:26:11-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 11:26:11-debug: Init bundle root assets start..., progress: 0%
2025-9-9 11:26:11-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 11:26:11-debug:   Number of all scenes: 9
2025-9-9 11:26:11-debug:   Number of other assets: 2123
2025-9-9 11:26:11-debug:   Number of all scripts: 245
2025-9-9 11:26:11-debug: Init bundle root assets success..., progress: 0%
2025-9-9 11:26:11-debug: // ---- build task 查询 Asset Bundle ---- (29ms)
2025-9-9 11:26:11-log: run build task 查询 Asset Bundle success in 29 ms√, progress: 5%
2025-9-9 11:26:11-debug: [Build Memory track]: 查询 Asset Bundle start:214.04MB, end 213.23MB, increase: -828.36KB
2025-9-9 11:26:11-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 11:26:11-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 11:26:11-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-9 11:26:11-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-9 11:26:11-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 11:26:11-debug: [Build Memory track]: 查询 Asset Bundle start:213.26MB, end 213.54MB, increase: 283.83KB
2025-9-9 11:26:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 11:26:11-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.56MB, end 213.59MB, increase: 25.84KB
2025-9-9 11:26:11-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-9 11:26:11-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 11:26:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 11:26:11-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 11:26:11-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-9 11:26:11-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-9 11:26:11-debug: [Build Memory track]: 填充脚本数据到 settings.json start:213.62MB, end 213.64MB, increase: 26.17KB
2025-9-9 11:26:11-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 11:26:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 11:26:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-9 11:26:11-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-9 11:26:11-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.67MB, end 213.96MB, increase: 296.31KB
2025-9-9 11:26:26-debug: refresh db internal success
2025-9-9 11:26:26-debug: refresh db assets success
2025-9-9 11:26:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:26:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:26:26-debug: asset-db:refresh-all-database (167ms)
2025-9-9 11:26:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:26:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:26:50-debug: refresh db internal success
2025-9-9 11:26:50-debug: refresh db assets success
2025-9-9 11:26:50-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:26:50-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:26:50-debug: asset-db:refresh-all-database (108ms)
2025-9-9 11:26:50-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:26:50-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:26:53-debug: refresh db internal success
2025-9-9 11:26:54-debug: refresh db assets success
2025-9-9 11:26:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:26:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:26:54-debug: asset-db:refresh-all-database (116ms)
2025-9-9 11:26:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:26:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:38:56-debug: refresh db internal success
2025-9-9 11:38:56-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\Emitter.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:38:56-debug: refresh db assets success
2025-9-9 11:38:56-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:38:56-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:38:56-debug: asset-db:refresh-all-database (146ms)
2025-9-9 11:38:56-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:38:56-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:38:58-debug: refresh db internal success
2025-9-9 11:38:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:38:58-debug: refresh db assets success
2025-9-9 11:38:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:38:58-debug: asset-db:refresh-all-database (122ms)
2025-9-9 11:38:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:38:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:42:18-debug: refresh db internal success
2025-9-9 11:42:18-debug: refresh db assets success
2025-9-9 11:42:18-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:42:18-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:42:18-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:42:18-debug: asset-db:refresh-all-database (113ms)
2025-9-9 11:42:18-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:42:23-debug: refresh db internal success
2025-9-9 11:42:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:42:23-debug: refresh db assets success
2025-9-9 11:42:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:42:23-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:42:23-debug: asset-db:refresh-all-database (104ms)
2025-9-9 11:42:23-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 11:46:02-debug: refresh db internal success
2025-9-9 11:46:02-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:46:02-debug: refresh db assets success
2025-9-9 11:46:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:46:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:46:02-debug: asset-db:refresh-all-database (148ms)
2025-9-9 11:46:02-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-9 11:46:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:46:18-debug: refresh db internal success
2025-9-9 11:46:19-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:46:19-debug: refresh db assets success
2025-9-9 11:46:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:46:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:46:19-debug: asset-db:refresh-all-database (143ms)
2025-9-9 11:46:40-debug: refresh db internal success
2025-9-9 11:46:40-debug: %cImport%c: E:\M2Game\Client\assets\editor\emitter\EmitterEditor.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:46:41-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:46:41-debug: refresh db assets success
2025-9-9 11:46:41-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:46:41-debug: asset-db:refresh-all-database (153ms)
2025-9-9 11:46:41-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:46:41-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:48:16-debug: refresh db internal success
2025-9-9 11:48:16-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:48:16-debug: refresh db assets success
2025-9-9 11:48:16-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:48:16-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:48:16-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:48:16-debug: asset-db:refresh-all-database (152ms)
2025-9-9 11:48:16-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:50:06-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:50:06-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (3ms)
2025-9-9 11:50:10-debug: %cImport%c: E:\M2Game\Client\assets\scenes\BulletEditor.scene
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:50:10-debug: asset-db:reimport-asset7613cf20-b279-4703-8e26-ea381e5129fc (4ms)
2025-9-9 11:50:59-debug: refresh db internal success
2025-9-9 11:51:00-debug: refresh db assets success
2025-9-9 11:51:00-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:51:00-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:51:00-debug: asset-db:refresh-all-database (168ms)
2025-9-9 11:51:00-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:51:00-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 11:53:20-debug: refresh db internal success
2025-9-9 11:53:20-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\condition\condition.md
background: #ffb8b8; color: #000;
color: #000;
2025-9-9 11:53:20-debug: %cDestroy%c: E:\M2Game\Client\assets\scripts\Game\condition
background: #ffb8b8; color: #000;
color: #000;
2025-9-9 11:53:20-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:53:20-debug: refresh db assets success
2025-9-9 11:53:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:53:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:53:20-debug: asset-db:refresh-all-database (143ms)
2025-9-9 11:53:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:53:20-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:54:19-debug: refresh db internal success
2025-9-9 11:54:19-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:54:19-debug: refresh db assets success
2025-9-9 11:54:19-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:54:19-debug: asset-db:refresh-all-database (119ms)
2025-9-9 11:54:30-debug: refresh db internal success
2025-9-9 11:54:30-debug: refresh db assets success
2025-9-9 11:54:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:54:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:54:30-debug: asset-db:refresh-all-database (111ms)
2025-9-9 11:54:30-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-9 11:54:42-debug: refresh db internal success
2025-9-9 11:54:42-debug: refresh db assets success
2025-9-9 11:54:42-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:54:42-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:54:42-debug: asset-db:refresh-all-database (108ms)
2025-9-9 11:54:42-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-9 11:55:10-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\4.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:55:10-debug: asset-db:reimport-asset63344f11-bb27-454f-88a7-397b8e522b06 (7ms)
2025-9-9 11:55:12-debug: refresh db internal success
2025-9-9 11:55:12-debug: refresh db assets success
2025-9-9 11:55:12-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:55:12-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:55:12-debug: asset-db:refresh-all-database (170ms)
2025-9-9 11:55:12-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:55:12-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:55:14-debug: refresh db internal success
2025-9-9 11:55:14-debug: refresh db assets success
2025-9-9 11:55:14-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:55:14-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:55:14-debug: asset-db:refresh-all-database (119ms)
2025-9-9 11:55:14-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 11:55:14-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 11:56:02-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:56:02-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (6ms)
2025-9-9 11:57:19-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 11:57:19-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (3ms)
2025-9-9 11:57:31-debug: refresh db internal success
2025-9-9 11:57:31-debug: refresh db assets success
2025-9-9 11:57:31-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 11:57:31-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 11:57:31-debug: asset-db:worker-effect-data-processing (8ms)
2025-9-9 11:57:31-debug: asset-db:refresh-all-database (152ms)
2025-9-9 11:57:31-debug: asset-db-hook-engine-extends-afterRefresh (8ms)
2025-9-9 12:18:02-debug: refresh db internal success
2025-9-9 12:18:02-debug: refresh db assets success
2025-9-9 12:18:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 12:18:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 12:18:02-debug: asset-db:refresh-all-database (147ms)
2025-9-9 12:18:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 12:18:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 14:23:40-debug: refresh db internal success
2025-9-9 14:23:40-debug: refresh db assets success
2025-9-9 14:23:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 14:23:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 14:23:40-debug: asset-db:refresh-all-database (166ms)
2025-9-9 14:23:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 14:23:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 14:23:43-debug: refresh db internal success
2025-9-9 14:23:43-debug: refresh db assets success
2025-9-9 14:23:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 14:23:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 14:23:43-debug: asset-db:refresh-all-database (130ms)
2025-9-9 14:23:45-debug: refresh db internal success
2025-9-9 14:23:46-debug: refresh db assets success
2025-9-9 14:23:46-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 14:23:46-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 14:23:46-debug: asset-db:refresh-all-database (105ms)
2025-9-9 14:32:39-debug: refresh db internal success
2025-9-9 14:32:39-debug: refresh db assets success
2025-9-9 14:32:39-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 14:32:39-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 14:32:39-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 14:32:39-debug: asset-db:refresh-all-database (120ms)
2025-9-9 14:32:39-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 14:35:58-debug: refresh db internal success
2025-9-9 14:35:58-debug: refresh db assets success
2025-9-9 14:35:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 14:35:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 14:35:58-debug: asset-db:worker-effect-data-processing (5ms)
2025-9-9 14:35:58-debug: asset-db:refresh-all-database (143ms)
2025-9-9 14:35:58-debug: asset-db-hook-engine-extends-afterRefresh (5ms)
2025-9-9 15:02:08-debug: refresh db internal success
2025-9-9 15:02:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 15:02:08-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data
background: #aaff85; color: #000;
color: #000;
2025-9-9 15:02:08-debug: refresh db assets success
2025-9-9 15:02:08-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 15:02:08-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 15:02:08-debug: asset-db:refresh-all-database (177ms)
2025-9-9 15:02:08-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 15:02:08-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 15:02:09-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 15:02:09-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (9ms)
2025-9-9 15:10:35-debug: refresh db internal success
2025-9-9 15:10:35-debug: refresh db assets success
2025-9-9 15:10:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 15:10:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 15:10:35-debug: asset-db:refresh-all-database (155ms)
2025-9-9 15:10:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 15:11:34-debug: refresh db internal success
2025-9-9 15:11:34-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 15:11:34-debug: refresh db assets success
2025-9-9 15:11:34-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 15:11:34-debug: asset-db:refresh-all-database (179ms)
2025-9-9 15:13:58-debug: refresh db internal success
2025-9-9 15:13:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 15:13:58-debug: refresh db assets success
2025-9-9 15:13:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 15:13:58-debug: asset-db:refresh-all-database (148ms)
2025-9-9 15:16:57-debug: refresh db internal success
2025-9-9 15:16:57-debug: refresh db assets success
2025-9-9 15:16:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 15:16:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 15:16:57-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 15:16:57-debug: asset-db:refresh-all-database (143ms)
2025-9-9 15:16:57-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 15:16:59-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 15:16:59-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (6ms)
2025-9-9 17:16:38-debug: refresh db internal success
2025-9-9 17:16:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 17:16:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\actions\IEventAction.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 17:16:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\conditions\IEventCondition.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 17:16:38-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\bullet\EventGroupData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 17:16:38-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 17:16:38-debug: refresh db assets success
2025-9-9 17:16:38-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 17:16:38-debug: asset-db:refresh-all-database (181ms)
2025-9-9 17:16:38-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 17:16:38-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 17:26:31-debug: refresh db internal success
2025-9-9 17:26:31-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 17:26:31-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 17:26:32-debug: refresh db assets success
2025-9-9 17:26:32-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 17:26:32-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 17:26:32-debug: asset-db:refresh-all-database (159ms)
2025-9-9 17:26:32-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 17:27:58-debug: refresh db internal success
2025-9-9 17:27:58-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 17:27:58-debug: refresh db assets success
2025-9-9 17:27:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 17:27:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 17:27:58-debug: asset-db:refresh-all-database (149ms)
2025-9-9 17:27:58-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 17:27:58-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 17:28:35-debug: refresh db internal success
2025-9-9 17:28:35-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 17:28:35-debug: refresh db assets success
2025-9-9 17:28:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 17:28:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 17:28:35-debug: asset-db:refresh-all-database (145ms)
2025-9-9 17:28:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 17:28:43-debug: refresh db internal success
2025-9-9 17:28:43-debug: refresh db assets success
2025-9-9 17:28:43-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 17:28:43-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 17:28:43-debug: asset-db:refresh-all-database (141ms)
2025-9-9 17:28:43-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 17:29:24-debug: refresh db internal success
2025-9-9 17:29:24-debug: refresh db assets success
2025-9-9 17:29:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 17:29:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 17:29:24-debug: asset-db:refresh-all-database (118ms)
2025-9-9 17:29:30-debug: refresh db internal success
2025-9-9 17:29:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 17:29:30-debug: refresh db assets success
2025-9-9 17:29:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 17:29:30-debug: asset-db:refresh-all-database (106ms)
2025-9-9 17:32:02-debug: refresh db internal success
2025-9-9 17:32:02-debug: refresh db assets success
2025-9-9 17:32:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 17:32:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 17:32:02-debug: asset-db:refresh-all-database (132ms)
2025-9-9 17:32:02-debug: asset-db:worker-effect-data-processing (-1ms)
2025-9-9 17:32:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 17:40:20-debug: refresh db internal success
2025-9-9 17:40:20-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 17:40:20-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 17:40:20-debug: refresh db assets success
2025-9-9 17:40:20-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 17:40:20-debug: asset-db:refresh-all-database (152ms)
2025-9-9 17:40:20-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 17:40:20-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 17:40:25-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-9 17:40:25-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (4ms)
2025-9-9 17:40:25-debug: refresh db internal success
2025-9-9 17:40:25-debug: refresh db assets success
2025-9-9 17:40:25-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 17:40:25-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 17:40:25-debug: asset-db:refresh-all-database (112ms)
2025-9-9 17:40:25-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 17:40:25-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 18:02:01-debug: refresh db internal success
2025-9-9 18:02:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 18:02:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 18:02:01-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 18:02:01-debug: refresh db assets success
2025-9-9 18:02:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 18:02:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 18:02:01-debug: asset-db:refresh-all-database (150ms)
2025-9-9 18:02:01-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 18:02:01-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (4ms)
2025-9-9 18:02:02-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 18:02:02-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (3ms)
2025-9-9 18:02:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 18:02:24-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (5ms)
2025-9-9 18:05:24-debug: refresh db internal success
2025-9-9 18:05:24-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 18:05:24-debug: refresh db assets success
2025-9-9 18:05:24-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 18:05:24-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 18:05:24-debug: asset-db:refresh-all-database (156ms)
2025-9-9 18:05:24-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 18:05:24-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (9ms)
2025-9-9 19:04:30-debug: refresh db internal success
2025-9-9 19:04:30-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:04:30-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:04:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:04:30-debug: refresh db assets success
2025-9-9 19:04:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:04:30-debug: asset-db:refresh-all-database (194ms)
2025-9-9 19:04:30-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:04:30-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 19:04:30-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:04:30-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (6ms)
2025-9-9 19:04:47-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:04:47-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (3ms)
2025-9-9 19:05:40-debug: refresh db internal success
2025-9-9 19:05:40-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\data\WaveData.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:05:40-debug: refresh db assets success
2025-9-9 19:05:40-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:05:40-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:05:40-debug: asset-db:refresh-all-database (147ms)
2025-9-9 19:05:40-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:05:40-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:05:58-debug: refresh db internal success
2025-9-9 19:05:58-debug: refresh db assets success
2025-9-9 19:05:58-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:05:58-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:05:58-debug: asset-db:refresh-all-database (118ms)
2025-9-9 19:06:21-debug: refresh db internal success
2025-9-9 19:06:21-debug: refresh db assets success
2025-9-9 19:06:21-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:06:21-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:06:21-debug: asset-db:refresh-all-database (112ms)
2025-9-9 19:06:21-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-9 19:06:21-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 19:07:51-debug: refresh db internal success
2025-9-9 19:07:51-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:07:52-debug: refresh db assets success
2025-9-9 19:07:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:07:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:07:52-debug: asset-db:refresh-all-database (154ms)
2025-9-9 19:07:54-debug: refresh db internal success
2025-9-9 19:07:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:07:54-debug: refresh db assets success
2025-9-9 19:07:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:07:54-debug: asset-db:refresh-all-database (143ms)
2025-9-9 19:08:09-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\1.json
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:08:09-debug: asset-db:reimport-assetd4b69b47-f21e-498c-8785-c8324a4213f3 (4ms)
2025-9-9 19:08:09-debug: refresh db internal success
2025-9-9 19:08:09-debug: refresh db assets success
2025-9-9 19:08:09-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:08:09-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:08:09-debug: asset-db:refresh-all-database (117ms)
2025-9-9 19:08:09-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:08:09-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:08:10-debug: Query all assets info in project
2025-9-9 19:08:10-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:08:10-debug: Skip compress image, progress: 0%
2025-9-9 19:08:10-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:08:10-debug: Init all bundles start..., progress: 0%
2025-9-9 19:08:10-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:08:10-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:08:10-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:08:11-debug:   Number of all scenes: 9
2025-9-9 19:08:10-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:08:11-debug:   Number of other assets: 2122
2025-9-9 19:08:11-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:08:11-debug:   Number of all scripts: 246
2025-9-9 19:08:11-debug: // ---- build task 查询 Asset Bundle ---- (31ms)
2025-9-9 19:08:11-log: run build task 查询 Asset Bundle success in 31 ms√, progress: 5%
2025-9-9 19:08:11-debug: [Build Memory track]: 查询 Asset Bundle start:221.27MB, end 222.01MB, increase: 752.66KB
2025-9-9 19:08:11-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:08:11-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:08:11-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-9 19:08:11-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-9 19:08:11-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:08:11-debug: [Build Memory track]: 查询 Asset Bundle start:222.04MB, end 222.32MB, increase: 282.85KB
2025-9-9 19:08:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:08:11-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:222.35MB, end 222.37MB, increase: 25.67KB
2025-9-9 19:08:11-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:08:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 19:08:11-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-9 19:08:11-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:08:11-debug: // ---- build task 填充脚本数据到 settings.json ---- (2ms)
2025-9-9 19:08:11-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:08:11-log: run build task 填充脚本数据到 settings.json success in 2 ms√, progress: 13%
2025-9-9 19:08:11-debug: [Build Memory track]: 填充脚本数据到 settings.json start:222.40MB, end 222.43MB, increase: 27.41KB
2025-9-9 19:08:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:08:11-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-9 19:08:11-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-9 19:08:11-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:222.45MB, end 222.74MB, increase: 295.82KB
2025-9-9 19:08:15-debug: refresh db internal success
2025-9-9 19:08:15-debug: refresh db assets success
2025-9-9 19:08:15-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:08:15-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:08:15-debug: asset-db:refresh-all-database (142ms)
2025-9-9 19:08:15-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:08:15-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:10:29-debug: refresh db internal success
2025-9-9 19:10:30-debug: refresh db assets success
2025-9-9 19:10:30-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:10:30-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:10:30-debug: asset-db:refresh-all-database (123ms)
2025-9-9 19:10:30-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:10:35-debug: refresh db internal success
2025-9-9 19:10:35-debug: refresh db assets success
2025-9-9 19:10:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:10:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:10:35-debug: asset-db:refresh-all-database (112ms)
2025-9-9 19:10:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:10:35-debug: Query all assets info in project
2025-9-9 19:10:35-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:10:35-debug: Skip compress image, progress: 0%
2025-9-9 19:10:35-debug: Init all bundles start..., progress: 0%
2025-9-9 19:10:35-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:10:35-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:10:35-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:10:35-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:10:35-debug:   Number of all scenes: 9
2025-9-9 19:10:35-debug:   Number of other assets: 2122
2025-9-9 19:10:35-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:10:35-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:10:35-debug:   Number of all scripts: 246
2025-9-9 19:10:35-debug: // ---- build task 查询 Asset Bundle ---- (21ms)
2025-9-9 19:10:35-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:10:35-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:10:35-debug: [Build Memory track]: 查询 Asset Bundle start:213.04MB, end 213.56MB, increase: 534.96KB
2025-9-9 19:10:35-log: run build task 查询 Asset Bundle success in 21 ms√, progress: 5%
2025-9-9 19:10:35-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-9 19:10:35-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-9 19:10:35-debug: [Build Memory track]: 查询 Asset Bundle start:213.59MB, end 213.88MB, increase: 292.46KB
2025-9-9 19:10:35-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:10:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:10:35-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-9 19:10:35-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:213.91MB, end 213.92MB, increase: 16.50KB
2025-9-9 19:10:35-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:10:35-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:10:35-debug: [Build Memory track]: 填充脚本数据到 settings.json start:213.95MB, end 213.98MB, increase: 26.35KB
2025-9-9 19:10:35-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-9-9 19:10:35-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:10:35-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-9-9 19:10:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:10:35-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 19:10:35-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.00MB, end 214.29MB, increase: 296.53KB
2025-9-9 19:10:35-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-9 19:15:17-debug: refresh db internal success
2025-9-9 19:15:17-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:15:17-debug: refresh db assets success
2025-9-9 19:15:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:15:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:15:17-debug: asset-db:refresh-all-database (147ms)
2025-9-9 19:15:18-debug: Query all assets info in project
2025-9-9 19:15:18-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:15:18-debug: Skip compress image, progress: 0%
2025-9-9 19:15:18-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:15:18-debug: Init all bundles start..., progress: 0%
2025-9-9 19:15:18-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:15:18-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:15:18-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:15:18-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:15:18-debug:   Number of all scenes: 9
2025-9-9 19:15:19-debug:   Number of other assets: 2122
2025-9-9 19:15:19-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-9 19:15:19-debug:   Number of all scripts: 246
2025-9-9 19:15:19-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:15:19-debug: [Build Memory track]: 查询 Asset Bundle start:215.92MB, end 216.56MB, increase: 654.59KB
2025-9-9 19:15:19-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:15:19-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-9 19:15:19-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:15:19-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-9 19:15:19-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-9 19:15:19-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:15:19-debug: [Build Memory track]: 查询 Asset Bundle start:216.59MB, end 216.87MB, increase: 287.02KB
2025-9-9 19:15:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:15:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (-1ms)
2025-9-9 19:15:19-log: run build task 整理部分构建选项内数据到 settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-9-9 19:15:19-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:216.90MB, end 216.92MB, increase: 26.05KB
2025-9-9 19:15:19-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:15:19-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:15:19-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-9-9 19:15:19-debug: [Build Memory track]: 填充脚本数据到 settings.json start:216.95MB, end 216.98MB, increase: 26.30KB
2025-9-9 19:15:19-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-9-9 19:15:19-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:15:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:15:19-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-9 19:15:19-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:217.01MB, end 217.29MB, increase: 292.43KB
2025-9-9 19:15:19-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 19:16:05-debug: refresh db internal success
2025-9-9 19:16:05-debug: refresh db assets success
2025-9-9 19:16:05-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:16:05-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:16:05-debug: asset-db:refresh-all-database (134ms)
2025-9-9 19:16:15-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:16:15-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (1ms)
2025-9-9 19:16:52-debug: refresh db internal success
2025-9-9 19:16:52-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:16:52-debug: refresh db assets success
2025-9-9 19:16:52-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:16:52-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:16:52-debug: asset-db:refresh-all-database (153ms)
2025-9-9 19:16:57-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:16:57-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-9 19:16:57-debug: refresh db internal success
2025-9-9 19:16:57-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:16:57-debug: refresh db assets success
2025-9-9 19:16:57-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:16:57-debug: asset-db:refresh-all-database (124ms)
2025-9-9 19:16:58-debug: Query all assets info in project
2025-9-9 19:16:58-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:16:58-debug: Skip compress image, progress: 0%
2025-9-9 19:16:58-debug: Init all bundles start..., progress: 0%
2025-9-9 19:16:58-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:16:58-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:16:58-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:16:58-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:16:58-debug:   Number of all scripts: 246
2025-9-9 19:16:58-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:16:58-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:16:58-debug:   Number of all scenes: 9
2025-9-9 19:16:58-debug:   Number of other assets: 2122
2025-9-9 19:16:58-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-9 19:16:58-log: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-9-9 19:16:58-debug: [Build Memory track]: 查询 Asset Bundle start:209.79MB, end 212.14MB, increase: 2.35MB
2025-9-9 19:16:58-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:16:58-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:16:58-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-9 19:16:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:16:58-debug: [Build Memory track]: 查询 Asset Bundle start:212.17MB, end 212.44MB, increase: 284.01KB
2025-9-9 19:16:58-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-9 19:16:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:16:58-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-9 19:16:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.47MB, end 212.49MB, increase: 16.76KB
2025-9-9 19:16:58-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:16:58-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:16:58-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 19:16:58-debug: [Build Memory track]: 填充脚本数据到 settings.json start:212.52MB, end 212.53MB, increase: 16.46KB
2025-9-9 19:16:58-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:16:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:16:58-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 19:16:58-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-9 19:16:58-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:212.56MB, end 207.09MB, increase: -5599.55KB
2025-9-9 19:18:23-debug: refresh db internal success
2025-9-9 19:18:23-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:18:23-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:18:23-debug: refresh db assets success
2025-9-9 19:18:23-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:18:23-debug: asset-db:refresh-all-database (143ms)
2025-9-9 19:18:26-debug: refresh db internal success
2025-9-9 19:18:26-debug: refresh db assets success
2025-9-9 19:18:26-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:18:26-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:18:26-debug: asset-db:refresh-all-database (136ms)
2025-9-9 19:18:26-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:18:26-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:20:54-debug: refresh db internal success
2025-9-9 19:20:54-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:20:54-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:20:54-debug: refresh db assets success
2025-9-9 19:20:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:20:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:20:54-debug: asset-db:refresh-all-database (118ms)
2025-9-9 19:21:02-debug: Query all assets info in project
2025-9-9 19:21:02-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:21:02-debug: Skip compress image, progress: 0%
2025-9-9 19:21:02-debug: Init all bundles start..., progress: 0%
2025-9-9 19:21:02-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:21:02-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:21:02-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:21:02-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:21:02-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:21:02-debug:   Number of all scenes: 9
2025-9-9 19:21:02-debug:   Number of other assets: 2122
2025-9-9 19:21:02-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:21:02-debug:   Number of all scripts: 246
2025-9-9 19:21:02-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-9 19:21:02-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-9 19:21:02-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:21:02-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:21:02-debug: [Build Memory track]: 查询 Asset Bundle start:213.79MB, end 215.69MB, increase: 1.90MB
2025-9-9 19:21:02-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-9 19:21:02-debug: [Build Memory track]: 查询 Asset Bundle start:215.72MB, end 214.51MB, increase: -1237.70KB
2025-9-9 19:21:02-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:21:02-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-9 19:21:02-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:21:02-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-9 19:21:02-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:21:02-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.54MB, end 214.55MB, increase: 16.53KB
2025-9-9 19:21:02-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:21:02-log: run build task 填充脚本数据到 settings.json success in -1 h -1 min -1 s√, progress: 13%
2025-9-9 19:21:02-debug: // ---- build task 填充脚本数据到 settings.json ---- (-1ms)
2025-9-9 19:21:02-debug: [Build Memory track]: 填充脚本数据到 settings.json start:214.58MB, end 214.61MB, increase: 26.08KB
2025-9-9 19:21:02-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:21:02-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:21:02-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 19:21:02-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-9 19:21:02-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:214.64MB, end 214.92MB, increase: 291.93KB
2025-9-9 19:24:48-debug: refresh db internal success
2025-9-9 19:24:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\bullet\BulletController.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:24:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:24:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\move\Movable.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:24:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\ui\plane\enemy\EnemyPlane.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:24:48-debug: refresh db assets success
2025-9-9 19:24:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:24:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:24:48-debug: asset-db:refresh-all-database (150ms)
2025-9-9 19:24:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:24:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:25:35-debug: refresh db internal success
2025-9-9 19:25:35-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:25:35-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:25:35-debug: refresh db assets success
2025-9-9 19:25:35-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:25:35-debug: asset-db:refresh-all-database (119ms)
2025-9-9 19:25:35-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:25:35-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:25:37-debug: Query all assets info in project
2025-9-9 19:25:37-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:25:37-debug: Skip compress image, progress: 0%
2025-9-9 19:25:37-debug: Init all bundles start..., progress: 0%
2025-9-9 19:25:37-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:25:37-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:25:37-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:25:37-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:25:37-debug:   Number of all scenes: 9
2025-9-9 19:25:37-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:25:37-debug:   Number of all scripts: 246
2025-9-9 19:25:37-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:25:37-debug:   Number of other assets: 2122
2025-9-9 19:25:37-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-9 19:25:37-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-9 19:25:37-debug: [Build Memory track]: 查询 Asset Bundle start:221.28MB, end 221.38MB, increase: 107.01KB
2025-9-9 19:25:37-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:25:37-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:25:37-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-9 19:25:37-debug: [Build Memory track]: 查询 Asset Bundle start:221.41MB, end 221.69MB, increase: 284.46KB
2025-9-9 19:25:37-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-9 19:25:37-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:25:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:25:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 19:25:37-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-9 19:25:37-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:25:37-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.72MB, end 221.74MB, increase: 26.20KB
2025-9-9 19:25:37-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:25:37-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-9 19:25:37-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-9 19:25:37-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:25:37-debug: [Build Memory track]: 填充脚本数据到 settings.json start:221.78MB, end 221.81MB, increase: 34.12KB
2025-9-9 19:25:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:25:37-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (3ms)
2025-9-9 19:25:37-log: run build task 整理部分构建选项内数据到 settings.json success in 3 ms√, progress: 15%
2025-9-9 19:25:37-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:221.84MB, end 222.12MB, increase: 292.24KB
2025-9-9 19:26:11-debug: refresh db internal success
2025-9-9 19:26:11-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:26:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:26:11-debug: refresh db assets success
2025-9-9 19:26:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:26:11-debug: asset-db:refresh-all-database (150ms)
2025-9-9 19:26:14-debug: Query all assets info in project
2025-9-9 19:26:14-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:26:14-debug: Skip compress image, progress: 0%
2025-9-9 19:26:14-debug: Init all bundles start..., progress: 0%
2025-9-9 19:26:14-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:26:14-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:26:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:26:14-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:26:14-debug:   Number of all scenes: 9
2025-9-9 19:26:14-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:26:14-debug:   Number of all scripts: 246
2025-9-9 19:26:14-debug:   Number of other assets: 2122
2025-9-9 19:26:14-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:26:14-debug: // ---- build task 查询 Asset Bundle ---- (27ms)
2025-9-9 19:26:14-log: run build task 查询 Asset Bundle success in 27 ms√, progress: 5%
2025-9-9 19:26:14-debug: [Build Memory track]: 查询 Asset Bundle start:205.61MB, end 206.15MB, increase: 552.88KB
2025-9-9 19:26:14-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:26:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:26:14-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-9 19:26:14-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-9 19:26:14-debug: [Build Memory track]: 查询 Asset Bundle start:206.18MB, end 206.46MB, increase: 284.42KB
2025-9-9 19:26:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:26:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:26:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (-1ms)
2025-9-9 19:26:14-log: run build task 整理部分构建选项内数据到 settings.json success in -1 h -1 min -1 s√, progress: 12%
2025-9-9 19:26:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.49MB, end 206.51MB, increase: 26.00KB
2025-9-9 19:26:14-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:26:14-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:26:14-debug: [Build Memory track]: 填充脚本数据到 settings.json start:206.54MB, end 206.56MB, increase: 16.86KB
2025-9-9 19:26:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:26:14-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 19:26:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:26:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-9 19:26:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:206.59MB, end 206.88MB, increase: 296.21KB
2025-9-9 19:26:14-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-9 19:27:11-debug: refresh db internal success
2025-9-9 19:27:11-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:27:11-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:27:11-debug: refresh db assets success
2025-9-9 19:27:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:27:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:27:11-debug: asset-db:refresh-all-database (149ms)
2025-9-9 19:27:11-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:27:11-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 19:27:14-debug: Query all assets info in project
2025-9-9 19:27:14-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:27:14-debug: Skip compress image, progress: 0%
2025-9-9 19:27:14-debug: Init all bundles start..., progress: 0%
2025-9-9 19:27:14-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:27:14-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:27:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:27:14-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:27:14-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:27:14-debug:   Number of all scripts: 246
2025-9-9 19:27:14-debug:   Number of other assets: 2122
2025-9-9 19:27:14-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:27:14-debug:   Number of all scenes: 9
2025-9-9 19:27:14-log: run build task 查询 Asset Bundle success in 19 ms√, progress: 5%
2025-9-9 19:27:14-debug: [Build Memory track]: 查询 Asset Bundle start:209.98MB, end 213.44MB, increase: 3.47MB
2025-9-9 19:27:14-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:27:14-debug: // ---- build task 查询 Asset Bundle ---- (19ms)
2025-9-9 19:27:14-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:27:14-debug: // ---- build task 查询 Asset Bundle ---- (2ms)
2025-9-9 19:27:14-log: run build task 查询 Asset Bundle success in 2 ms√, progress: 10%
2025-9-9 19:27:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:27:14-debug: [Build Memory track]: 查询 Asset Bundle start:213.47MB, end 210.92MB, increase: -2610.07KB
2025-9-9 19:27:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:27:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 19:27:14-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 12%
2025-9-9 19:27:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.95MB, end 210.98MB, increase: 25.79KB
2025-9-9 19:27:14-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:27:14-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:27:14-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-9 19:27:14-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-9 19:27:14-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:27:14-debug: [Build Memory track]: 填充脚本数据到 settings.json start:211.00MB, end 211.03MB, increase: 26.02KB
2025-9-9 19:27:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:27:14-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-9 19:27:14-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:211.06MB, end 211.34MB, increase: 292.27KB
2025-9-9 19:27:14-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-9 19:29:06-debug: refresh db internal success
2025-9-9 19:29:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:29:06-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:29:06-debug: refresh db assets success
2025-9-9 19:29:06-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:29:06-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:29:06-debug: asset-db:refresh-all-database (148ms)
2025-9-9 19:29:06-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:29:09-debug: Query all assets info in project
2025-9-9 19:29:09-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:29:09-debug: Skip compress image, progress: 0%
2025-9-9 19:29:09-debug: Init all bundles start..., progress: 0%
2025-9-9 19:29:09-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:29:09-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:29:09-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:29:09-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:29:09-debug:   Number of all scripts: 246
2025-9-9 19:29:09-debug:   Number of other assets: 2122
2025-9-9 19:29:09-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:29:09-debug:   Number of all scenes: 9
2025-9-9 19:29:09-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:29:09-debug: // ---- build task 查询 Asset Bundle ---- (20ms)
2025-9-9 19:29:09-log: run build task 查询 Asset Bundle success in 20 ms√, progress: 5%
2025-9-9 19:29:09-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:29:09-debug: [Build Memory track]: 查询 Asset Bundle start:216.79MB, end 215.33MB, increase: -1486.63KB
2025-9-9 19:29:09-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:29:09-debug: // ---- build task 查询 Asset Bundle ---- (1ms)
2025-9-9 19:29:09-debug: [Build Memory track]: 查询 Asset Bundle start:215.36MB, end 215.64MB, increase: 283.88KB
2025-9-9 19:29:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:29:09-log: run build task 查询 Asset Bundle success in 1 ms√, progress: 10%
2025-9-9 19:29:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:29:09-log: run build task 整理部分构建选项内数据到 settings.json success in √, progress: 12%
2025-9-9 19:29:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.67MB, end 215.68MB, increase: 16.76KB
2025-9-9 19:29:09-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:29:09-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:29:09-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 19:29:09-debug: [Build Memory track]: 填充脚本数据到 settings.json start:215.71MB, end 215.73MB, increase: 16.03KB
2025-9-9 19:29:09-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:29:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:29:09-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 19:29:09-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-9 19:29:09-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:215.76MB, end 216.04MB, increase: 290.96KB
2025-9-9 19:31:11-debug: refresh db internal success
2025-9-9 19:31:11-debug: refresh db assets success
2025-9-9 19:31:11-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:31:11-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:31:11-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-9 19:31:11-debug: asset-db:refresh-all-database (141ms)
2025-9-9 19:31:11-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 19:32:17-debug: refresh db internal success
2025-9-9 19:32:17-debug: refresh db assets success
2025-9-9 19:32:17-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:32:17-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:32:17-debug: asset-db:refresh-all-database (136ms)
2025-9-9 19:32:17-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:32:17-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 19:33:54-debug: refresh db internal success
2025-9-9 19:33:54-debug: refresh db assets success
2025-9-9 19:33:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:33:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:33:54-debug: asset-db:refresh-all-database (115ms)
2025-9-9 19:33:54-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:33:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:34:02-debug: refresh db internal success
2025-9-9 19:34:02-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:34:02-debug: refresh db assets success
2025-9-9 19:34:02-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:34:02-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:34:02-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:34:02-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:34:02-debug: asset-db:refresh-all-database (120ms)
2025-9-9 19:34:14-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Game.scene
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:34:14-debug: asset-db:reimport-asset6b52bce3-af16-4791-a85c-1786c6ed769a (4ms)
2025-9-9 19:34:17-debug: Query all assets info in project
2025-9-9 19:34:17-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:34:17-debug: Skip compress image, progress: 0%
2025-9-9 19:34:17-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:34:17-debug: Init all bundles start..., progress: 0%
2025-9-9 19:34:17-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:34:17-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:34:17-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:34:17-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:34:17-debug:   Number of all scenes: 9
2025-9-9 19:34:17-debug:   Number of all scripts: 246
2025-9-9 19:34:17-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:34:17-debug:   Number of other assets: 2122
2025-9-9 19:34:17-debug: // ---- build task 查询 Asset Bundle ---- (17ms)
2025-9-9 19:34:17-log: run build task 查询 Asset Bundle success in 17 ms√, progress: 5%
2025-9-9 19:34:17-debug: [Build Memory track]: 查询 Asset Bundle start:225.30MB, end 227.19MB, increase: 1.89MB
2025-9-9 19:34:17-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:34:17-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:34:17-debug: [Build Memory track]: 查询 Asset Bundle start:227.22MB, end 225.86MB, increase: -1393.61KB
2025-9-9 19:34:17-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-9 19:34:17-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:34:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:34:17-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-9 19:34:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (15ms)
2025-9-9 19:34:17-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:225.89MB, end 204.58MB, increase: -21818.01KB
2025-9-9 19:34:17-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:34:17-log: run build task 整理部分构建选项内数据到 settings.json success in 15 ms√, progress: 12%
2025-9-9 19:34:17-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:34:17-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:34:17-log: run build task 填充脚本数据到 settings.json success in √, progress: 13%
2025-9-9 19:34:17-debug: [Build Memory track]: 填充脚本数据到 settings.json start:204.61MB, end 204.64MB, increase: 28.38KB
2025-9-9 19:34:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:34:17-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (1ms)
2025-9-9 19:34:17-log: run build task 整理部分构建选项内数据到 settings.json success in 1 ms√, progress: 15%
2025-9-9 19:34:17-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:204.66MB, end 204.95MB, increase: 296.77KB
2025-9-9 19:36:37-debug: refresh db internal success
2025-9-9 19:36:37-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\manager\WaveManager.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:36:37-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\wave\Wave.ts
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:36:37-debug: refresh db assets success
2025-9-9 19:36:37-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:36:37-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:36:37-debug: asset-db:refresh-all-database (152ms)
2025-9-9 19:36:37-debug: asset-db:worker-effect-data-processing (2ms)
2025-9-9 19:36:37-debug: asset-db-hook-engine-extends-afterRefresh (2ms)
2025-9-9 19:36:38-debug: Query all assets info in project
2025-9-9 19:36:38-debug: init custom config: keepNodeUuid: false, useCache: true
2025-9-9 19:36:38-debug: Skip compress image, progress: 0%
2025-9-9 19:36:38-debug: Init all bundles start..., progress: 0%
2025-9-9 19:36:38-debug: Num of bundles: 11..., progress: 0%
2025-9-9 19:36:38-debug: 查询 Asset Bundle start, progress: 0%
2025-9-9 19:36:38-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:36:38-debug: Init bundle root assets start..., progress: 0%
2025-9-9 19:36:38-debug: Query preload assets/scripts from cc.config.json: 60f7195c-ec2a-45eb-ba94-8955f60e81d0,1c02ae6f-4492-4915-b8f8-7492a3b1e4cd,810e96e4-e456-4468-9b59-f4e8f39732c0,efe8e2a3-eace-427b-b4f1-cb8a937ec77d,e9aa9a3e-5b2b-4ac7-a2c7-073de2b2b24f,8bbdbcdd-5cd4-4100-b6d5-b7c9625b6107,50f4348b-c883-4e2f-8f11-ce233b859fa1,fda095cb-831d-4601-ad94-846013963de8,f92806d7-1768-443f-afe8-12bcde84d0f0,dd3a144d-ab7f-41f0-82b8-2e43a090d496,f0416e68-0200-4b77-a926-4f9d16e494da,970b0598-bcb0-4714-91fb-2e81440dccd8,bcd64cc6-2dd9-43f6-abbe-66318d332032,d930590d-bb92-4cc8-8bd1-23cd027f9edf,a3cd009f-0ab0-420d-9278-b9fdab939bbc,6a2d0734-bd9e-4ddf-946e-caa52498cb75,c27215d8-6835-4b68-bfbb-bdeac6100c04,b5d6115f-0370-4d7c-aad3-c194cc71cf98,871c3b6c-7379-419d-bda3-794b239ab90d,ff9b0199-ce04-4cfe-86cc-6c719f08d6e4,de1c2107-70c8-4021-8459-6399f24d01c6,cbf30902-517f-40dc-af90-a550bac27cf1
2025-9-9 19:36:38-debug:   Number of all scripts: 246
2025-9-9 19:36:38-debug:   Number of other assets: 2122
2025-9-9 19:36:38-debug: Init bundle root assets success..., progress: 0%
2025-9-9 19:36:38-debug:   Number of all scenes: 9
2025-9-9 19:36:38-debug: // ---- build task 查询 Asset Bundle ---- (22ms)
2025-9-9 19:36:38-debug: [Build Memory track]: 查询 Asset Bundle start:210.90MB, end 209.87MB, increase: -1059.99KB
2025-9-9 19:36:38-debug: 查询 Asset Bundle start, progress: 5%
2025-9-9 19:36:38-log: run build task 查询 Asset Bundle success in 22 ms√, progress: 5%
2025-9-9 19:36:38-debug: // ---- build task 查询 Asset Bundle ----
2025-9-9 19:36:38-debug: // ---- build task 查询 Asset Bundle ---- (3ms)
2025-9-9 19:36:38-log: run build task 查询 Asset Bundle success in 3 ms√, progress: 10%
2025-9-9 19:36:38-debug: [Build Memory track]: 查询 Asset Bundle start:209.89MB, end 210.17MB, increase: 283.39KB
2025-9-9 19:36:38-debug: 整理部分构建选项内数据到 settings.json start, progress: 10%
2025-9-9 19:36:38-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:36:38-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-9 19:36:38-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 12%
2025-9-9 19:36:38-debug: 填充脚本数据到 settings.json start, progress: 12%
2025-9-9 19:36:38-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.20MB, end 210.22MB, increase: 25.87KB
2025-9-9 19:36:38-debug: // ---- build task 填充脚本数据到 settings.json ----
2025-9-9 19:36:38-log: run build task 填充脚本数据到 settings.json success in 1 ms√, progress: 13%
2025-9-9 19:36:38-debug: 整理部分构建选项内数据到 settings.json start, progress: 13%
2025-9-9 19:36:38-debug: [Build Memory track]: 填充脚本数据到 settings.json start:210.25MB, end 210.28MB, increase: 25.64KB
2025-9-9 19:36:38-debug: // ---- build task 填充脚本数据到 settings.json ---- (1ms)
2025-9-9 19:36:38-debug: // ---- build task 整理部分构建选项内数据到 settings.json ----
2025-9-9 19:36:38-debug: // ---- build task 整理部分构建选项内数据到 settings.json ---- (2ms)
2025-9-9 19:36:38-debug: [Build Memory track]: 整理部分构建选项内数据到 settings.json start:210.31MB, end 210.59MB, increase: 291.96KB
2025-9-9 19:36:38-log: run build task 整理部分构建选项内数据到 settings.json success in 2 ms√, progress: 15%
2025-9-9 19:38:49-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\wave\wave_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-9 19:38:49-debug: asset-db:reimport-assetd5f5e836-9495-4c08-890f-d92cc6696838 (2ms)
2025-9-9 19:38:49-debug: refresh db internal success
2025-9-9 19:38:49-debug: refresh db assets success
2025-9-9 19:38:49-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:38:49-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:38:49-debug: asset-db:refresh-all-database (113ms)
2025-9-9 19:41:53-debug: refresh db internal success
2025-9-9 19:41:53-debug: refresh db assets success
2025-9-9 19:41:53-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:41:53-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:41:53-debug: asset-db:refresh-all-database (125ms)
2025-9-9 19:42:55-debug: refresh db internal success
2025-9-9 19:42:55-debug: refresh db assets success
2025-9-9 19:42:55-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:42:55-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:42:55-debug: asset-db:refresh-all-database (108ms)
2025-9-9 19:42:55-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:42:55-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-9 19:48:01-debug: refresh db internal success
2025-9-9 19:48:01-debug: refresh db assets success
2025-9-9 19:48:01-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:48:01-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:48:01-debug: asset-db:refresh-all-database (111ms)
2025-9-9 19:48:07-debug: refresh db internal success
2025-9-9 19:48:07-debug: refresh db assets success
2025-9-9 19:48:07-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-9 19:48:07-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-9 19:48:07-debug: asset-db:refresh-all-database (113ms)
2025-9-9 19:48:07-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-9 19:48:07-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-10 11:15:48-debug: refresh db internal success
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_skyisland\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_shop\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_story\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_talent\script
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\type
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\event
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\module
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_shop\script\data
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_skyisland\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_skyisland\script\data
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_shop\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_story\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_talent\script\ui
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_talent\script\data
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\dialogue
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\fight
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\mail
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\pk
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\button
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\dropdown
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\list
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\SelectList
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\back_pack
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\display
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\HomePlaneEntry.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\HomeEntry.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_skyisland\script\HomeSkyIslandEntry.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_shop\script\HomeShopEntry.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_story\script\HomeStoryEntry.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_talent\script\HomeTalentEntry.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\type\BottomTab.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\TopBlockInputUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\event\HomeUIEvent.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\BottomUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\HomeUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\PopupUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\PlaneShowUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\ToastUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\TopUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\module\PlaneEvent.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\WheelSpinnerUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\module\PlaneTypes.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\PlaneCombineResultUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\PlaneEquipInfoUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_shop\script\ui\ShopUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\PlaneUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_skyisland\script\ui\SkyIslandUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_story\script\ui\BuidingUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_story\script\ui\BuildingInfoUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_talent\script\ui\TalentUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\dialogue\DialogueUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_story\script\ui\StoryUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\fight\RogueSelectIcon.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\fight\RogueUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend\FriendAddUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend\FriendCellUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend\FriendStrangerUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend\FriendListUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\friend\FriendUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\mail\MailCellUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\mail\MailUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\pk\PKHistoryCellUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\pk\PKHistoryUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\pk\PKUI.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\script\ui\pk\PKRewardIcon.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\button\ButtonPlus.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\dropdown\DropDown.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\button\DragButton.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\list\List.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\SelectList\uiSelect.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\SelectList\uiSelectItem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\back_pack\BagGrid.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\common\script\ui\components\list\ListItem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\back_pack\BagItem.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\back_pack\Tabs.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\display\CombineDisplay.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\back_pack\SortTypeDropdown.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\script\ui\components\display\EquipDisplay.ts
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home\prefab\Entry.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_plane\prefab\Entry.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_shop\prefab\Entry.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_skyisland\prefab\Entry.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_story\prefab\Entry.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cDestroy%c: E:\M2Game\Client\assets\bundles\home_talent\prefab\Entry.prefab
background: #ffb8b8; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\texture
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event\HomeUIEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event\PlaneUIEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\shop
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\skyisland
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\talent
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\story
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\task
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\TopBlockInputUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\BottomUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\BottomTab.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\dialogue
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\friend
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\fight
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\mail
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\HomeUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\pk
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\PopupUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\PlaneShowUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\ToastUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\TopUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\WheelSpinnerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\PlaneCombineResultUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\PlaneEquipInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\PlaneTypes.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\PlaneUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\shop\ShopUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\skyisland\SkyIslandUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\story\BuidingUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\story\BuildingInfoUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\talent\TalentUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\story\StoryUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\task\TaskUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\button
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\dropdown
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\list
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\SelectList
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\dialogue\DialogueUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\fight\RogueUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\fight\RogueSelectIcon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\friend\FriendAddUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\friend\FriendCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\friend\FriendStrangerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\friend\FriendListUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\mail\MailCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\friend\FriendUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\mail\MailUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\pk\PKHistoryCellUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\pk\PKHistoryUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\pk\PKRewardIcon.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\home\pk\PKUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\back_pack
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\display
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\button\ButtonPlus.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\list\List.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\button\DragButton.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\dropdown\DropDown.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\list\ListItem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\SelectList\uiSelect.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\common\components\SelectList\uiSelectItem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\back_pack\BagItem.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\back_pack\BagGrid.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\back_pack\SortTypeDropdown.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\back_pack\Tabs.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\display\CombineDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui\plane\components\display\EquipDisplay.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_task\prefab\ui\TaskUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\Bundle.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_shop
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_skyisland
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_story
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\editor\level
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_talent
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ColliderTest.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scenes\Main.scene
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_plane\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_skyisland\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_shop\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_story\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home_talent\prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorBaseUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\LevelEditorLayerUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\editor\level\utils.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\randTerrain
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\scenes
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\leveldata\leveldata.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ResUpdate\ResUpdate.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\DevLoginUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\game
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\UIMgr.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\CommonEntry.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\randTerrain
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\2.json
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\randTerrain\RandTerrain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\Game\scenes\GameMain.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\scripts\ui\game\MBoomUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\pk
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\event\DataEvent.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui\GmButtonUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\gm\script\ui\GmUI.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\HomeUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\home\prefab\ui\TopUI.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\resources\Game\level\randTerrain\randomTer_1.prefab
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\equip\EquipSlots.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: %cImport%c: E:\M2Game\Client\assets\bundles\common\script\data\pk\PK.ts
background: #aaff85; color: #000;
color: #000;
2025-9-10 11:15:48-debug: refresh db assets success
2025-9-10 11:15:48-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-10 11:15:48-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-10 11:15:48-debug: asset-db:refresh-all-database (233ms)
2025-9-10 11:15:48-debug: asset-db:worker-effect-data-processing (1ms)
2025-9-10 11:15:48-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-10 11:15:54-debug: refresh db internal success
2025-9-10 11:15:54-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-10 11:15:54-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-10 11:15:54-debug: refresh db assets success
2025-9-10 11:15:54-debug: asset-db:refresh-all-database (112ms)
2025-9-10 11:15:54-debug: asset-db-hook-engine-extends-afterRefresh (1ms)
2025-9-10 11:45:28-debug: refresh db internal success
2025-9-10 11:45:28-debug: refresh db assets success
2025-9-10 11:45:28-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-10 11:45:28-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-10 11:45:28-debug: asset-db:refresh-all-database (116ms)
2025-9-10 11:48:29-debug: refresh db internal success
2025-9-10 11:48:29-debug: refresh db assets success
2025-9-10 11:48:29-debug: Run asset db hook engine-extends:afterRefresh ...
2025-9-10 11:48:29-debug: Run asset db hook engine-extends:afterRefresh success!
2025-9-10 11:48:29-debug: asset-db:refresh-all-database (112ms)
