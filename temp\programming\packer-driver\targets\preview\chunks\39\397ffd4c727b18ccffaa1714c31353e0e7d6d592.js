System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4", "__unresolved_5", "__unresolved_6", "__unresolved_7", "__unresolved_8"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, _decorator, BundleName, BottomTab, BaseUI, UILayer, UIMgr, BagGrid, SortTypeDropdown, Tabs, CombineDisplay, EquipDisplay, _dec, _dec2, _dec3, _dec4, _dec5, _dec6, _class, _class2, _descriptor, _descriptor2, _descriptor3, _descriptor4, _descriptor5, _crd, ccclass, property, PlaneUI;

  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

  function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

  function _initializerDefineProperty(target, property, descriptor, context) { if (!descriptor) return; Object.defineProperty(target, property, { enumerable: descriptor.enumerable, configurable: descriptor.configurable, writable: descriptor.writable, value: descriptor.initializer ? descriptor.initializer.call(context) : void 0 }); }

  function _applyDecoratedDescriptor(target, property, decorators, descriptor, context) { var desc = {}; Object.keys(descriptor).forEach(function (key) { desc[key] = descriptor[key]; }); desc.enumerable = !!desc.enumerable; desc.configurable = !!desc.configurable; if ('value' in desc || desc.initializer) { desc.writable = true; } desc = decorators.slice().reverse().reduce(function (desc, decorator) { return decorator(target, property, desc) || desc; }, desc); if (context && desc.initializer !== void 0) { desc.value = desc.initializer ? desc.initializer.call(context) : void 0; desc.initializer = undefined; } if (desc.initializer === void 0) { Object.defineProperty(target, property, desc); desc = null; } return desc; }

  function _initializerWarningHelper(descriptor, context) { throw new Error('Decorating class property failed. Please ensure that ' + 'transform-class-properties is enabled and runs after the decorators transform.'); }

  function _reportPossibleCrUseOfBundleName(extras) {
    _reporterNs.report("BundleName", "db://assets/bundles/Bundle", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBottomTab(extras) {
    _reporterNs.report("BottomTab", "db://assets/bundles/common/script/ui/home/<USER>", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseUI(extras) {
    _reporterNs.report("BaseUI", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUILayer(extras) {
    _reporterNs.report("UILayer", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfUIMgr(extras) {
    _reporterNs.report("UIMgr", "db://assets/scripts/ui/UIMgr", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBagGrid(extras) {
    _reporterNs.report("BagGrid", "./components/back_pack/BagGrid", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSortTypeDropdown(extras) {
    _reporterNs.report("SortTypeDropdown", "./components/back_pack/SortTypeDropdown", _context.meta, extras);
  }

  function _reportPossibleCrUseOfTabs(extras) {
    _reporterNs.report("Tabs", "./components/back_pack/Tabs", _context.meta, extras);
  }

  function _reportPossibleCrUseOfCombineDisplay(extras) {
    _reporterNs.report("CombineDisplay", "./components/display/CombineDisplay", _context.meta, extras);
  }

  function _reportPossibleCrUseOfEquipDisplay(extras) {
    _reporterNs.report("EquipDisplay", "./components/display/EquipDisplay", _context.meta, extras);
  }

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      _decorator = _cc._decorator;
    }, function (_unresolved_2) {
      BundleName = _unresolved_2.BundleName;
    }, function (_unresolved_3) {
      BottomTab = _unresolved_3.BottomTab;
    }, function (_unresolved_4) {
      BaseUI = _unresolved_4.BaseUI;
      UILayer = _unresolved_4.UILayer;
      UIMgr = _unresolved_4.UIMgr;
    }, function (_unresolved_5) {
      BagGrid = _unresolved_5.BagGrid;
    }, function (_unresolved_6) {
      SortTypeDropdown = _unresolved_6.SortTypeDropdown;
    }, function (_unresolved_7) {
      Tabs = _unresolved_7.Tabs;
    }, function (_unresolved_8) {
      CombineDisplay = _unresolved_8.CombineDisplay;
    }, function (_unresolved_9) {
      EquipDisplay = _unresolved_9.EquipDisplay;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "cbb0bBZgydFdKEMC0GFaZBz", "PlaneUI", undefined);

      __checkObsolete__(['_decorator']);

      ({
        ccclass,
        property
      } = _decorator);

      _export("PlaneUI", PlaneUI = (_dec = ccclass('PlaneUI'), _dec2 = property(_crd && BagGrid === void 0 ? (_reportPossibleCrUseOfBagGrid({
        error: Error()
      }), BagGrid) : BagGrid), _dec3 = property(_crd && Tabs === void 0 ? (_reportPossibleCrUseOfTabs({
        error: Error()
      }), Tabs) : Tabs), _dec4 = property(_crd && SortTypeDropdown === void 0 ? (_reportPossibleCrUseOfSortTypeDropdown({
        error: Error()
      }), SortTypeDropdown) : SortTypeDropdown), _dec5 = property(_crd && EquipDisplay === void 0 ? (_reportPossibleCrUseOfEquipDisplay({
        error: Error()
      }), EquipDisplay) : EquipDisplay), _dec6 = property(_crd && CombineDisplay === void 0 ? (_reportPossibleCrUseOfCombineDisplay({
        error: Error()
      }), CombineDisplay) : CombineDisplay), _dec(_class = (_class2 = class PlaneUI extends (_crd && BaseUI === void 0 ? (_reportPossibleCrUseOfBaseUI({
        error: Error()
      }), BaseUI) : BaseUI) {
        constructor() {
          super(...arguments);

          /**背包区域组件 */
          _initializerDefineProperty(this, "bagGrid", _descriptor, this);

          //背包格子
          _initializerDefineProperty(this, "tabs", _descriptor2, this);

          //标签页
          _initializerDefineProperty(this, "sortTypeDropDown", _descriptor3, this);

          //排序下拉列表

          /**展示区域组件 */
          _initializerDefineProperty(this, "equipDisplay", _descriptor4, this);

          //装备展示区域
          _initializerDefineProperty(this, "combineDisplay", _descriptor5, this);
        }

        static getUrl() {
          return "prefab/ui/PlaneUI";
        }

        static getLayer() {
          return (_crd && UILayer === void 0 ? (_reportPossibleCrUseOfUILayer({
            error: Error()
          }), UILayer) : UILayer).Background;
        }

        static getBundleName() {
          return (_crd && BundleName === void 0 ? (_reportPossibleCrUseOfBundleName({
            error: Error()
          }), BundleName) : BundleName).HomePlane;
        }

        //合成展示区域
        onLoad() {}

        onShow() {
          var _this = this;

          return _asyncToGenerator(function* () {
            _this.tabs.init();
          })();
        }

        onHide() {
          return _asyncToGenerator(function* () {})();
        }

        onClose() {
          return _asyncToGenerator(function* () {})();
        }

        onTabOpen(tab) {
          return _asyncToGenerator(function* () {
            if (tab == (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).Plane) {
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).openUI(PlaneUI);
            }
          })();
        }

        onTabHide(tab) {
          return _asyncToGenerator(function* () {
            if (tab == (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).Plane) {
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).hideUI(PlaneUI);
            }
          })();
        }

        onTabClose(tab) {
          return _asyncToGenerator(function* () {
            if (tab == (_crd && BottomTab === void 0 ? (_reportPossibleCrUseOfBottomTab({
              error: Error()
            }), BottomTab) : BottomTab).Plane) {
              (_crd && UIMgr === void 0 ? (_reportPossibleCrUseOfUIMgr({
                error: Error()
              }), UIMgr) : UIMgr).closeUI(PlaneUI);
            }
          })();
        }

        update(dt) {}

      }, (_descriptor = _applyDecoratedDescriptor(_class2.prototype, "bagGrid", [_dec2], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor2 = _applyDecoratedDescriptor(_class2.prototype, "tabs", [_dec3], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor3 = _applyDecoratedDescriptor(_class2.prototype, "sortTypeDropDown", [_dec4], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor4 = _applyDecoratedDescriptor(_class2.prototype, "equipDisplay", [_dec5], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      }), _descriptor5 = _applyDecoratedDescriptor(_class2.prototype, "combineDisplay", [_dec6], {
        configurable: true,
        enumerable: true,
        writable: true,
        initializer: function initializer() {
          return null;
        }
      })), _class2)) || _class));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=397ffd4c727b18ccffaa1714c31353e0e7d6d592.js.map