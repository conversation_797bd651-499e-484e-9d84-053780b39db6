System.register(["__unresolved_0", "cc", "__unresolved_1", "__unresolved_2", "__unresolved_3", "__unresolved_4"], function (_export, _context) {
  "use strict";

  var _reporterNs, _cclegacy, __checkObsolete__, __checkObsoleteInNamespace__, logInfo, logWarn, BaseComp, MyApp, SkillComp, randomRange, <PERSON><PERSON>, <PERSON><PERSON><PERSON>omp, _crd;

  function _reportPossibleCrUseOflogInfo(extras) {
    _reporterNs.report("logInfo", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOflogWarn(extras) {
    _reporterNs.report("logWarn", "db://assets/scripts/Utils/Logger", _context.meta, extras);
  }

  function _reportPossibleCrUseOfBaseComp(extras) {
    _reporterNs.report("BaseComp", "../../base/BaseComp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfMyApp(extras) {
    _reporterNs.report("MyApp", "db://assets/scripts/MyApp", _context.meta, extras);
  }

  function _reportPossibleCrUseOfbuffer(extras) {
    _reporterNs.report("buffer", "db://assets/scripts/AutoGen/Luban/schema", _context.meta, extras);
  }

  function _reportPossibleCrUseOfPlaneBase(extras) {
    _reporterNs.report("PlaneBase", "../PlaneBase", _context.meta, extras);
  }

  function _reportPossibleCrUseOfSkillComp(extras) {
    _reporterNs.report("SkillComp", "./SkillComp", _context.meta, extras);
  }

  _export({
    Buff: void 0,
    default: void 0
  });

  return {
    setters: [function (_unresolved_) {
      _reporterNs = _unresolved_;
    }, function (_cc) {
      _cclegacy = _cc.cclegacy;
      __checkObsolete__ = _cc.__checkObsolete__;
      __checkObsoleteInNamespace__ = _cc.__checkObsoleteInNamespace__;
      randomRange = _cc.randomRange;
    }, function (_unresolved_2) {
      logInfo = _unresolved_2.logInfo;
      logWarn = _unresolved_2.logWarn;
    }, function (_unresolved_3) {
      BaseComp = _unresolved_3.default;
    }, function (_unresolved_4) {
      MyApp = _unresolved_4.MyApp;
    }, function (_unresolved_5) {
      SkillComp = _unresolved_5.default;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "43149/j3FZB+pqbrElL7T/l", "BuffComp", undefined);

      __checkObsolete__(['random', 'randomRange']);

      _export("Buff", Buff = class Buff {
        constructor(data) {
          this.id = void 0;
          this.res = void 0;
          this.time = 0;
          this.duration = 0;
          this.cycleTimes = 0;
          this.id = Buff.incID++;
          this.res = data;
          this.duration = this.res.duration == -1 ? -1 : this.res.duration + randomRange(0, this.res.durationBonus);
        }

      });

      Buff.incID = 1;

      _export("default", BuffComp = class BuffComp extends (_crd && BaseComp === void 0 ? (_reportPossibleCrUseOfBaseComp({
        error: Error()
      }), BaseComp) : BaseComp) {
        constructor() {
          super(...arguments);
          this.buffs = new Map();
        }

        ApplyBuff(buffID) {
          (_crd && logInfo === void 0 ? (_reportPossibleCrUseOflogInfo({
            error: Error()
          }), logInfo) : logInfo)("Buff", "apply buff " + buffID);
          var buffData = (_crd && MyApp === void 0 ? (_reportPossibleCrUseOfMyApp({
            error: Error()
          }), MyApp) : MyApp).lubanTables.Tbbuffer.get(buffID);

          if (!buffData) {
            (_crd && logWarn === void 0 ? (_reportPossibleCrUseOflogWarn({
              error: Error()
            }), logWarn) : logWarn)("Buff", "apply buff " + buffID + " but config not found");
            return;
          }

          var buff;

          if (buffData.duration != 0) {
            buff = new Buff(buffData);
            var buffs = this.buffs.get(buffID);

            if (!buffs) {
              buffs = [];
              this.buffs.set(buffID, buffs);
            }

            var stack = buffData.maxStack < 1 ? 1 : buffData.maxStack;

            if (buffData.refreshType && buff.res.duration != -1) {
              buffs.forEach(b => {
                b.duration = b.time + buff.duration;
              });
            }

            buffs.push(buff);

            while (buffs.length > stack) {
              this.removeBuff(buffs[0], buffs, 0, buffID);
            }
          }

          buffData.effects.forEach(applyEffect => {
            (_crd && SkillComp === void 0 ? (_reportPossibleCrUseOfSkillComp({
              error: Error()
            }), SkillComp) : SkillComp).forEachByTargetType(this.entity, applyEffect.target, entity => {
              entity.ApplyBuffEffect(buff, applyEffect);
            });
          });
        }

        update(dt) {
          this.buffs.forEach((buffs, buffID) => {
            buffs.forEach((buff, index) => {
              buff.time += dt * 1000;

              if (buff.res.cycle > 0 && buff.time >= (buff.cycleTimes + 1) * buff.res.cycle && (buff.res.cycleTimes == 0 || buff.cycleTimes < buff.res.cycleTimes)) {
                buff.cycleTimes++;
                buff.res.effects.forEach(applyEffect => {
                  (_crd && SkillComp === void 0 ? (_reportPossibleCrUseOfSkillComp({
                    error: Error()
                  }), SkillComp) : SkillComp).forEachByTargetType(this.entity, applyEffect.target, entity => {
                    entity.ApplyBuffEffect(buff, applyEffect);
                  });
                });
              }

              if (buff.duration != -1 && buff.time >= buff.duration) {
                this.removeBuff(buff, buffs, index, buffID);
              }
            });
          });
        }

        removeBuff(buff, buffs, index, buffID) {
          buff.res.effects.forEach(applyEffect => {
            // 这个地方和加的时候查出来的target会不同
            // 1. 需要保证查出来的target只多不少
            // 2. remove接口里面需要判断时候是这个buff的效果
            (_crd && SkillComp === void 0 ? (_reportPossibleCrUseOfSkillComp({
              error: Error()
            }), SkillComp) : SkillComp).forEachByTargetType(this.entity, applyEffect.target, entity => {
              entity.RemoveBuffEffect(buff, applyEffect);
            });
          });
          buffs.splice(index, 1);

          if (buffs.length === 0) {
            this.buffs.delete(buffID);
          }
        }

      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=fd53a9c427ded64fb4ec9d7ed0758892a251069c.js.map