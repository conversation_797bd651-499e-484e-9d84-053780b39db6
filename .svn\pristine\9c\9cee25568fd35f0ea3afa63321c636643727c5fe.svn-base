import { _decorator, director } from 'cc';

import { GameIns } from 'db://assets/scripts/Game/GameIns';
import { MyApp } from '../../../../../scripts/MyApp';
import { LoadingUI } from '../../../../../scripts/ui/LoadingUI';
import { BaseUI, UILayer, UIMgr } from '../../../../../scripts/ui/UIMgr';
import { BundleName } from '../../../../Bundle';
import { DataMgr } from '../../data/DataManager';
import { EventMgr } from '../../event/EventManager';
import { HomeUIEvent } from '../../event/HomeUIEvent';
import { ButtonPlus } from '../common/components/button/ButtonPlus';
import { StoryUI } from '../story/StoryUI';

const { ccclass, property } = _decorator;

@ccclass('HomeUI')
export class HomeUI extends BaseUI {
    public static getUrl(): string { return "prefab/ui/HomeUI"; }
    public static getLayer(): UILayer { return UILayer.Background }
    public static getBundleName(): string { return BundleName.Home }
    @property(ButtonPlus)
    btnBattle: ButtonPlus | null = null;
    @property(ButtonPlus)
    btnStory: ButtonPlus | null = null;

    protected onLoad(): void {
        this.btnBattle!.addClick(this.onBattleClick, this);
        this.btnStory!.addClick(this.onStoryClick, this);
    }

    getLocalIP(): string | null {
        const interfaces = require('os').networkInterfaces();
        for (const name of Object.keys(interfaces)) {
            for (const iface of interfaces[name]) {
                if (iface.family === 'IPv4' && !iface.internal) {
                    return iface.address;
                }
            }
        }
        return null;
    }

    async onShow(...args: any[]): Promise<void> {
    }
    async onHide(...args: any[]): Promise<void> {
    }
    async onClose(...args: any[]): Promise<void> {
        EventMgr.targetOff(this)
    }
    protected onDestroy(): void {
        this.unscheduleAllCallbacks();
    }
    protected update(dt: number): void {
    }

    async onBattleClick() {
        MyApp.globalMgr.chapterID = 0;
        this.onBattle();
    }

    async onBattle() {
        await UIMgr.openUI(LoadingUI)
        EventMgr.emit(HomeUIEvent.Leave)
        director.preloadScene("Game", async () => {
            let planeData = DataMgr.planeInfo.getPlaneInfoById();
            GameIns.battleManager.setBattleInfo(1, 1, planeData);
            director.loadScene("Game")
        })
    }

    async onStoryClick() {
        await UIMgr.openUI(StoryUI);
        //EventMgr.emit(HomeUIEvent.Leave)
    }
}

