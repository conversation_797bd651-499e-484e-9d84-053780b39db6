System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, HomeUIEvent;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "7fd27qBOeZG2ajarNJ+WyOR", "HomeUIEvent", undefined);

      _export("HomeUIEvent", HomeUIEvent = {
        BottomTabRegister: "HomeUI_Register",
        Leave: "HomeUI_Leave"
      });

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=76fa0be13354db51a2601305aede5e7accf2ef16.js.map