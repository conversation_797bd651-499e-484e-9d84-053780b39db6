{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/story/BuidingUI.ts"], "names": ["_decorator", "Component", "Label", "Sprite", "MyApp", "DataEvent", "EventMgr", "ButtonPlus", "ccclass", "property", "BuidingUI", "index", "onLoad", "getComponent", "addClick", "onClick", "onDestroy", "emit", "BattleItemClick", "title", "string", "setNewFrame", "imageUrl", "resMgr", "loadIcon", "sprt", "setTitle", "onRenderItem", "item"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;AAAWC,MAAAA,K,OAAAA,K;AAAOC,MAAAA,M,OAAAA,M;;AAC9BC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,U,iBAAAA,U;;;;;;;;;OAEH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBT,U,GAE9B;;2BAEaU,S,WADZF,OAAO,CAAC,WAAD,C,UAGHC,QAAQ,CAACP,KAAD,C,UAGRO,QAAQ,CAACN,MAAD,C,2BANb,MACaO,SADb,SAC+BT,SAD/B,CACyC;AAAA;AAAA;;AAAA;;AAAA;;AAAA,eAQ7BU,KAR6B,GAQhB,CARgB;AAAA;;AAU3BC,QAAAA,MAAM,GAAS;AACrB,eAAKC,YAAL;AAAA;AAAA,wCAA+BC,QAA/B,CAAwC,KAAKC,OAA7C,EAAsD,IAAtD;AACH;;AAESC,QAAAA,SAAS,GAAS,CAE3B;;AAEOD,QAAAA,OAAO,GAAG;AACd;AAAA;AAAA,oCAASE,IAAT,CAAc;AAAA;AAAA,sCAAUC,eAAxB,EAAyC,KAAKP,KAA9C,EAAqD,KAAKQ,KAAL,CAAYC,MAAjE;AACH;;AAEMC,QAAAA,WAAW,CAACC,QAAD,EAAmB;AACjC;AAAA;AAAA,8BAAMC,MAAN,CAAaC,QAAb,CAAsBF,QAAtB,EAAgC,KAAKG,IAArC;AACH;;AAEMC,QAAAA,QAAQ,CAACf,KAAD,EAAaQ,KAAb,EAA4B;AACvC,eAAKR,KAAL,GAAaA,KAAb;AACA,eAAKQ,KAAL,CAAYC,MAAZ,GAAqBD,KAArB;AAEH,SA9BoC,CAgCrC;;;AACAQ,QAAAA,YAAY,CAACC,IAAD,EAAyB,CAEpC;;AAnCoC,O;;;;;iBAGf,I;;;;;;;iBAGA,I", "sourcesContent": ["import { _decorator, Component, Label, Sprite } from \"cc\";\r\nimport { MyApp } from \"db://assets/scripts/MyApp\";\r\nimport { DataEvent } from \"../../event/DataEvent\";\r\nimport { EventMgr } from \"../../event/EventManager\";\r\nimport { ButtonPlus } from \"../common/components/button/ButtonPlus\";\r\n\r\nconst { ccclass, property } = _decorator;\r\n\r\n//参考 BagItem\r\n@ccclass('BuidingUI')\r\nexport class BuidingUI extends Component {\r\n\r\n    @property(Label)\r\n    title: Label | null = null;\r\n\r\n    @property(Sprite)\r\n    sprt: Sprite | null = null;\r\n\r\n    private index: any = 0;\r\n\r\n    protected onLoad(): void {\r\n        this.getComponent(ButtonPlus)!.addClick(this.onClick, this)\r\n    }\r\n\r\n    protected onDestroy(): void {\r\n\r\n    }\r\n\r\n    private onClick() {\r\n        EventMgr.emit(DataEvent.BattleItemClick, this.index, this.title!.string);\r\n    }\r\n\r\n    public setNewFrame(imageUrl: string) {\r\n        MyApp.resMgr.loadIcon(imageUrl, this.sprt!);\r\n    }\r\n\r\n    public setTitle(index: any, title: string) {\r\n        this.index = index;\r\n        this.title!.string = title;\r\n\r\n    }\r\n\r\n    //cell的容器去手动调用\r\n    onRenderItem(item: { name: string }) {\r\n\r\n    }\r\n}"]}