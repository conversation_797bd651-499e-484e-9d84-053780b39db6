import { _decorator, Node, Sprite, Animation, Label, Vec2, tween, UIOpacity, instantiate, sp, UITransform, Tween, Color, view, v3, v2, SpriteFrame, size, SpriteAtlas, Prefab } from "cc";
import { GameConst } from "../../../const/GameConst";
import { GameIns } from "../../../GameIns";
import Bullet from "../../bullet/Bullet";
import EffectLayer from "../../layer/EffectLayer";
import FBoxCollider from "../../../collider-system/FBoxCollider";
import FCollider, { ColliderGroupType } from "../../../collider-system/FCollider";
import { MyApp } from "db://assets/scripts/MyApp";
import { PlaneData } from "db://assets/bundles/common/script/data/plane/PlaneData";
import { Plane } from "db://assets/bundles/common/script/ui/Plane";
import { AttributeConst } from "db://assets/bundles/common/script/const/AttributeConst";
import { AttributeData } from "db://assets/bundles/common/script/data/base/AttributeData";
import GameResourceList from "../../../const/GameResourceList";
import { Emitter } from "../../../bullet/Emitter";
import PlaneBase from "../PlaneBase";

const { ccclass, property } = _decorator;


@ccclass("MainPlane")
export class MainPlane extends PlaneBase {

    @property(Node)
    planeParent: Node | null = null;
    @property(Node)
    NodeEmitter: Node | null = null;
    @property(Node)
    hpNode: Node | null = null;

    hpMidActin: Tween | null = null; // 血条动画
    m_screenDatas: number[][] = []; // 屏幕数据
    m_moveEnable = true; // 是否允许移动
    emitterComp: Emitter | null = null; // 发射器

    _hurtActTime = 0; // 受伤动画时间
    _hurtActDuration = 0.5; // 受伤动画持续时间

    _planeData:PlaneData | null = null;//飞机数据
    _plane:Plane | null = null;//飞机显示节点


    onLoad() {
        this.collideComp = this.getComponent(FBoxCollider) || this.addComponent(FBoxCollider);
        this.collideComp!.init(this, size(40, 40)); // 初始化碰撞组件
        this.collideComp!.groupType = ColliderGroupType.PLAYER;
        this.colliderEnabled = false;
    }

    start() {
        // 禁用射击
        this.setFireEnable(false);
    }

    update(dt: number) {
        this._hurtActTime += dt;
    }

    initPlane(planeData:PlaneData) {
        this._planeData = planeData;

        //加载飞机显示
        let plane = MyApp.planeMgr.getPlane(planeData);
        this._plane = plane.getComponent(Plane);
        this.planeParent?.addChild(plane);

        //设置飞机发射组件
        this.setEmitter();

        this.curHp = this._planeData?.getFinalAttributeByKey(AttributeConst.MaxHP);;
        this.maxHp = this.curHp;
    }

    setEmitter(){
        //后期根据飞机的数据，加载不同的发送组件预制体
        let path = GameResourceList.EmitterPrefabPath + "Emitter_main_01";
        MyApp.resMgr.loadAsync(path, Prefab).then((prefab) => {
            let node = instantiate(prefab);
            this.NodeEmitter?.addChild(node);
            node.setPosition(0,0);

            this.emitterComp = node.getComponent(Emitter);
        });
    }

    initBattle() {
        this.node.active = true;
        this.colliderEnabled = false;
        this.updateHpUI();
    }

    /**
 * 主飞机入场动画
 */
    planeIn(): void {
        const self = this;

        // 设置初始位置和状态
        this.planeParent!.setScale(1, 1);
        let posY = -view.getVisibleSize().height - 80;
        this.node.setPosition(0, posY)
        this.setFireEnable(false);
        this.setMoveAble(false)
        Tween.stopAllByTarget(this.node)

        let frame = GameConst.ActionFrameTime / GameIns.battleManager.animSpeed;
        // 飞机入场动画
        this.scheduleOnce(() => {
            const targetY = -view.getVisibleSize().height * 0.7;
            const targetX = this.node.position.x;
            tween(this.node)
                .to(20 * frame, { position: v3(targetX, targetY - 17) })
                .to(11 * frame, { position: v3(targetX, targetY + 57) })
                .to(10 * frame, { position: v3(targetX, targetY + 76) })
                .to(27 * frame, { position: v3(targetX, targetY) })
                .call(() => {
                    self.begine();
                })
                .start();

            tween(this.planeParent!)
                .to(20 * frame, { scale: v3(1.9, 1.9) })
                .to(11 * frame, { scale: v3(1.4, 1.4) })
                .to(10 * frame, { scale: v3(1, 1) })
                .to(27 * frame, { scale: v3(1, 1) })
                .start();

            if (this.hpNode) {
                tween(this.hpNode!.getComponent(UIOpacity)!)
                    .to(0, { opacity: 0 })
                    .delay(31 * frame)
                    .to(10 * frame, { opacity: 255 })
                    .start();
            }
        }, 7 * frame);
    }

    /**
     * 退出战斗
     */
    battleQuit() {
        
    }

    /**
     * 碰撞处理
     * @param {Object} collision 碰撞对象
     */
    onCollide(collision: FCollider) {

        let damage = 0;
        if (collision.entity instanceof Bullet) {
            damage = collision.entity.getAttack();
        }

        if (damage > 0) {
            this.hurt(damage)
        }
    }

    /**
     * 控制飞机移动
     * @param {number} moveX 水平方向的移动量
     * @param {number} moveY 垂直方向的移动量
     */
    onControl(posX: number, posY: number) {
        if (!GameIns.mainPlaneManager.planeFightData.die && this.m_moveEnable) {
            // 限制飞机移动范围
            posX = Math.min(360, posX);
            posX = Math.max(-360, posX);
            posY = Math.min(0, posY);
            posY = Math.max(-GameConst.ViewHeight, posY);
            this.node.setPosition(posX, posY);
        }
    }


    relife() {
        GameIns.gameDataManager.reviveCount += 1; // 增加复活次数
        // this.playRelifeAim(); // 播放复活动画
        GameIns.mainPlaneManager.planeFightData!.die = false; // 设置飞机为非死亡状态
        GameIns.mainPlaneManager.planeFightData!.revive = true; // 设置复活状态
        this.scheduleOnce(() => {
            GameIns.mainPlaneManager.planeFightData!.revive = false;
        }, 0.5);

        this.curHp = this.maxHp; // 恢复满血
        this.updateHpUI();; // 触发血量更新事件
    }

    /**
     * 获取攻击力
     * @returns {number} 当前攻击力
     */
    getAttack() {
        return this._planeData?.getFinalAttributeByKey(AttributeConst.Attack);
    }

    toDie(): boolean {
        if (!super.toDie()) {
            return false;
        }

        // 设置玩家状态为死亡
        GameIns.mainPlaneManager.planeFightData!.die = true;

        // 播放死亡动画
        this._playDieAnim();
        return true;
    }

    //实现父类的方法
    playHurtAnim() {
        if (this._hurtActTime > this._hurtActDuration) {
            this._hurtActTime = 0;
            // 显示红屏效果
            EffectLayer.me.showRedScreen();
        }
    }

    /**
     * 播放死亡动画
     */
    _playDieAnim() {
        // this.blast!.node!.getComponent(UIOpacity)!.opacity = 255; // 显示爆炸效果
        // this.blast!.setCompleteListener(this._dieAnimEnd.bind(this)); // 设置动画完成回调
        // this.blast!.setAnimation(0, "play", false); // 播放爆炸动画
        GameIns.battleManager.battleFail();
    }

    /**
     * 设置飞机是否可移动
     * @param {boolean} enable 是否可移动
     */
    setMoveAble(enable: boolean) {
        this.m_moveEnable = enable;
    }

    setFireEnable(enable: boolean) {
        // for (let i = 0; i < this.m_fires.length; i++) {
        //     const fire = this.m_fires[i];
        //     fire.isEnabled = enable;
        // }
    }

    /**
     * 开始战斗
     * @param {boolean} isContinue 是否继续战斗
     */
    begine(isContinue = false) {
        if (isContinue) {
            this.setFireEnable(true);
            this.setMoveAble(true);
            this.colliderEnabled = true;
        } else {
            GameIns.battleManager.onPlaneIn();
        }
    }

    get attribute(): AttributeData {
        return this._planeData!;
    }
}