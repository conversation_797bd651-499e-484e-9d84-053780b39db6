{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/bundles/common/script/ui/home/<USER>/FriendAddUI.ts"], "names": ["_decorator", "Component", "List", "FriendCellUI", "ccclass", "property", "FriendAddUI", "start", "list", "node", "active", "numItems", "update", "deltaTime", "onList<PERSON>ender", "listItem", "row", "cell", "getComponent", "setType", "txtName", "string"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,S,OAAAA,S;;AACdC,MAAAA,I;;AACEC,MAAAA,Y,iBAAAA,Y;;;;;;;;;OACH;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBL,U;;6BAGjBM,W,WADZF,OAAO,CAAC,aAAD,C,UAEHC,QAAQ;AAAA;AAAA,uB,2BAFb,MACaC,WADb,SACiCL,SADjC,CAC2C;AAAA;AAAA;;AAAA;AAAA;;AAGvCM,QAAAA,KAAK,GAAG;AACJ,eAAKC,IAAL,CAAWC,IAAX,CAAgBC,MAAhB,GAAyB,IAAzB;AACA,eAAKF,IAAL,CAAWG,QAAX,GAAsB,EAAtB;AACH;;AAEDC,QAAAA,MAAM,CAACC,SAAD,EAAoB,CAEzB;;AACDC,QAAAA,YAAY,CAACC,QAAD,EAAiBC,GAAjB,EAA8B;AACtC,cAAMC,IAAI,GAAGF,QAAQ,CAACG,YAAT;AAAA;AAAA,2CAAb;;AACA,cAAID,IAAI,KAAK,IAAb,EAAmB;AACfA,YAAAA,IAAI,CAACE,OAAL,CAAa,CAAb;AACAF,YAAAA,IAAI,CAACG,OAAL,CAAcC,MAAd,GAAuB,UAAUL,GAAG,GAAG,EAAhB,CAAvB;AACH;AACJ;;AAjBsC,O;;;;;iBAEnB,I", "sourcesContent": ["import { _decorator, Component, Node } from 'cc';\r\nimport List from '../../common/components/list/List';\r\nimport { FriendCellUI } from './FriendCellUI';\r\nconst { ccclass, property } = _decorator;\r\n\r\n@ccclass('FriendAddUI')\r\nexport class FriendAddUI extends Component {\r\n    @property(List)\r\n    list: List | null = null;\r\n    start() {\r\n        this.list!.node.active = true;\r\n        this.list!.numItems = 10;\r\n    }\r\n\r\n    update(deltaTime: number) {\r\n\r\n    }\r\n    onListRender(listItem: Node, row: number) {\r\n        const cell = listItem.getComponent(FriendCellUI);\r\n        if (cell !== null) {\r\n            cell.setType(2);\r\n            cell.txtName!.string = \"小师妹：\" + (row + 11);\r\n        }\r\n    }\r\n}\r\n\r\n\r\n"]}