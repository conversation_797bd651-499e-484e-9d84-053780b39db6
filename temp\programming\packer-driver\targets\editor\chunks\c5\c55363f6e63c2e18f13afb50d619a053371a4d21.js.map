{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/Game/manager/BattleManager.ts"], "names": ["BattleManager", "SingletonBase", "GameEnum", "GameIns", "GameMapRun", "UIMgr", "LoadingUI", "BulletSystem", "_percent", "gameType", "GameType", "Common", "initBattleEnd", "gameStart", "animSpeed", "_gameTime", "mainStage", "subStage", "_loadFinish", "_loadTotal", "_loadCount", "setBattleInfo", "planeData", "mainPlaneManager", "setPlaneData", "mainReset", "enemyManager", "boss<PERSON><PERSON><PERSON>", "waveManager", "reset", "instance", "clear", "bulletManager", "hurtEffectManager", "gameRuleManager", "subReset", "checkLoadFinish", "loadingUI", "get", "updateProgress", "initBattle", "closeUI", "addLoadCount", "count", "startLoading", "gameSortie", "gameResManager", "preload", "preLoad", "initData", "stageManager", "mainPlane", "planeIn", "onPlaneIn", "beginBattle", "begine", "update", "dt", "isGameOver", "gamePlaneManager", "enemyTarget", "isInBattle", "isGameWillOver", "updateGameLogic", "tick", "battleDie", "gamePause", "battleFail", "gameMainUI", "showGameResult", "hpNode", "active", "endBattle", "battleSucc", "checkStage", "startNextBattle", "gameOver", "removeAll", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "bossWillEnter", "bossFightStart", "setFireEnable", "moveAble", "getRatio", "isGameType"], "mappings": ";;;4GAWaA,a;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAVJC,MAAAA,a,iBAAAA,a;;AAEDC,MAAAA,Q,iBAAAA,Q;;AACCC,MAAAA,O,iBAAAA,O;;AACFC,MAAAA,U;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,S,iBAAAA,S;;AAEAC,MAAAA,Y,iBAAAA,Y;;;;;;;+BAEIP,a,GAAN,MAAMA,aAAN;AAAA;AAAA,0CAAyD;AAAA;AAAA;AAAA,eAE5DQ,QAF4D,GAEjD,CAFiD;AAAA,eAG5DC,QAH4D,GAGjD;AAAA;AAAA,oCAASC,QAAT,CAAkBC,MAH+B;AAAA,eAI5DC,aAJ4D,GAI5C,KAJ4C;AAAA,eAK5DC,SAL4D,GAKhD,KALgD;AAAA,eAM5DC,SAN4D,GAMhD,CANgD;AAAA,eAO5DC,SAP4D,GAOhD,CAPgD;AAAA,eAS5DC,SAT4D,GAShD,CATgD;AAAA,eAU5DC,QAV4D,GAUjD,CAViD;AAAA,eAY5DC,WAZ4D,GAY9C,KAZ8C;AAAA,eAa5DC,UAb4D,GAa/C,CAb+C;AAAA,eAc5DC,UAd4D,GAc/C,CAd+C;AAAA;;AAgB5DC,QAAAA,aAAa,CAACL,SAAD,EAAoBC,QAApB,EAAqCK,SAArC,EAA0D;AACnE,eAAKN,SAAL,GAAiBA,SAAjB;AACA,eAAKC,QAAL,GAAgBA,QAAhB;AACA;AAAA;AAAA,kCAAQM,gBAAR,CAAyBC,YAAzB,CAAsCF,SAAtC;AACH;;AAEDG,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQC,YAAR,CAAqBD,SAArB;AACA;AAAA;AAAA,kCAAQE,WAAR,CAAoBF,SAApB;AACA;AAAA;AAAA,kCAAQG,WAAR,CAAoBC,KAApB;AACA;AAAA;AAAA,kCAAQN,gBAAR,CAAyBE,SAAzB;AACA;AAAA;AAAA,wCAAWK,QAAX,CAAqBD,KAArB;AACA;AAAA;AAAA,wCAAWC,QAAX,CAAqBC,KAArB;AACA;AAAA;AAAA,kCAAQC,aAAR,CAAsBD,KAAtB;AACA;AAAA;AAAA,kCAAQE,iBAAR,CAA0BF,KAA1B;AACA;AAAA;AAAA,kCAAQG,eAAR,CAAwBL,KAAxB;AACH;;AAEDM,QAAAA,QAAQ,GAAG;AACP;AAAA;AAAA,kCAAQD,eAAR,CAAwBL,KAAxB;AACA;AAAA;AAAA,kCAAQD,WAAR,CAAoBC,KAApB;AACA;AAAA;AAAA,kCAAQH,YAAR,CAAqBS,QAArB;AACA;AAAA;AAAA,kCAAQR,WAAR,CAAoBQ,QAApB;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,eAAe,GAAG;AACd,eAAKhB,UAAL;AACA,cAAIiB,SAAS,GAAG;AAAA;AAAA,8BAAMC,GAAN;AAAA;AAAA,qCAAhB;AACAD,UAAAA,SAAS,CAACE,cAAV,CAAyB,KAAKnB,UAAL,GAAkB,KAAKD,UAAhD;;AACA,cAAI,KAAKC,UAAL,IAAmB,KAAKD,UAA5B,EAAwC;AACpC,iBAAKqB,UAAL;AACA;AAAA;AAAA,gCAAMC,OAAN;AAAA;AAAA;AACH;AAEJ;;AAEDC,QAAAA,YAAY,CAACC,KAAD,EAAgB;AACxB,eAAKxB,UAAL,IAAmBwB,KAAnB;AACH;;AAEDC,QAAAA,YAAY,GAAG;AACX;AAAA;AAAA,kCAAQV,eAAR,CAAwBW,UAAxB;AACA;AAAA;AAAA,kCAAQC,cAAR,CAAuBC,OAAvB;AACA;AAAA;AAAA,kCAAQxB,gBAAR,CAAyBwB,OAAzB;AACA;AAAA;AAAA,kCAAQf,aAAR,CAAsBgB,OAAtB,CAA8B,KAAKhC,SAAnC,EAJW,CAImC;;AAC9C;AAAA;AAAA,kCAAQiB,iBAAR,CAA0Be,OAA1B,GALW,CAKyB;;AACpC;AAAA;AAAA,wCAAWlB,QAAX,CAAqBmB,QAArB,CAA8B,KAAKjC,SAAnC,EANW,CAMmC;;AAC9C;AAAA;AAAA,kCAAQU,YAAR,CAAqBsB,OAArB,GAPW,CAOoB;;AAC/B;AAAA;AAAA,kCAAQrB,WAAR,CAAoBqB,OAApB,GARW,CAQmB;AACjC;;AAIDR,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQU,YAAR,CAAqBV,UAArB,CAAgC,KAAKxB,SAArC,EAAgD,KAAKC,QAArD;AACA;AAAA;AAAA,kCAAQM,gBAAR,CAAyB4B,SAAzB,CAAoCX,UAApC;AACA;AAAA;AAAA,kCAAQjB,gBAAR,CAAyB4B,SAAzB,CAAoCC,OAApC;AACH;;AAEDC,QAAAA,SAAS,GAAG;AACR,eAAKzC,aAAL,GAAqB,IAArB;AACA,eAAK0C,WAAL;AACH;;AAEDA,QAAAA,WAAW,GAAG;AACV,cAAI,KAAK1C,aAAL,IAAsB,CAAC,KAAKC,SAAhC,EAA2C;AACvC,iBAAKA,SAAL,GAAiB,IAAjB;AAEA;AAAA;AAAA,oCAAQqC,YAAR,CAAqBrC,SAArB;AACA;AAAA;AAAA,oCAAQe,WAAR,CAAoBf,SAApB;AACA;AAAA;AAAA,oCAAQqB,eAAR,CAAwBrB,SAAxB;AAEA;AAAA;AAAA,oCAAQU,gBAAR,CAAyB4B,SAAzB,CAAoCI,MAApC,CAA2C,IAA3C;AACH;AACJ;AAED;AACJ;AACA;AACA;;;AACIC,QAAAA,MAAM,CAACC,EAAD,EAAY;AACd,cAAI;AAAA;AAAA,kCAAQvB,eAAR,CAAwBwB,UAAxB,EAAJ,EAA0C;AACtC,gBAAI;AAAA;AAAA,oCAAQC,gBAAZ,EAA8B;AAC1B;AAAA;AAAA,sCAAQA,gBAAR,CAAyBC,WAAzB,GAAuC,IAAvC;AACH;;AACD;AACH;;AAED,cAAI;AAAA;AAAA,kCAAQ1B,eAAR,CAAwB2B,UAAxB,MAAwC;AAAA;AAAA,kCAAQ3B,eAAR,CAAwB4B,cAAxB,EAA5C,EAAsF;AAClF;AAAA;AAAA,oCAAQH,gBAAR,CAAyBH,MAAzB,CAAgCC,EAAhC;AACA;AAAA;AAAA,oCAAQ7B,WAAR,CAAoBmC,eAApB,CAAoCN,EAApC;AACA;AAAA;AAAA,oCAAQ/B,YAAR,CAAqBqC,eAArB,CAAqCN,EAArC;AACA;AAAA;AAAA,oCAAQ9B,WAAR,CAAoBoC,eAApB,CAAoCN,EAApC;AAEA;AAAA;AAAA,oCAAQvB,eAAR,CAAwB6B,eAAxB,CAAwCN,EAAxC,EANkF,CAQlF;;AACA;AAAA;AAAA,8CAAaO,IAAb,CAAkBP,EAAlB;AAEA,iBAAK1C,SAAL,IAAkB0C,EAAlB;AACH,WAZD,MAYO,IAAI;AAAA;AAAA,kCAAQE,gBAAZ,EAA8B;AACjC;AAAA;AAAA,oCAAQA,gBAAR,CAAyBC,WAAzB,GAAuC,IAAvC;AACH;AACJ;AAED;AACJ;AACA;;;AACmB,cAATK,SAAS,GAAG;AACd;AACA;AAAA;AAAA,kCAAQ/B,eAAR,CAAwBgC,SAAxB;AACH,SAlI2D,CAoI5D;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQC,UAAR,CAAoBC,cAApB,CAAmC,KAAnC;AACA;AAAA;AAAA,kCAAQ9C,gBAAR,CAAyB4B,SAAzB,CAAoCmB,MAApC,CAA4CC,MAA5C,GAAqD,KAArD;AACA,eAAKC,SAAL;AACH;AAED;AACJ;AACA;;;AACIC,QAAAA,UAAU,GAAG;AACT;AAAA;AAAA,kCAAQlD,gBAAR,CAAyB4B,SAAzB,CAAoCmB,MAApC,CAA4CC,MAA5C,GAAqD,KAArD;AACA,eAAKC,SAAL;;AAEA,cAAI;AAAA;AAAA,kCAAQtB,YAAR,CAAqBwB,UAArB,CAAgC,KAAK1D,SAArC,EAA+C,KAAKC,QAAL,GAAgB,CAA/D,CAAJ,EAAsE;AAClE,iBAAK0D,eAAL;AACH,WAFD,MAEK;AACD;AAAA;AAAA,oCAAQP,UAAR,CAAoBC,cAApB,CAAmC,IAAnC;AACH;AACJ;AACG;AACR;AACA;;;AACIM,QAAAA,eAAe,GAAG;AACd,eAAKxC,QAAL;AACA,eAAKlB,QAAL,IAAiB,CAAjB;AACA,eAAKuB,UAAL;AACH;AAED;AACJ;AACA;;;AACIgC,QAAAA,SAAS,GAAG;AACR;AAAA;AAAA,kCAAQtC,eAAR,CAAwB0C,QAAxB;AACA;AAAA;AAAA,kCAAQ5C,aAAR,CAAsB6C,SAAtB,CAAgC,KAAhC,EAAuC,IAAvC;AAEA,eAAKhE,SAAL,GAAiB,KAAjB;AACA,eAAKD,aAAL,GAAqB,KAArB;AACH;AAGD;AACJ;AACA;AACA;;;AACIkE,QAAAA,gBAAgB,CAACC,QAAD,EAAmB,CAC/B;AACA;AACA;AACA;AACA;AACA;AACH;;AAEDC,QAAAA,aAAa,GAAG,CACZ;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACH;AACD;AACJ;AACA;;;AACIC,QAAAA,cAAc,GAAG;AACb;AAAA;AAAA,kCAAQ1D,gBAAR,CAAyB4B,SAAzB,CAAoC+B,aAApC,CAAkD,IAAlD;AACA;AAAA;AAAA,kCAAQ3D,gBAAR,CAAyB4D,QAAzB,GAAoC,IAApC;AAEA;AAAA;AAAA,kCAAQxD,WAAR,CAAoBsD,cAApB;AACH;AAED;AACJ;AACA;AACA;;;AACIG,QAAAA,QAAQ,GAAG;AACP,iBAAO,QAAP,CADO,CACU;AACpB;;AAEDC,QAAAA,UAAU,CAAC5E,QAAD,EAAoB;AAC1B,iBAAO,KAAKA,QAAL,IAAiBA,QAAxB;AACH;;AA7O2D,O", "sourcesContent": ["\r\nimport { SingletonBase } from \"../../core/base/SingletonBase\";\r\nimport { GameConst } from \"../const/GameConst\";\r\nimport {GameEnum} from \"../const/GameEnum\";\r\nimport { GameIns } from \"../GameIns\";\r\nimport GameMapRun from \"../ui/map/GameMapRun\";\r\nimport { UIMgr } from \"../../ui/UIMgr\";\r\nimport { LoadingUI } from \"../../ui/LoadingUI\";\r\nimport { PlaneData } from \"db://assets/bundles/common/script/data/plane/PlaneData\";\r\nimport { BulletSystem } from \"../bullet/BulletSystem\";\r\n\r\nexport class BattleManager extends SingletonBase<BattleManager> {\r\n\r\n    _percent = 0;\r\n    gameType = GameEnum.GameType.Common;\r\n    initBattleEnd = false;\r\n    gameStart = false;\r\n    animSpeed = 1;\r\n    _gameTime = 0;\r\n\r\n    mainStage = 0;\r\n    subStage = 0;\r\n\r\n    _loadFinish = false;\r\n    _loadTotal = 0;\r\n    _loadCount = 0;\r\n\r\n    setBattleInfo(mainStage: number, subStage: number,planeData:PlaneData) {\r\n        this.mainStage = mainStage;\r\n        this.subStage = subStage;\r\n        GameIns.mainPlaneManager.setPlaneData(planeData);\r\n    }\r\n\r\n    mainReset() {\r\n        GameIns.enemyManager.mainReset();\r\n        GameIns.bossManager.mainReset();\r\n        GameIns.waveManager.reset();\r\n        GameIns.mainPlaneManager.mainReset();\r\n        GameMapRun.instance!.reset();\r\n        GameMapRun.instance!.clear();\r\n        GameIns.bulletManager.clear();\r\n        GameIns.hurtEffectManager.clear();\r\n        GameIns.gameRuleManager.reset();\r\n    }\r\n\r\n    subReset() {\r\n        GameIns.gameRuleManager.reset();\r\n        GameIns.waveManager.reset();\r\n        GameIns.enemyManager.subReset();\r\n        GameIns.bossManager.subReset();\r\n    }\r\n\r\n    /**\r\n     * 检查所有资源是否加载完成\r\n     */\r\n    checkLoadFinish() {\r\n        this._loadCount++;\r\n        let loadingUI = UIMgr.get(LoadingUI)\r\n        loadingUI.updateProgress(this._loadCount / this._loadTotal)\r\n        if (this._loadCount >= this._loadTotal) {\r\n            this.initBattle();\r\n            UIMgr.closeUI(LoadingUI)\r\n        }\r\n\r\n    }\r\n\r\n    addLoadCount(count :number) {\r\n        this._loadTotal += count;\r\n    }\r\n\r\n    startLoading() {\r\n        GameIns.gameRuleManager.gameSortie();\r\n        GameIns.gameResManager.preload();\r\n        GameIns.mainPlaneManager.preload();\r\n        GameIns.bulletManager.preLoad(this.mainStage);//子弹资源\r\n        GameIns.hurtEffectManager.preLoad();//伤害特效资源\r\n        GameMapRun.instance!.initData(this.mainStage);//地图背景初始化\r\n        GameIns.enemyManager.preLoad();//敌人资源\r\n        GameIns.bossManager.preLoad();//boss资源\r\n    }\r\n\r\n\r\n\r\n    initBattle() {\r\n        GameIns.stageManager.initBattle(this.mainStage, this.subStage);\r\n        GameIns.mainPlaneManager.mainPlane!.initBattle();\r\n        GameIns.mainPlaneManager.mainPlane!.planeIn();\r\n    }\r\n\r\n    onPlaneIn() {\r\n        this.initBattleEnd = true;\r\n        this.beginBattle();\r\n    }\r\n\r\n    beginBattle() {\r\n        if (this.initBattleEnd && !this.gameStart) {\r\n            this.gameStart = true;\r\n\r\n            GameIns.stageManager.gameStart();\r\n            GameIns.waveManager.gameStart();\r\n            GameIns.gameRuleManager.gameStart();\r\n\r\n            GameIns.mainPlaneManager.mainPlane!.begine(true);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 更新游戏逻辑\r\n     * @param {number} dt 每帧的时间间隔\r\n     */\r\n    update(dt:number) {\r\n        if (GameIns.gameRuleManager.isGameOver()) {\r\n            if (GameIns.gamePlaneManager) {\r\n                GameIns.gamePlaneManager.enemyTarget = null;\r\n            }\r\n            return;\r\n        }\r\n\r\n        if (GameIns.gameRuleManager.isInBattle() || GameIns.gameRuleManager.isGameWillOver()) {\r\n            GameIns.gamePlaneManager.update(dt);\r\n            GameIns.waveManager.updateGameLogic(dt);\r\n            GameIns.enemyManager.updateGameLogic(dt);\r\n            GameIns.bossManager.updateGameLogic(dt);\r\n\r\n            GameIns.gameRuleManager.updateGameLogic(dt);\r\n\r\n            //子弹发射器系统\r\n            BulletSystem.tick(dt);\r\n\r\n            this._gameTime += dt;\r\n        } else if (GameIns.gamePlaneManager) {\r\n            GameIns.gamePlaneManager.enemyTarget = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * 战斗失败逻辑\r\n     */\r\n    async battleDie() {\r\n        // GameFunc.addDialog(ReplayUI.default);\r\n        GameIns.gameRuleManager.gamePause();\r\n    }\r\n\r\n    //     /**\r\n    //      * 战斗复活逻辑\r\n    //      */\r\n    //     relifeBattle() {\r\n    //         GameIns.eventManager.emit(GameEvent.MainRelife);\r\n    //         GameIns.gameRuleManager.gameResume();\r\n    //     }\r\n\r\n    /**\r\n     * 战斗失败结算\r\n     */\r\n    battleFail() {\r\n        GameIns.gameMainUI!.showGameResult(false);\r\n        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;\r\n        this.endBattle();\r\n    }\r\n\r\n    /**\r\n     * 战斗胜利逻辑\r\n     */\r\n    battleSucc() {\r\n        GameIns.mainPlaneManager.mainPlane!.hpNode!.active = false;\r\n        this.endBattle();\r\n\r\n        if (GameIns.stageManager.checkStage(this.mainStage,this.subStage + 1)){\r\n            this.startNextBattle();\r\n        }else{\r\n            GameIns.gameMainUI!.showGameResult(true);\r\n        }\r\n    }\r\n        /**\r\n     * 继续下一场战斗\r\n     */\r\n    startNextBattle() {\r\n        this.subReset();\r\n        this.subStage += 1;\r\n        this.initBattle();\r\n    }\r\n\r\n    /**\r\n     * 结束战斗\r\n     */\r\n    endBattle() {\r\n        GameIns.gameRuleManager.gameOver();\r\n        GameIns.bulletManager.removeAll(false, true);\r\n\r\n        this.gameStart = false;\r\n        this.initBattleEnd = false;\r\n    }\r\n\r\n\r\n    /**\r\n     * Boss切换完成\r\n     * @param {string} bossName Boss名称\r\n     */\r\n    bossChangeFinish(bossName: string) {\r\n        // const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);\r\n        // if (bossEnterDialog) {\r\n        //     bossEnterDialog.node.active = true;\r\n        //     GameIns.mainPlaneManager.moveAble = false;\r\n        //     bossEnterDialog.showTips(bossName);\r\n        // }\r\n    }\r\n\r\n    bossWillEnter() {\r\n        //        GameIns.mainPlaneManager.fireEnable = false;\r\n        //        GameIns.mainPlaneManager.moveAble = false;\r\n        //         WinePlaneManager.default.me.pauseBattle();\r\n\r\n        //         const inGameUI = GameIns.uiManager.getDialog(InGameUI.default);\r\n        //         if (inGameUI) {\r\n        //             inGameUI.hideUI();\r\n        //         }\r\n\r\n        //         const bossEnterDialog = GameIns.uiManager.getDialog(BossEnterDialog.default);\r\n        //         if (bossEnterDialog) {\r\n        //             if (!bossEnterDialog.node.parent) {\r\n        //                 GameIns.uiManager.addDialog(BossEnterDialog.default, bossEnterDialog);\r\n        //             }\r\n        //             bossEnterDialog.node.active = true;\r\n        //             bossEnterDialog.play();\r\n        //         }\r\n\r\n        //         GameIns.audioManager.playbg(\"bg_3\");\r\n    }\r\n    /**\r\n     * 开始Boss战斗\r\n     */\r\n    bossFightStart() {\r\n        GameIns.mainPlaneManager.mainPlane!.setFireEnable(true);\r\n        GameIns.mainPlaneManager.moveAble = true;\r\n\r\n        GameIns.bossManager.bossFightStart();\r\n    }\r\n\r\n    /**\r\n     * 获取屏幕比例\r\n     * @returns {number} 屏幕比例\r\n     */\r\n    getRatio() {\r\n        return 0.666667; // 固定比例值\r\n    }\r\n\r\n    isGameType(gameType : number) {\r\n        return this.gameType == gameType;\r\n    }\r\n}"]}