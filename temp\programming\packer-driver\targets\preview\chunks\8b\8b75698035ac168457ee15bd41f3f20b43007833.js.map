{"version": 3, "sources": ["file:///E:/M2Game/Client/assets/scripts/ui/DevLoginUI.ts"], "names": ["_decorator", "<PERSON><PERSON>", "EditBox", "csproto", "MyApp", "DevLoginData", "logDebug", "logError", "BundleName", "initBundle", "DataMgr", "uiSelect", "BaseUI", "<PERSON><PERSON><PERSON><PERSON>", "UIMgr", "ccclass", "property", "DevLoginUI", "getUrl", "<PERSON><PERSON><PERSON><PERSON>", "Top", "onHide", "onShow", "loginButton", "node", "on", "EventType", "CLICK", "onLoginButtonClick", "netMgr", "registerHandler", "cs", "CS_CMD", "CS_CMD_GET_ROLE", "onGetRole", "serverList", "for<PERSON>ach", "value", "key", "serverSelect", "itemDatas", "push", "setChooseItemData", "instance", "servername", "onChooseItem", "itemData", "usernameEditBox", "string", "user", "passwordEditBox", "password", "onClose", "unregister<PERSON><PERSON><PERSON>", "username", "platformSDK", "login", "err", "info", "msg", "closeUI", "needLogin", "init", "Gm"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAASA,MAAAA,U,OAAAA,U;AAAYC,MAAAA,M,OAAAA,M;AAAQC,MAAAA,O,OAAAA,O;;AACtBC,MAAAA,O;;AACEC,MAAAA,K,iBAAAA,K;;AACAC,MAAAA,Y,iBAAAA,Y;;AACAC,MAAAA,Q,iBAAAA,Q;AAAUC,MAAAA,Q,iBAAAA,Q;;AAEVC,MAAAA,U,iBAAAA,U;AAAYC,MAAAA,U,iBAAAA,U;;AACZC,MAAAA,O,iBAAAA,O;;AACAC,MAAAA,Q,iBAAAA,Q;;AACAC,MAAAA,M,iBAAAA,M;AAAQC,MAAAA,O,iBAAAA,O;AAASC,MAAAA,K,iBAAAA,K;;;;;;;;;OACpB;AAAEC,QAAAA,OAAF;AAAWC,QAAAA;AAAX,O,GAAwBhB,U;;4BAGjBiB,U,WADZF,OAAO,CAAC,YAAD,C,UAMHC,QAAQ,CAACf,MAAD,C,UAERe,QAAQ,CAACd,OAAD,C,UAERc,QAAQ,CAACd,OAAD,C,UAGRc,QAAQ;AAAA;AAAA,+B,sCAbb,MACaC,UADb;AAAA;AAAA,4BACuC;AAAA;AAAA;;AAAA;;AAAA;;AAAA;;AAAA;AAAA;;AACf,eAANC,MAAM,GAAW;AAAE,iBAAO,eAAP;AAAwB;;AACnC,eAARC,QAAQ,GAAY;AAAE,iBAAO;AAAA;AAAA,kCAAQC,GAAf;AAAoB;;AAalDC,QAAAA,MAAM,GAAgC;AAAA;AAC3C;;AAEKC,QAAAA,MAAM,GAAgC;AAAA;;AAAA;AACxC,YAAA,KAAI,CAACC,WAAL,CAAiBC,IAAjB,CAAsBC,EAAtB,CAAyBxB,MAAM,CAACyB,SAAP,CAAiBC,KAA1C,EAAiD,KAAI,CAACC,kBAAtD,EAA0E,KAA1E;;AACA;AAAA;AAAA,gCAAMC,MAAN,CAAaC,eAAb,CAA6B;AAAA;AAAA,oCAAQC,EAAR,CAAWC,MAAX,CAAkBC,eAA/C,EAAgE,KAAI,CAACC,SAArE,EAAgF,KAAhF;AAEA;AAAA;AAAA,8CAAaC,UAAb,CAAwBC,OAAxB,CAAgC,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAC5C,cAAA,KAAI,CAACC,YAAL,CAAkBC,SAAlB,CAA4BC,IAA5B,CAAiCH,GAAjC;;AACA,cAAA,KAAI,CAACC,YAAL,CAAkBC,SAAlB,CAA4BC,IAA5B,CAAiCH,GAAG,GAAG,GAAvC;AACH,aAHD;;AAIA,YAAA,KAAI,CAACC,YAAL,CAAkBG,iBAAlB,CAAoC;AAAA;AAAA,8CAAaC,QAAb,CAAsBC,UAA1D;;AACA,YAAA,KAAI,CAACL,YAAL,CAAkBM,YAAlB,GAAkCC,QAAD,IAAsB;AACnD;AAAA;AAAA,wCAAS,SAAT,qBAAqCA,QAArC;AACA;AAAA;AAAA,gDAAaH,QAAb,CAAsBC,UAAtB,GAAmCE,QAAnC;AACH,aAHD;;AAIA,YAAA,KAAI,CAACC,eAAL,CAAqBC,MAArB,GAA8B;AAAA;AAAA,8CAAaL,QAAb,CAAsBM,IAApD;AACA,YAAA,KAAI,CAACC,eAAL,CAAqBF,MAArB,GAA8B;AAAA;AAAA,8CAAaL,QAAb,CAAsBQ,QAApD;AAdwC;AAe3C;;AAEKC,QAAAA,OAAO,GAAgC;AAAA;;AAAA;AACzC;AAAA;AAAA,gCAAMvB,MAAN,CAAawB,iBAAb,CAA+B;AAAA;AAAA,oCAAQtB,EAAR,CAAWC,MAAX,CAAkBC,eAAjD,EAAkE,MAAI,CAACC,SAAvE,EAAkF,MAAlF;AADyC;AAE5C;;AAEDN,QAAAA,kBAAkB,GAAG;AACjB,cAAI0B,QAAQ,GAAG,KAAKP,eAAL,CAAqBC,MAApC;AACA,cAAIG,QAAQ,GAAG,KAAKD,eAAL,CAAqBF,MAApC;AACA;AAAA;AAAA,4CAAaL,QAAb,CAAsBM,IAAtB,GAA6BK,QAA7B;AACA;AAAA;AAAA,4CAAaX,QAAb,CAAsBQ,QAAtB,GAAiCA,QAAjC;AAEA;AAAA;AAAA,8BAAMI,WAAN,CAAkBC,KAAlB,CAAwB,CAACC,GAAD,EAAMC,IAAN,KAAe;AACnC,gBAAID,GAAJ,EAAS;AACL;AAAA;AAAA,wCAAS,YAAT,oBAAuCA,GAAvC;AACA;AACH;;AACD;AAAA;AAAA,gCAAM5B,MAAN,CAAa2B,KAAb,CAAmBE,IAAnB;AACH,WAND;AAOH;;AAEDxB,QAAAA,SAAS,CAACyB,GAAD,EAA0B;AAC/B;AAAA;AAAA,8BAAMC,OAAN,CAAc3C,UAAd;AACAA,UAAAA,UAAU,CAAC4C,SAAX,GAAuB,KAAvB;AACA;AAAA;AAAA,kCAAQC,IAAR;AACA;AAAA;AAAA,wCAAW;AAAA;AAAA,wCAAWC,EAAtB;AACH;;AA3DkC,O,UAG5BF,S,GAAY,I;;;;;iBAGG,I;;;;;;;iBAEK,I;;;;;;;iBAEA,I;;;;;;;iBAGF,I", "sourcesContent": ["import { _decorator, But<PERSON>, EditBox } from 'cc';\nimport csproto from '../AutoGen/PB/cs_proto.js';\nimport { MyApp } from '../MyApp';\nimport { DevLoginData } from '../PlatformSDK/DevLoginData';\nimport { logDebug, logError } from '../Utils/Logger';\n\nimport { BundleName, initBundle } from 'db://assets/bundles/Bundle';\nimport { DataMgr } from '../../bundles/common/script/data/DataManager';\nimport { uiSelect } from '../../bundles/common/script/ui/common/components/SelectList/uiSelect';\nimport { BaseUI, UILayer, UIMgr } from './UIMgr';\nconst { ccclass, property } = _decorator;\n\n@ccclass('DevLoginUI')\nexport class DevLoginUI extends BaseUI {\n    public static getUrl(): string { return \"ui/DevLoginUI\" };\n    public static getLayer(): UILayer { return UILayer.Top }\n    static needLogin = true;\n\n    @property(Button)\n    loginButton: Button = null!;\n    @property(EditBox)\n    usernameEditBox: EditBox = null!;\n    @property(EditBox)\n    passwordEditBox: EditBox = null!;\n\n    @property(uiSelect)\n    serverSelect: uiSelect = null!;\n\n    async onHide(...args: any[]): Promise<void> {\n    }\n\n    async onShow(...args: any[]): Promise<void> {\n        this.loginButton.node.on(Button.EventType.CLICK, this.onLoginButtonClick, this);\n        MyApp.netMgr.registerHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole, this);\n\n        DevLoginData.serverList.forEach((value, key) => {\n            this.serverSelect.itemDatas.push(key)\n            this.serverSelect.itemDatas.push(key + \"1\")\n        });\n        this.serverSelect.setChooseItemData(DevLoginData.instance.servername)\n        this.serverSelect.onChooseItem = (itemData: string) => {\n            logDebug(\"LoginUI\", `choose server ${itemData}`)\n            DevLoginData.instance.servername = itemData;\n        }\n        this.usernameEditBox.string = DevLoginData.instance.user;\n        this.passwordEditBox.string = DevLoginData.instance.password;\n    }\n\n    async onClose(...args: any[]): Promise<void> {\n        MyApp.netMgr.unregisterHandler(csproto.cs.CS_CMD.CS_CMD_GET_ROLE, this.onGetRole, this)\n    }\n\n    onLoginButtonClick() {\n        var username = this.usernameEditBox.string;\n        var password = this.passwordEditBox.string;\n        DevLoginData.instance.user = username;\n        DevLoginData.instance.password = password;\n\n        MyApp.platformSDK.login((err, info) => {\n            if (err) {\n                logError(\"DevLoginUI\", `login failed ${err}`);\n                return;\n            }\n            MyApp.netMgr.login(info);\n        });\n    }\n\n    onGetRole(msg: csproto.cs.IS2CMsg) {\n        UIMgr.closeUI(DevLoginUI);\n        DevLoginUI.needLogin = false;\n        DataMgr.init()\n        initBundle(BundleName.Gm)\n    }\n}"]}