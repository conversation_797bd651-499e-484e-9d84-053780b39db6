System.register(["cc"], function (_export, _context) {
  "use strict";

  var _cclegacy, _crd, TabStatus, BagSortType, EquipMentAction, OpenEquipInfoUISource;

  return {
    setters: [function (_cc) {
      _cclegacy = _cc.cclegacy;
    }],
    execute: function () {
      _crd = true;

      _cclegacy._RF.push({}, "d71bazAMe1IL6WKavjuPdtF", "PlaneTypes", undefined);

      _export("TabStatus", TabStatus = /*#__PURE__*/function (TabStatus) {
        TabStatus["None"] = "None";
        TabStatus["Bag"] = "Bag";
        TabStatus["Merge"] = "Merge";
        return TabStatus;
      }({}));

      _export("BagSortType", BagSortType = /*#__PURE__*/function (BagSortType) {
        BagSortType["None"] = "None";
        BagSortType["Quality"] = "Quality";
        BagSortType["Part"] = "Part";
        BagSortType["Merge"] = "Merge";
        return BagSortType;
      }({}));

      _export("EquipMentAction", EquipMentAction = /*#__PURE__*/function (EquipMentAction) {
        EquipMentAction["Equip"] = "Equip";
        EquipMentAction["UnEquip"] = "UnEquip";
        return EquipMentAction;
      }({}));

      _export("OpenEquipInfoUISource", OpenEquipInfoUISource = /*#__PURE__*/function (OpenEquipInfoUISource) {
        OpenEquipInfoUISource["DisPlay"] = "DisPlay";
        OpenEquipInfoUISource["BagGrid"] = "BagGrid";
        return OpenEquipInfoUISource;
      }({}));

      _cclegacy._RF.pop();

      _crd = false;
    }
  };
});
//# sourceMappingURL=3764cf8be269f5acce3c0a176d2b0406bc950c0e.js.map